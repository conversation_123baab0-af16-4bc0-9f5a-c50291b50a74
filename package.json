{"name": "web", "private": true, "scripts": {"build": "dotenv -e env/.env.production -- turbo run build", "build:deploy": "dotenv -e env/.env.deploy -- turbo run build:deploy", "build:test": "dotenv -e env/.env.test -- turbo run build:test", "build:whdev": "dotenv -e env/.env.whdev -- turbo run build:whdev", "build:whprod": "dotenv -e env/.env.whprod -- turbo run build:whprod", "build:v2cloud": "dotenv -e env/.env.v2cloud -- turbo run build:v2cloud", "pack:dev": "dotenv -e ./env/.env.deploy -- pnpm -F electron-main run pack:dev", "pack:preview": "dotenv -e ./env/.env.deploy -- pnpm -F electron-main preview", "pack:deploy": "dotenv -e ./env/.env.deploy -- pnpm -F electron-main pack:deploy", "dev": "dotenv -e ./env/.env.dev -- turbo dev", "dev:mp-weixin": "dotenv -e ./env/.env.dev -- turbo dev:mp-weixin", "dev:h5": "dotenv -e ./env/.env.dev -- turbo dev:h5", "switch-env": "node ./scripts/copy-deploy-env.js", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "openapi": "node --loader ts-node/esm ./packages/config/openapi.config.ts", "release:patch": "standard-version --release-as patch && lerna version patch --yes --force-publish", "release:minor": "standard-version --release-as minor && lerna version minor --yes --force-publish", "release:major": "standard-version --release-as major && lerna version major --yes --force-publish"}, "standard-version": {"skip": {"tag": true}}, "dependencies": {"@arco-design/web-vue": "^2.56.1", "dayjs": "^1.11.13", "lodash": "^4.17.21", "marked": "^15.0.12", "pinia": "^2.2.2"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/lbs": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/lodash": "^4.17.7", "@types/node": "^20.16.5", "@umijs/openapi": "^1.12.1", "autoprefixer": "^10.4.20", "axios": "^1.7.7", "dotenv": "^16.4.7", "dotenv-cli": "latest", "eslint": "^8.57.0", "fs-extra": "^11.2.0", "lerna": "^8.1.9", "postcss": "^8.4.45", "postcss-html": "^1.7.0", "prettier": "^3.3.3", "standard-version": "^9.5.0", "tailwindcss": "^3.4.10", "ts-node": "^10.9.2", "turbo": "latest", "typescript": "^5.6.2", "vite": "^6.0.9"}, "engines": {"node": ">=18"}, "packageManager": "pnpm@9.9.0", "version": "2.1.53"}