const packageJsonInfo = require('../package.json');

module.exports = {
  latestVersion: {
    version: packageJsonInfo.version,
    description: 'ing',
  },
  getPrivateKeyPath: () => {
    const path = require('path');
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const projectConfigPath = path.resolve(process.cwd(), './dist/build/mp-weixin/project.config.json');
    const appId = require(projectConfigPath).appid;
    return path.resolve(__dirname, `./keys/private.${appId}.key`);
  },
};
