import { uniPostcssPlugin } from '@dcloudio/uni-cli-shared';
import tailwindcss from 'tailwindcss';
import nested from 'tailwindcss/nesting';
import tailwindcssConfig from './tailwind.config.cjs'; // 注意匹配实际文件
import postcssPresetEnv from 'postcss-preset-env';
import uniTailwind from '@uni-helper/vite-plugin-uni-tailwind';

const uniInputDir = process.env.UNI_INPUT_DIR;

export default {
  plugins: [
    nested(),
    tailwindcss({
      config: tailwindcssConfig,
    }),
    postcssPresetEnv({
      stage: 3,
      features: { 'nesting-rules': false },
    }),
    // postcssImport({
    //   resolve(id) {
    //     if (id.startsWith('~@/')) {
    //       return path.resolve(uniInputDir, id.substr(3));
    //     } else if (id.startsWith('@/')) {
    //       return path.resolve(uniInputDir, id.substr(2));
    //     } else if (id.startsWith('/') && !id.startsWith('//')) {
    //       return path.resolve(uniInputDir, id.substr(1));
    //     }
    //     return id;
    //   },
    // }),
    // tailwindcss(),
    // 使用postcss-class-name 包将小程序不支持的类名转换为支持的类名
    // postcssClassRename({
    //   '\\\\:': '--',
    //   '\\\\/': '--',
    //   '\\\\.': '--',
    //   // '.:': '--',
    //   '\\*': '--',
    // }),
    // cssByebye({
    //   rulesToRemove: [/\*/],
    //   map: false,
    // }),
    uniPostcssPlugin(),
    // autoprefixer({
    //   remove: true
    // }),
    // require('tailwindcss'),
    require('postcss-rem-to-responsive-pixel')({
      rootValue: 32,
      propList: ['*'],
      transformUnit: 'rpx',
    }),
  ],
};
