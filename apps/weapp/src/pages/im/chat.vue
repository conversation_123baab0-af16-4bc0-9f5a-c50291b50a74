<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { onLoad } from '@dcloudio/uni-app';
  import { useGuardianStore } from '@/common/store/gardian';
  import openapi from '@repo/infrastructure/openapi';
  import BottomSafe from '@/components/bottomSafe.vue';
  import useWebSocket from '@/common/hooks/websocket';
  import MessageList from '@/components/im/messageList.vue';
  import Avatar from '@/components/im/avatar.vue';
  import { getAppMode, PROJECT_URLS } from '@/common/env';

  const conversation = ref<API.Conversation>();
  const guardianStore = useGuardianStore();
  const userInfo = guardianStore.getUserInfo();
  const currentContent = ref('');
  const messageListRef = ref();

  const { sendChatMessage, onMessage } = useWebSocket();
  const loadConversation = async (id: number) => {
    const { data } = await openapi.conversationController.guardianGetConversation(
      {
        id,
      },
      { baseURL: PROJECT_URLS.GO_PROJECT_API },
    );

    conversation.value = data;
  };

  const me = computed(() => {
    return conversation.value?.fromUser?.userId === userInfo.id
      ? conversation.value?.fromUser
      : conversation.value?.toUser;
  });

  const other = computed<API.ConversationUser>(() => {
    return (
      conversation.value?.fromUser?.userId === userInfo.id ? conversation.value?.toUser : conversation.value?.fromUser
    ) as API.ConversationUser;
  });

  const handleSend = async () => {
    if (!currentContent.value?.trim()) {
      uni.showToast({
        title: '请输入消息内容',
        icon: 'none',
      });
      return;
    }
    if (other.value?.sessionId) {
      try {
        if (!conversation.value?.id) {
          uni.showToast({
            title: '会话不存在',
            icon: 'none',
          });
          return;
        }
        const sendedMsg = await sendChatMessage({
          data: currentContent.value,
          conversationId: conversation.value?.id,
        });

        currentContent.value = '';
        await messageListRef.value.onNewMessageSend(sendedMsg, me.value?.sessionId);
      } catch (e) {
        console.error(e);
        uni.showToast({
          title: '发送失败',
          icon: 'none',
        });
        const currentEnv = getAppMode();
        if (currentEnv !== 'release') {
          uni.showModal({
            title: 'error',
            content: JSON.stringify(e),
            icon: 'none',
          });
        }
      }
    }
  };

  const handleReceiveMessage = async (msg: any) => {
    if (msg.type === 'userOnline' && msg.data === other.value?.sessionId) {
      other.value.online = true;
      return;
    }
    if (msg.type === 'userOffline' && msg.data === other.value?.sessionId) {
      other.value.online = false;
      return;
    }
    if (msg.conversationId === conversation.value?.id) {
      await messageListRef.value.onNewMessageSend(msg, other.value?.sessionId);
    }
  };

  onLoad(async (query: any) => {
    await loadConversation(query.conversationId);
    await openapi.conversationController.setLastMessageRead(
      {
        conversationId: query.conversationId,
      },
      { baseURL: PROJECT_URLS.GO_PROJECT_API },
    );
    onMessage(async (msg: any) => {
      await handleReceiveMessage(msg);
    });
  });
</script>

<template>
  <view class="conversation-wrapper flex flex-col">
    <view
      v-if="conversation"
      class="header bg-slate-100 p-2 border-0 border-b border-solid border-b-slate-300 flex gap-4 items-center"
    >
      <avatar :avatar-resource-id="other.avatar" />
      <view class="flex gap-2 items-center flex-1">
        <view>{{ other.name }}</view>
        <view class="text-gray-500 text-xs">{{ other.description }}</view>
      </view>
      <view class="">
        <view v-if="other.online" class="flex gap-1 items-center text-xs">
          <uni-badge text=" " type="success" />
          在线
        </view>
        <view v-else class="flex gap-1 items-center text-xs">
          <uni-badge text=" " type="info" />
          离线
        </view>
      </view>
    </view>
    <message-list
      v-if="conversation?.id"
      ref="messageListRef"
      class="flex-1 overflow-hidden"
      :me="me"
      :other="other"
      :conversation="conversation"
    />
    <textarea
      v-model="currentContent"
      class="w-full h-12 bg-transparen footer bg-slate-100 px-2 py-3 border-0 border-t border-solid border-t-slate-300"
      :always-embed="true"
      :adjust-position="true"
      :cursor-spacing="30"
      :fixed="true"
      :hold-keyboard="true"
      :confirm-hold="true"
      :show-confirm-bar="false"
      confirm-type="send"
      placeholder="请输入消息内容"
      @confirm="handleSend"
      @keydown.enter="handleSend"
    />
    <bottom-safe class="bg-slate-100" />
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarBackgroundColor": "#1989fa",
    "navigationBarTextStyle": "white",
    "navigationBarTitleText": "在线咨询"
  }
}
</route>

<style scoped lang="scss">
  .conversation-wrapper {
    background: #f2f2f2;
    height: 100vh;
  }
</style>
