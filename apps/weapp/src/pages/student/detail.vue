<script setup lang="ts">
  import { onLoad, onShow } from '@dcloudio/uni-app';
  import { ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@/common/env';

  const student = ref<any>({});

  const loadStudent = async (id) => {
    uni.showLoading({
      title: '加载中',
    });
    try {
      const { data } = await request(`/resourceRoom/student/${id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      student.value = data;

      uni.setNavigationBarTitle({
        title: data.name,
      });
    } finally {
      uni.hideLoading();
    }
  };

  const handleMakePhoneCall = (phone) => {
    if (!phone) {
      return;
    }
    uni.makePhoneCall({
      phoneNumber: phone,
    });
  };

  onLoad(async (query: any) => {
    await loadStudent(query.id);
  });
</script>

<template>
  <view class="bg-gray-global py-4 px-2">
    <uni-list>
      <uni-list-item title="姓名" :right-text="student.name" />
      <uni-list-item title="系统证号" :right-text="student.symbol || '-'" />
      <uni-list-item title="性别" :right-text="student.gender" />
      <uni-list-item title="年龄" :right-text="`${student.age || '-'} 岁`" />
      <uni-list-item title="学校" :right-text="student.fusionSchool?.name || '-'" />
      <uni-list-item title="班级" :right-text="student.gradeClass?.name || '-'" />
      <uni-list-item title="民族" :right-text="student.nation || '-'" />
      <uni-list-item title="出生日期" :right-text="student.birthday || '-'" />
      <uni-list-item title="障碍类型" :right-text="student.disorders || '-'" />
      <uni-list-item title="残疾等级" :right-text="student.disabilityLevel || '-'" />
      <uni-list-item title="残疾证号" :right-text="student.disabilityCertificateNo || '-'" />
      <uni-list-item title="家庭住址" :right-text="student.address || '-'" />
      <uni-list-item title="监护人" :right-text="student.guardian || '-'" />
      <uni-list-item title="联系电话" :right-text="student.guardianPhone || '-'" />
      <uni-list-item title="班主任" :right-text="student.classMaster || '-'" />
    </uni-list>

    <view class="mt-2">
      <uni-list>
        <uni-list-item title="家庭状况" :note="student.familyStatus" />
        <uni-list-item title="发展史" :note="student.historyOfDevelopment" />
        <uni-list-item title="教育史" :note="student.historyOfEducation" />
      </uni-list>
    </view>

    <view class="mt-2">
      <uni-section v-if="student.familyMembers?.length" title="家庭成员" type="line">
        <uni-list>
          <uni-list-item
            v-for="(fm, idx) in student.familyMembers"
            :key="idx"
            :title="fm.relationShip ? `[${fm.relationShip}] ${fm.name}` : fm.name"
            :right-text="fm.phone"
            clickable
            show-arrow
            @click="() => handleMakePhoneCall(fm.phone)"
          />
        </uni-list>
      </uni-section>
    </view>
  </view>
  <bottom-safe />
</template>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "我的学生",
    "enablePullDownRefresh": true
  }
}
</route>

<style scoped lang="scss"></style>
