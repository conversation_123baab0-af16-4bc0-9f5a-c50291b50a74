<script setup lang="ts">
  import { onLoad, onPullDownRefresh, onReachBottom, onShow } from '@dcloudio/uni-app';
  import useList from '@/common/hooks/list';
  import { PROJECT_URLS } from '@/common/env';
  import { goPage } from '@/common/page';
  import Empty from '@/components/empty.vue';
  import { ref,computed } from 'vue';
  import {request} from "@repo/infrastructure/request";
  import {watch} from "vue";

  let api = '/resourceRoom/sendEducationPlan'
  const { dataList, refresh, hasMore, loadMore, queryParams, loading } = useList(
    {
      api: api,
    },
    {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    },
  );

  const teacherMode = ref(false);

  const handleGoDetail = (item: any) => {
    goPage(`/student/sendEdu/${isArchiveType.value?'archiveDetail':'detail'}?id=${item.id}&teacherMode=${teacherMode.value ? '1' : ''}&isParent=${isParent.value}`,{
      isParent:isParent.value
    });
  };

  onPullDownRefresh(async () => {
    if(isArchiveType.value){
      await loadPlan(api);
    }else{
      await refresh();
      uni.stopPullDownRefresh();
    }
  });


  const studentId = ref<any>(null)
  const loadPlan = async (api:string)=>{
    let params:any={};
    if(studentId.value!==null && studentId.value!==undefined){
      params={
        studentId:studentId.value,
      }
    }

    const { data } = await request(api, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'GET',
      params,
    });
    dataList.value = data?.items || [data] || []
  }

  onShow(async () => {
    if(isArchiveType.value){
      await loadPlan(api);
    }else{
      await refresh();
    }
  });

  const isReady = ref(false);
  const sendVersion = ref()
  const getSendVersion = async ()=>{
      const { data } = await request('/resourceRoom/centralConfiguration/findByCompany', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'GET',
      });
    sendVersion.value = data.sendVersion;
  }

  const isArchiveType = computed(()=>{
    return sendVersion.value===2
  })



  const isParent = ref(false)
  // 如果有学生id 说明是家长 如果是teacherModal 说明是教师
  onLoad(async (query: any) => {
    await getSendVersion();
    queryParams.value = {
      ...queryParams.value,
      student: query.studentId,
      submitStatus: query.submitStatus,
    };

    studentId.value = Number(query?.studentId) || null;
    isParent.value = query?.isParent === 'true'
    console.log(query,studentId.value)
    teacherMode.value = query.teacherMode === '1';

    if(isArchiveType.value){
      if(studentId.value!==null && studentId.value!==undefined){
        // 家长
        api = `/resourceRoom/d2dEducation/findMyKidPlan/${studentId.value}`
      }else{
        // 教师
        api = '/resourceRoom/d2dEducation'
      }
      await loadPlan(api);
    }
    isReady.value= true
  });

  watch(()=>isArchiveType.value,async(newVal)=>{
    if(newVal){
      api = '/resourceRoom/d2dEducation'
      await loadPlan(api);
    }
  })

  onReachBottom(async () => {
    if(isArchiveType.value){
      await loadPlan(api); // 触底加载更多page ++
    }else{
      await loadMore();
    }
  });
</script>

<template>
  <view class="p-4">
    <uni-section v-if="isReady" :title="isArchiveType?'送教档案':'送教计划'" type="line">
      <uni-list v-if="dataList.length && !teacherMode" :border="false">
        <uni-list-item
          v-for="item in dataList"
          :key="item.id"
          :right-text="item.personInCharge ? `负责人：${item.personInCharge}` : ''"
          :title="item.dateRange?.join(' ~ ')"
          clickable
          show-arrow
          @click="goPage(`/student/sendEdu/detail?id=${item.id}`)"
        />
      </uni-list>
      <uni-list v-else-if="dataList.length && teacherMode" :border="false">
        <uni-list-item v-for="item in dataList" :key="item.id" clickable show-arrow @click="() => handleGoDetail(item)">
          <template #header>
            <view>
              {{ item.student.name }}
              <text class="text-xs">
                {{ item.period }}
              </text>
            </view>
            <view class="text-xs text-gray-400 py-1">
              {{ item.dateRange?.join(' ~ ') }}
            </view>
          </template>
        </uni-list-item>
      </uni-list>
    </uni-section>

    <empty v-if="dataList.length === 0 && !loading" title="暂无数据" />
    <empty v-else-if="!hasMore" />
    <bottom-safe />
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "送教计划",
    "enablePullDownRefresh": true
  }
}
</route>

<style scoped lang="scss"></style>
