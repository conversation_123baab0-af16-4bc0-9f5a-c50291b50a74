<script setup lang="ts">
import {ref} from 'vue';
import {onLoad, onPullDownRefresh, onShow} from '@dcloudio/uni-app';
import {request} from '@repo/infrastructure/request';
import {PROJECT_URLS} from '@/common/env';
import Empty from '@/components/empty.vue';
import useDocument from '@/common/hooks/document';
import BottomSafe from '@/components/bottomSafe.vue';
import {goPage, goWebview} from '@/common/page';

const d2d = ref<any>({});
const records = ref<any[]>([]);
const d2dId = ref(0);

const {handlePreviewDoc} = useDocument();
const useCustomizedComponent = ref(false);
const teacherMode = ref(false);
const isArchiveType = ref(false);

// 控制加载的api
const loadData = async () => {
  if (d2dId.value <= 0) {
    return;
  }

  uni.showLoading({title: '加载中', mask: true});
  try {
    const {data} = (await request(`/resourceRoom/sendEducationPlan/${d2dId.value}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    })) as any;

    const {data: recordsList} = await request(`/resourceRoom/sendEducationRecord`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        sendEducationPlan: data.id,
        pageSize: 999,
      },
    });

    d2d.value = data as any;
    records.value = (recordsList?.items as any[]) || [];
  } finally {
    uni.hideLoading();
  }
};

const handleGoCustomDetail = () => {
  goWebview(`/sendEducationPlan/detail?id=${d2dId.value}`);
};

const handleGoEditRecord = (record?: any) => {
  // if (useCustomizedComponent.value) {
  goWebview(`/sendEducationPlan/editRecord?d2dId=${d2dId.value}&id=${record?.id || ''}`);
  // }
};

const handleGoRecordDetail = (record: any) => {
  goWebview(
      `/sendEducationPlan/recordDetail?id=${record.id}&d2dId=${d2dId.value}&teacherMode=${teacherMode.value ? '1' : ''}`,
  );
  // if (useCustomizedComponent.value) {
  //   goWebview(`/sendEducationPlan/recordDetail?id=${record.id}&d2dId=${d2dId.value}`);
  // } else {
  //   goPage('/student/sendEdu/record', {
  //     id: record.id,
  //     d2dId: d2d.value.id,
  //   });
  // }
};

onLoad(async (query: any) => {
  d2dId.value = query.id;
  teacherMode.value = query.teacherMode;
  isArchiveType.value = query.isArchiveType;
  if(!isArchiveType.value){
    const {data} = await request('/resourceRoom/centralConfiguration/customizeComponent', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        page: 'View',
        client: 'UniAPP',
        module: 'SendEducationPlan',
      },
    });

    useCustomizedComponent.value = !!data;
  }
});

onShow(async () => {
  await loadData();
});

onPullDownRefresh(async () => {
  await loadData();
  uni.stopPullDownRefresh();
});
</script>

<template>
  <view v-if="d2d.id" class="p-4">
    <view>
      <uni-section title="基本信息" type="line">
        <uni-list :border="false">
          <uni-list-item :title="d2d.dateRange?.join(' ~ ')"/>
          <uni-list-item title="状态" :right-text="d2d.finished ? '已结束' : '进行中'"/>
          <uni-list-item title="学生姓名" :right-text="`${d2d.student?.gradeClass?.name} ${d2d.student?.name}`"/>
          <uni-list-item title="所属学校" :right-text="d2d.student?.fusionSchoolName"/>
          <uni-list-item title="学期" :right-text="d2d.period"/>
          <uni-list-item title="送教负责人" :right-text="d2d.personInCharge"/>
          <uni-list-item title="制定计划教师" :note="d2d.planTeacher"/>
          <uni-list-item
              v-if="useCustomizedComponent"
              title="查看详情"
              clickable
              show-arrow
              @click="handleGoCustomDetail"
          />
        </uni-list>
      </uni-section>
    </view>

    <view v-if="!useCustomizedComponent" class="mt-2">
      <uni-section title="送教措施" type="line">
        <view v-if="d2d.sendEducationMeasures" class="py-2 px-4">
          {{ d2d.sendEducationMeasures }}
        </view>
        <empty v-else/>
      </uni-section>
    </view>

    <view v-if="!useCustomizedComponent" class="mt-2">
      <foldable-panel :title="`送教计划 (${d2d.planDetails?.length || 0} 个)`" type="line" :collapsed="true">
        <view v-for="(item, index) in d2d.planDetails" :key="index" class="p-2 bg-slate-100">
          <uni-list :border="false">
            <uni-list-item title="时间" :right-text="item.date"/>
            <uni-list-item title="负责人" :right-text="item.personInCharge || '无'"/>
            <uni-list-item title="教学目标" :note="item.target || '无'"/>
            <uni-list-item title="内容" :note="item.content || '无'"/>
            <uni-list-item title="备注" :note="item.remark || '无'"/>
          </uni-list>
        </view>
        <empty v-if="!d2d.planDetails?.length"/>
      </foldable-panel>
    </view>
    <view class="mt-2">
      <foldable-panel :title="`送教记录 (${records?.length || 0} 次)`" type="line" :collapsed="true">
        <uni-list :border="false">
          <uni-list-item
              v-for="(item, index) in records"
              :key="index"
              :title="`${item.date?.substr(0, 10)} ${item.type}`"
              :right-text="item.classHour ? `${item.classHour}课时` : ''"
              :note="`送教教师：${item.teacher}`"
              clickable
              show-arrow
              @click="() => handleGoRecordDetail(item)"
          />
        </uni-list>
        <empty v-if="!d2d.planDetails?.length"/>
      </foldable-panel>
    </view>
    <view v-if="!useCustomizedComponent" class="mt-2">
      <uni-section title="详情内容" type="line">
        <view class="py-2 px-4">
          <rich-text v-if="d2d.detailContent" :nodes="d2d.detailContent"/>
          <empty v-else/>
        </view>
      </uni-section>
    </view>

    <view v-if="teacherMode">
      <fixed-button-safe/>
      <fixed-bottom-button @click="handleGoEditRecord"> 创建送教记录</fixed-bottom-button>
    </view>
  </view>
</template>

<route lang="json">
{
"style": {
"navigationBarTitleText": "送教档案",
"enablePullDownRefresh": true
}
}
</route>

<style scoped lang="scss"></style>
