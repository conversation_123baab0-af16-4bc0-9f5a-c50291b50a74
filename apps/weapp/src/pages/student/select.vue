<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@/common/env';
  import { watch } from 'vue';
  import { onLoad } from '@dcloudio/uni-app';

  const selectedIds = ref([]);
  const multiple = ref(false);
  const studentList = ref<any[]>([]);

  const loadStudents = async () => {
    uni.showLoading({
      title: '加载中',
    });

    try {
      const {
        data: { items },
      } = await request({
        url: '/resourceRoom/student/simpleList',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          pageSize: 999,
        },
      });

      studentList.value = items || [];
    } finally {
      uni.hideLoading();
    }
  };

  const handleConfirm = () => {
    const selectedStudents = studentList.value.filter((student) => selectedIds.value.includes(student.id));

    uni.navigateBack({
      success: () => {
        uni.$emit('studentSelected', multiple.value ? selectedStudents : selectedStudents[0]);
      },
    });
  };

  const handleChangeSelected = (student: any) => {
    if (!selectedIds.value.includes(student.id)) {
      selectedIds.value.push(student.id);
    } else {
      selectedIds.value = selectedIds.value.filter((id) => id !== student.id);
    }

    uni.setNavigationBarTitle({
      title: `已选择 ${selectedIds.value.length} 人`,
    });
  };

  const handleSelectSingle = (student: any) => {
    if (multiple.value) {
      handleChangeSelected(student);
    } else {
      selectedIds.value = [student.id];
      handleConfirm();
    }
  };

  onLoad(async (query: any) => {
    selectedIds.value = query?.selectedIds?.split(',')?.map((item) => Number(item)) || [];
    multiple.value = query?.multiple === '1';

    uni.setNavigationBarTitle({
      title: `已选择 ${selectedIds.value.length} 人`,
    });

    await loadStudents();
  });
</script>

<template>
  <view class="bg-white m-2 p-3 rounded">
    <uni-list :border="false">
      <uni-list-item
        v-for="(student, idx) in studentList"
        :key="idx"
        :show-arrow="!multiple"
        clickable
        @click="() => handleSelectSingle(student)"
      >
        <template #header>
          <view class="flex gap-2 items-center">
            <view v-if="multiple">
              <checkbox-group>
                <checkbox :value="student.id" :checked="selectedIds.includes(student.id)" />
              </checkbox-group>
            </view>
            <view class="flex-1 text-sm flex gap-2 items-center">
              {{ student.name }}
              <view class="text-xs text-gray-400">
                {{ student.gender || '' }} {{ student.age || '-' }} 岁 {{ student.disorders || '' }}
              </view>
            </view>
          </view>
        </template>
      </uni-list-item>
    </uni-list>
  </view>

  <view v-if="multiple">
    <fixed-bottom-button @click="handleConfirm">
      <view> 完成 </view>
    </fixed-bottom-button>

    <fixed-button-safe />
  </view>
</template>

<style scoped lang="scss"></style>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "选择学生",
    "enablePullDownRefresh": true
  }
}
</route>
