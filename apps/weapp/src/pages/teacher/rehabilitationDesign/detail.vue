<script setup lang="ts">
  import { onLoad, onShow } from '@dcloudio/uni-app';
  import { computed, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import BottomSafe from '@/components/bottomSafe.vue';
  import { PROJECT_URLS } from '@/common/env';

  const recordId = ref(0);
  const currentTab = ref(0);
  const tabs = ['基本信息', '教学活动设计目标'];
  const record = ref({
    content: '',
  } as any);

  const handleTabSwitch = ({ currentIndex }: any) => {
    currentTab.value = currentIndex;
  };

  const loadDetailData = async () => {
    if (!recordId.value) {
      await uni.navigateBack();
    }

    const { data } = await request(`/rehabilitation/rehabilitationDesign/${recordId.value}`, {
      method: 'GET',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    record.value = data;

    uni.setNavigationBarTitle({
      title: data.subject,
    });
  };

  const completeImgTags = (htmlString: string) => {
    // 正则表达式匹配 <img ...> 但不包括自闭合的 <img .../>
    // 注意：这个简单的正则表达式可能无法处理所有复杂的HTML结构，特别是当属性或标签内包含特殊字符或注释时。
    const imgTagRegex = /<img(.*?)>/g;

    // 使用replace方法替换匹配的标签
    const completedHtmlString = htmlString.replace(imgTagRegex, function (match, p1) {
      // 确保我们只处理开放的<img>标签
      if (!/\/>$/.test(match)) {
        // 将开放标签替换为自闭合标签
        return `<img${p1} />`;
      }
      // 如果已经是自闭合标签，则不做任何更改
      return match;
    });

    return completedHtmlString;
  };

  const content = computed(() => {
    let newContent = record.value?.steps?.replace(/<img[^>]*>/gi, function (match: any) {
      match = match.replace(/style="[^"]+"/gi, '').replace(/style='[^']+'/gi, '');
      match = match.replace(/width="[^"]+"/gi, '').replace(/width='[^']+'/gi, '');
      match = match.replace(/height="[^"]+"/gi, '').replace(/height='[^']+'/gi, '');
      return match;
    });
    newContent = newContent.replace(/style="[^"]+"/gi, function (match: any) {
      match = match.replace(/width:[^;]+;/gi, 'max-width:100%;').replace(/width:[^;]+;/gi, 'max-width:100%;');
      return match;
    });
    // 替换掉 style
    newContent = newContent.replace(/style="[^"]?"/gi, '');

    // 图片最大宽度 600rpx
    newContent = newContent.replace(/<img(.*?)>/g, '<img$1 style="max-width: 100%; height: auto; margin: 10px 0">');

    // eg: <img src="" style="width: 100px; height: 100px;"> replace to <img src=""  style="width: 100px; height: 100px;" />

    newContent = completeImgTags(newContent);

    return newContent;
  });

  onLoad((query: any) => {
    recordId.value = query.id;
  });

  onShow(async () => {
    await loadDetailData();
  });
</script>

<template>
  <view v-if="record.id" class="p-3">
    <uni-segmented-control style-type="text" :current="currentTab" :values="tabs" @click-item="handleTabSwitch" />
    <view class="mt-4">
      <view v-if="currentTab === 0">
        <view class="border border-gray-50 rounded shadow bg-white">
          <uni-list :border="false">
            <uni-list-item title="康复机构" :right-text="record.rehabilitationInstitution?.name || '-'" />
            <uni-list-item title="执教老师" :right-text="record.rehabilitationEmployee?.name || '-'" />
            <uni-list-item title="上课时间" :right-text="record.classDate" />
            <uni-list-item title="教学主题" :note="record.subject" />
            <uni-list-item title="教学准备" :note="record.subject" />
            <uni-list-item title="教学内容及步骤" />
          </uni-list>

          <view class="px-4 pb-4">
            <rich-text class="content text-base text-gray-700" :nodes="content" />
          </view>
        </view>
      </view>
      <view v-else-if="currentTab === 1">
        <view
          v-for="(tc, idx) in record.targetContent"
          :key="idx"
          class="border border-gray-50 rounded shadow bg-white mb-2"
        >
          <uni-list :border="false">
            <uni-list-item title="目标内容" :note="tc" />
          </uni-list>
        </view>
      </view>
    </view>
    <bottom-safe />
  </view>
</template>

<style scoped lang="scss">
  .content {
    :deep {
      image,
      img {
        width: 100%;
      }
    }
  }
</style>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "康复设计",
    "enablePullDownRefresh": true,
    "navigationBarBackgroundColor": "#1989fa",
    "navigationBarTextStyle": "white"
  }
}
</route>
