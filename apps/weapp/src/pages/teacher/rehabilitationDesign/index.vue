<script setup lang="ts">
  import useList from '@/common/hooks/list';
  import { onLoad, onPullDownRefresh, onReachBottom, onShow } from '@dcloudio/uni-app';
  import Empty from '@/components/empty.vue';
  import dayjs from 'dayjs';
  import { goPage } from '@/common/page';
  import { PROJECT_URLS } from '@/common/env';

  const { dataList, refresh, loading, loadMore } = useList(
    {
      api: '/rehabilitation/rehabilitationDesign',
    },
    {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    },
  );

  const handleGoDetail = (item: any) => {
    goPage('/teacher/rehabilitationDesign/detail', {
      id: item.id,
    });
  };

  onPullDownRefresh(async () => {
    await refresh();
    uni.stopPullDownRefresh();
  });

  onShow(async () => {
    await refresh();
  });

  onReachBottom(async () => {
    await loadMore();
  });

  onLoad(async () => {});
</script>

<template>
  <view class="p-2">
    <view
      v-for="item in dataList"
      :key="item.id"
      class="border border-gray-50 rounded shadow bg-white"
      @click="() => handleGoDetail(item)"
    >
      <uni-list :border="false">
        <uni-list-item :title="item.subject" />
        <uni-list-item title="康复机构" :right-text="item.rehabilitationInstitution?.name || '-'" />
        <uni-list-item title="执教老师" :right-text="item.rehabilitationEmployee?.name || '-'" />
        <uni-list-item title="上课时间" :right-text="item.classDate" />
      </uni-list>
    </view>

    <empty v-if="dataList.length === 0 && !loading" title="暂无相关数据" />
    <!--    <empty v-else-if="!hasMore" />-->
  </view>
</template>

<style scoped lang="scss"></style>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "康复设计",
    "enablePullDownRefresh": true,
    "navigationBarBackgroundColor": "#1989fa",
    "navigationBarTextStyle": "white"
  }
}
</route>
