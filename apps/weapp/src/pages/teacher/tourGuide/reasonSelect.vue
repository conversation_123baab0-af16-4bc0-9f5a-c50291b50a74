<script setup lang="ts">
  import { onLoad } from '@dcloudio/uni-app';
  import { ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@/common/env';
  const id = ref(0);
  const raw = ref({});

  const defaultReasons = Array.from(
    [
      '特殊教育需求儿童的个案评估',
      '特殊教育需求儿童的个案咨询',
      '特殊教育需求儿童的康复训练',
      '资源教室的建设',
      '特殊教育需求儿童的课题研究',
    ],
    (item) => {
      return {
        content: item,
        checked: false,
      };
    },
  );

  const reasons = ref<any[]>([]);

  const updateSelectedCount = () => {
    const selectedCount = reasons.value.filter((reason) => reason.checked).length;
    uni.setNavigationBarTitle({
      title: `已选择 ${selectedCount} 项`,
    });
  };

  const loadData = async () => {
    uni.showLoading({
      title: '加载中',
    });
    try {
      const { data } = await request({
        url: `/resourceRoom/tourGuide/${id.value}`,
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });

      raw.value = data;

      reasons.value = data.applyReason || defaultReasons;

      updateSelectedCount();
    } finally {
      uni.hideLoading();
    }
  };

  const handleRemoveReason = (idx: number) => {
    uni.showModal({
      title: '提示',
      content: '确认删除该原因吗？',
      success: (res) => {
        if (res.confirm) {
          reasons.value.splice(idx, 1);
          updateSelectedCount();
        }
      },
    });
  };

  const handleSwitchChecked = (idx: number) => {
    reasons.value[idx].checked = !reasons.value[idx].checked;
    updateSelectedCount();
  };

  const handleSave = () => {
    const selectedReasons = reasons.value || [];
    uni.navigateBack({
      success: () => {
        uni.$emit('reasonSelected', selectedReasons);
      },
    });
  };

  const handleAddReason = () => {
    uni.showModal({
      title: '新增原因',
      content: '',
      editable: true,
      placeholderText: '请输入原因',
      success: (res) => {
        if (res.confirm) {
          reasons.value.push({
            content: res.content,
            checked: true,
          });
          updateSelectedCount();
        }
      },
    });
  };

  uni.$on('goReasonSelect', (items) => {
    reasons.value = items?.length ? items || [] : defaultReasons;
  });

  onLoad(async (query: any) => {
    id.value = query.id;
  });
</script>

<template>
  <view class="p-2 m-2 bg-white rounded">
    <uni-list :border="false">
      <uni-list-item v-for="(reason, idx) in reasons" :key="idx">
        <template #header>
          <view class="flex gap-2 justify-between items-center w-full">
            <view class="flex gap-2 text-sm flex-1 items-center" @click="() => handleSwitchChecked(idx)">
              <checkbox-group>
                <checkbox :value="idx" :checked="reason.checked" />
              </checkbox-group>
              <view>{{ reason.content }}</view>
            </view>
            <button @click="() => handleRemoveReason(idx)">
              <uni-icons type="trash" />
            </button>
          </view>
        </template>
      </uni-list-item>
      <uni-list-item title="点此新增原因" clickable show-arrow @click="handleAddReason"> </uni-list-item>
    </uni-list>
  </view>

  <fixed-button-safe />
  <fixed-bottom-button @click="() => handleSave()">
    <view> 完成 </view>
  </fixed-bottom-button>
</template>

<style scoped lang="scss"></style>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "申请原因",
    "enablePullDownRefresh": true,
    "navigationBarBackgroundColor": "#1989fa",
    "navigationBarTextStyle": "white"
  }
}
</route>
