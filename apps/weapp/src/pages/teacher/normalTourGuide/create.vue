<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { onShow } from '@dcloudio/uni-app';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@/common/env';
  import Empty from '@/components/empty.vue';

  const availableTeachers = ref([]);
  const selectedTeacherIndex = ref(0);
  const selectedDateIndex = ref(0);
  const selectedTimeIndex = ref(0);
  const formData = ref<any>({});

  const handleTeacherChange = (e) => {
    selectedTeacherIndex.value = e.detail.value;
    formData.value.teacher = availableTeachers.value[e.detail.value];
    formData.value.major = availableTeachers.value[e.detail.value]?.major;
    formData.value.normalGuideTeacher = availableTeachers.value[e.detail.value];

    if (!formData.value.normalGuideTeacher.planTimes?.length) {
      formData.value.planGuideDate = '';
      formData.value.planGuideTimes = '';

      selectedDateIndex.value = 0;
      selectedTimeIndex.value = 0;

      uni.showToast({
        title: '该老师暂无可预约时间',
        icon: 'none',
      });
    }
  };

  const handleDateChange = (e) => {
    selectedDateIndex.value = e.detail.value;
    formData.value.planGuideDate = formData.value.normalGuideTeacher?.planTimes[e.detail.value]?.planGuideDate;
    formData.value.planDateRaw = formData.value.normalGuideTeacher?.planTimes[e.detail.value];

    if (!formData.value.planGuideDate) {
      formData.value.timeRange = '';
      selectedTimeIndex.value = 0;

      uni.showToast({
        title: '当天已约满',
        icon: 'none',
      });
    }
  };

  const handleTimePeriodChange = (e) => {
    selectedTimeIndex.value = e.detail.value;
    formData.value.timeRange = timeRanges.value[e.detail.value]?.value;
  };

  const timeRanges = computed(() => {
    if (!formData.value.planGuideDate || !formData.value.planDateRaw) {
      return [];
    }

    let options = [];

    switch (formData.value.planDateRaw.timeRange) {
      case 0:
        options = [
          { label: '全天', value: 0 },
          { label: '上午', value: 1 },
          { label: '下午', value: 2 },
        ];
        break;
      case 1:
        options = [{ label: '上午', value: 1 }];
        break;
      case 2:
        options = [{ label: '下午', value: 2 }];
        break;
      default:
        break;
    }

    return options;
  });

  const timeRangeDisplay = (timeRange: number) => {
    if (!timeRange) {
      return '';
    }

    switch (timeRange) {
      case 0:
        return '全天';
      case 1:
        return '上午';
      case 2:
        return '下午';
      default:
        return '';
    }
  };

  const formValidated = computed(() => {
    return !formData.value.teacher || !formData.value.planGuideDate || !formData.value.timeRange;
  });

  const handleSubmit = () => {
    uni.showModal({
      title: '提示',
      content: '确认提交申请吗？',
      success: async (res) => {
        if (res.confirm) {
          uni.showLoading({
            title: '提交中...',
          });

          try {
            const { data } = await request('/resourceCenter/normalTourGuideOrder', {
              method: 'POST',
              baseURL: PROJECT_URLS.MAIN_PROJECT_API,
              data: formData.value,
            });

            if (data) {
              uni.showToast({
                title: '提交成功',
                icon: 'success',
              });

              uni.navigateBack();
            }
          } finally {
            uni.hideLoading();
          }
        }
      },
    });
  };

  onShow(async () => {
    const { data } = await request('/resourceCenter/normalTourGuidePlan/allPlanTeachers', {
      method: 'GET',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        pageSize: 999,
      },
    });

    availableTeachers.value =
      data?.map((item) => {
        return {
          ...item,
          name: item.teacher.name,
        };
      }) || [];
  });
</script>

<template>
  <view class="p-2 bg-white m-2 rounded">
    <uni-forms ref="baseForm" :model-value="formData" :label-width="90" label-align="right">
      <uni-forms-item label="巡回教师" required>
        <view class="h-full flex items-center">
          <picker
            :value="selectedTeacherIndex"
            range-key="name"
            :range="availableTeachers"
            @change="handleTeacherChange"
          >
            <view class="uni-input">{{ formData.teacher?.name || '点此选择巡回指导教师' }} </view>
          </picker>
        </view>
      </uni-forms-item>
      <view v-if="formData.normalGuideTeacher">
        <uni-forms-item label="专业方向">
          <view class="h-full flex items-center">
            {{ formData.normalGuideTeacher?.major }}
          </view>
        </uni-forms-item>
        <uni-forms-item label="日期" required>
          <view class="h-full flex items-center">
            <picker
              :value="selectedDateIndex"
              range-key="planGuideDate"
              :range="formData.normalGuideTeacher.planTimes"
              @change="handleDateChange"
            >
              <view class="uni-input">{{ formData.planGuideDate || '请选择预约指导日期' }} </view>
            </picker>
          </view>
        </uni-forms-item>
        <uni-forms-item v-if="formData.planGuideDate" label="预约时段" required>
          <view class="h-full flex items-center">
            <picker :value="selectedTimeIndex" range-key="label" :range="timeRanges" @change="handleTimePeriodChange">
              <view class="uni-input">{{ timeRangeDisplay(formData.timeRange) || '请选择时间段' }} </view>
            </picker>
          </view>
        </uni-forms-item>
        <uni-forms-item label="备注说明">
          <uni-easyinput
            v-model="formData.remarks"
            type="textarea"
            auto-height
            placeholder="请输入内容"
          ></uni-easyinput>
        </uni-forms-item>
      </view>
    </uni-forms>

    <fixed-bottom-button :disabled="formValidated" @click="() => handleSubmit()"> 提交 </fixed-bottom-button>
    <fixed-button-safe />
  </view>
</template>

<style scoped lang="scss"></style>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "常规巡回指导预约",
    "enablePullDownRefresh": true,
    "navigationBarBackgroundColor": "#1989fa",
    "navigationBarTextStyle": "white"
  }
}
</route>
