<script setup lang="ts">
  import { onLoad, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app';
  import useList from '@/common/hooks/list';
  import { PROJECT_URLS } from '@/common/env';
  import Empty from '@/components/empty.vue';
  import { goPage } from '@/common/page';
  import dayjs from 'dayjs';
  import { isNumber } from 'lodash';

  const { rawResult, refresh, loading } = useList(
    {
      api: '/resourceCenter/conference/available',
      pageSize: 20,
    },
    {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    },
  );

  const coverSize = {
    width: 240,
    height: 160,
  };

  const getCoverUrl = (url: string) => {
    if (/^\d+$/.test(url)) {
      return `${PROJECT_URLS.MAIN_PROJECT_API}/common/uploadedResource/image/${url}?width=${coverSize.width * 2}&height=${coverSize.height * 2}`;
    }
    return url;
  };

  onLoad(async () => {
    await refresh();
  });

  onPullDownRefresh(async () => {
    await refresh();
    uni.stopPullDownRefresh();
  });
</script>

<template>
  <view>
    <view class="p-2">
      <view v-for="item in rawResult" :key="item.id" class="m-2">
        <view
          v-if="item.id"
          class="flex gap-2 mb-2 pb-2 border-0 border-b border-solid border-slate-300"
          @tap="
            () =>
              goPage('/teacher/conference/detail', {
                id: item.id,
              })
          "
        >
          <view v-if="item.coverImageId">
            <image
              :src="getCoverUrl(item.coverImageId)"
              mode="aspectFill"
              :style="{
                width: `${coverSize.width}rpx`,
                height: `${coverSize.height}rpx`,
              }"
            />
          </view>
          <view class="flex-1">
            <view class="text-sm text-gray-500 mb-1">发布于 {{ dayjs(item.createdAt).format('YYYY-MM-DD') }}</view>
            <view class="text-base text-ellipsis h-12 overflow-hidden">{{ item.subject }}</view>
          </view>
        </view>
      </view>

      <empty v-if="rawResult?.length === 0 && !loading" title="暂无相关教研培训" />
    </view>
  </view>
</template>

<route>
  {
    "style": {
      "navigationBarTitleText": "教研培训",
      "enablePullDownRefresh": true
    }
  }
</route>

<style scoped lang="scss"></style>
