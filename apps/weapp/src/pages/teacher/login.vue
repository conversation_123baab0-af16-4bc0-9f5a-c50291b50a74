<script setup lang="ts">
  import { onLoad } from '@dcloudio/uni-app';
  import { useTeacherStore } from '@/common/store/teacher';
  import { relaunchToPage } from '@/common/page';
  import { computed, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@/common/env';
  import { setToken } from '@repo/infrastructure/auth';

  const teacherStore = useTeacherStore();
  const teacherInfo = teacherStore.userInfo;
  const from = ref('');

  const init = async () => {
    await teacherStore.refreshUserInfo();
    if (teacherInfo.id && teacherInfo.mobile) {
      relaunchToPage(from.value || '/index/index');
    }
  };

  const handleGetPhoneNumber = async (e: any) => {
    const { errMsg, code } = e.detail;
    uni.showToast({
      title: '请稍后...',
      icon: 'loading',
      duration: 2000,
    });
    try {
      if (errMsg === 'getPhoneNumber:ok') {
        const { code: openIdCode } = await uni.login();
        const { data } = await request('/org/session', {
          method: 'POST',
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          data: {
            usernameOrMobile: 'this-is-any',
            code: code,
            loginType: 'weappMobileAuto',
            roleType: 'Company',
            rememberMe: true,
            wxAuthCodeForOpenId: openIdCode,
          },
        });
        teacherStore.setUserInfo(data.userInfo);
        setToken(data.token);
        await uni.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 2000,
        });

        relaunchToPage(from.value || '/home/<USER>');
      }
    } catch (e: any) {
      const { message } = e.data;
      uni.showModal({
        title: '提示',
        content: `${message}，或账户未绑定手机号`,
        showCancel: false,
      });
    } finally {
      uni.hideToast();
    }
  };

  const loginData = ref({
    usernameOrMobile: '',
    password: '',
  });

  const passwordLogin = ref(true);

  const checked = computed(() => {
    return !loginData.value.usernameOrMobile || !loginData.value.password;
  });

  const handleGoHome = () => {
    relaunchToPage(from.value || '/index/index');
  };

  const handleLogin = async () => {
    uni.showLoading({ title: '请稍后...' });
    const data = {
      loginType: passwordLogin.value ? 'password' : 'verifyCode',
      roleType: 'Company',
      ...loginData.value,
      captcha: 'xx',
    };

    try {
      const { data: res } = await request('/org/session', {
        data,
        method: 'POST',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        headers: {
          'Login-Source': 'WeApp',
        },
      });

      const { token, userInfo, expireTime } = res;

      if (!token) {
        uni.showToast({
          title: '登录失败',
          icon: 'none',
          duration: 2000,
        });
        return;
      }
      uni.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 2000,
      });
      setToken(token, expireTime);
      handleGoHome();
    } finally {
      uni.hideLoading();
    }
  };


  onLoad(async (query: any) => {
    from.value = query.from;
    await init();
  });
</script>

<template>
  <view class="p-4">
    <!--    <view v-if="!teacherInfo.mobile">-->
    <!--      <view> 老师您好，请先登录</view>-->
    <!--      <view class="my-4"> 如果您还没有系统账号，请先联系中心或学校管理员为您开通账号 </view>-->
    <!--      <view class="mt-10">-->
    <!--        <button open-type="getPhoneNumber" class="py-3" type="primary" @getphonenumber="handleGetPhoneNumber">-->
    <!--          点此一键绑定-->
    <!--        </button>-->
    <!--      </view>-->
    <!--      <view class="mt-2">-->
    <!--        <button class="py-3" @tap="handleGoHome"> 暂不绑定 </button>-->
    <!--      </view>-->
    <!--    </view>-->
    <uni-section type="line" title="请先登陆">
      <view class="p-4">
        <uni-forms ref="baseForm" :model-value="loginData">
          <uni-forms-item label="账号" required>
            <uni-easyinput v-model.trim="loginData.usernameOrMobile" placeholder="账号或已绑定的手机号码" />
          </uni-forms-item>
          <uni-forms-item label="密码" required>
            <uni-easyinput v-model.trim="loginData.password" placeholder="请输入密码" type="password" />
          </uni-forms-item>
          <!--        <uni-forms-item>-->
          <!--          <aliyun-captcha-->
          <!--              id="captcha-element"-->
          <!--              v-if="this.data.loadCaptcha"-->
          <!--              :props="this.data.pluginProps"-->
          <!--          />-->
          <!--        </uni-forms-item>-->
          <uni-forms-item>
            <view class="text-gray-500"> * 如果您还没有系统账号，请先联系中心或学校管理员为您开通账号 </view>
            <button type="primary" class="mt-2 py-2" :disabled="checked" @click="handleLogin">登录</button>
            <button class="mt-2 py-2" @click="handleGoHome">返回</button>
          </uni-forms-item>
        </uni-forms>
      </view>

      <view class="mt-4 flex justify-center items-center">
        <button
            open-type="getPhoneNumber"
            class="remove-border flex items-center justify-center  p-0   transition-transform"
            @getphonenumber="handleGetPhoneNumber"
        >
          <uni-icons
              type="weixin"
              size="36"
              color="#07C160"
              class="hover:opacity-80"
          />
        </button>
      </view>
    </uni-section>
  </view>

  <!--  "mp-weixin": {-->
  <!--  "usingComponents": {-->
  <!--  "aliyun-captcha": "plugin://AliyunCaptcha/captcha"-->
  <!--  }-->
  <!--  }-->
</template>

<route lang="json">
{
  "style": {
    "navigationBarBackgroundColor": "#1989fa",
    "navigationBarTextStyle": "white"
  }
}
</route>

<style scoped lang="scss">
.remove-border{
  border: none !important;
}
</style>
