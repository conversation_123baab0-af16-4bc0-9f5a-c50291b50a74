<script setup lang="ts">
  import teacherTabBarItems from '@/components/tabbar/teacher';
  import TabBarPage from '@/components/tabbar/tabBarPage.vue';
  import TeacherIndex from '@/components/teacher/index.vue';
  import ImIndex from '@/components/im/index.vue';
  import MeIndex from '@/components/teacher/me.vue';
  import { ref } from 'vue';
  import { onLoad } from '@dcloudio/uni-app';
  import { useTeacherStore } from '@/common/store/teacher';
  import { envConfig } from '@/common/env';
  import { getToken } from '@repo/infrastructure/auth';

  const teacherStore = useTeacherStore();

  const tabBarPageRef = ref(null);
  const activeTabKey = ref('index');
  const ready = ref(false);
  const userInfo = ref({});

  onLoad(async (query: any) => {
    uni.setStorageSync('LoginSource', 'WeApp');
    uni.setStorageSync('defaultOrgId', envConfig.defaultOrgId);
    try {
      if (!getToken()) {
        await Promise.all([teacherStore.loginViaWeapp()]);
      } else {
        await teacherStore.refreshUserInfo();
      }

      // await Promise.all([
      //   request('/common/configure/dataStructure', {
      //     baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      //   }),
      // ]);

      userInfo.value = teacherStore.userInfo || {};
      uni.setNavigationBarTitle({
        title: userInfo.value.company?.shortName || '首页',
      });

      if (query?.activeTab) {
        activeTabKey.value = query.activeTab;
      }
    } finally {
      ready.value = true;
    }
  });
</script>

<route lang="json">
{
  "style": {
    "navigationBarBackgroundColor": "#1989fa",
    "navigationBarTextStyle": "white"
  }
}
</route>

<template>
  <tab-bar-page
    v-if="ready"
    ref="tabBarPageRef"
    v-model:active-tab-key="activeTabKey"
    :tab-bar-items="teacherTabBarItems"
  >
    <teacher-index v-if="activeTabKey === 'index'" />
    <im-index v-else-if="activeTabKey === 'conversation'" role="teacher" />
    <me-index v-else-if="activeTabKey === 'me'" />
  </tab-bar-page>
</template>
