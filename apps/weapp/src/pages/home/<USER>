<script setup lang="ts">
  import guardianTabBarItems from '@/components/tabbar/guardian';
  import TabBarPage from '@/components/tabbar/tabBarPage.vue';
  import GuardianIndex from '@/components/guardian/index.vue';
  import ImIndex from '@/components/im/index.vue';
  import MeIndex from '@/components/guardian/me.vue';
  import { ref } from 'vue';
  import { onLoad } from '@dcloudio/uni-app';
  import { useGuardianStore } from '@/common/store/gardian';
  import { envConfig, PROJECT_URLS } from '@/common/env';
  import { getToken } from '@repo/infrastructure/auth';
  const tabBarPageRef = ref(null);
  const activeTabKey = ref('index');
  const ready = ref(false);

  const guardianStore = useGuardianStore();

  onLoad(async () => {
    uni.setStorageSync('LoginSource', 'GuardianWeapp');
    uni.setStorageSync('defaultOrgId', envConfig.defaultOrgId);
    try {
      if (!getToken()) {
        await Promise.all([guardianStore.loginViaWeapp()]);
      }
    } finally {
      ready.value = true;
    }
  });
</script>

<route lang="json">
{
  "style": {
    "navigationBarBackgroundColor": "#1989fa",
    "navigationBarTextStyle": "white"
  }
}
</route>

<template>
  <tab-bar-page
    v-if="ready"
    ref="tabBarPageRef"
    v-model:active-tab-key="activeTabKey"
    :tab-bar-items="guardianTabBarItems"
  >
    <guardian-index v-if="activeTabKey === 'index'" />
    <im-index v-else-if="activeTabKey === 'conversation'" role="guardian" />
    <me-index v-else-if="activeTabKey === 'me'" />
  </tab-bar-page>
</template>
