<script setup lang="ts">
  import useList from '@/common/hooks/list';
  import { onLoad, onPullDownRefresh, onReachBottom, onShow } from '@dcloudio/uni-app';
  import { computed, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import Empty from '@/components/empty.vue';
  import NewsListItem from '@/components/news/newsListItem.vue';
  import { PROJECT_URLS } from '@/common/env';

  const { dataList, refresh, queryParams, loading, loadMore, hasMore } = useList(
    {
      api: '/guardian/news',
    },
    {
      baseURL: PROJECT_URLS.GO_PROJECT_API,
    },
  );

  const newsCategories = ref<any[]>([]);

  onPullDownRefresh(async () => {
    await refresh();
    uni.stopPullDownRefresh();
  });

  onShow(async () => {
    await refresh();
  });

  const values = computed(() => {
    return ['全部资讯'].concat(newsCategories.value.map((item) => item.name) || []);
  });

  const handleCategoryChange = async (e: any) => {
    const index = e.currentIndex;
    if (index === 0) {
      delete queryParams.value.categoryId;
      await uni.setNavigationBarTitle({ title: '新闻资讯' });
    } else {
      queryParams.value.categoryId = newsCategories.value[index - 1].id;
      await uni.setNavigationBarTitle({ title: newsCategories.value[index - 1].name });
    }
    await refresh();
  };

  const navigatorWidth = computed(() => {
    let wordCount = 0;
    values.value.forEach((item) => {
      wordCount += item.length;
    });
    return (wordCount + values.value.length) * 32;
  });

  onReachBottom(async () => {
    await loadMore();
  });

  onLoad(async () => {
    queryParams.value.visibleIn = uni.getStorageSync('currentRole');
    const { data } = await request<any>('/guardian/newsCategory', {
      params: {
        pageSize: 999,
      },
      baseURL: PROJECT_URLS.GO_PROJECT_API,
    });

    newsCategories.value = data.list || [];
  });
</script>

<template>
  <view>
    <scroll-view :scroll-x="true" class="m-2">
      <view :style="`width:${navigatorWidth}rpx`">
        <uni-segmented-control style-type="text" :values="values" @click-item="handleCategoryChange" />
      </view>
    </scroll-view>

    <view class="p-2">
      <view v-for="item in dataList" :key="item.id" class="m-2">
        <news-list-item :item="item" />
      </view>

      <empty v-if="dataList.length === 0 && !loading" title="暂无相关新闻" />
      <empty v-else-if="!hasMore" />
    </view>
  </view>
</template>

<style scoped lang="scss">
  :deep {
    .segmented-control__item {
      padding: 0 16rpx;
      flex: auto !important;
    }
  }
</style>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "新闻资讯",
    "enablePullDownRefresh": true
  }
}
</route>
