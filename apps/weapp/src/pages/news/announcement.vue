<script setup lang="ts">
  import useList from '@/common/hooks/list';
  import { onLoad, onPullDownRefresh, onReachBottom, onShow } from '@dcloudio/uni-app';
  import Empty from '@/components/empty.vue';
  import dayjs from 'dayjs';
  import { goPage } from '@/common/page';
  import { PROJECT_URLS } from '@/common/env';

  const { dataList, refresh, loading, loadMore } = useList(
    {
      api: '/guardian/announcement',
    },
    {
      baseURL: PROJECT_URLS.GO_PROJECT_API,
    },
  );

  const handleGoDetail = (item: any) => {
    goPage('/news/announcementDetail', {
      id: item.id,
    });
  };

  onPullDownRefresh(async () => {
    await refresh();
    uni.stopPullDownRefresh();
  });

  onShow(async () => {
    await refresh();
  });

  onReachBottom(async () => {
    await loadMore();
  });

  onLoad(async () => {});
</script>

<template>
  <view class="p-2">
    <view
      v-for="item in dataList"
      :key="item.id"
      class="m-2 rounded-md py-4 px-2 border-solid border-gray-200 bg-gray-200 text-slate-600"
      @click="() => handleGoDetail(item)"
    >
      <view class="text-sm">[ {{ dayjs(item.createdAt).format('YYYY-MM-DD') }} ]</view>
      <view class="mt-2">
        {{ item.title }}
      </view>
    </view>

    <empty v-if="dataList.length === 0 && !loading" title="暂无相关通知" />
    <!--    <empty v-else-if="!hasMore" />-->
  </view>
</template>

<style scoped lang="scss"></style>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "通知公告",
    "enablePullDownRefresh": true,
    "navigationBarBackgroundColor": "#1989fa",
    "navigationBarTextStyle": "white"
  }
}
</route>
