<script setup lang="ts">
  import openapi from '@repo/infrastructure/openapi';
  import { useGuardianStore } from '@/common/store/gardian';
  import { ref } from 'vue';
  import { onLoad } from '@dcloudio/uni-app';
  import { relaunchToPage } from '@/common/page';
  import { PROJECT_URLS } from '@/common/env';

  const guardianStore = useGuardianStore();
  const guardianInfo = ref<API.Guardian>(guardianStore.getUserInfo());
  const from = ref('');

  const init = async () => {
    await guardianStore.refreshUserInfo();
    guardianInfo.value = guardianStore.getUserInfo();
    if (guardianInfo.value.mobile && guardianInfo.value.currentStudent?.id) {
      relaunchToPage(from.value || '/index/index');
    }
  };

  const handleGetPhoneNumber = async (e: any) => {
    const { errMsg, encryptedData, iv, code } = e.detail;
    if (errMsg === 'getPhoneNumber:ok') {
      await openapi.guardianController.guardianBindMobile(
        { encryptedData, iv, code },
        { baseURL: PROJECT_URLS.GO_PROJECT_API },
      );
      await uni.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 2000,
      });

      relaunchToPage(from.value || '/home/<USER>');
    }
  };

  const handleGoHome = () => {
    relaunchToPage(from.value || '/index/index');
  };

  const handleSyncMyChildren = async () => {
    const res = await uni.showModal({
      title: '同步我的学生信息',
      content: '如果您同步后，仍未显示您的学生信息，请联系学校老师为学生绑定您当前登录的手机号码',
      showCancel: false,
      confirmText: '同步信息',
    });

    if (res.confirm) {
      await openapi.guardianController.syncMyChildren({ baseURL: PROJECT_URLS.GO_PROJECT_API });
      await init();
      if (guardianInfo.value.studentList?.length) {
        await uni.showToast({
          title: '同步成功',
          icon: 'success',
          duration: 2000,
        });
        uni.reLaunch({
          url: '/pages/my/index',
        });
      } else {
        await uni.showModal({
          title: '同步失败',
          content: '未能获取到您学生的信息，请联系学校老师为学生绑定您当前登录的手机号码',
          showCancel: false,
          confirmText: '知道了',
        });
      }
    }
  };

  onLoad(async (query?: any) => {
    from.value = query.from;
    await init();
  });
</script>

<template>
  <view class="text-sm mt-16 p-4 text-center">
    <view v-if="!guardianInfo.mobile">
      <view> 家长您好，您还没有绑定手机号码，请先绑定。 </view>
      <view class="my-4"> 绑定后请联系学校老师将绑定手机号设置为您的学生设置监护人手机号码 </view>
      <view class="mt-10">
        <button open-type="getPhoneNumber" class="py-2" type="primary" @getphonenumber="handleGetPhoneNumber">
          点此一键绑定
        </button>
      </view>
      <view class="mt-2">
        <button class="py-2" @tap="handleGoHome"> 暂不绑定 </button>
      </view>
    </view>
    <view v-else>
      <view> 家长您好，没有在平台学校中找到您的信息。 </view>
      <view class="my-4">
        如果您是在学校登记的学生监护人，请联系老师将您学生监护人手机号码设为当前绑定手机号码，否则请联系学生的监护人将您添加为家庭成员
      </view>
      <view class="mt-10">
        <button type="primary" class="py-2" @tap="handleSyncMyChildren"> 同步我的学生信息 </button>
      </view>
      <view class="mt-2">
        <button class="py-2" @tap="handleGoHome"> 返回首页 </button>
      </view>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarBackgroundColor": "#1989fa",
    "navigationBarTextStyle": "white"
  }
}
</route>

<style scoped lang="scss"></style>
