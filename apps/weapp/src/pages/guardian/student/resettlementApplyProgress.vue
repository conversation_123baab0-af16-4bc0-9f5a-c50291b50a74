<script setup lang="ts">
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@/common/env';

  import { ref } from 'vue';
  import { onLoad, onShow } from '@dcloudio/uni-app';
  const studentId = ref(0);
  const student = ref<any>();

  const loadStudent = async () => {
    const { data } = await request(`/resourceRoom/student/${studentId.value}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    student.value = data as any;
    uni.setNavigationBarTitle({
      title: `${student.value.name}的安置申请`,
    });
  };

  onLoad((query: any) => {
    studentId.value = query.id;
  });

  onShow(async () => {
    await loadStudent();
  });
</script>

<template>
  <view v-if="student" class="m-4 text-center bg-slate-50 border-slate-200 p-4">
    <view v-if="!student.additionalData?.assessmentDate" class="">您的申请仍在处理中，请耐心等待</view>
    <view v-else>
      您的申请已处理，请于
      <text class="font-bold text-blue-500"> {{ student.additionalData?.assessmentDate }}</text>
      按短信通知前往评估。</view
    >
  </view>
</template>
