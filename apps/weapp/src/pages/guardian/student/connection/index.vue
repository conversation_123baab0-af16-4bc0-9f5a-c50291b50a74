<script setup lang="ts">
  import useList from '@/common/hooks/list';
  import { onLoad, onPullDownRefresh, onShow } from '@dcloudio/uni-app';
  import { PROJECT_URLS } from '@/common/env';
  import { goPage } from '@/common/page';
  import Empty from '@/components/empty.vue';

  const { dataList, refresh, queryParams } = useList(
    {
      api: '/resourceRoom/homeSchoolConnection',
    },
    {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    },
  );

  onLoad((query: any) => {
    queryParams.value = {
      ...queryParams.value,
      studentId: query.id,
    };
  });

  onPullDownRefresh(async () => {
    await refresh();
    uni.stopPullDownRefresh();
  });

  onShow(async () => {
    await refresh();
  });
</script>

<template>
  <view class="p-4">
    <uni-section title="家校联系" type="line">
      <uni-list v-if="dataList.length" :border="false">
        <uni-list-item
          v-for="item in dataList"
          :key="item.id"
          :right-text="`${item.teacher}联系`"
          :title="item.connectionDate?.split(' ')[0]"
          clickable
          show-arrow
          @click="goPage(`/guardian/student/connection/detail?id=${item.id}`)"
        />
      </uni-list>
      <empty v-else />
    </uni-section>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "家校联系",
    "enablePullDownRefresh": true
  }
}
</route>

<style scoped lang="scss"></style>
