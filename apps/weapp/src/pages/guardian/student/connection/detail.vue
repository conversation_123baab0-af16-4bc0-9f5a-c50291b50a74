<script setup lang="ts">
  import { onLoad, onShow } from '@dcloudio/uni-app';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@/common/env';
  import { ref } from 'vue';
  import { cloneDeep } from 'lodash';

  const hcId = ref(0);
  const detail = ref<any>();
  const currentTab = ref(0);
  const tabs = ['在校表现', '在家表现', '存在问题', '解决方案'];

  const handleTabClick = (e: any) => {
    currentTab.value = e.currentIndex;
  };

  const managePerformanceAtHomePopup = ref<any>(null);
  const currentManageIndex = ref();
  const currentPerformAtHome = ref<any>({});

  const loadDetail = async () => {
    const { data } = await request(`/resourceRoom/homeSchoolConnection/${hcId.value}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    detail.value = data;
  };

  const handleShowManage = (index: number) => {
    currentManageIndex.value = index;
    currentPerformAtHome.value = cloneDeep(detail.value.performancesAtHome[index]);
    managePerformanceAtHomePopup.value?.open();
  };

  const handleAddPerformanceAtHome = () => {
    currentManageIndex.value = -1;
    currentPerformAtHome.value = {
      domain: '',
      performance: '',
    };
    managePerformanceAtHomePopup.value?.open();
  };

  const handleSavePerformanceAtHome = async () => {
    const raw = cloneDeep(detail.value.performancesAtHome || []) || [];
    if (currentManageIndex.value === -1) {
      raw.push(currentPerformAtHome.value);
    } else {
      raw[currentManageIndex.value] = currentPerformAtHome.value;
    }

    uni.showLoading({ title: '保存中' });
    try {
      const { data } = await request(`/resourceRoom/homeSchoolConnection/guardianOpinion/${hcId.value}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'PUT',
        data: {
          ...detail.value,
          performancesAtHome: raw,
        },
      });

      detail.value.performancesAtHome = raw;

      uni.showToast({ title: '保存成功', icon: 'success' });
      managePerformanceAtHomePopup.value?.close();
    } finally {
      uni.hideLoading();
    }
  };

  onLoad((query: any) => {
    hcId.value = query.id;
  });

  onShow(async () => {
    await loadDetail();
  });
</script>

<template>
  <view v-if="detail" class="m-4">
    <uni-section type="line" title="基本信息">
      <uni-list>
        <uni-list-item title="学生" :right-text="`${detail.student.fusionSchoolName} - ${detail.student.name}`" />
        <uni-list-item title="联系时间" :right-text="detail.connectionDate?.substr(0, 10)" />
        <uni-list-item title="联系教师" :right-text="detail.teacher" />
        <uni-list-item title="联系家长" :right-text="detail.guardianName" />
      </uni-list>
    </uni-section>
    <view class="my-4">
      <uni-segmented-control :current="currentTab" :values="tabs" style-type="text" @click-item="handleTabClick" />
    </view>
    <view v-if="currentTab === 0">
      <uni-section type="line" title="在校表现">
        <uni-list>
          <uni-list-item
            v-for="(p, i) in detail.performancesAtSchool"
            :key="i"
            :title="`${i + 1}. ${p.domain}`"
            :note="p.performance"
          />
        </uni-list>
      </uni-section>
    </view>
    <view v-else-if="currentTab === 1">
      <uni-section type="line" title="在家表现">
        <uni-list>
          <uni-list-item
            v-for="(p, i) in detail.performancesAtHome"
            :key="i"
            clickable
            show-arrow
            :title="`${i + 1}. ${p.domain}`"
            :note="p.performance"
            @click="() => handleShowManage(i)"
          />
        </uni-list>
      </uni-section>

      <button type="primary" class="w-full py-2 mt-2" @click="handleAddPerformanceAtHome"> 添加在家表现 </button>
    </view>
    <view v-else-if="currentTab === 2">
      <uni-section type="line" title="存在问题">
        <uni-list>
          <uni-list-item
            v-for="(p, i) in detail.problems"
            :key="i"
            :title="`${i + 1}. ${p.mainProblem}`"
            :note="p.detail"
          />
        </uni-list>
      </uni-section>
    </view>
    <view v-else-if="currentTab === 3">
      <uni-section type="line" title="解决方案">
        <uni-list>
          <uni-list-item
            v-for="(p, i) in detail.solutions"
            :key="i"
            :title="`${i + 1}. ${p.mainProblem}`"
            :note="`${p.personInCharge}: ${p.solution}`"
          />
        </uni-list>
      </uni-section>
    </view>
  </view>
  <bottom-safe />

  <uni-popup ref="managePerformanceAtHomePopup" type="bottom">
    <view class="bg-white p-4">
      <uni-forms>
        <uni-forms-item label="领域">
          <uni-easyinput v-model="currentPerformAtHome.domain" />
        </uni-forms-item>
        <uni-forms-item label="表现">
          <uni-easyinput v-model="currentPerformAtHome.performance" type="textarea" />
        </uni-forms-item>
      </uni-forms>
      <view class="flex gap-2 mt-2">
        <button type="primary" class="flex-1 py-2" @click="handleSavePerformanceAtHome"> 保存 </button>
        <button type="default" class="flex-1 py-2" @click="managePerformanceAtHomePopup.close"> 取消 </button>
      </view>
      <bottom-safe />
    </view>
  </uni-popup>
</template>

<style scoped lang="scss"></style>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "家校联系"
  }
}
</route>
