<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@/common/env';
  import { onLoad, onPullDownRefresh, onShow } from '@dcloudio/uni-app';
  import useDocument from '@/common/hooks/document';
  import Empty from '@/components/empty.vue';
  import BottomSafe from '@/components/bottomSafe.vue';

  const recordId = ref(0);
  const detail = ref<any>({});
  const { handlePreviewDoc } = useDocument();

  const attachments = computed(() => {
    const data =
      detail.value.attachments?.map((item: any) => {
        return {
          url: `${PROJECT_URLS.MAIN_PROJECT_API}/common/uploadedResource/image/${item.id}?width=750&height=1000`,
          name: item.name,
          extname: item.name.split('.').pop(),
        };
      }) || [];
    return data;
  });

  const loadRecord = async () => {
    uni.showLoading({
      title: '加载中',
      mask: true,
    });
    try {
      const { data } = await request(`/resourceRoom/d2dEducationRecord/${recordId.value}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      detail.value = data;
    } finally {
      uni.hideLoading();
    }
  };

  onLoad((query: any) => {
    recordId.value = query.id;
  });

  onShow(async () => {
    await loadRecord();
  });

  onPullDownRefresh(async () => {
    await loadRecord();
    uni.stopPullDownRefresh();
  });
</script>

<template>
  <view v-if="detail.id" class="p-4">
    <view>
      <uni-section title="基本信息" type="line">
        <uni-list :border="false">
          <uni-list-item
            title="学生姓名"
            :right-text="`${detail.d2dEducation.student?.gradeClass?.name} ${detail.d2dEducation.student?.name}`"
          />
          <uni-list-item title="所属学校" :right-text="detail.d2dEducation.student?.fusionSchoolName" />
          <uni-list-item title="学期" :right-text="detail.d2dEducation.period" />
          <uni-list-item title="本月" :right-text="`第${detail.recordSequence?.sequenceInMonth}次`" />
          <uni-list-item title="本学期" :right-text="`第${detail.recordSequence?.sequenceInPeriod}次`" />
          <uni-list-item title="送教负责人" :right-text="detail.d2dEducation.chargeUser?.name" />
          <uni-list-item title="状态" :right-text="detail.d2dEducation.finished ? '已结束' : '进行中'" />
        </uni-list>
      </uni-section>
    </view>

    <view class="mt-4">
      <uni-section title="送教记录" type="line">
        <uni-list v-if="detail.docTemplates?.length" :border="false">
          <uni-list-item
            v-for="(item, index) in detail.docTemplates"
            :key="index"
            :title="`${index + 1}、${item.name}`"
            clickable
            show-arrow
            @click="
              () =>
                handlePreviewDoc({
                  sourceId: item.id,
                  docTplIndex: Number(index),
                  type: 'SendPlan',
                  docTemplate: item,
                })
            "
          />
        </uni-list>
        <empty v-else />
      </uni-section>
    </view>

    <view v-if="detail.attachments?.length > 0" class="mt-4">
      <uni-section title="现场照片" type="line">
        <uni-file-picker v-model="attachments" readonly mode="grid" />
      </uni-section>
    </view>

    <bottom-safe />
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "送教记录",
    "enablePullDownRefresh": true
  }
}
</route>

<style scoped lang="scss"></style>
