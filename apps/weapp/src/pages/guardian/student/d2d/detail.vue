<script setup lang="ts">
  import { ref } from 'vue';
  import { onLoad, onPullDownRefresh, onShow } from '@dcloudio/uni-app';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@/common/env';
  import Empty from '@/components/empty.vue';
  import useDocument from '@/common/hooks/document';
  import BottomSafe from '@/components/bottomSafe.vue';
  import { goPage } from '@/common/page';

  const d2d = ref<any>({});
  const records = ref<any[]>([]);
  const d2dId = ref(0);

  const { handlePreviewDoc } = useDocument();

  const loadData = async () => {
    if (d2dId.value <= 0) {
      return;
    }

    uni.showLoading({ title: '加载中', mask: true });

    try {
      const { data } = (await request(`/resourceRoom/d2dEducation/${d2dId.value}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      })) as any;

      const { data: recordsList } = await request(`/resourceRoom/d2dEducationRecord`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          d2dEducation: data.id,
          pageSize: 999,
        },
      });

      d2d.value = data as any;
      records.value = (recordsList?.items as any[]) || [];
    } finally {
      uni.hideLoading();
    }
  };

  onLoad((query: any) => {
    d2dId.value = query.id;
  });

  onShow(async () => {
    await loadData();
  });

  onPullDownRefresh(async () => {
    await loadData();
    uni.stopPullDownRefresh();
  });
</script>

<template>
  <view v-if="d2d.id" class="p-4">
    <view>
      <uni-section title="基本信息" type="line">
        <uni-list :border="false">
          <uni-list-item title="学生姓名" :right-text="`${d2d.student?.gradeClass?.name} ${d2d.student?.name}`" />
          <uni-list-item title="所属学校" :right-text="d2d.student?.fusionSchoolName" />
          <uni-list-item title="学期" :right-text="d2d.period" />
          <uni-list-item title="档案负责人" :right-text="d2d.chargeUser?.name" />
          <uni-list-item title="状态" :right-text="d2d.finished ? '已结束' : '进行中'" />
        </uni-list>
      </uni-section>
    </view>

    <view class="mt-4">
      <uni-section title="送教内容" type="line">
        <uni-list v-if="d2d.docTemplates?.length" :border="false">
          <uni-list-item
            v-for="(item, index) in d2d.docTemplates"
            :key="index"
            :title="`${index + 1}、${item.name}`"
            clickable
            show-arrow
            @click="
              () =>
                handlePreviewDoc({
                  sourceId: d2d.id,
                  docTplIndex: Number(index),
                  type: 'SendPlan',
                  docTemplate: item,
                })
            "
          />
        </uni-list>
        <empty v-else />
      </uni-section>
    </view>

    <view class="mt-4">
      <uni-section title="送教记录" type="line">
        <uni-list v-if="records?.length" :border="false">
          <uni-list-item
            v-for="(item, index) in records"
            :key="index"
            :title="`${item.date?.substr(0, 10)} ${item.type}`"
            :right-text="item.classHour ? `${item.classHour}课时` : ''"
            :note="`送教教师：${item.sendTeachers?.map((t) => t.name).join(', ')}`"
            clickable
            show-arrow
            @click="
              () =>
                goPage('/guardian/student/d2d/record', {
                  id: item.id,
                  d2dId: d2d.id,
                })
            "
          />
        </uni-list>
        <empty v-else />
      </uni-section>
    </view>

    <bottom-safe />
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "送教档案",
    "enablePullDownRefresh": true
  }
}
</route>

<style scoped lang="scss"></style>
