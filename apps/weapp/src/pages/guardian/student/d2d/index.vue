<script setup lang="ts">
  import { onLoad, onPullDownRefresh, onShow } from '@dcloudio/uni-app';
  import useList from '@/common/hooks/list';
  import { PROJECT_URLS } from '@/common/env';
  import { goPage } from '@/common/page';
  import Empty from '@/components/empty.vue';

  const { dataList, refresh, queryParams } = useList(
    {
      api: '/resourceRoom/d2dEducation',
    },
    {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    },
  );

  onPullDownRefresh(async () => {
    await refresh();
    uni.stopPullDownRefresh();
  });

  onShow(async () => {
    await refresh();
  });

  onLoad((query: any) => {
    queryParams.value = {
      ...queryParams.value,
      studentId: query.id || -1,
    };
  });
</script>

<template>
  <view class="p-4">
    <uni-section title="送教档案" type="line">
      <uni-list v-if="dataList.length" :border="false">
        <uni-list-item
          v-for="item in dataList"
          :key="item.id"
          :right-text="item.chargeUser ? `负责人：${item.chargeUser.name}` : ''"
          :title="item.period"
          clickable
          show-arrow
          @click="goPage(`/guardian/student/d2d/detail?id=${item.id}`)"
        />
      </uni-list>
      <empty v-else />
    </uni-section>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "送教档案",
    "enablePullDownRefresh": true
  }
}
</route>

<style scoped lang="scss"></style>
