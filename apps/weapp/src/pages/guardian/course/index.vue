<script setup lang="ts">
  import useList from '@/common/hooks/list';
  import { onLoad, onPullDownRefresh, onReachBottom, onShow } from '@dcloudio/uni-app';
  import { computed, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import Empty from '@/components/empty.vue';
  import CourseListItem from '@/components/course/courseListItem.vue';
  import { PROJECT_URLS } from '@/common/env';

  const { dataList, refresh, queryParams, loading, loadMore, hasMore } = useList(
    {
      api: '/rehabilitationLibrary/platformRehabilitationResource',
      defaultQueryParams: {
        sort: '-id',
        availableClient: 'Guardian',
      },
    },
    { baseURL: PROJECT_URLS.MAIN_PROJECT_API },
  );

  const courseCatalogs = ref<any[]>([]);

  onPullDownRefresh(async () => {
    await refresh();
    uni.stopPullDownRefresh();
  });

  onShow(async () => {
    await refresh();
  });

  const values = computed(() => {
    return ['全部课程'].concat(courseCatalogs.value.map((item) => item.name) || []);
  });

  const handleCategoryChange = async (e: any) => {
    const index = e.currentIndex;
    if (index === 0) {
      delete queryParams.value.category;
      await uni.setNavigationBarTitle({ title: '家长课程' });
    } else {
      queryParams.value.category = courseCatalogs.value[index - 1].allDescendantIdsWithSelf?.join();
      await uni.setNavigationBarTitle({ title: courseCatalogs.value[index - 1].name });
    }
    await refresh();
  };

  const navigatorWidth = computed(() => {
    let wordCount = 0;
    values.value.forEach((item) => {
      wordCount += item.length;
    });
    return (wordCount + values.value.length) * 32;
  });

  onReachBottom(async () => {
    await loadMore();
  });

  onLoad(async () => {
    const { data } = await request<any>('/rehabilitationLibrary/platformRehabilitationResourceCategory/getByParent', {
      params: {
        pageSize: 999,
        availableClient: 'Guardian',
      },
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    courseCatalogs.value = data.items || data || [];
  });
</script>

<template>
  <view>
    <scroll-view :scroll-x="true" class="m-2">
      <view :style="{ width: `${navigatorWidth}rpx` }">
        <uni-segmented-control style-type="text" :values="values" @click-item="handleCategoryChange" />
      </view>
    </scroll-view>

    <view class="p-2">
      <view v-for="item in dataList" :key="item.id" class="m-2">
        <course-list-item :item="item" />
      </view>
      <empty v-if="dataList.length === 0 && !loading" title="暂无相关课程" />
      <empty v-else-if="!hasMore" />
    </view>
  </view>
</template>

<style scoped lang="scss">
  :deep {
    .segmented-control__item {
      padding: 0 16rpx;
      flex: auto !important;
    }
  }
</style>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "家长课程",
    "enablePullDownRefresh": true
  }
}
</route>
