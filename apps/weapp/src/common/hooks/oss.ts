import { PROJECT_URLS } from '@/common/env';

const useOss = () => {
  const videoCoverUrl = (rawUrl: string, width: number, height: number, second?: number): string => {
    // rand 1-10
    const randSec = Math.floor(Math.random() * 10) + 1;
    return `${rawUrl}?x-oss-process=video/snapshot,t_${second || randSec},f_jpg,w_${width},h_${height}`;
  };

  const videoCoverUrlById = (id: number): string => {
    if (!id) {
      return '';
    }
    // rand 1-10
    const randSec = Math.floor(Math.random() * 10) + 1;
    return `${PROJECT_URLS.MAIN_PROJECT_API}/common/uploadedResource/videoCover/${id}`;
  };

  const videoCoverUrlByAttachments = (attachments: any[]): string => {
    const firstMp4Id = attachments.find((attachment: any) => attachment.name.endsWith('.mp4'))?.id;
    return videoCoverUrlById(firstMp4Id);
  };

  const imageThumbUrl = (rawUrl: string, width: number, height: number, mode?: string): string => {
    mode = mode || 'm_fill';
    return `${rawUrl}?x-oss-process=image/resize,${mode},w_${width},h_${height}`;
  };

  return {
    videoCoverUrl,
    videoCoverUrlById,
    videoCoverUrlByAttachments,
    imageThumbUrl,
  };
};

export default useOss;
