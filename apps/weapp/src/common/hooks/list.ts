import { type Ref, ref } from 'vue';
import { request } from '@repo/infrastructure/request';

type UserListOptions = {
  api: string;
  pageSize?: number;
  appendOrPrepend?: string | ((oldList: any[], newList: any[]) => any[]);
  defaultQueryParams?: Record<string, any>;
};

const useList = <T>(
  { api, pageSize = 10, appendOrPrepend = 'append', defaultQueryParams }: UserListOptions,
  config?: any,
) => {
  const dataList = ref<T[]>([] as T[]);
  const totalItems = ref(0);
  const loading = ref(false);
  const hasMore = ref(true);
  const currentPage = ref(1);
  const size = ref(pageSize);
  const queryParams = ref<any>({});
  const rawResult = ref<any>();

  config = config || {};
  const loadData = async (options?: Record<string, any>) => {
    loading.value = true;
    if (!hasMore.value) {
      loading.value = false;
      return;
    }
    uni.showLoading({
      title: '加载中',
      mask: true,
      success: async () => {
        options = options || {};
        try {
          const { data } = await request<any>(api, {
            method: 'GET',
            params: {
              page: currentPage.value,
              pageSize: size.value,
              ...defaultQueryParams,
              ...queryParams.value,
              ...options,
            },
            ...config,
          });

          rawResult.value = data;

          let list = data.list || data.items || [];

          if (typeof config.dataFormat === 'function') {
            list = config.dataFormat(list);
          }

          totalItems.value = data.total;
          if (appendOrPrepend === 'append') {
            dataList.value = currentPage.value === 1 ? list : dataList.value.concat(list);
          } else if (typeof appendOrPrepend === 'function') {
            dataList.value = appendOrPrepend(dataList.value, list);
          } else {
            dataList.value = currentPage.value === 1 ? list : list.concat(dataList.value);
          }

          hasMore.value = list?.length >= pageSize;
        } finally {
          loading.value = false;
          uni.hideLoading();
        }
      },
    });
  };

  const refresh = async (options?: Record<string, any>) => {
    hasMore.value = true;
    currentPage.value = 1;
    await loadData(options);
  };

  const loadMore = async () => {
    if (!hasMore.value) return;
    currentPage.value += 1;
    await loadData();
  };

  return {
    dataList: dataList as Ref<any[]>,
    rawResult,
    totalItems,
    loading,
    hasMore,
    currentPage,
    size,
    loadData,
    refresh,
    queryParams,
    loadMore,
  };
};

export default useList;
