import { ref } from 'vue';
import { PROJECT_URLS } from '@/common/env';
import { getToken } from '@repo/infrastructure/auth';

type ConversationMessage = {
  data: string;
  conversationId: number;
  conversationMessageType?: string;
};

const useWebSocket = () => {
  const wsUrl = PROJECT_URLS.WS_URL;
  const heartBeatTimer = ref<any>(null);
  const socketTask = ref();

  const onMessage = (callback: (data: any) => void) => {
    uni.onSocketMessage((res) => {
      if (res.data === 'pong') {
        return;
      }
      callback(JSON.parse(res.data));
    });
  };

  const connect = async () => {
    if (heartBeatTimer.value) {
      clearInterval(heartBeatTimer.value);
    }
    return new Promise((resolve, reject) => {
      const token = getToken();
      // const headers = {
      //   'content-type': 'application/json',
      //   'Guardian-Token': `Bearer ${token}`,
      //   'Login-Source': 'GuardianWeapp',
      // };

      socketTask.value = uni.connectSocket({
        url: `${wsUrl}?token=${token}&loginSource=${uni.getStorageSync('LoginSource')}`,
        success: () => {
          console.log('WebSocket连接中');
          resolve(true);
        },
        fail: () => {
          console.log('WebSocket连接失败');
          // eslint-disable-next-line prefer-promise-reject-errors
          reject(false);
        },
      });

      heartBeatTimer.value = setInterval(async () => {
        // eslint-disable-next-line no-use-before-define
        await checkSocketStatus();
      }, 10 * 1000);
    });
  };

  const checkSocketStatus = async () => {
    return new Promise((resolve, reject) => {
      uni.sendSocketMessage({
        data: 'ping',
        success: () => {
          resolve(true);
        },
        fail: async () => {
          const res = await connect();
          if (res) {
            uni.onSocketOpen(() => {
              console.log('WebSocket连接成功');
              resolve(true);
            });
            uni.onSocketClose(() => {
              console.log('WebSocket连接关闭');
              // eslint-disable-next-line prefer-promise-reject-errors
              reject(false);
            });
          }
          resolve(false);
        },
      });
    });
  };

  const sendChatMessage = async (data: ConversationMessage) => {
    const msg = {
      type: 'conversation',
      ...data,
      conversationMessageType: data.conversationMessageType || 'text',
    };
    await uni.sendSocketMessage({
      data: JSON.stringify(msg),
    });

    return msg;
  };

  return {
    connect,
    checkSocketStatus,
    onMessage,
    sendChatMessage,
  };
};

export default useWebSocket;
