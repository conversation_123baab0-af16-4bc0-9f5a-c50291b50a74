import { request } from '@repo/infrastructure/request';
import { goPage } from '@/common/page';
import { PROJECT_URLS } from '@/common/env';

type GetDocumentContentOptions = {
  sourceId: string;
  docTplIndex: number;
  type: DocumentType;
  docTemplate: any;
};

type DocumentType = 'SendPlan' | 'SendRecord' | 'IEPArchive' | 'IEPPlan';

const useDocument = () => {
  const getDocumentContent = async ({
    sourceId,
    docTplIndex,
    type,
    docTemplate,
  }: GetDocumentContentOptions): Promise<any> => {
    const { data } = await request(
      `/document/document/getContent?sourceId=${sourceId}&docTplIndex=${docTplIndex}&type=${type}`,
      {
        method: 'POST',
        data: {
          ...docTemplate,
        },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      },
    );
    return data;
  };

  const handlePreviewDoc = async ({ sourceId, docTplIndex, type, docTemplate }: GetDocumentContentOptions) => {
    uni.showLoading({
      title: '加载中',
      mask: true,
    });
    try {
      let document = await getDocumentContent({ sourceId, docTplIndex, type, docTemplate });
      if (!document.useOnlineDoc && docTemplate.onlineDoc) {
        document = await request(
          `/document/document/switchMode?sourceId=${sourceId}&docTplIndex=${docTplIndex}&type=SendRecord&mode=online`,
          {
            method: 'PUT',
            data: {
              ...docTemplate,
            },
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          },
        );
      }

      if (!document.useOnlineDoc) {
        uni.hideLoading();
        goPage('/attachment/docPreview', {
          id: document.id,
        });
      } else if (document.wpsFileId) {
        const { data } = await request(`/common/uploadedResource/wpsById/${document.wpsFileId}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        });

        const res = await uni.downloadFile({
          url: data as string,
        });
        const filePath = res.tempFilePath;
        const ext = docTemplate.attachment?.name.split('.').pop();
        await uni.openDocument({
          filePath,
          fileType: ext,
        });
      } else {
        uni.hideLoading();
        uni.showToast({
          title: '文档当前不可预览',
          icon: 'none',
        });
      }
    } catch (e) {
      uni.showToast({
        title: '打开文档失败',
        icon: 'none',
      });
    } finally {
      uni.hideLoading();
    }
  };

  return {
    handlePreviewDoc,
    getDocumentContent,
  };
};

export default useDocument;
