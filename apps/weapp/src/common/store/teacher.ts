import { defineStore } from 'pinia';
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@/common/env';
import { clearToken, setToken } from '@repo/infrastructure/auth';

export const useTeacherStore = defineStore({
  id: 'teacherUser',
  state: () => ({
    userInfo: {} as any,
  }),
  actions: {
    setUserInfo(userInfo: any) {
      this.userInfo = userInfo || {};
      uni.setStorageSync('teacherInfo', userInfo);
    },
    getUserInfo(): any {
      if (this.userInfo.id) {
        return this.userInfo;
      }
      return uni.getStorageSync('teacherInfo') || {};
    },
    async loginViaWeapp() {
      const { code } = await uni.login();
      clearToken();
      const { data } = await request('/org/session', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'POST',
        data: {
          usernameOrMobile: 'this-is-any',
          code: 'codeok',
          loginType: 'weappOpenIdAuto',
          roleType: 'Company',
          rememberMe: true,
          wxAuthCodeForOpenId: code,
        },
      });
      console.log(code,data,'teacherUser');

      if (data?.token) {
        setToken(data.token, data.expireTime);
        await this.refreshUserInfo();
      }
    },
    async refreshUserInfo(): Promise<any> {
      const { data } = await request('/org/session/me', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      this.setUserInfo(data || {});

      return data || {};
    },
    async logout() {
      clearToken();
      this.setUserInfo({});
      uni.clearStorageSync();
    }
  },
})
