import { defineStore } from 'pinia';
import openapi from '@repo/infrastructure/openapi';
import {getToken, setToken} from '@repo/infrastructure/auth';
import { PROJECT_URLS } from '@/common/env';
// import { axiosInterceptor } from '@repo/infrastructure/request';

export const useGuardianStore = defineStore({
  id: 'guardianUser',
  state: () => ({
    userInfo: {} as API.Guardian,
  }),
  actions: {
    setUserInfo(userInfo: API.Guardian) {
      this.userInfo = userInfo || {};
      uni.setStorageSync('userInfo', userInfo);
    },
    getUserInfo(): API.Guardian {
      if (this.userInfo.id) {
        return this.userInfo;
      }
      return uni.getStorageSync('userInfo') || {};
    },
    async loginViaWeapp() {
      const { code } = await uni.login();
      const { data } = await openapi.guardianController.guardianLoginViaWeapp(
        {
          code,
        },
        { baseURL: PROJECT_URLS.GO_PROJECT_API },
      );
      console.log(code,data,'guardianUser');
      setToken(data.token, data.expireAt ? data.expireAt * 1000 : undefined);
      this.setUserInfo(data.guardianInfo || {});
    },
    async refreshUserInfo(): Promise<API.Guardian> {
      // axiosInterceptor();
      if (!getToken()) {
        await this.loginViaWeapp();
      }
      const { data } = await openapi.guardianController.guardianGetMyInfo({ baseURL: PROJECT_URLS.GO_PROJECT_API });
      this.setUserInfo(data);
      return data;
    },
    async logout() {
      uni.clearStorageSync()
      this.setUserInfo({});
      await uni.reLaunch({ url: '/pages/index/index' });
    }
  },
});
