import GetLocationSuccess = UniNamespace.GetLocationSuccess;

export type FetchLocationResult = {
  data: GetLocationSuccess;
  result: boolean | 'DENIED';
};

export const fetchLocation = async (): Promise<FetchLocationResult> => {
  let res = {} as GetLocationSuccess;
  try {
    res = await uni.getLocation({
      type: 'gcj02',
    });
  } catch (e: any) {
    res.errMsg = e.errMsg;
  }

  switch (res.errMsg) {
    case 'getLocation:ok':
      return {
        data: res,
        result: true,
      };
    case 'getLocation:fail':
      return {
        data: res,
        result: false,
      };
    case 'getLocation:fail auth deny':
      return {
        data: res,
        result: 'DENIED',
      };
    default:
      return {
        data: res,
        result: false,
      };
  }
};

export const showLocationDeniedTips = async (msg?: string) => {
  await uni.showModal({
    title: '提示',
    showCancel: false,
    content: msg || '需要获取您的位置确定您所属学校，请在设置中打开定位权限',
  });
};

export const userLocationAuthed = async () => {
  const settings = await uni.getSetting();
  return settings.authSetting['scope.userLocation'];
};
