/**
 * NotParticipated(0, "未参加"),
 *     //迟到
 *     Late(1, "迟到"),
 *     //早退
 *     LeaveEarly(2, "早退"),
 *     //正常签退
 *     NormalSignOut(3, "正常签退"),
 *     //正常签到
 *     NormalSignIn(4, "正常签到"),
 *     // 全勤
 *     FullAttendance(5, "全勤"),
 *     // 未签到
 *     NotSignIn(6, "未签到"),
 *     // 未签退
 *     NotSignOut(7, "未签退");
 */

const AttendanceStatus = [
  { label: '未参加', value: 'NotParticipated' },
  { label: '迟到', value: 'Late' },
  { label: '早退', value: 'LeaveEarly' },
  { label: '正常签退', value: 'NormalSignOut' },
  { label: '正常签到', value: 'NormalSignIn' },
  { label: '全勤', value: 'FullAttendance' },
  { label: '未签到', value: 'NotSignIn' },
  { label: '未签退', value: 'NotSignOut' },
];

const AttendanceStatusMap = AttendanceStatus.reduce((acc, cur) => {
  acc[cur.value] = cur.label;
  return acc;
});

export { AttendanceStatus, AttendanceStatusMap };
