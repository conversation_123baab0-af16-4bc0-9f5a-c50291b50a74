// eg: https://www.example.com/v1/entry?workStationId=1&id=2
import { PROJECT_URLS } from '@/common/env';

export const parseQueryInUrl = (url: string) => {
  const queryStr = url.split('?')[1];

  if (!queryStr) {
    return {};
  }

  const result: Record<string, any> = {};

  queryStr.split('&').forEach((item) => {
    const [key, value] = item.split('=');
    result[key] = value;
  });

  return result;
};

export const makePhoneCall = (phone: string) => {
  uni.makePhoneCall({
    phoneNumber: phone,
  });
};

export const avatarSrc = (avatarId: any) => {
  const resourceId = Number.parseInt(avatarId, 10);
  if (resourceId) {
    return `${PROJECT_URLS.MAIN_PROJECT}/common/uploadedResource/image/${resourceId}?width=100&height=100`;
  }
  return 'https://img.yzcdn.cn/vant/cat.jpeg';
};
