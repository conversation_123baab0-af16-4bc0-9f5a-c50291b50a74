<script setup lang="ts">
  import { computed } from 'vue';

  const props = defineProps({
    status: {
      type: String,
      default: '未确认',
    },
    size: {
      type: String,
      default: 'small',
    },
  });

  const statusList = [
    { id: 'Normal', label: '正常', tag: 'success', inverted: true },
    { id: 'Graduation', label: '毕业', tag: 'primary', inverted: true },
    { id: 'Suspension', label: '毕业', tag: 'danger', inverted: true },
    { id: 'TransferOut', label: '毕业', tag: 'warning', inverted: true },
  ];

  const statusItem = computed<any>(() => {
    return statusList.find((item) => item.id === props.status) || {};
  });
</script>

<template>
  <uni-tag :size="size" :text="statusItem.label" :inverted="statusItem.inverted" :type="statusItem.tag" />
</template>

<style scoped lang="scss"></style>
