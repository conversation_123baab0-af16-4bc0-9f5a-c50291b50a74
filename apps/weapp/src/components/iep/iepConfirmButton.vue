<script setup lang="ts">
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@/common/env';
  import dayjs from 'dayjs';

  const props = defineProps({
    iepArchive: {
      type: Object,
      required: true,
    },
    loadData: {
      type: Function,
      required: true,
    },
  });

  const handleConfirm = async () => {
    if (!props.iepArchive.id || props.iepArchive.id.confirmed) {
      return;
    }
    uni.showModal({
      title: '确认',
      content: '确认已查看并同意该IEP档案内容？',
      success: async (res) => {
        if (res.confirm) {
          uni.showLoading({ title: '确认中' });
          try {
            await request(`/resourceRoom/individualizedEducation/confirm/${props.iepArchive.id}`, {
              method: 'PUT',
              baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            });

            uni.showToast({ title: '确认成功', icon: 'success' });
            await props.loadData();
          } finally {
            uni.hideLoading();
          }
        }
      },
    });
  };
</script>

<template>
  <view v-if="!iepArchive.guardianConfirmed">
    <fixed-button-safe />
    <fixed-bottom-button @click="handleConfirm"> 确认 </fixed-bottom-button>
  </view>
  <view v-else>
    <fixed-button-safe />
    <fixed-bottom-button disabled>
      已确认
      <text class="text-xs">
        {{ dayjs(iepArchive.guardianConfirmTime).format('YYYY-MM-DD HH:mm') }}
      </text>
    </fixed-bottom-button>
  </view>
</template>

<style scoped lang="scss"></style>
