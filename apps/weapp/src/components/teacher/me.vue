<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { goPage, relaunchToPage } from '@/common/page';
  import { useTeacherStore } from '@/common/store/teacher';
  import { loginGuard } from '@/common/auth';
  import { useUserStore } from '@repo/infrastructure/store';
  import { request } from '@repo/infrastructure/request';
  import { setToken } from '@repo/infrastructure/auth';
  import { PROJECT_URLS } from '@/common/env';

  const teacherStore = useTeacherStore();
  const userInfo = teacherStore.userInfo;
  const userStore = useUserStore();

  const studentLinks = computed(() => {
    return [
      {
        title: `我的学生`,
        url: '/student/list',
        permNode: 'student:view',
      },
      {
        title: '个别化教育',
        url: '/student/iep/index?teacherMode=1',
        permNode: 'teaching:teachingPlan:iepPlan',
      },
      {
        title: '支持计划',
        url: '/student/isp/index?teacherMode=1',
        permNode: 'teaching:teachingPlan:supportPlan',
      },
      {
        title: '送教计划',
        url: '/student/sendEdu/index?teacherMode=1',
        permNode: 'teaching:teachingPlan:send',
      },
    ].filter((item) => {
      if (!item.permNode) {
        return true;
      }

      return userStore.isAuthorized(item.permNode, userInfo.authorities || []);
    });
  });

  const workLinks = computed(() => {
    return [
      {
        title: `康复设计`,
        url: '/teacher/rehabilitationDesign/index',
        permNode: 'rehabilitation:design',
      },
      {
        title: '常规巡回指导预约',
        url: '/teacher/normalTourGuide/index',
        permNode: 'dailyWork:normalTourGuide',
      },
      {
        title: '教研培训记录',
        url: '/teacher/conference/records',
      },
    ].filter((item) => {
      if (!item.permNode) {
        return true;
      }

      return userStore.isAuthorized(item.permNode, userInfo.authorities || []);
    });
  });

  const accounts = ref<any[]>([]);
  const switchAccountPopup = ref<any>(null);
  const toUserId = ref<number>();

  const handleLoadAvatars = async () => {
    const { data } = await request('/org/companyUser/myAvatars', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    uni.hideToast();

    accounts.value = data;
    toUserId.value = userInfo.id;
  };

  const handleSwitchAccount = async () => {
    uni.showLoading({ title: '请稍后...' });
    switchAccountPopup.value.close();

    const { data } = await request(`/org/companyUser/switchTo/${toUserId.value}`, {
      method: 'PUT',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    if (data) {
      setToken(data);
      await teacherStore.refreshUserInfo();
      relaunchToPage('/home/<USER>', {
        activeTab: 'me',
      });
    }
  };

  const handleAccountCheck = (e) => {
    toUserId.value = e.detail.value;
  };

  const settings = [
    {
      title: '切换账户',
      handler: async () => {
        await handleLoadAvatars();
        switchAccountPopup.value.open('bottom');
      },
    },
    {
      title: '退出登陆',
      handler: async () => {
        uni.showModal({
          title: '提示',
          content: '确定退出登录？',
          success: async (res) => {
            if (res.confirm) {
              await teacherStore.logout();
              await uni.reLaunch({
                url: '/pages/index/index',
              });
            }
          },
        });
      },
    },
  ];

  const init = async () => {
    await loginGuard('teacher', '/home/<USER>');
  };
  onMounted(async () => {
    await init();
  });
</script>

<template>
  <view v-if="userInfo?.id">
    <view>
      <view class="header p-4">
        <view class="flex gap-4 justify-between">
          <view class="flex-1 flex gap-4">
            <view class="avatar border-2 border-white border-solid rounded-full w-12 h-12"> - </view>
            <view class="info">
              <view class="name mt-1">{{ userInfo.name }}</view>
              <view class="phone text-xs mt-1">{{ userInfo.mobile || '未绑定手机号码' }}</view>
            </view>
          </view>
          <view v-if="userInfo.currentStudent?.id" class="right text-sm" />
        </view>
      </view>

      <view v-if="studentLinks?.length" class="px-4 mt-4">
        <uni-section title="我的学生" type="line">
          <uni-list border>
            <uni-list-item
              v-for="(link, idx) in studentLinks"
              :key="idx"
              :title="link.title"
              show-arrow
              clickable
              @click="goPage(link.url, { id: userInfo.id })"
            />
          </uni-list>
        </uni-section>
      </view>

      <view class="px-4 mt-4">
        <uni-section title="教学工作" type="line">
          <uni-list border>
            <uni-list-item
              v-for="(link, idx) in workLinks"
              :key="idx"
              :title="link.title"
              show-arrow
              clickable
              @click="goPage(link.url, { id: userInfo.id })"
            />
          </uni-list>
        </uni-section>
      </view>

      <view class="px-4 mt-4">
        <uni-section title="设置" type="line">
          <uni-list border>
            <uni-list-item
              v-for="(link, idx) in settings"
              :key="idx"
              :title="link.title"
              show-arrow
              clickable
              @click="link.handler ? link.handler() : () => goPage(link.url, { id: userInfo.id })"
            />
          </uni-list>
        </uni-section>
      </view>
    </view>

    <uni-popup ref="switchAccountPopup" type="bottom">
      <view style="border-radius: 10px 10px 0 0" class="bg-white p-4 pb-10">
        <radio-group @change="handleAccountCheck">
          <label v-for="(item, index) in accounts" :key="item.id" class="flex gap-2 py-2">
            <view>
              <radio :value="item.id" :checked="item.id === toUserId" />
            </view>
            <view>{{ item.name }}({{ item.udf1 }})</view>
          </label>
        </radio-group>

        <button type="primary" class="py-2 mt-2" @click="handleSwitchAccount">确认切换</button>
      </view>
    </uni-popup>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarBackgroundColor": "#1989fa",
    "navigationBarTextStyle": "white"
  }
}
</route>

<style scoped lang="scss">
  .header {
    background-color: $primaryBlue;
    color: #fff;
  }
</style>
