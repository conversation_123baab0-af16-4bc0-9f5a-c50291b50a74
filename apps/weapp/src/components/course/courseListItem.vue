<script setup lang="ts">
  import { computed, type PropType } from 'vue';
  import { goPage } from '@/common/page';
  import useOss from '@/common/hooks/oss';
  import dayjs from 'dayjs';
  import { PROJECT_URLS } from '@/common/env';

  const props = defineProps({
    item: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const coverSize = {
    width: 240,
    height: 160,
  };

  const oss = useOss();

  const thumb = computed(() => {
    if (props.item.thumb) {
      return props.item.thumb;
    }

    const firstMp4Id = props.item.attachments.find((attachment: any) => attachment.name.endsWith('.mp4'))?.id;
    if (firstMp4Id) {
      return `${PROJECT_URLS.MAIN_PROJECT_API}/common/uploadedResource/videoCover/${firstMp4Id}`;
    }

    return null;
  });

  const getItemThumb = (item: any) => {};
</script>

<template>
  <view
    v-if="item.id"
    class="flex gap-2 mb-2 pb-2 border-0 border-b border-solid border-slate-300"
    @tap="
      () =>
        goPage('/guardian/course/detail', {
          id: item.id,
        })
    "
  >
    <view v-if="thumb">
      <image
        :src="thumb"
        mode="aspectFill"
        :style="{
          width: `${coverSize.width}rpx`,
          height: `${coverSize.height}rpx`,
        }"
      />
    </view>
    <view class="flex-1">
      <view class="text-sm text-gray-500 mb-1">
        {{ dayjs(item.createdAt).format('YYYY-MM-DD') }}
      </view>
      <view class="text-base text-ellipsis h-12 mt-1 overflow-hidden">{{ item.name }}</view>
    </view>
  </view>
</template>

<style scoped lang="scss"></style>
