<script setup lang="ts">
  import openapi from '@repo/infrastructure/openapi';
  import { onMounted, ref } from 'vue';
  import { goPage, relaunchToPage } from '@/common/page';
  import useWebSocket from '@/common/hooks/websocket';
  import { request } from '@repo/infrastructure/request';
  import useOss from '@/common/hooks/oss';
  import Empty from '@/components/empty.vue';
  import { PROJECT_URLS } from '@/common/env';
  import { useGuardianStore } from '@/common/store/gardian';
  import { getToken } from '@repo/infrastructure/auth';

  const carousels = ref<API.GuardianWeappCarousel[]>([]);
  const newsList = ref<any[]>([]);
  const announcement = ref<API.GuardianAnnouncement | any>();

  const currentSwiperIndex = ref(0);
  const guardianCourses = ref<any[]>([]);
  const oss = useOss();
  const guardianStore = useGuardianStore();
  const userInfo = guardianStore.userInfo;

  const quickButtons = [
    {
      text: '通知',
      icon: 'notification',
      class: 'bg-green-500',
      handler() {
        goPage('/news/announcement');
      },
    },
    {
      text: '预约',
      icon: 'paperplane',
      class: 'bg-cyan-500',
      handler() {
        goPage('/guardian/reservation/index');
      },
    },
    // {
    //   text: '专家',
    //   icon: 'chat',
    //   class: 'bg-blue-500',
    //   handler() {
    //     uni.switchTab({
    //       url: '/pages/im/index',
    //     });
    //   },
    // },
  ] as any[];

  const loadGuardianCourses = async () => {
    const { data } = (await request('/rehabilitationLibrary/platformRehabilitationResource', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        pageSize: 4,
        sort: '-id',
        availableClient: 'Guardian',
      },
    })) as any;
    guardianCourses.value = data?.items || [];
  };

  const init = async () => {
    const { data } = await openapi.guardianController.guardianWeappInit(
      {
        visibleIn: 'guardian',
      },
      { baseURL: PROJECT_URLS.GO_PROJECT_API },
    );
    carousels.value = data.carouselList || [];
    newsList.value = data.newsList || [];
    announcement.value = data.announcementList?.find(
      (item) => item.indexBanner && item.visibleIn?.includes('guardian'),
    );
    console.log(announcement.value);
    await loadGuardianCourses();
  };

  const handleSwiperChange = (e: any) => {
    currentSwiperIndex.value = e.detail.current;
  };

  const ws = useWebSocket();

  const handleGoCarousel = (item: API.GuardianWeappCarousel) => {
    if (item.link) {
      uni.navigateTo({
        url: item.link,
      });
    }
  };

  onMounted(async () => {
    await Promise.all([await init(), await ws.checkSocketStatus()]);
    if (getToken()) {
      const { data: unreadCount } = await openapi.genericSessionController.getUnreadMessageCount({
        baseURL: PROJECT_URLS.GO_PROJECT_API,
      });

      if (unreadCount > 0) {
        // uni.showTabBarRedDot({
        //   index: 1,
        // });
      } else {
        // uni.hideTabBarRedDot({
        //   index: 1,
        // });
      }
    }
  });
</script>

<template>
  <view>
    <uni-swiper-dot class="uni-swiper-dot-box" :info="carousels || []" :current="currentSwiperIndex" field="content">
      <swiper class="swiper-box !w-full" :current="currentSwiperIndex" @change="handleSwiperChange">
        <swiper-item v-for="(item, index) in carousels || []" :key="index" class="w-full">
          <view  :class="[`swiper-item${index}`,'swiper-item !w-full']" @click="() => handleGoCarousel(item)">
            <image
              :src="oss.imageThumbUrl(item.image, 750, 400)"
              class="store-photo !w-full"
              mode="aspectFill"
              lazy-load
            />
          </view>
        </swiper-item>
      </swiper>
    </uni-swiper-dot>

    <uni-notice-bar
      v-if="announcement"
      :speed="50"
      :text="`${announcement?.title}`"
      more-text="查看详情"
      :scrollable="true"
      :show-get-more="true"
      :show-icon="true"
      @click="goPage('/news/announcementDetail', { id: announcement?.id })"
    />

    <view class="grid grid-cols-3 px-2 py-4 bg-white text-center">
      <view
        v-for="(btn, index) in quickButtons"
        :key="index"
        class="flex flex-col items-center gap-2 justify-center"
        @tap="btn.handler"
      >
        <view class="rounded-full w-14 h-14 flex items-center justify-center" :class="btn.class">
          <uni-icons :type="btn.icon" size="40" color="#fff" />
        </view>
        <text class="text-sm">{{ btn.text }}</text>
      </view>
    </view>

    <view class="mt-4 mx-2">
      <uni-section title="家长课程" type="line">
        <template #right>
          <navigator
            v-if="userInfo?.currentStudent?.id"
            url="/pages/guardian/course/index"
            class="text-gray-500 text-xs"
          >
            更多 &gt;&gt;
          </navigator>
        </template>
        <view class="border-0 border-t border-gray-200 border-solid py-4 px-2">
          <view v-if="guardianCourses?.length" class="grid grid-cols-2 gap-4">
            <view
              v-for="(course, index) in guardianCourses"
              :key="index"
              class="flex flex-col gap-2"
              @tap="goPage('/guardian/course/detail', { id: course.id })"
            >
              <image
                :src="course.coverImage || course.thumb || oss.videoCoverUrlByAttachments(course.attachments)"
                mode="aspectFill"
                class="w-full h-24 bg-gray-300"
              />
              <view class="truncate text-ellipsis text-sm">
                {{ course.name }}
              </view>
            </view>
          </view>
          <empty v-else title="暂无相关课程" />
        </view>
      </uni-section>
    </view>

    <view v-if="newsList.length" class="mt-2 mx-2">
      <uni-section title="最新资讯" type="line">
        <template #right>
          <navigator url="/pages/news/index" class="text-gray-500 text-xs"> 更多 &gt;&gt; </navigator>
        </template>
        <uni-list border>
          <uni-list-item
            v-for="(news, index) in newsList"
            :key="index"
            :title="news.title"
            :ellipsis="1"
            clickable
            show-arrow
            @click="goPage('/news/detail', { id: news.id })"
          />
        </uni-list>
      </uni-section>
    </view>
  </view>
</template>

<style scoped lang="scss">
  .swiper-box,
  .swiper-item,
  .store-photo {
    width: 750rpx;
    height: 400rpx;
    background: #ddd;
  }
</style>
