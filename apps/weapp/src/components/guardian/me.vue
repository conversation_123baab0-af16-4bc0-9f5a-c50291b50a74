<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import openapi from '@repo/infrastructure/openapi';
  import { goPage } from '@/common/page';
  import { useGuardianStore } from '@/common/store/gardian';
  import { TOKEN_CACHE_KEY as Token<PERSON>acheKey } from '@repo/infrastructure/constants';
  import { loginGuard } from '@/common/auth';
  import { PROJECT_URLS } from '@/common/env';

  /**
   * student.status === 5 : waiting resettlement
   */

  const guardianStore = useGuardianStore();
  const guardianInfo = ref<API.Guardian>(guardianStore.getUserInfo());

  const links = computed(() => {
    const studentTitle =
      guardianInfo.value.currentStudent?.status === '5'
        ? `${guardianInfo.value.currentStudent?.name} 安置申请进度查看`
        : `${guardianInfo.value.currentStudent?.name}的档案`;
    if (guardianInfo.value.currentStudent?.status === '5') {
      return [
        {
          title: studentTitle,
          url: '/guardian/student/resettlementApplyProgress',
        },
      ];
    }
    return [
      {
        title: studentTitle,
        url: '/guardian/student/archive',
      },
      {
        title: '家校联系',
        url: '/guardian/student/connection/index',
      },
      {
        title: '个别化教育',
        url: `/student/iep/index?studentId=${guardianInfo.value.currentStudent?.id}&submitStatus=Submitted`,
      },
      {
        // 查询sendVersion 决定跳转到那个模式的送教
        title: '送教计划',
        url: `/student/sendEdu/index?studentId=${guardianInfo.value.currentStudent?.id}&submitStatus=Submitted&teacherMode=1&isParent=true`,
      },
      {
        title: '上门记录',
        url: `/student/sendEdu/records?studentId=${guardianInfo.value.currentStudent?.id}&submitStatus=Submitted&isParent=true`,
      },
    ];
  });

  const init = async () => {
    await loginGuard('guardian', '/home/<USER>');
  };

  const handleSyncMyChildren = async () => {
    const res = await uni.showModal({
      title: '同步我的学生信息',
      content: '如果您同步后，仍未显示您的学生信息，请联系学校老师为学生绑定您当前登录的手机号码',
      showCancel: false,
      confirmText: '同步信息',
    });

    if (res.confirm) {
      await openapi.guardianController.syncMyChildren({ baseURL: PROJECT_URLS.GO_PROJECT_API });
      await init();
      if (guardianInfo.value.studentList?.length) {
        await uni.showToast({
          title: '同步成功',
          icon: 'success',
          duration: 2000,
        });
      } else {
        await uni.showModal({
          title: '同步失败',
          content: '未能获取到您学生的信息，请联系学校老师为学生绑定您当前登录的手机号码',
          showCancel: false,
          confirmText: '知道了',
        });
      }
    }
  };

  const allStudents = computed(() => {
    return guardianInfo.value.studentList?.map((student) => {
      return {
        id: student.id,
        name: student.name,
      };
    });
  });

  const allStudentNames = computed<any>(() => {
    return guardianInfo.value.studentList?.map((student) => student.name);
  });

  const handleCurrentStudentSwitch = async (e: any) => {
    const index = e.detail.value;
    const student = guardianInfo.value.studentList?.[index];
    if (student?.id) {
      uni.showLoading({
        title: '切换中...',
      });

      try {
        await openapi.guardianController.guardianSwitchStudent(
          {
            studentId: student.id,
          },
          { baseURL: PROJECT_URLS.GO_PROJECT_API },
        );

        uni.removeStorageSync('userInfo');
        uni.removeStorageSync(TokenCacheKey);

        await uni.reLaunch({
          url: '/pages/home/<USER>',
        });
      } finally {
        uni.hideLoading();
      }
    }
  };

  const settings = [
    {
      title: '退出登陆',
      handler: async () => {
        uni.showModal({
          title: '提示',
          content: '确定退出登录？',
          success: async (res) => {
            if (res.confirm) {
              await guardianStore.logout();
              await uni.reLaunch({
                url: '/pages/index/index',
              });
            }
          },
        });
      },
    },
  ];

  const currentStudentIndex = computed(() => {
    return guardianInfo.value.studentList?.findIndex((student) => student.id === guardianInfo.value.currentStudent?.id);
  });

  onMounted(async () => {
    await init();
  });
</script>

<template>
  <view v-if="guardianInfo?.id">
    <view v-if="guardianInfo.mobile">
      <view class="header p-4">
        <view class="flex gap-4 justify-between">
          <view class="flex-1 flex gap-4">
            <view class="avatar border-2 border-white border-solid rounded-full w-12 h-12"> - </view>
            <view class="info">
              <view class="name mt-1">{{ guardianInfo.name }}</view>
              <view class="phone text-xs mt-1">{{ guardianInfo.mobile }}</view>
            </view>
          </view>
          <view v-if="guardianInfo.currentStudent?.id" class="right text-sm">
            <picker :range="allStudentNames" :value="currentStudentIndex" @change="handleCurrentStudentSwitch">
              <view class="flex items-center">
                <view class="text-right">
                  <view class="text-xs text-gray-100"> 当前学生 </view>
                  <view> {{ guardianInfo.currentStudent.name }} </view>
                </view>
                <uni-icons class="ml-2" type="right" size="18" color="#fff" />
              </view>
            </picker>
          </view>
        </view>
      </view>

      <view class="p-4">
        <uni-section title="家长您好" type="line">
          <uni-list v-if="guardianInfo.studentList?.length" border>
            <uni-list-item
              v-for="(link, idx) in links"
              :key="idx"
              :title="link.title"
              show-arrow
              clickable
              @click="goPage(link.url, { id: guardianInfo.currentStudent?.id })"
            />
          </uni-list>
          <uni-list v-else border>
            <uni-list-item title="由已绑定监护人添加权限" note="请联系老师绑定的学生监护人，为您当前手机号添加权限" />
            <uni-list-item
              title="同步我的学生信息"
              show-arrow
              clickable
              note="如果您是老师绑定的学生监护人，请点此同步学生信息"
              @click="handleSyncMyChildren"
            />
          </uni-list>
        </uni-section>

        <view class="mt-4">
          <uni-section title="设置" type="line">
            <uni-list border>
              <uni-list-item
                v-for="(link, idx) in settings"
                :key="idx"
                :title="link.title"
                show-arrow
                clickable
                @click="link.handler ? link.handler() : () => goPage(link.url, { id: userInfo.id })"
              />
            </uni-list>
          </uni-section>
        </view>
      </view>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarBackgroundColor": "#1989fa",
    "navigationBarTextStyle": "white"
  }
}
</route>

<style scoped lang="scss">
  .header {
    background-color: $primaryBlue;
    color: #fff;
  }
</style>
