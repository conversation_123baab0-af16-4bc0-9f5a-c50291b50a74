<script setup lang="ts">
  import { ref } from 'vue';

  const props = defineProps({
    type: {
      type: String,
      default: 'line',
    },
    collapsed: {
      type: Boolean,
      default: false,
    },
  });

  const collapse = ref(props.collapsed);

  const handleSwitch = () => {
    collapse.value = !collapse.value;
  };
</script>

<template>
  <uni-section :type="type" v-bind="{ ...($attrs || {}) }" clickable @click="handleSwitch">
    <template #right>
      <view>
        <uni-icons
          v-if="collapse"
          class="switch-button"
          type="down"
          size="20"
          color="#999"
          :class="{ rotate: collapse }"
        />
        <uni-icons v-else type="up" class="switch-button" size="20" color="#999" :class="{ rotate: collapse }" />
      </view>
    </template>
    <view class="content" :class="{ collapsed: collapse }">
      <slot />

      <view class="bg-blue-200 py-3 m-2 text-center" clickable @click="() => handleSwitch()">
        <uni-icons type="up"></uni-icons> 收起
      </view>
    </view>
  </uni-section>
</template>

<style scoped lang="scss">
  .content {
    display: block;
    transition: all linear 0.5s;
    max-height: 5000px;
    overflow: hidden;

    &.collapsed {
      max-height: 0;
    }
  }

  .switch-button {
    transition: all linear 0.5s;

    &.rotate {
      transform: rotate(180deg);
    }
  }
</style>
