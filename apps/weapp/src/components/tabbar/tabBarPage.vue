<script setup lang="ts">
  import type { PropType } from 'vue';
  import type { TabBarItem } from '@/common/types';
  import { computed } from 'vue';

  const props = defineProps({
    tabBarItems: {
      type: Array as PropType<TabBarItem[]>,
      required: true,
    },
    activeTabKey: {
      type: String,
    },
  });

  const emit = defineEmits(['update:activeTabKey']);

  const activeTab = computed({
    get() {
      if (!props.activeTabKey) {
        return props.tabBarItems[0];
      }
      return props.tabBarItems.find((item: any) => item.key === props.activeTabKey);
    },
    set(value: TabBarItem) {
      emit('update:activeTabKey', value.key);
    },
  });

  const tabSwitch = (page: TabBarItem) => {
    activeTab.value = page;
    uni.setNavigationBarTitle({ title: page.title || page.text });
  };

  defineExpose({ activeTab });
</script>

<template>
  <view class="tab-page-content">
    <scroll-view class="scroll-view" scroll-y>
      <slot></slot>
    </scroll-view>
    <view class="flex space-around items-center gap-4 tab-bar-wrapper px-2 pt-2">
      <view
        v-for="(item, idx) in tabBarItems"
        :key="idx"
        class="tab-bar-item"
        :class="{ active: activeTab.key === item.key }"
        @click="() => tabSwitch(item)"
      >
        <view class="flex flex-col items-center gap-1">
          <view class="iconfont" :class="item.icon"></view>
          <text class="text-sm">{{ item.text }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
  .tab-page-content {
    height: 100%;
  }
  .scroll-view {
    height: calc(100vh - 220rpx);
    overflow-y: scroll;
  }
  .tab-bar-wrapper {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.1);
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }

  .tab-bar-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 0;

    .iconfont {
      font-size: 36rpx;
    }

    &.active {
      color: #1989fa;
      font-weight: 500;
    }
  }
</style>
