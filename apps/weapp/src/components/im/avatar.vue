<script setup lang="ts">
  import { avatarSrc } from '@/common/utils/utils';

  defineProps({
    avatarResourceId: {
      type: [Number, String],
      required: true,
    },
    mode: {
      type: String,
      default: 'aspectFill',
    },
    extraClass: {
      type: String,
      default: 'w-8 h-8',
    },
  });
</script>

<template>
  <image :src="avatarSrc(avatarResourceId)" :class="extraClass" :mode="mode" />
</template>
