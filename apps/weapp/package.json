{"name": "linch-se-weapp", "version": "2.1.52", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin --mode dev", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit", "switch:v2": "ts-node ./deploy/env/switchEnv.ts v2", "switch:wh": "ts-node ./deploy/env/switchEnv.ts wh", "upload:mp-weixin": "ts-node ./deploy/upload.ts"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4040520250104002", "@dcloudio/uni-app-harmony": "3.0.0-4040520250104002", "@dcloudio/uni-app-plus": "3.0.0-4040520250104002", "@dcloudio/uni-components": "3.0.0-4040520250104002", "@dcloudio/uni-h5": "3.0.0-4040520250104002", "@dcloudio/uni-mp-alipay": "3.0.0-4040520250104002", "@dcloudio/uni-mp-baidu": "3.0.0-4040520250104002", "@dcloudio/uni-mp-jd": "3.0.0-4040520250104002", "@dcloudio/uni-mp-kuaishou": "3.0.0-4040520250104002", "@dcloudio/uni-mp-lark": "3.0.0-4040520250104002", "@dcloudio/uni-mp-qq": "3.0.0-4040520250104002", "@dcloudio/uni-mp-toutiao": "3.0.0-4040520250104002", "@dcloudio/uni-mp-weixin": "3.0.0-4040520250104002", "@dcloudio/uni-mp-xhs": "3.0.0-4040520250104002", "@dcloudio/uni-quickapp-webview": "3.0.0-4040520250104002", "@dcloudio/uni-ui": "^1.5.2", "@repo/config": "workspace:*", "@repo/env-config": "workspace:*", "@repo/infrastructure": "workspace:*", "@repo/lbs": "workspace:*", "@uni-helper/axios-adapter": "^1.5.2", "@uni-helper/uni-network": "^0.18.1", "decode-uri-component": "^0.4.1", "fast-decode-uri-component": "^1.0.1", "fast-querystring": "^1.1.2", "fs-extra": "^11.2.0", "lodash": "^4.17.21", "lodash.merge": "^4.6.2", "miniprogram-api-typings": "^3.12.2", "miniprogram-ci": "^2.0.10", "open-im-sdk": "^3.5.2", "sass": "^1.71.1", "statuses": "^2.0.1", "uuid": "^9.0.1", "vue": "^3.5.12", "vue-i18n": "^9.14.1"}, "devDependencies": {"@dcloudio/types": "^3.4.14", "@dcloudio/uni-automator": "3.0.0-4040520250104002", "@dcloudio/uni-cli-shared": "3.0.0-4040520250104002", "@dcloudio/uni-stacktracey": "3.0.0-4040520250104002", "@dcloudio/vite-plugin-uni": "3.0.0-4040520250104002", "@types/fs-extra": "^11.0.4", "@types/node": "^20.11.20", "@types/postcss-import": "^14.0.3", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@uni-helper/manifest-json-schema": "^0.2.7", "@uni-helper/uni-app-types": "1.0.0-alpha.6", "@uni-helper/uni-ui-types": "^0.5.11", "@uni-helper/vite-plugin-uni-components": "^0.0.8", "@uni-helper/vite-plugin-uni-manifest": "^0.2.7", "@uni-helper/vite-plugin-uni-pages": "^0.2.14", "@uni-helper/vite-plugin-uni-tailwind": "^0.15.0", "@vue/runtime-core": "^3.5.12", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.18", "css-byebye": "^4.0.1", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.22.0", "postcss": "^8.4.36", "postcss-class-rename": "^1.0.1", "postcss-preset-env": "^10.0.7", "postcss-rem-to-responsive-pixel": "^6.0.1", "prettier": "3.2.5", "tailwindcss": "^3.4.1", "tailwindcss-miniprogram-preset": "^2.1.2", "typescript": "^5.3.3", "vite": "^5.2.8", "vue-tsc": "^1.0.24"}}