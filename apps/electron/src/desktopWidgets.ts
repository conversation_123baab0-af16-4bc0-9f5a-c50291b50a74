import { cloneDeep } from "lodash";

export type WidgetWindowOptions = {
  width: number;
  height: number;
  alwaysOnTop?: boolean;
  fullScreenable?: boolean;
  maximizable?: boolean;
  resizable?: boolean;
};

export const widgets: Record<string, WidgetWindowOptions> = {
  "/widget/resources": {
    width: 800,
    height: 600,
    alwaysOnTop: true,
  },
  "/widget/resourcePreview": {
    width: 1200,
    height: 960,
    alwaysOnTop: true,
    maximizable: true,
    resizable: true,
  },
  "/widget/timetable": {
    width: 800,
    height: 600,
    alwaysOnTop: true,
  },
  "/widget/classBehaviorRecord": {
    width: 400,
    height: 600,
    alwaysOnTop: true,
  },
};

export const getDesktopWidgetOptions = (path: string): WidgetWindowOptions => {
  return cloneDeep(widgets[path]);
};
