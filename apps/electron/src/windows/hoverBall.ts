import { BrowserWindow, screen } from "electron";
import { getClientUrl, getTeacherClientUrl, isDev, isPackaged } from "/@/utils";
import { join } from "node:path";
import { setWindow } from "/@/utils/windowManager";
import windowProps from "/@/windowProps";

export const createHoverBallWindow = async () => {
  const win = new BrowserWindow({
    ...windowProps.hoverBall,
    show: false,
    webPreferences: {
      // https://www.electronjs.org/docs/latest/api/webview-tag#warning
      webviewTag: false,
      preload: isPackaged
        ? join(__dirname, "./preload/index.cjs")
        : join(__dirname, "../../preload/dist/index.cjs"),
    },
  });

  const { left, top } = {
    left: screen.getPrimaryDisplay().workAreaSize.width - 250,
    top: screen.getPrimaryDisplay().workAreaSize.height - 250,
  };
  console.log("x:", left, "y:", top);
  win.setPosition(left, top); //设置悬浮球位置

  // win.setIgnoreMouseEvents(true, { forward: true });

  win.on("ready-to-show", () => {
    console.log("hover ball ready-to-show");
    win?.show();

    if (isDev) {
      win?.webContents.openDevTools({ mode: "detach" });
    }
  });

  console.log("isDev:", isDev, import.meta.env);
  await win.loadURL(getClientUrl("hover-ball"));

  setWindow("hover-ball", win);

  return win;
};
