import { join } from "node:path";
import { BrowserWindow } from "electron";
import { isDev, isPackaged, getTeacherClientUrl } from "/@/utils/";
import { createLoginWindow } from "/@/windows/login";
import { getWindow } from "/@/utils/windowManager";
import { createHoverBallWindow } from "/@/windows/hoverBall";

/**
 * 恢复现有的浏览器窗口或创建新的浏览器窗口
 */
export async function restoreOrCreateWindow() {
  let window = BrowserWindow.getAllWindows().find((w) => !w.isDestroyed());

  if (window === undefined) {
    window = await createLoginWindow();
    // window = await createHoverBallWindow();
    // clear all localStorage
    window.webContents.session.clearStorageData();
  }

  if (window!.isMinimized()) {
    window!.restore();
  }

  window!.focus();
}
