import { autoUpdater, UpdateDownloadedEvent } from "electron-updater";
import { isDev } from "/@/utils";
import { getWindow } from "/@/utils/windowManager";

let mainWindow = null;
export async function updateHandler(window, feedUrl) {
  mainWindow = window;
  //设置更新包的地址
  if (isDev) {
    autoUpdater.forceDevUpdateConfig = true;
    autoUpdater.autoInstallOnAppQuit = true;
    autoUpdater.autoDownload = true;
  } else {
    autoUpdater.autoDownload = true;
    autoUpdater.autoInstallOnAppQuit = true;
  }

  autoUpdater.setFeedURL(feedUrl);
  //监听升级失败事件
  autoUpdater.on("error", function (error, message) {
    sendUpdateMessage({
      cmd: "error",
      message: message,
      error: error,
    });
  });
  //监听开始检测更新事件
  autoUpdater.on("checking-for-update", () => {
    sendUpdateMessage({
      cmd: "checking-for-update",
    });
  });
  //监听发现可用更新事件
  autoUpdater.on("update-available", function (message) {
    sendUpdateMessage({
      cmd: "update-available",
      message: message,
    });
  });
  //监听没有可用更新事件
  autoUpdater.on("update-not-available", function (message) {
    sendUpdateMessage({
      cmd: "update-not-available",
      message: message,
    });
  });

  // 更新下载进度事件
  autoUpdater.on("download-progress", function (progressObj) {
    sendUpdateMessage({
      cmd: "download-progress",
      message: progressObj,
    });
  });

  //监听下载完成事件
  autoUpdater.on("update-downloaded", (event: UpdateDownloadedEvent) => {
    sendUpdateMessage({
      cmd: "update-downloaded",
      url: event.downloadedFile,
    });
    //退出并安装更新包
    autoUpdater.quitAndInstall();
  });

  return autoUpdater;

  // await autoUpdater.checkForUpdates();
}
//给渲染进程发送消息
export const sendUpdateMessage = (param?: any) => {
  const window = getWindow();
  console.log("sendUpdateMessage", {
    name: param.cmd,
    payload: [param],
  });
  window.webContents.send("IPC-bridge", {
    name: param.cmd,
    payload: [param],
  });
};
