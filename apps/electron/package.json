{"name": "electron-main", "version": "2.1.52", "private": false, "description": "牧云融教通", "author": {"name": "牧云融教", "url": "https://www.tejiaoedu.com"}, "main": "dist/main.cjs", "engines": {"node": ">=v16.13", "npm": ">=8.1"}, "scripts": {"fix": "electron-fix start", "preview": "dotenvx run -f ../../env/.env.dev -- tsx ../../scripts/watch.ts", "bundle": "npx rimraf ./dist && webpack --config ./webpack.config.js", "build:dev": "vite build --mode dev && pnpm bundle", "build:prod": "vite build && pnpm bundle", "pack:app": "npx tsx scripts/build.ts", "pack:dev": "cross-env ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/ && pnpm build:dev && dotenvx run -f ../../env/.env.dev -- pnpm pack:app", "pack:deploy": "cross-env ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/ && pnpm build:prod && dotenvx run -f ../../env/.env.deploy -- pnpm pack:app", "typecheck": "tsc --noEmit -p tsconfig.json", "postinstall": "if [ \"$CI\" != \"true\" ]; then cross-env ELECTRON_RUN_AS_NODE=1 electron ../../scripts/update-electron-vendors.js; else echo 'Skipping Electron postinstall in CI environment'; fi"}, "dependencies": {"@repo/config": "workspace:*"}, "devDependencies": {"@dotenvx/dotenvx": "^1.32.1", "@electron/notarize": "^2.5.0", "@repo/electron-preload": "workspace:*", "@types/node": "^22.10.5", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "electron": "33.3.1", "electron-builder": "25.1.8", "electron-fix": "^1.1.4", "electron-updater": "6.3.9", "rimraf": "^6.0.1", "terser-webpack-plugin": "^5.3.11", "tsx": "^4.19.2", "typescript": "5.7.3", "vite": "^6.0.7", "webpack": "^5.97.1", "webpack-cli": "^6.0.1"}}