const wxLogin = (options: any, size?: any): void => {
  let selfRedirect = 'default';
  if (options.self_redirect === true) {
    selfRedirect = 'true';
  } else if (options.self_redirect === false) {
    selfRedirect = 'false';
  }

  const iframe = document.createElement('iframe');
  let src = `https://open.weixin.qq.com/connect/qrconnect?appid=${options.appid}&scope=${options.scope}&redirect_uri=${options.redirect_uri}&state=${options.state}&login_type=jssdk&self_redirect=${selfRedirect}`;
  src += options.styletype ? `&styletype=${options.styletype}` : '';
  src += options.sizetype ? `&sizetype=${options.sizetype}` : '';
  src += options.bgcolor ? `&bgcolor=${options.bgcolor}` : '';
  src += options.rst ? `&rst=${options.rst}` : '';
  src += options.style ? `&style=${options.style}` : '';
  src += options.href ? `&href=${options.href}` : '';
  src += options.lang === 'en' ? '&lang=en' : '';
  src += options.stylelite === 1 ? '&stylelite=1' : '';
  src += options.fast_login === 0 ? '&fast_login=0' : '';

  iframe.src = src;
  iframe.frameBorder = '0';
  // @ts-ignore
  iframe.allowTransparency = 'true';
  iframe.scrolling = 'no';
  iframe.width = `${size?.width ? `${size?.width}px` : '400px'}`;
  iframe.height = `${size?.height ? `${size?.height}px` : '400px'}`;

  const container = document.getElementById(options.id);
  if (container) {
    container.innerHTML = '';
    container.appendChild(iframe);
  }
};

export { wxLogin };
