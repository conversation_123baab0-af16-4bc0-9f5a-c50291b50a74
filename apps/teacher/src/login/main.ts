import '@repo/infrastructure/iconfont';

import { createApp } from 'vue';
import { axiosInstance } from '@repo/infrastructure/request';
import { createPinia } from 'pinia';
import { Message } from '@arco-design/web-vue';

import App from './index.vue';

const app = createApp(App);

const pinia = createPinia();

app.config.globalProperties.axios = axiosInstance;
app.config.globalProperties.globalData = {};
app.use(pinia);
app.mount('#app-login');

// @ts-ignore
window.handleRequestError = (msg) => {
  Message.error(msg);
};

export default app;
