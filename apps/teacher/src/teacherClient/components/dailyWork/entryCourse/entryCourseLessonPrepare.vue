<script setup lang="ts">
  import { EditorOrDisplay } from '@repo/rich-editor';
  import { computed } from 'vue';
  import { Modal } from '@arco-design/web-vue';
  import { usePrompt } from '@repo/ui/components';

  const props = defineProps({
    modelValue: {
      type: Object,
      required: true,
    },
  });

  const emit = defineEmits(['update:modelValue']);

  const { prompt } = usePrompt();

  const contents = computed({
    get: () => props.modelValue || [],
    set: (value) => emit('update:modelValue', value),
  });

  const handleAddItem = () => {
    contents.value = [
      ...contents.value,
      {
        name: '',
        udf1: '',
      },
    ];
  };

  const handleUpdateContentName = async (index, content) => {
    const name = await prompt({
      title: '修改备课安排名称',
      placeholder: '请输入备课安排项目名称',
      raw: content.name,
    });

    contents.value[index].name = name;
  };

  const handleDeleteContentItem = (index, content) => {
    Modal.confirm({
      title: '请确认',
      content: `确定删除该备课安排项目吗(${content.name})？`,
      onOk: () => {
        contents.value.splice(index, 1);
      },
    });
  };
</script>

<template>
  <div class="content-wrapper">
    <a-space>
      <a-button size="mini" type="outline" @click="handleAddItem">
        <template #icon>
          <IconPlus />
        </template>
        添加备课安排
      </a-button>
    </a-space>
    <div v-for="(c, idx) in contents" :key="idx" class="mt-4">
      <a-space>
        <div class="content-title font-medium text-base"> {{ idx + 1 }}、{{ c.name }} </div>
        <a-space>
          <IconEdit class="cursor-pointer" @click="() => handleUpdateContentName(idx, c)" />
          <IconDelete class="cursor-pointer" @click="() => handleDeleteContentItem(idx, c)" />
        </a-space>
      </a-space>
      <editor-or-display
        v-model="c.udf1"
        class="mt-2"
        :editor-style="{ minHeight: '200px', maxHeight: '200px' }"
        @save="handleSave"
      />
    </div>
    <a-empty v-if="!contents.length" description="暂无备课安排，请先添加" />
  </div>
</template>

<style scoped lang="scss"></style>
