<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { WangEditor } from '@repo/rich-editor';
  import UploaderModal from '@repo/ui/components/upload/uploaderModal.vue';
  import { Message } from '@arco-design/web-vue';
  import { randomColor } from '@repo/ui/components/utils/randomColor';
  import AttachmentPreviewModal from '@repo/ui/components/data-display/attachmentPreviewModal.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
    },
    schema: {
      type: Object,
    },
    record: {
      type: Object,
    },
  });
  const mould = {
    application: null,
    applier: '',
    applierPhone: '',
    applyDate: '',
    attachments: [],
    d2dType: '',
    school: '',
    schoolContact: '',
    schoolPhone: '',
    student: null,
  };
  const formRef = ref(null);
  const formData = ref(props.record?.id ? { ...props.record } : JSON.parse(JSON.stringify(mould)));
  const emits = defineEmits(['update:modelValue']);
  const size = 'mini';
  const student = ref([]);

  const handleReset = () => {
    formData.value = JSON.parse(JSON.stringify(mould));
  };
  const handleCancel = () => {
    emits('update:modelValue', false);
  };
  const handlePreOk = async () => {
    const valid = await formRef.value.validate();

    if (valid === undefined || Object.keys(valid).length === 0) {
      await request('/resourceRoom/d2dService', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'post',
        data: formData.value,
      }).then((res) => {
        formData.value = res.data;
        Message.success('操作成功');
      });
    } else {
      Message.warning('请完善表格');
    }
  };
  const handleChange = (val) => {
    student.value.forEach((item) => {
      if (item.id === val) formData.value.student = item;
    });
  };

  const loadStudent = async () => {
    const { data } = await request(`/resourceRoom/student`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    student.value = data.items;
  };

  const updateStatusVisible = ref(false);
  const updateStatusData = ref({ remark: '', status: 'Waiting' });
  const updateStatus = async () => {
    if (!props.isEdit) await handlePreOk();
    if (formData.value.id) {
      await request(`resourceRoom/d2dService/updateStatus/${formData.value.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'PUT',
        data: updateStatusData.value,
      }).then(() => {
        Message.success('操作成功');
        updateStatusVisible.value = false;
        emits('update:modelValue', false);
      });
    } else {
      Message.warning('请先保存');
    }
  };
  const selected = ref(null);
  const previewVisible = ref(false);
  const handleUpdate = () => {};
  const rules = [{ required: true, message: '此项为必填' }];
  onMounted(async () => {
    await loadStudent();
    if (props.record?.id) selected.value = props.record?.student.id;
  });
</script>

<template>
  <a-modal fullscreen :visible="visible" title="上门服务申请" :closable="false">
    <template #footer>
      <div class="flex space-x-2 justify-end mb-2">
        <a-button size="small" @click="handleCancel">返回</a-button>
        <a-button v-if="!isEdit" type="primary" size="small" @click="handlePreOk">保存</a-button>
        <a-button
          v-if="!['Approved', 'Waiting'].includes(record?.status)"
          type="primary"
          status="success"
          size="small"
          @click="updateStatus"
        >
          <span v-if="!isEdit">保存并</span>提交审核
        </a-button>
        <a-button v-if="!isEdit" size="small" type="outline" @click="handleReset">重置</a-button>
      </div>
    </template>
    <div class="w-full flex justify-center">
      <a-form ref="formRef" class="px-4 w-2/3 border rounded" auto-label-width :disabled="isEdit" :model="formData">
        <a-divider orientation="left"><span class="font-bold">申请人</span></a-divider>
        <a-row>
          <a-col :span="18" class="flex space-x-4 justify-start">
            <a-form-item label="申请人" field="applier" :rules="rules">
              <a-input v-model="formData.applier" />
            </a-form-item>
            <a-form-item label="申请人电话" field="applierPhone" :rules="rules">
              <a-input v-model="formData.applierPhone" />
            </a-form-item>
            <a-form-item label="申请日期" field="applyDate" :rules="rules">
              <a-date-picker v-model="formData.applyDate" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-divider orientation="left"><span class="font-bold">基本信息</span></a-divider>
        <a-row>
          <a-col :span="20" class="flex space-x-4 justify-start">
            <a-form-item label="学生" field="student" :rules="rules">
              <a-select v-model="selected" placeholder="请选择学生" @change="handleChange">
                <a-option v-for="item in student" :key="item.id" :value="item.id">{{ item.name }}</a-option>
              </a-select>
            </a-form-item>
            <a-form-item label="系统证号">
              <span>{{ formData.student?.symbol }}</span>
            </a-form-item>
            <a-form-item label="学生性别">
              <span>{{ formData.student?.gender }}</span>
            </a-form-item>
            <a-form-item label="学生学校">
              <span>{{ formData.student?.fusionSchool.name }}</span>
            </a-form-item>
            <a-form-item label="上门服务类型" field="d2dType" :rules="rules">
              <a-input v-model="formData.d2dType" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-divider orientation="left"><span class="font-bold">申请书</span></a-divider>
        <a-row>
          <a-col :span="24" class="flex space-x-4 justify-start">
            <a-form-item label="申请书" field="application" :rules="rules">
              <wang-editor v-model="formData.application" :aria-disabled="isEdit" class="w-full mt-2" />
            </a-form-item>
          </a-col>
          <a-col>
            <a-form-item :disabled="false" label="附件">
              <uploader-modal
                v-model="formData.attachments"
                btn-text-empty="上传附件"
                btn-text="查看附件"
                @update:model-value="handleUpdate"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-divider v-if="false" orientation="left"><span class="font-bold">其它</span></a-divider>
        <a-row v-if="false">
          <a-col :span="18" class="flex space-x-4 justify-start">
            <a-form-item label="学校">
              <a-input v-model="formData.school" />
            </a-form-item>
            <a-form-item label="学校联系人">
              <a-input v-model="formData.schoolContact" />
            </a-form-item>
            <a-form-item label="学校电话">
              <a-input v-model="formData.schoolPhone" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <!--<a-steps v-if="record?.id" :current="record?.operationRecords.length + 1" direction="vertical" class="mt-2 ml-5">
      <a-step v-for="(item, index) in record.operationRecords" :key="index">
        <a-tag color="green">
          {{ item.operatorName }}
        </a-tag>
        <span class="ml-2 mr-2 text-gray-400">{{ item.operation }}</span>
        <span>{{ item.targetObject }}</span>
        <div class="mt-2 mb-2 rounded bg-gray-50 p-2">{{ item.remark }}</div>
        <div>{{ item.date }}</div>
      </a-step>
    </a-steps>-->
      <div class="w-1/3 h-[100vh] overflow-auto">
        <div class="w-full flex items-center justify-center font-bold text-lg">上门记录</div>
        <a-steps
          v-if="record?.id"
          :current="record?.operationRecords.length + 1"
          direction="vertical"
          class="mt-2 ml-5 w-full"
        >
          <a-step v-for="(item, index) in record.operationRecords" :key="index" status="finish">
            <attachment-preview-modal v-model="previewVisible" :files-list="item?.attachments" :open-index="9" />

            <template #icon>
              <icon-check-circle-fill />
            </template>
            <div class="bg-gray-100 relative rounded-lg shadow px-3 py-2 mb-4 w-full w-[300px]">
              <icon-attachment
                v-if="item?.attachments?.length"
                class="absolute right-2 top-2 cursor-pointer"
                @click="previewVisible = true"
              />
              <a-tag :color="randomColor(29)" class="mr-2" size="mini">
                {{ item.operatorName }}
              </a-tag>
              <span class="text-sm">{{ item.targetObject }}</span>
              <div class="mt-4 mb-4 rounded-lg bg-white px-2 py-4">{{ item.remark }}</div>
              <div class="flex justify-between">
                <div class="text-blue-500 text-xs">{{ item.date }}</div>
                <a-tag class="ml-2 mr-2" :color="randomColor(50)" size="mini">{{ item.operation }}</a-tag>
              </div>
            </div>
          </a-step>
        </a-steps>
      </div>
    </div>
  </a-modal>
  <a-modal
    :visible="updateStatusVisible"
    title="确认提交？"
    :on-before-ok="updateStatus"
    @cancel="
      () => {
        updateStatusVisible = false;
      }
    "
  >
    请输入备注：
    <a-input v-model="updateStatusData.remark" />
  </a-modal>
</template>

<style scoped lang="scss"></style>
