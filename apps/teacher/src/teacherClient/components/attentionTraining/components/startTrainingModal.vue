<script setup lang="ts">
  import { ref } from 'vue';
  import SchulteGridGame from '@/teacherClient/components/attentionTraining/trainingGames/schulteGridGame.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    trainingSetting: {
      type: Object,
    },
  });
  const isLoading = ref(false);
  const emit = defineEmits(['update:modelValue', 'startTraining', 'flush']);

  const resetData = () => {};

  const handlePreOk = async () => {
    isLoading.value = false;
    resetData();
    emit('update:modelValue', false);
  };
  const handleCancel = () => {
    isLoading.value = false;
    resetData();
    emit('update:modelValue', false);
  };
  const handelFinishTraining = (val) => {
    emit('flush');
    handleCancel();
  };
</script>

<template>
  <a-modal
    :visible="visible"
    fullscreen
    :closable="false"
    :on-before-ok="handlePreOk"
    style="height: 100%; width: 100%"
    hide-cancel
    @cancel="handleCancel"
  >
    <template #footer>
      <a-button class="m-0 p-0" @click="handleCancel">取消</a-button>
    </template>
    <schulte-grid-game
      v-if="visible"
      style="width: 100%; height: 90vh"
      :training-setting="trainingSetting"
      @finish-training="handelFinishTraining"
    />
  </a-modal>
</template>

<style scoped lang="scss"></style>
