<template>
  <a-modal v-model:visible="modalVisible">
    <div class="max-w-3xl mx-auto">
      <a-card>
        <div class="mb-6 rounded bg-gray-50 p-2">
          <p>这是一个 Stop-signal 任务实验。在实验中，您将看到屏幕中央出现的箭头。</p>
          <p>基本规则：</p>
          <ul class="list-disc list-inside space-y-1">
            <li>看到左箭头时，请按左方向键</li>
            <li>看到右箭头时，请按右方向键</li>
            <li>如果听到蜂鸣声，请尝试抑制您的按键反应</li>
          </ul>
        </div>

        <a-form ref="formRef" :model="formState" auto-label-width :style="{ width: '100%' }" @submit="handleSubmit">
          <a-form-item
            field="studentId"
            label="学生"
            :rules="[{ required: true, message: '请选择学生' }, { validator: validateStudent }]"
          >
            <a-select v-model="formState.studentId" :size="size" placeholder="请选择学生" :options="studentOptions" />
          </a-form-item>
          <a-form-item
            field="totalTrials"
            label="试次总数"
            :rules="[{ required: true, message: '请输入试次总数' }, { validator: validateTrials }]"
          >
            <a-input-number v-model="formState.totalTrials" :size="size" :min="10" :max="200" :step="10" />
          </a-form-item>
          <a-form-item field="soundEnabled" label="声音设置">
            <a-switch v-model="formState.soundEnabled" :size="size">
              <template #checked>开启</template>
              <template #unchecked>关闭</template>
            </a-switch>
          </a-form-item>

          <a-form-item
            field="minIntervalTime"
            label="最小间隔时间 (ms)"
            :rules="[{ required: true, message: '请输入最小间隔时间' }, { validator: validateMinInterval }]"
          >
            <a-input-number v-model="formState.minIntervalTime" :size="size" :min="300" :max="1000" :step="100" />
          </a-form-item>

          <a-form-item
            field="maxIntervalTime"
            label="最大间隔时间 (ms)"
            :rules="[{ required: true, message: '请输入最大间隔时间' }, { validator: validateMaxInterval }]"
          >
            <a-input-number v-model="formState.maxIntervalTime" :size="size" :min="500" :max="2000" :step="100" />
          </a-form-item>
          <a-form-item
            field="stopSignalProbability"
            label="停止信号出现概率 (%)"
            :rules="[{ required: true, message: '请输入出现概率' }, { validator: validateProbability }]"
          >
            <a-input-number v-model="formState.stopSignalProbability" :size="size" :min="20" :max="40" :step="5" />
          </a-form-item>
          <a-form-item
            field="stimulusDuration"
            label="Go刺激呈现时间 (ms)"
            :rules="[{ required: true, message: '请输入呈现时间' }, { validator: validateDuration }]"
          >
            <a-input-number v-model="formState.stimulusDuration" :size="size" :min="500" :max="2000" :step="50" />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" html-type="submit" :size="size" long> 开始实验</a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    config: {},
    modelValue: {},
    students: {
      type: Array,
    },
  });

  const emit = defineEmits(['start', 'update:modelValue']);

  const modalVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  });
  const studentOptions = computed(() => {
    return props.students.map((item) => ({ label: item.name, value: item.id }));
  });

  const size = 'mini';
  const formState = ref<any>({
    stimulusDuration: 1000,
    totalTrials: 100,
    stopSignalProbability: 25,
    soundEnabled: true,
    minIntervalTime: 500,
    maxIntervalTime: 1500,
  });

  const validateDuration = (value: number) => {
    if (value < 500 || value > 2000) {
      return '呈现时间必须在 500-2000ms 之间';
    }
    return true;
  };

  const validateTrials = (value: number) => {
    if (value < 10 || value > 200) {
      return '试次总数必须在 10-200 之间';
    }
    return true;
  };

  const validateStudent = (value: any) => {
    if (!value) {
      return '请选择学生';
    }
    return true;
  };

  const validateProbability = (value: number) => {
    if (value < 20 || value > 40) {
      return '概率必须在 20%-40% 之间';
    }
    return true;
  };

  const validateMinInterval = (value: number) => {
    if (value < 300 || value > 1000) {
      return '最小间隔时间必须在 300-1000ms 之间';
    }
    if (value >= formState.value.maxIntervalTime) {
      return '最小间隔时间必须小于最大间隔时间';
    }
    return true;
  };

  const validateMaxInterval = (value: number) => {
    if (value < 500 || value > 2000) {
      return '最大间隔时间必须在 500-2000ms 之间';
    }
    if (value <= formState.value.minIntervalTime) {
      return '最大间隔时间必须大于最小间隔时间';
    }
    return true;
  };

  const formRef = ref();
  const handleSubmit = async () => {
    const valid = await formRef.value.validate();
    if (valid === undefined || Object.keys(valid).length === 0) {
      emit('start', formState.value);
      modalVisible.value = false;
    }
  };
</script>
