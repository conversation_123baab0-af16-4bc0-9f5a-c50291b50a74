<template>
  <a-modal
    v-model:visible="visible"
    :fullscreen="true"
    :body-style="bodyStyle"
    :closable="false"
    @close="handleClearRecord"
  >
    <div class="w-full h-full flex items-center justify-center">
      <div class="space-y-6 w-3/5 mx-auto my-auto border p-8 rounded-lg bg-white shadow-2xl">
        <h2 class="text-2xl font-semibold mb-4">测试结果</h2>
        <div class="grid grid-cols-2 gap-4">
          <div class="bg-blue-100 p-4 rounded">
            <h3 class="font-semibold mb-2">正确率</h3>
            <p class="text-2xl">{{ correctRate }}%</p>
          </div>
          <div v-if="!isOralMode" class="bg-green-100 p-4 rounded">
            <h3 class="font-semibold mb-2">平均反应时间</h3>
            <p class="text-2xl">{{ formatTime(averageReactionTime) }}秒</p>
          </div>
        </div>
        <div v-if="!isOralMode">
          <h3 class="font-semibold mb-2">详细数据</h3>
          <div class="overflow-x-auto">
            <table class="min-w-full">
              <thead>
                <tr>
                  <th class="px-4 py-2">题号</th>
                  <th class="px-4 py-2">文字</th>
                  <th class="px-4 py-2">文字颜色</th>
                  <th class="px-4 py-2">选择颜色</th>
                  <th class="px-4 py-2">反应时间 (秒)</th>
                  <th class="px-4 py-2">是否正确</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in testData" :key="item.questionIndex">
                  <td class="border px-4 py-2">{{ item.questionIndex }}</td>
                  <td class="border px-4 py-2">{{ item.word }}</td>
                  <td class="border px-4 py-2">{{ item.textColor }}</td>
                  <td class="border px-4 py-2">{{ item.selectedColor }}</td>
                  <td class="border px-4 py-2">{{ formatTime(item.reactionTime) }}</td>
                  <td class="border px-4 py-2">
                    <span :class="item.correct ? 'text-green-600' : 'text-red-600'">
                      {{ item.correct ? '正确' : '错误' }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div v-if="false" class="flex justify-center space-x-4">
          <a-button v-if="!isOralMode" size="large" @click="$emit('show-history')">查看历史</a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { computed, PropType } from 'vue';

  interface TestData {
    questionIndex: number;
    word: string;
    textColor: string;
    selectedColor: string;
    reactionTime: number;
    correct: boolean;
  }

  const props = defineProps({
    testData: {
      type: Object as PropType<any>,
    },
    config: {
      type: Object as PropType<any>,
    },
    visible: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['show-config', 'show-history', 'update:visible', 'clearRecord']);
  const bodyStyle = {
    height: '100%',
    overflow: 'auto',
    padding: 0,
    backgroundColor: 'rgba(243,243,243,0.4)',
  };

  const visible = computed({
    get: () => props.visible,
    set: (val) => {
      emit('update:visible', val);
    },
  });
  const correctRate = computed(() => {
    if (props.testData.length === 0) return '0.00';
    const correctAnswers = props.testData.filter((item) => item.correct).length;
    return ((correctAnswers / props.testData.length) * 100).toFixed(2);
  });

  const averageReactionTime = computed(() => {
    if (!props.testData.length) return 0;
    const totalTime = props.testData.reduce((sum, item) => sum + item.reactionTime, 0);
    return totalTime / props.testData.length;
  });

  // 新增：格式化时间的函数，将毫秒转换为秒，并保留一位小数
  const formatTime = (ms: number) => {
    return (ms / 1000).toFixed(1);
  };

  // 添加口答模式判断
  const isOralMode = computed(() => props.config?.mode === 'oral');
  const handleClearRecord = () => {
    emit('clearRecord');
  };
</script>
