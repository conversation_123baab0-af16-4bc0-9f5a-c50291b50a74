export interface TestConfig {
  difficulty: 'easy' | 'medium' | 'hard';
  testLength: number;
  contrast: 'low' | 'medium' | 'high';
}

export interface TestDataItem {
  questionIndex: number;
  word: string;
  textColor: string;
  selectedColor: string;
  reactionTime: number;
  correct: boolean;
}

export interface TestRecord {
  id: string;
  date: string;
  config: TestConfig;
  data: TestDataItem[];
  stats: {
    correctRate: number;
    averageReactionTime: number;
  };
}

export interface Question {
  word: string;
  textColor: string;
}

export interface ChartData {
  index: string;
  correctRate: number;
  reactionTime: number;
  difficulty: string;
  date: string;
}
