// 使用 Vite 的资源导入
import correctSoundFile from '../audio/soundCorrect.mp3';
import failSoundFile from '../audio/soundFail.mp3';

// 创建音频实例
const correctSound = new Audio(correctSoundFile);
const failSound = new Audio(failSoundFile);

// 添加音频加载事件监听
correctSound.addEventListener('canplaythrough', () => {
  // console.log('正确音效加载完成');
});

failSound.addEventListener('canplaythrough', () => {
  // console.log('错误音效加载完成');
});

// 添加错误事件监听
correctSound.addEventListener('error', (e) => {
  // console.error('正确音效加载失败:', e);
  // console.error('错误代码:', correctSound.error?.code);
  // console.error('音频源:', correctSound.src);
});

failSound.addEventListener('error', (e) => {
  // console.error('错误音效加载失败:', e);
  // console.error('错误代码:', failSound.error?.code);
  // console.error('音频源:', failSound.src);
});

export const playCorrectSound = async () => {
  try {
    correctSound.currentTime = 0;
    await correctSound.play();
  } catch (err) {
    // console.error('播放正确音效失败:', err);
    /**/
  }
};

export const playFailSound = async () => {
  try {
    failSound.currentTime = 0;
    await failSound.play();
  } catch (err) {
    // console.error('播放错误音效失败:', err);
    /**/
  }
};
