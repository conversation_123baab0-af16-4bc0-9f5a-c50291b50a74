<script setup lang="ts">
  import { computed, PropType, provide, ref } from 'vue';
  import RecordDetail from '@repo/ui/components/record-detail/index.vue';
  import CustomizeComponent from '@repo/infrastructure/customizeComponent/customizeComponent.vue';
  import { VuePrintNext } from 'vue-print-next';
  import { useUserStore } from '@repo/infrastructure/store';
  // import PrintDemo2 from '@/teacherClient/components/placementReport/printDemo2.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    record: {
      type: Object as PropType<any>,
    },
    schema: {
      type: Object as PropType<any>,
    },
  });
  const emits = defineEmits(['update:visible', 'flush']);
  const record = ref(props.record);

  provide('VuePrintNext', VuePrintNext);

  const visible = computed({
    get: () => {
      return props.visible;
    },
    set: (val) => {
      emits('update:visible', val);
    },
  });
  const printRef = ref(null);
  const handlePrint = async () => {
    await printRef.value?.handlePrint();
  };
  const originalName = record.value.student?.name;
  const handleChange = (val) => {
    if (val) record.value.student.name = val;
    else record.value.student.name = originalName;
  };

  const userStore = useUserStore();
  provide('userStore', userStore);
</script>

<template>
  <a-modal v-if="visible" v-model:visible="visible" class="bg-gray-100" fullscreen ok-text="打印" @ok="handlePrint">
    <template #title>
      <div class="flex w-full justify-center">
        <div class="flex space-x-2">
          <a-input size="mini" placeholder="修改打印学生名" @change="handleChange" />
        </div>
      </div>
    </template>
    <!--    <printDemo ref="printRef" :record="record" />-->
    <customize-component
      v-if="visible"
      ref="printRef"
      v-model="record"
      :schema="schema"
      module="SpecialCommitteeTeacherClient"
      page="Print"
    >
      <template #default>
        <record-detail ref="recordDetailRef" :raw="record" :schema="schema" />
      </template>
    </customize-component>
  </a-modal>
</template>

<style scoped lang="scss"></style>
