<template>
  <a-modal v-model:visible="visible" fullscreen>
    <div class="report-page">
      <a-card class="mb-6">
        <template #title>
          <div style="display: flex; justify-content: space-between">
            <span class="text-lg font-medium">儿童行为量表（CBCL）评估报告</span>
            <a-button v-if="viewType === 'PC'" type="outline" class="ml-auto" size="mini" @click="printReport">
              <template #icon><icon-printer /></template>
              打印报告
            </a-button>
          </div>
        </template>

        <!-- 基本信息 -->
        <div class="basic-info mb-8 print-section">
          <h2 class="text-base font-medium border-l-4 border-primary pl-2 mb-4">基本信息</h2>
          <a-descriptions :column="{ xs: 1, sm: 2, md: 3 }" bordered>
            <a-descriptions-item label="儿童姓名">{{ assessmentData.student.name }}</a-descriptions-item>
            <a-descriptions-item label="性别">{{ assessmentData.student.gender }}</a-descriptions-item>
            <a-descriptions-item label="年龄">{{ assessmentData.student.age }} 岁</a-descriptions-item>
            <a-descriptions-item label="出生日期">{{ assessmentData.student?.birthday }}</a-descriptions-item>
            <a-descriptions-item label="年级">{{ assessmentData.general?.grade?.id }}</a-descriptions-item>
            <a-descriptions-item label="填表人">{{ getPerson(assessmentData.general.filledBy) }}</a-descriptions-item>
            <a-descriptions-item label="填表日期">{{ assessmentData.general.filledDate }}</a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 社会能力评估结果 -->
        <div class="social-ability-results mb-8 print-section">
          <h2 class="text-base font-medium border-l-4 border-primary pl-2 mb-4">社会能力评估结果</h2>

          <!-- 改进的信息提示框样式 -->
          <div class="mb-4 p-4 bg-blue-50 text-blue-900 border border-blue-200 rounded flex items-center">
            <icon-info-circle class="text-blue-500 mr-3 flex-shrink-0" style="font-size: 18px" />
            <span
              >社会能力的分数越高越好，绝大多数的分数处于2百分位和69百分位之间(即T分30-55)。低于2百分位者(即T分＜30)被认为可能异常。</span
            >
          </div>

          <!-- 三个因子分数 -->
          <div style="display: flex; justify-content: space-between; align-items: center; margin: 20px">
            <a-card :bordered="false" class="bg-gray-50">
              <div class="text-center">
                <h3 class="font-medium mb-2">活动能力</h3>
                <a-progress
                  type="circle"
                  :percent="formatPercentage(socialAbilityScores.activityScore) || 0"
                  :stroke-color="getScoreColor(socialAbilityScores.activityScore)"
                />
                <p class="mt-2">{{ getScoreDescription(socialAbilityScores.activityScore) }}</p>
                <p class="text-xs text-gray-500 mt-1">分值: {{ socialAbilityScores.activityScore.score.toFixed(1) }}</p>
                <p class="text-xs text-gray-500">百分位: {{ socialAbilityScores.activityScore.percentile }}</p>
              </div>
            </a-card>

            <a-card :bordered="false" class="bg-gray-50">
              <div class="text-center">
                <h3 class="font-medium mb-2">社交情况</h3>
                <a-progress
                  type="circle"
                  :percent="formatPercentage(socialAbilityScores.socialScore) || 0"
                  :stroke-color="getScoreColor(socialAbilityScores.socialScore)"
                />
                <p class="mt-2">{{ getScoreDescription(socialAbilityScores.socialScore) }}</p>
                <p class="text-xs text-gray-500 mt-1">分值: {{ socialAbilityScores.socialScore.score.toFixed(1) }}</p>
                <p class="text-xs text-gray-500">百分位: {{ socialAbilityScores?.socialScore?.percentile }}</p>
              </div>
            </a-card>

            <a-card :bordered="false" class="bg-gray-50">
              <div class="text-center">
                <h3 class="font-medium mb-2">学校情况</h3>
                <a-progress
                  type="circle"
                  :percent="formatPercentage(socialAbilityScores.schoolScore) || 0"
                  :stroke-color="getScoreColor(socialAbilityScores.schoolScore)"
                />
                <p class="mt-2">{{ getScoreDescription(socialAbilityScores.schoolScore) }}</p>
                <p class="text-xs text-gray-500 mt-1">分值: {{ socialAbilityScores.schoolScore.score.toFixed(1) }}</p>
                <p class="text-xs text-gray-500">百分位: {{ socialAbilityScores?.schoolScore?.percentile }}</p>
              </div>
            </a-card>
          </div>

          <!-- 社会能力评估图表 -->
          <div ref="chartContainer" class="social-ability-chart mb-4">
            <!-- 图表将在mounted钩子中渲染 -->
          </div>

          <!-- 新增：社会能力填报详情 -->
          <a-collapse :default-active-key="[]" class="print-section mb-8">
            <a-collapse-item key="social-details" header="社会能力填报详情">
              <a-descriptions :column="{ xs: 1, sm: 2, md: 2 }" layout="vertical" bordered class="social-details-desc">
                <!-- 体育运动 -->
                <a-descriptions-item label="Ⅰ. 体育运动项目">
                  {{ formatList(assessmentData.socialAbility.sportsActivities.list) }}
                </a-descriptions-item>
                <a-descriptions-item label="体育运动 - 花费时间">
                  {{ formatTimeSpent(assessmentData.socialAbility.sportsActivities.timeSpent) }}
                </a-descriptions-item>
                <a-descriptions-item label="体育运动 - 运动水平">
                  {{ formatLevel(assessmentData.socialAbility.sportsActivities.level) }}
                </a-descriptions-item>

                <!-- 爱好 -->
                <a-descriptions-item label="Ⅱ. 爱好（体育以外）">
                  {{ formatList(assessmentData.socialAbility.hobbies.list) }}
                </a-descriptions-item>
                <a-descriptions-item label="爱好 - 花费时间">
                  {{ formatTimeSpent(assessmentData.socialAbility.hobbies.timeSpent) }}
                </a-descriptions-item>
                <a-descriptions-item label="爱好 - 水平如何">
                  {{ formatLevel(assessmentData.socialAbility.hobbies.level) }}
                </a-descriptions-item>

                <!-- 组织 -->
                <a-descriptions-item label="Ⅲ. 参加的组织/俱乐部">
                  {{ formatList(assessmentData.socialAbility.organizations.list) }}
                </a-descriptions-item>
                <a-descriptions-item label="组织 - 活跃程度">
                  {{ formatActiveness(assessmentData.socialAbility.organizations.activeness) }}
                </a-descriptions-item>

                <!-- 工作/家务 -->
                <a-descriptions-item label="Ⅳ. 工作或家务">
                  {{ formatList(assessmentData.socialAbility.jobs.list) }}
                </a-descriptions-item>
                <a-descriptions-item label="工作/家务 - 质量如何">
                  {{ formatQuality(assessmentData.socialAbility.jobs.quality) }}
                </a-descriptions-item>

                <!-- 朋友 -->
                <a-descriptions-item label="Ⅴ. 要好的朋友数量">
                  {{ formatFriendCount(assessmentData.socialAbility.friends.count) }}
                </a-descriptions-item>
                <a-descriptions-item label="朋友 - 每周相处次数">
                  {{ formatFriendFrequency(assessmentData.socialAbility.friends.frequency) }}
                </a-descriptions-item>

                <!-- 相处情况 -->
                <a-descriptions-item label="Ⅵ. 与兄弟姊妹相处">
                  {{ formatRelationshipLevel(assessmentData.socialAbility.relationship.siblings) }}
                </a-descriptions-item>
                <a-descriptions-item label="与其他人相处">
                  {{ formatRelationshipLevel(assessmentData.socialAbility.relationship.otherChildren) }}
                </a-descriptions-item>
                <a-descriptions-item label="对父母的行为">
                  {{ formatRelationshipLevel(assessmentData.socialAbility.relationship.parents) }}
                </a-descriptions-item>
                <a-descriptions-item label="自己工作和游戏">
                  {{ formatRelationshipLevel(assessmentData.socialAbility.relationship.selfPlay) }}
                </a-descriptions-item>

                <!-- 学习情况 -->
                <template v-if="assessmentData.socialAbility.academics.inSchool">
                  <a-descriptions-item label="Ⅶ. 学习情况 - 阅读课">
                    {{ formatAcademicScore(assessmentData.socialAbility.academics.subjects.reading) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="学习情况 - 写作课">
                    {{ formatAcademicScore(assessmentData.socialAbility.academics.subjects.writing) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="学习情况 - 算术课">
                    {{ formatAcademicScore(assessmentData.socialAbility.academics.subjects.math) }}
                  </a-descriptions-item>
                  <a-descriptions-item label="学习情况 - 拼音课">
                    {{ formatAcademicScore(assessmentData.socialAbility.academics.subjects.phonetics) }}
                  </a-descriptions-item>

                  <!-- 其他课程 -->
                  <template
                    v-if="
                      assessmentData.socialAbility.academics.subjects.others &&
                      assessmentData.socialAbility.academics.subjects.others.length > 0
                    "
                  >
                    <a-descriptions-item
                      v-for="(subject, index) in assessmentData.socialAbility.academics.subjects.others"
                      :key="`other-subj-${index}`"
                      :label="`学习情况 - 其他课程 (${subject.name})`"
                    >
                      {{ formatAcademicScore(subject.score) }}
                    </a-descriptions-item>
                  </template>

                  <a-descriptions-item label="是否在特殊班级">
                    {{ formatBoolean(assessmentData.socialAbility.academics.specialClass) }}
                    <template v-if="assessmentData.socialAbility.academics.specialClass">
                      ({{ assessmentData.socialAbility.academics.specialClassName || '未说明' }})
                    </template>
                  </a-descriptions-item>
                  <a-descriptions-item label="是否留过级">
                    {{ formatBoolean(assessmentData.socialAbility.academics.retained) }}
                    <template v-if="assessmentData.socialAbility.academics.retained">
                      (年级: {{ assessmentData.socialAbility.academics.retainedGrade || '未说明' }}, 理由:
                      {{ assessmentData.socialAbility.academics.retainedReason || '未说明' }})
                    </template>
                  </a-descriptions-item>
                  <a-descriptions-item label="在校有无其他问题">
                    {{ formatBoolean(assessmentData.socialAbility.academics.problems) }}
                    <template v-if="assessmentData.socialAbility.academics.problems">
                      <div class="text-xs text-gray-500 mt-1">
                        <p>描述: {{ assessmentData.socialAbility.academics.problemsContent || '未说明' }}</p>
                        <p>开始时间: {{ assessmentData.socialAbility.academics.problemsStart || '未说明' }}</p>
                        <p
                          >是否解决: {{ formatBoolean(assessmentData.socialAbility.academics.problemsSolved) }}
                          <span v-if="assessmentData.socialAbility.academics.problemsSolved">
                            (解决时间: {{ assessmentData.socialAbility.academics.problemsSolvedTime || '未说明' }})
                          </span>
                        </p>
                      </div>
                    </template>
                  </a-descriptions-item>
                </template>
                <template v-else>
                  <a-descriptions-item label="Ⅶ. 学习情况">未上学</a-descriptions-item>
                </template>
              </a-descriptions>
            </a-collapse-item>
          </a-collapse>
        </div>

        <!-- 行为问题评估结果 -->
        <div class="behavior-problems-results print-section">
          <h2 class="text-base font-medium border-l-4 border-primary pl-2 mb-4">行为问题评估结果</h2>

          <!-- 改进的信息提示框样式 -->
          <div class="mb-4 p-4 bg-blue-50 text-blue-900 border border-blue-200 rounded flex items-center">
            <icon-info-circle class="text-blue-500 mr-3 flex-shrink-0" style="font-size: 18px" />
            <span>行为问题评估结果中，分数超过98百分位时即认为可能异常，应予复查。分数低于69百分位时仍属正常。</span>
          </div>

          <!-- 行为问题因子分表格 -->
          <div class="overflow-x-auto">
            <a-table
              :columns="behaviorFactorColumns"
              :data="behaviorFactorData"
              :pagination="false"
              :scroll="{ x: 900 }"
              bordered
            >
              <template #factor="{ record }">
                <span>{{ record.factor }}</span>
              </template>
              <template #score="{ record }">
                <span>{{ record.score.toFixed(1) }}</span>
              </template>
              <template #percentile="{ record }">
                <span>{{ record.percentile }}</span>
              </template>
              <template #status="{ record }">
                <a-tag :color="record.status === '正常' ? 'green' : 'red'">
                  {{ record.status }}
                </a-tag>
              </template>
              <template #items="{ record }">
                <span class="cursor-help">查看详情</span>
                <a-popover v-if="false" :content="formatItemsList(record.items)"> </a-popover>
              </template>
            </a-table>
          </div>

          <!-- 高分项目列表 -->
          <div v-if="highScoreItems.length > 0" class="high-score-items mt-6">
            <h3 class="text-base font-medium mb-2">关注点（得分较高的行为项目）</h3>
            <a-list :bordered="true">
              <a-list-item v-for="item in highScoreItems" :key="item.id">
                <div class="w-full">
                  <div class="flex justify-between items-center">
                    <span>{{ item.id }}. {{ item.content }}</span>
                    <a-tag color="orange">得分: {{ item.score }}</a-tag>
                  </div>
                  <div v-if="item.hasNote && item.noteContent" class="mt-1 text-sm text-gray-600 pl-4">
                    <span class="font-medium">说明：</span>{{ item.noteContent }}
                  </div>
                </div>
              </a-list-item>
            </a-list>
          </div>

          <!-- 标记关注的项目列表 -->
          <div v-if="markedItems.length > 0" class="marked-items mt-6">
            <h3 class="text-base font-medium mb-2">特别关注项（家长/老师标记的项目）</h3>
            <a-list :bordered="true">
              <a-list-item v-for="item in markedItems" :key="item.id">
                <div class="w-full">
                  <div class="flex justify-between items-center">
                    <span>{{ item.id }}. {{ item.content }}</span>
                    <a-tag color="red">特别关注</a-tag>
                  </div>
                  <div v-if="item.hasNote && item.noteContent" class="mt-1 text-sm text-gray-600 pl-4">
                    <span class="font-medium">说明：</span>{{ item.noteContent }}
                  </div>
                </div>
              </a-list-item>
            </a-list>
          </div>
        </div>

        <!-- 结论与建议 -->
        <div class="conclusion mt-8 print-section">
          <h2 class="text-base font-medium border-l-4 border-primary pl-2 mb-4">结论与建议</h2>
          <a-card class="bg-gray-50">
            <p class="mb-4"> {{ assessmentData.general.childName }} 的综合评估结果显示： </p>
            <div v-if="hasAbnormalFactors">
              <p class="mb-2">行为问题评估中，以下因子分超过了正常范围：</p>
              <ul class="list-disc list-inside mb-4">
                <li v-for="factor in abnormalFactors" :key="factor.factor">
                  {{ factor.factor }}（得分：{{ factor.score.toFixed(1) }}，属于异常范围）
                </li>
              </ul>
              <p class="mb-2">建议：</p>
              <ul class="list-disc list-inside">
                <li>建议家长和老师密切关注孩子的情绪和行为变化。</li>
                <li>可考虑咨询专业的儿童心理健康专家进行深入评估和干预。</li>
                <li>建立积极的家庭支持系统，为孩子创造稳定、温暖的家庭环境。</li>
              </ul>
            </div>
            <div v-else-if="highScoreItems.length > 0 || markedItems.length > 0">
              <p class="mb-2">行为问题评估中，各项因子分均在正常范围内，但有以下需要关注的行为项目：</p>
              <ul class="list-disc list-inside mb-4">
                <template v-if="highScoreItems.length > 0">
                  <li v-for="item in highScoreItems.slice(0, 3)" :key="`high-${item.id}`">
                    {{ item.content }}
                    <span v-if="item.hasNote && item.noteContent" class="text-gray-600"
                      >（{{ item.noteContent }}）</span
                    >
                  </li>
                </template>
                <template v-if="markedItems.length > 0">
                  <li v-for="item in markedItems.slice(0, 3)" :key="`marked-${item.id}`" class="font-medium">
                    {{ item.content }} <span class="text-red-500">（特别关注）</span>
                    <span v-if="item.hasNote && item.noteContent" class="text-gray-600 font-normal">
                      （{{ item.noteContent }}）
                    </span>
                  </li>
                </template>
              </ul>
              <p class="mb-2">建议：</p>
              <ul class="list-disc list-inside">
                <li>建议家长关注孩子在上述方面的表现，给予适当的引导与支持。</li>
                <li>保持良好的家庭教育环境，关注孩子的心理健康发展。</li>
              </ul>
            </div>
            <div v-else>
              <p>各项评估指标均在正常范围内，建议继续保持良好的家庭教育环境，关注孩子的身心健康发展。</p>
            </div>
          </a-card>
        </div>

        <!-- 评估说明 -->
        <div class="assessment-note mt-8 print-section">
          <a-collapse>
            <a-collapse-item key="1" header="评估说明">
              <p class="text-sm text-gray-600">
                儿童行为量表（CBCL）是由美国心理学家Achenbach开发的一种常用于评估儿童行为和情绪问题的标准化工具，适用于4-16岁儿童。
                该量表由三个部分组成：一般项目、社会能力和行为问题。评分结果仅供参考，不能作为诊断依据。
                如有疑问，请咨询专业心理健康工作者获取更准确的评估和建议。
              </p>
            </a-collapse-item>
          </a-collapse>
        </div>
      </a-card>
    </div>
  </a-modal>
</template>

<script setup>
  import { ref, computed, onMounted, nextTick } from 'vue';
  import { IconPrinter, IconInfoCircle } from '@arco-design/web-vue/es/icon';
  import * as echarts from 'echarts/core';
  import { BarChart } from 'echarts/charts';
  import { GridComponent, TooltipComponent, TitleComponent, LegendComponent } from 'echarts/components';
  import { CanvasRenderer } from 'echarts/renderers';
  import cbclScoreCalculator from './utils/cbclScoreCalculator';

  // 注册必须的组件
  echarts.use([BarChart, GridComponent, TooltipComponent, TitleComponent, LegendComponent, CanvasRenderer]);

  const props = defineProps({
    assessmentData: {
      type: Object,
      required: true,
    },
    visible: {
      type: Boolean,
    },
    viewType: {
      type: String,
      default: 'PC',
    },
  });

  const emits = defineEmits(['update:visible']);
  const visible = computed({
    get: () => {
      return props.visible;
    },
    set: (val) => {
      emits('update:visible', val);
    },
  });

  // 图表容器引用
  const chartContainer = ref(null);
  let chart = null;

  // 计算社会能力得分
  const socialAbilityScores = computed(() => {
    const { gender, age } = props.assessmentData.general;
    const socialData = props.assessmentData.socialAbility;

    return cbclScoreCalculator.calculateSocialAbilityScores(socialData, gender, age);
  });

  // 计算行为问题因子分
  const behaviorFactorData = computed(() => {
    const { gender, age } = props.assessmentData.general;
    const behaviorData = props.assessmentData.behaviorProblems;

    return cbclScoreCalculator.calculateBehaviorFactorScores(behaviorData, gender, age);
  });

  const getPerson = (key) => {
    switch (key) {
      case 'father':
        return '父亲';
      case 'mother':
        return '母亲';
      default:
        return '其他人';
    }
  };
  // 行为问题表格列定义
  const behaviorFactorColumns = [
    { title: '因子', dataIndex: 'factor', slotName: 'factor', width: 120, fixed: 'left' },
    { title: '得分', dataIndex: 'score', slotName: 'score', width: 80 },
    { title: '百分位', dataIndex: 'percentile', slotName: 'percentile', width: 80 },
    { title: '状态', dataIndex: 'status', slotName: 'status', width: 80 },
    { title: '包含项目', dataIndex: 'items', slotName: 'items' },
  ];
  // 获取行为项目描述
  function getItemsWithDescription() {
    return [
      { id: 1, content: '行为幼稚与其年龄不符', note: '' },
      { id: 2, content: '过敏性症状', note: '填具体表现' },
      { id: 3, content: '喜欢争论', note: '' },
      { id: 4, content: '哮喘病', note: '' },
      { id: 5, content: '举动向异性', note: '' },
      { id: 6, content: '随地大便', note: '' },
      { id: 7, content: '喜欢吹牛或自夸', note: '' },
      { id: 8, content: '精神不能集中，注意力不能持久', note: '' },
      { id: 9, content: '老是想某些事情，不能摆脱，强迫观念', note: '说明内容' },
      { id: 10, content: '坐立不安活动过多', note: '' },
      { id: 11, content: '喜欢缠着大人或过分依赖', note: '' },
      { id: 12, content: '常说感到寂寞', note: '' },
      { id: 13, content: '胡里胡涂，如在云里雾中', note: '' },
      { id: 14, content: '常常哭叫', note: '' },
      { id: 15, content: '虐待动物', note: '' },
      { id: 16, content: '虐待、欺侮别人或吝啬', note: '' },
      { id: 17, content: '好做白日梦或呆想', note: '' },
      { id: 18, content: '故意伤害自己或企图自杀', note: '' },
      { id: 19, content: '需要别人经常注意自己', note: '' },
      { id: 20, content: '破坏自己的东西', note: '' },
      { id: 21, content: '破坏家里或其他儿童的东西', note: '' },
      { id: 22, content: '在家不听话', note: '' },
      { id: 23, content: '在学校不听话', note: '' },
      { id: 24, content: '不肯好好吃饭', note: '' },
      { id: 25, content: '不与其他儿童相处', note: '' },
      { id: 26, content: '有不良行为后不感到内疚', note: '' },
      { id: 27, content: '易嫉妒', note: '' },
      { id: 28, content: '好吃不能作为食物的东西', note: '说明内容' },
      { id: 29, content: '除怕上学外，还害怕某些动物、处境或地方', note: '说明内容' },
      { id: 30, content: '怕上学', note: '' },
      { id: 31, content: '怕自己想坏念头或做坏事', note: '' },
      { id: 32, content: '觉得自己必须十全十美', note: '' },
      { id: 33, content: '觉得或抱怨没有人喜欢自己', note: '' },
      { id: 34, content: '觉得别人存心捉弄自己', note: '' },
      { id: 35, content: '觉得自己无用或有自卑感', note: '' },
      { id: 36, content: '身体经常弄伤，容易出事故', note: '' },
      { id: 37, content: '经常打架', note: '' },
      { id: 38, content: '常被人戏弄', note: '' },
      { id: 39, content: '爱和出麻烦的儿童在一起', note: '' },
      { id: 40, content: '听到某些实际上没有的声音', note: '说明内容' },
      { id: 41, content: '冲动或行为粗鲁', note: '' },
      { id: 42, content: '喜欢孤独', note: '' },
      { id: 43, content: '撒谎或欺骗', note: '' },
      { id: 44, content: '咬指甲', note: '' },
      { id: 45, content: '神经过敏，容易激动或紧张', note: '' },
      { id: 46, content: '动作紧张或带有抽动性', note: '说明内容' },
      { id: 47, content: '做恶梦', note: '' },
      { id: 48, content: '不被其他儿童喜欢', note: '' },
      { id: 49, content: '便秘', note: '' },
      { id: 50, content: '过度恐惧或担心', note: '' },
      { id: 51, content: '感到头昏', note: '' },
      { id: 52, content: '过份内疚', note: '' },
      { id: 53, content: '吃得过多', note: '' },
      { id: 54, content: '过份疲劳', note: '' },
      { id: 55, content: '身体过重', note: '' },
      {
        id: 56,
        content: '找不到原因的躯体症状',
        note: 'a.疼痛\nb.头痛\nc.恶心想吐\nd.眼睛有问题\ne.发疹或其他皮肤病\nf.腹部疼痛或绞痛\ng.呕吐\nh.其他',
      },
      { id: 57, content: '对别人身体进行攻击', note: '' },
      { id: 58, content: '挖鼻孔、皮肤或身体其他部分', note: '说明内容' },
      { id: 59, content: '公开玩弄自己的生殖器', note: '' },
      { id: 60, content: '过多地玩弄自己的生殖器', note: '' },
      { id: 61, content: '功课差', note: '' },
      { id: 62, content: '动作不灵活', note: '' },
      { id: 63, content: '喜欢和年龄较大的儿童在一起', note: '' },
      { id: 64, content: '喜欢和年龄较小的儿童在一起', note: '' },
      { id: 65, content: '不肯说话', note: '' },
      { id: 66, content: '不断重复某些动作，强迫行为', note: '说明内容' },
      { id: 67, content: '离家出走', note: '' },
      { id: 68, content: '经常尖叫', note: '' },
      { id: 69, content: '守口如瓶，有事不说出来', note: '' },
      { id: 70, content: '看到某些实际上没有的东西', note: '说明内容' },
      { id: 71, content: '感到不自然或容易发窘', note: '' },
      { id: 72, content: '玩火', note: '包括玩火柴或打火机' },
      { id: 73, content: '性方面的问题', note: '说明内容' },
      { id: 74, content: '夸耀自己或胡闹', note: '' },
      { id: 75, content: '害羞或胆小', note: '' },
      { id: 76, content: '比大多数孩子睡得少', note: '' },
      { id: 77, content: '比大多数孩子睡得多', note: '说明多多少，不包括赖床' },
      { id: 78, content: '玩弄粪便', note: '' },
      { id: 79, content: '言语问题', note: '例如口吃不清' },
      { id: 80, content: '茫然凝视', note: '' },
      { id: 81, content: '在家偷东西', note: '' },
      { id: 82, content: '在外偷东西', note: '' },
      { id: 83, content: '收藏自己不需要的东西', note: '不包括集邮等爱好' },
      { id: 84, content: '怪异行为', note: '不包括其他条已提及者' },
      { id: 85, content: '怪异想法', note: '不包括其他条已提及者' },
      { id: 86, content: '固执、绷着脸或容易激怒', note: '' },
      { id: 87, content: '情绪突然变化', note: '' },
      { id: 88, content: '常常生气', note: '' },
      { id: 89, content: '多疑', note: '' },
      { id: 90, content: '咒骂或讲粗话', note: '' },
      { id: 91, content: '声言要自杀', note: '' },
      { id: 92, content: '说梦话或有梦游', note: '说明内容' },
      { id: 93, content: '话太多', note: '' },
      { id: 94, content: '常戏弄他人', note: '' },
      { id: 95, content: '乱发脾气或脾气暴躁', note: '' },
      { id: 96, content: '对性的问题想得太多', note: '' },
      { id: 97, content: '威胁他人', note: '' },
      { id: 98, content: '吮吸大拇指', note: '' },
      { id: 99, content: '过分要求整齐清洁', note: '' },
      { id: 100, content: '睡眠不好', note: '说明内容' },
      { id: 101, content: '逃学', note: '' },
      { id: 102, content: '不够活跃，动作迟钝或精力不足', note: '' },
      { id: 103, content: '闷闷不乐，悲伤或抑郁', note: '' },
      { id: 104, content: '说话声音特别大', note: '' },
      { id: 105, content: '喝酒或使用成瘾药', note: '说明内容' },
      { id: 106, content: '损坏公物', note: '' },
      { id: 107, content: '白天遗尿', note: '' },
      { id: 108, content: '夜间遗尿', note: '' },
      { id: 109, content: '爱哭诉', note: '' },
      { id: 110, content: '希望成为异性', note: '' },
      { id: 111, content: '孤独、不合群', note: '' },
      { id: 112, content: '忧虑重重', note: '' },
      { id: 113, content: '其他问题', note: '' },
    ];
  }
  // 异常因子列表
  const abnormalFactors = computed(() => {
    return behaviorFactorData.value.filter((factor) => factor.status === '异常');
  });

  const hasAbnormalFactors = computed(() => {
    return abnormalFactors.value.length > 0;
  });

  // 高分项目列表
  const highScoreItems = computed(() => {
    const behaviorItems = getItemsWithDescription();
    const highScoreIndices = props.assessmentData.behaviorProblems
      .map((score, index) => ({ score, index }))
      .filter((item) => item.score === 2)
      .map((item) => item.index);

    return highScoreIndices.map((index) => ({
      id: index + 1,
      content: behaviorItems[index]?.content || `项目 ${index + 1}`,
      score: props.assessmentData.behaviorProblems[index],
      noteContent: props.assessmentData.behaviorNotes?.[index] || '',
      hasNote: behaviorItems[index]?.note !== '',
    }));
  });

  // 标记关注的项目列表
  const markedItems = computed(() => {
    if (!props.assessmentData.markedItems || !Array.isArray(props.assessmentData.markedItems)) {
      return [];
    }

    const behaviorItems = getItemsWithDescription();
    return props.assessmentData.markedItems.map((index) => ({
      id: index + 1,
      content: behaviorItems[index]?.content || `项目 ${index + 1}`,
      score: props.assessmentData.behaviorProblems[index],
      noteContent: props.assessmentData.behaviorNotes?.[index] || '',
      hasNote: behaviorItems[index]?.note !== '',
    }));
  });

  // 格式化填表人
  const formatFilledBy = (filledBy) => {
    if (filledBy === 'father') return '父亲';
    if (filledBy === 'mother') return '母亲';
    if (filledBy === 'other') return '其他人';
    return '';
  };

  // 格式化百分比
  const formatPercentage = (scoreObj) => {
    // 将分数转换为百分比展示
    if (!scoreObj || typeof scoreObj !== 'object') return 0;

    const percentileStr = scoreObj.percentile;
    if (percentileStr.startsWith('<')) {
      return 0;
    }
    if (percentileStr.startsWith('>')) {
      return 100;
    }
    return parseInt(percentileStr, 10);
  };

  // 获取分数颜色
  const getScoreColor = (scoreObj) => {
    if (scoreObj.status === '可能异常') return '#f53f3f'; // 红色-异常
    if (parseInt(scoreObj.percentile, 10) < 30) return '#ff7d00'; // 橙色-边缘
    return '#00b42a'; // 绿色-正常
  };

  // 获取分数描述
  const getScoreDescription = (scoreObj) => {
    if (scoreObj.status === '可能异常') return '需关注';
    if (parseInt(scoreObj.percentile, 10) < 30) return '尚可';
    return '良好';
  };

  // 格式化项目列表
  const formatItemsList = (items) => {
    return items.join(', ');
  };

  // 初始化图表
  const initChart = () => {
    if (chartContainer.value) {
      chart = echarts.init(chartContainer.value);

      const option = {
        title: {
          text: '社会能力评估因子分',
          left: 'center',
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c}',
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: ['活动能力', '社交情况', '学校情况'],
        },
        yAxis: {
          type: 'value',
          name: '分数',
          min: 0,
          max: 10,
        },
        series: [
          {
            name: '因子分',
            type: 'bar',
            data: [
              {
                value: parseFloat(socialAbilityScores.value.activityScore.score.toFixed(2)),
                itemStyle: { color: getScoreColor(socialAbilityScores.value.activityScore) },
              },
              {
                value: parseFloat(socialAbilityScores.value.socialScore.score.toFixed(2)),
                itemStyle: { color: getScoreColor(socialAbilityScores.value.socialScore) },
              },
              {
                value: parseFloat(socialAbilityScores.value.schoolScore.score.toFixed(2)),
                itemStyle: { color: getScoreColor(socialAbilityScores.value.schoolScore) },
              },
            ],
            label: {
              show: true,
              position: 'top',
              formatter: '{c}',
            },
          },
        ],
      };

      chart.setOption(option);

      // 响应窗口大小变化
      window.addEventListener('resize', () => {
        chart.resize();
      });
    }
  };

  // 打印报告
  const printReport = () => {
    window.print();
  };
  const formatNullableValue = (value, mapping) => {
    return mapping[value] !== undefined ? mapping[value] : '未填写/不知道';
  };
  const formatList = (list) => {
    if (!list || list.length === 0 || (list.length === 1 && !list[0])) return '无';
    return list.filter((item) => item).join('、');
  };
  const formatTimeSpent = (value) => formatNullableValue(value, { 0: '较少', 1: '一般', 2: '较多' });
  const formatLevel = (value) => formatNullableValue(value, { 0: '较低', 1: '一般', 2: '较高' });
  const formatActiveness = (value) => formatNullableValue(value, { 0: '较差', 1: '一般', 2: '较高' });
  const formatQuality = (value) => formatNullableValue(value, { 0: '较差', 1: '一般', 2: '较好' });
  const formatFriendCount = (value) => formatNullableValue(value, { 0: '无', 1: '1个', 2: '2-3个', 3: '4个及以上' });
  const formatFriendFrequency = (value) => formatNullableValue(value, { 0: '不到一次', 1: '1-2次', 2: '3次及以上' });
  const formatRelationshipLevel = (value) => formatNullableValue(value, { 0: '较差', 1: '差不多', 2: '较好' });
  const formatAcademicScore = (value) => formatNullableValue(value, { 0: '不及格', 1: '中下', 2: '中等', 3: '中上' });
  const formatBoolean = (value) => (value === true ? '是' : '否');
  onMounted(async () => {
    await nextTick();
    // 设置图表容器高度
    if (chartContainer.value) {
      chartContainer.value.style.height = '300px';
    }
    initChart();
  });
</script>

<style scoped>
  .report-page {
    max-width: 1200px;
    margin: 0 auto;
  }

  .border-primary {
    border-color: var(--primary-color);
  }

  @media print {
    .report-page {
      max-width: 100%;
    }

    .print-section {
      page-break-inside: avoid;
      margin-bottom: 20px;
    }
  }

  @media (max-width: 768px) {
    .report-page {
      padding: 0 0.5rem;
    }
  }
</style>
