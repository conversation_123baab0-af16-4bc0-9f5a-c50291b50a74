<template>
  <a-modal
    :visible="visible"
    width="auto"
    :closable="false"
    :on-before-ok="handlePreOk"
    fullscreen
    @cancel="handleCancel"
  >
    <div class="report-container">
      <div class="report-header">
        <div class="header-actions"></div>
        <h1>学习障碍筛查报告</h1>
        <div class="basic-info">
          <span>姓名: {{ record.student.name }}</span>
          <span>年龄: {{ record.age }}岁</span>
          <span>年级: {{ record.student.gradeClass?.grade.gradeName }}</span>
          <span>评测日期: {{ record.createdDate.split(' ')[0] }}</span>
        </div>
      </div>

      <div class="report-content">
        <!-- 总体评估结果 -->
        <div class="result-section">
          <div class="score-card main-score">
            <div class="score-value">
              <span class="number">{{ record.totalScore }}</span>
              <span class="max-score">/ {{ scoreResults.total.maxScore }}</span>
            </div>
            <div class="score-label">
              <h3>总体评估</h3>
              <div class="status" :class="{ warning: record.totalScore < 60 }">
                {{ record.totalScore >= 60 ? '正常' : 'LD可疑' }}
              </div>
            </div>
          </div>
          <!--65,20,40-->
          <!-- 分类评估结果 -->
          <div class="sub-scores">
            <div class="score-card">
              <div class="score-value">
                <span class="number">{{ record.speechAbility }}</span>
                <span class="max-score">/ {{ scoreResults.verbal.maxScore }}</span>
              </div>
              <div class="score-label">
                <h3>言语能力</h3>
                <div class="status" :class="{ warning: record.speechAbility < 20 }">
                  {{ record.speechAbility >= 20 ? '正常' : '需关注' }}
                </div>
              </div>
            </div>

            <div class="score-card">
              <div class="score-value">
                <span class="number">{{ record.nonverbalAbility }}</span>
                <span class="max-score">/ {{ scoreResults.nonVerbal.maxScore }}</span>
              </div>
              <div class="score-label">
                <h3>非言语能力</h3>
                <div class="status" :class="{ warning: record.nonverbalAbility < 45 }">
                  {{ record.nonverbalAbility >= 45 ? '正常' : '需关注' }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 详评估维度 -->
        <div class="dimensions">
          <!-- 言语能力评估 -->
          <div class="ability-section">
            <h3 class="section-title"
              >言语能力评估 ({{ scoreResults.verbal.score }} / {{ scoreResults.verbal.maxScore }})</h3
            >
            <div class="table-wrapper">
              <table class="assessment-table">
                <thead>
                  <tr>
                    <th width="20%">评估领域</th>
                    <th width="60%">评估项目</th>
                    <th width="20%">得分</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- A区：听觉理解和记忆 -->
                  <tr class="domain-row">
                    <td rowspan="4">
                      A. 听觉理解和记忆
                      <div class="domain-score"> 总分: {{ result['A'] }}</div>
                    </td>
                    <td>词汇理解能力</td>
                    <td
                      ><span :class="[getScoreLevel(details?.A['词汇理解能力'])]">
                        {{ details.A['词汇理解能力'] }}分
                      </span></td
                    >
                  </tr>
                  <tr>
                    <td>服从指令能力</td>
                    <td
                      ><span :class="[getScoreLevel(details?.A['服从指令的能力'])]">
                        {{ details.A['服从指令的能力'] }}分
                      </span></td
                    >
                  </tr>
                  <tr>
                    <td>在班级内交谈能力</td>
                    <td
                      ><span :class="[getScoreLevel(details.A['在班级内交谈能力'])]">
                        {{ details.A['在班级内交谈能力'] }}分
                      </span></td
                    >
                  </tr>
                  <tr>
                    <td>记忆力</td>
                    <td
                      ><span :class="[getScoreLevel(details.A['记忆力'])]"> {{ details.A['记忆力'] }}分 </span></td
                    >
                  </tr>

                  <!-- B区：语言表达 -->
                  <tr class="domain-row">
                    <td rowspan="5">
                      B. 语言表达
                      <div class="domain-score"> 总分: {{ result['B'] }}</div>
                    </td>
                    <td>词汇</td>
                    <td
                      ><span :class="[getScoreLevel(details.B['词汇'])]"> {{ details.B['词汇'] }}分 </span></td
                    >
                  </tr>
                  <tr>
                    <td>语法</td>
                    <td
                      ><span :class="[getScoreLevel(details.B['语法'])]"> {{ details.B['语法'] }}分 </span></td
                    >
                  </tr>
                  <tr>
                    <td>口语</td>
                    <td
                      ><span :class="[getScoreLevel(details.B['口语'])]"> {{ details.B['口语'] }}分 </span></td
                    >
                  </tr>
                  <tr>
                    <td>表述经验的能力</td>
                    <td
                      ><span :class="[getScoreLevel(details.B['表述经验的能力'])]">
                        {{ details.B['表述经验的能力'] }}分
                      </span></td
                    >
                  </tr>
                  <tr>
                    <td>表达思维的能力</td>
                    <td
                      ><span :class="[getScoreLevel(details.B['表达思维的能力'])]">
                        {{ details.B['表达思维的能力'] }}分
                      </span></td
                    >
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 非言语能力评估 -->
          <div class="ability-section">
            <h3 class="section-title"
              >非言语能力评估 ({{ scoreResults.nonVerbal.score }} / {{ scoreResults.nonVerbal.maxScore }})</h3
            >
            <div class="table-wrapper">
              <table class="assessment-table">
                <thead>
                  <tr>
                    <th width="20%">评估领域</th>
                    <th width="60%">评估项目</th>
                    <th width="20%">得分</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- C区：时间和方位判断 -->
                  <tr class="domain-row">
                    <td rowspan="3">
                      C. 时间和方位判断
                      <div class="domain-score"> 总分: {{ result['C'] }}</div>
                    </td>
                    <td>时间判断能力</td>
                    <td
                      ><span :class="[getScoreLevel(details.C['时间判断能力'])]">
                        {{ details.C['时间判断能力'] }}分
                      </span></td
                    >
                  </tr>
                  <tr>
                    <td>场地方向感</td>
                    <td
                      ><span :class="[getScoreLevel(details.C['场地方向感'])]">
                        {{ details.C['场地方向感'] }}分
                      </span></td
                    >
                  </tr>
                  <tr>
                    <td>位置感</td>
                    <td
                      ><span :class="[getScoreLevel(details.C['位置感'])]"> {{ details.C['位置感'] }}分 </span></td
                    >
                  </tr>

                  <!-- D区：运动能力 -->
                  <tr class="domain-row">
                    <td rowspan="3">
                      D. 运动能力
                      <div class="domain-score"> 总分: {{ result['D'] }}</div>
                    </td>
                    <td>一般运动（如走、跑、跳、爬、攀登等）</td>
                    <td
                      ><span :class="[getScoreLevel(details.D['一般运动（如走、跑、跳、爬、攀登等）'])]">
                        {{ details.D['一般运动（如走、跑、跳、爬、攀登等）'] }}分
                      </span></td
                    >
                  </tr>
                  <tr>
                    <td>平衡能力</td>
                    <td
                      ><span :class="[getScoreLevel(details.D['平衡能力'])]"> {{ details.D['平衡能力'] }}分 </span></td
                    >
                  </tr>
                  <tr>
                    <td>手灵活性（如使用筷子、手工、系纽扣等）</td>
                    <td
                      ><span
                        :class="[getScoreLevel(details.D['手灵活性（如使用筷子、手工、系纽扣、写字、绘画、持球等）'])]"
                      >
                        {{ details.D['手灵活性（如使用筷子、手工、系纽扣、写字、绘画、持球等）'] }}分
                      </span></td
                    >
                  </tr>

                  <!-- E区：社会行为 -->
                  <tr class="domain-row">
                    <td rowspan="8">
                      E. 社会行为
                      <div class="domain-score"> 总分: {{ result['E'] }}</div>
                    </td>
                    <td>班级内的协调性</td>
                    <td
                      ><span :class="[getScoreLevel(details.E['班级内的协调性'])]">
                        {{ details.E['班级内的协调性'] }}分
                      </span></td
                    >
                  </tr>
                  <tr>
                    <td>注意力</td>
                    <td
                      ><span :class="[getScoreLevel(details.E['注意力'])]"> {{ details.E['注意力'] }}分 </span></td
                    >
                  </tr>
                  <tr>
                    <td>调整顺序能力</td>
                    <td
                      ><span :class="[getScoreLevel(details.E['调整顺序能力'])]">
                        {{ details.E['调整顺序能力'] }}分
                      </span></td
                    >
                  </tr>
                  <tr>
                    <td>对新情况的适应性</td>
                    <td
                      ><span
                        :class="[getScoreLevel(details.E['对新情况的适应性（如生日聚会、联欢、旅游、课程变更等）'])]"
                      >
                        {{ details.E['对新情况的适应性（如生日聚会、联欢、旅游、课程变更等）'] }}分
                      </span></td
                    >
                  </tr>
                  <tr>
                    <td>社会交往</td>
                    <td
                      ><span :class="[getScoreLevel(details.E['社会交往'])]"> {{ details.E['社会交往'] }}分 </span></td
                    >
                  </tr>
                  <tr>
                    <td>责任感</td>
                    <td
                      ><span :class="[getScoreLevel(details.E['责任感'])]"> {{ details.E['责任感'] }}分 </span></td
                    >
                  </tr>
                  <tr>
                    <td>完成任务能力</td>
                    <td
                      ><span
                        :class="[getScoreLevel(details.E['完成任务能力（如作业、值日、规则、大家商定的事情等）'])]"
                      >
                        {{ details.E['完成任务能力（如作业、值日、规则、大家商定的事情等）'] }}分
                      </span></td
                    >
                  </tr>
                  <tr>
                    <td>关心他人</td>
                    <td
                      ><span :class="[getScoreLevel(details.E['关心他人'])]"> {{ details.E['关心他人'] }}分 </span></td
                    >
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- 评估说明 -->
        <div class="report-footer">
          <h3>评估说明</h3>
          <p>
            儿童学习障碍(learning disabilities,LD)是指不存在精神发育迟滞和视听觉障碍，
            亦无环境和教育剥夺以及原发性情绪障碍， 而出现阅读、书写、计算、拼写等特殊学习技术获得困难的状态，
            是教育和医学界特别关注的一类心理行为发育障碍。 该评估采用PRS量表进行筛查,总分低于65分表示存在学习障碍倾向。
            其中言语能力(听觉理解、语言表达)得分低于20分,
            或非言语能力(时空判断、运动、社会行为)得分低于40分均需要进一步专业评估。
          </p>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch } from 'vue';

  const props = defineProps({
    visible: {
      type: Boolean,
    },
    record: {
      type: Object,
    },
  });
  const emits = defineEmits(['update:modelValue']);
  const handleCancel = () => {
    emits('update:modelValue', false);
  };
  const handlePreOk = async () => {
    emits('update:modelValue', false);
  };

  const details = ref<any>({});
  const result = ref<any>({});
  onMounted(() => {
    if (props.record.detailedScores) {
      props.record.areaScores.forEach((scoresScore) => {
        result.value[scoresScore.area] = scoresScore.score;
      });
      props.record.detailedScores.forEach((item) => {
        const { area, ability, score } = item;
        if (!details.value[area]) {
          details.value[area] = {};
        }
        details.value[area][ability] = score;
      });
      console.log(result, '结果');
    }
  });
  // 初始化分数结果
  const scoreResults = ref({
    total: {
      score: 0,
      threshold: 65,
      maxScore: 120,
      passed: false,
      difference: 0,
    },
    verbal: {
      score: 0,
      threshold: 20,
      maxScore: 45,
      passed: false,
      difference: 0,
    },
    nonVerbal: {
      score: 0,
      threshold: 40,
      maxScore: 75,
      passed: false,
      difference: 0,
    },
  });

  // 添加新的辅助函数
  const getScoreLevel = (score: number) => {
    console.log(score, '分数');
    if (score >= 4) return 'excellent';
    if (score >= 3) return 'good';
    if (score >= 2) return 'fair';
    return 'poor';
  };
</script>

<style scoped>
  .report-container {
    max-width: 1000px;
    margin: 10px auto;
    padding: 10px;
    background: #fff;
    border-radius: 0;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  }

  .report-header {
    position: relative;
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
  }

  .header-actions {
    position: absolute;
    left: 0;
    top: 0;
  }

  .back-button {
    display: flex;
    align-items: center;
    color: #1a73e8;
    font-size: 14px;
  }

  .back-button:hover {
    color: #1557b0;
  }

  .report-header h1 {
    font-size: 24px;
    color: #333;
    margin-bottom: 16px;
  }

  .basic-info {
    display: flex;
    justify-content: center;
    gap: 24px;
    color: #666;
  }

  .result-section {
    margin-bottom: 40px;
  }

  .score-card {
    background: #f8f9fa;
    padding: 24px;
    border-radius: 0;
    text-align: center;
  }

  .main-score {
    margin-bottom: 24px;
  }

  .score-value {
    margin-bottom: 12px;
  }

  .number {
    font-size: 36px;
    font-weight: bold;
    color: #1a73e8;
  }

  .max-score {
    font-size: 18px;
    color: #666;
  }

  .score-label h3 {
    margin: 0;
    color: #333;
  }

  .status {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 0;
    font-size: 14px;
    color: #4caf50;
    background: #e8f5e9;
    margin-top: 8px;
  }

  .status.warning {
    color: #f44336;
    background: #ffebee;
  }

  .sub-scores {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .dimensions {
    margin-top: 40px;
    display: flex;
    flex-direction: column;
    gap: 40px;
  }

  .ability-section {
    background: #fff;
    border-radius: 0;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .section-title {
    margin: 0;
    padding: 20px 24px;
    font-size: 20px;
    color: #1d2129;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
  }

  .ability-content {
    padding: 24px;
  }

  .dimension-group {
    margin-bottom: 32px;
  }

  .dimension-group:last-child {
    margin-bottom: 0;
  }

  .dimension-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;
  }

  .dimension-header h4 {
    margin: 0;
    font-size: 16px;
    color: #1d2129;
  }

  .dimension-score {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .dimension-score .score {
    font-size: 18px;
    font-weight: 600;
    color: #1a73e8;
  }

  .dimension-score .label {
    padding: 2px 8px;
    border-radius: 8px;
    font-size: 12px;
    color: #4caf50;
    background: #e8f5e9;
  }

  .dimension-score .label.warning {
    color: #f44336;
    background: #ffebee;
  }

  .dimension-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
  }

  .score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 0;
  }

  .item-name {
    color: #4e5969;
    font-size: 14px;
  }

  .item-score {
    padding: 2px 12px;
    border-radius: 0;
    font-weight: 500;
    font-size: 14px;
  }

  .excellent {
    color: #1976d2;
    background: #e3f2fd;
    display: inline-block;
    padding: 2px 8px;
    border-radius: 0;
    font-weight: 500;
  }

  .good {
    color: #2e7d32;
    background: #e8f5e9;
    display: inline-block;
    padding: 2px 8px;
    border-radius: 0;
    font-weight: 500;
  }

  .fair {
    color: #f57c00;
    background: #fff3e0;
    display: inline-block;
    padding: 2px 8px;
    border-radius: 0;
    font-weight: 500;
  }

  .poor {
    color: #d32f2f;
    background: #ffebee;
    display: inline-block;
    padding: 2px 8px;
    border-radius: 0;
    font-weight: 500;
  }

  .report-footer {
    margin-top: 40px;
    padding: 24px;
    background: #f8f9fa;
    border-radius: 0;
    border: 1px solid #eee;
  }

  .report-footer h3 {
    color: #1d2129;
    margin-bottom: 16px;
    font-size: 18px;
  }

  .report-footer p {
    color: #4e5969;
    line-height: 1.8;
    margin: 0;
  }

  @media (max-width: 768px) {
    .basic-info {
      flex-direction: column;
      gap: 8px;
    }

    .sub-scores {
      grid-template-columns: 1fr;
    }

    .dimension-items {
      grid-template-columns: 1fr;
    }

    .item-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .item-name {
      min-width: unset;
    }

    .score-bar-wrapper {
      width: 100%;
    }
  }

  .table-wrapper {
    margin: 20px 0;
    overflow-x: auto;
  }

  .assessment-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    font-size: 14px;
  }

  .assessment-table th,
  .assessment-table td {
    padding: 12px 16px;
    border: 1px solid #eee;
    text-align: left;
    background: white;
  }

  .assessment-table th {
    background: #f8f9fa;
    font-weight: 500;
    color: #1d2129;
  }

  .domain-row td {
    background: white;
  }

  .domain-score {
    margin-top: 8px;
    font-size: 12px;
    color: #86909c;
  }

  .domain-score span {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 0;
    margin-left: 4px;
    background: #e8f5e9;
    color: #4caf50;
  }

  .domain-score span.tag-warning {
    background: #ffebee;
    color: #f44336;
  }

  .score-tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 0;
    font-weight: 500;
    background: #f5f6f7;
    color: #1d2129;
  }

  .score-tag.excellent,
  .score-tag.good,
  .score-tag.fair,
  .score-tag.poor {
    background: #f5f6f7;
    color: #1d2129;
  }

  .section-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
    margin: 0;
    font-size: 18px;
    color: #1d2129;
  }

  @media (max-width: 768px) {
    .assessment-table {
      font-size: 13px;
    }

    .assessment-table th,
    .assessment-table td {
      padding: 8px 12px;
    }
  }
</style>
