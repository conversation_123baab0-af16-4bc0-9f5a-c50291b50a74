<template>
  <a-modal :visible="modelValue" :on-before-ok="handlePreOk" fullscreen ok-text="保存">
    <div class="books">
      <a-card class="books-card">
        <template #title>
          <a-space>
            <icon-arrow-left class="back-icon" />
            字本管理 - {{ record?.student.name }}
          </a-space>
        </template>

        <div class="books-container">
          <!-- 生字本 -->
          <div class="book-section">
            <div class="book-header">
              <h2 class="book-title">
                <icon-experiment class="book-icon new" />
                生字本({{ thesaurus?.unfamiliar.length }})
              </h2>
              <a-button type="primary" @click="showAddNewCharsModal">
                <template #icon><icon-plus /></template>
                添加生字
              </a-button>
            </div>
            <div class="char-list">
              <template v-if="record">
                <div v-for="char in thesaurus?.unfamiliar" :key="char" class="char-item">
                  <span class="char">{{ char }}</span>
                  <a-button-group class="actions">
                    <a-popover content="移入熟字库">
                      <a-button type="text" size="mini" status="success" @click="moveToFamiliar(char)">
                        <icon-redo />
                      </a-button>
                    </a-popover>
                    <a-popover content="移出生字库">
                      <a-button type="text" size="mini" status="danger" @click="removeFromNew(char)">
                        <icon-delete />
                      </a-button>
                    </a-popover>
                  </a-button-group>
                </div>
              </template>
              <a-empty v-else description="暂无生字" />
            </div>
          </div>

          <!-- 熟字本 -->
          <div class="book-section">
            <div class="book-header">
              <h2 class="book-title">
                <icon-check-circle class="book-icon familiar" />
                熟字本({{ thesaurus?.familiar.length }})
              </h2>
              <a-button type="primary" status="success" @click="showAddFamiliarCharsModal">
                <template #icon><icon-plus /></template>
                添加熟字
              </a-button>
            </div>
            <div class="char-list">
              <template v-if="record">
                <div v-for="char in thesaurus?.familiar" :key="char" class="char-item">
                  <span class="char">{{ char }}</span>
                  <a-button-group class="actions">
                    <a-popover content="移入生字库">
                      <a-button type="text" size="mini" status="warning" @click="moveToNew(char)">
                        <icon-undo />
                      </a-button>
                    </a-popover>
                    <a-popover content="移出熟字库">
                      <a-button type="text" size="mini" status="danger" @click="removeFromFamiliar(char)">
                        <icon-delete />
                      </a-button>
                    </a-popover>
                  </a-button-group>
                </div>
              </template>
              <a-empty v-else description="暂无熟字" />
            </div>
          </div>
        </div>
      </a-card>

      <!-- 添加生字弹窗 -->
      <a-modal
        v-model:visible="addNewCharsVisible"
        title="添加生字"
        @ok="handleAddNewChars"
        @cancel="addNewCharsVisible = false"
      >
        <a-textarea
          v-model="newCharsInput"
          :auto-size="{ minRows: 4, maxRows: 8 }"
          placeholder="请输入要添加的汉字，系统会自动分字并去重"
        />
        <div v-if="processedNewChars.length > 0" class="preview">
          <div class="preview-title">处理结果 (共 {{ processedNewChars.length }} 个字)：</div>
          <div class="preview-chars">
            <span v-for="char in processedNewChars" :key="char" class="preview-char">
              {{ char }}
            </span>
          </div>
        </div>
      </a-modal>

      <!-- 添加熟字弹窗 -->
      <a-modal
        v-model:visible="addFamiliarCharsVisible"
        title="添加熟字"
        @ok="handleAddFamiliarChars"
        @cancel="addFamiliarCharsVisible = false"
      >
        <a-textarea
          v-model="familiarCharsInput"
          :auto-size="{ minRows: 4, maxRows: 8 }"
          placeholder="请输入要添加的汉字，系统会自动分字并去重"
        />
        <div v-if="processedFamiliarChars.length > 0" class="preview">
          <div class="preview-title">处理结果 (共 {{ processedFamiliarChars.length }} 个字)：</div>
          <div class="preview-chars">
            <span v-for="char in processedFamiliarChars" :key="char" class="preview-char">
              {{ char }}
            </span>
          </div>
        </div>
      </a-modal>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import {
    IconArrowLeft,
    IconExperiment,
    IconCheckCircle,
    IconPlus,
    IconRedo,
    IconDelete,
    IconUndo,
  } from '@arco-design/web-vue/es/icon';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    record: {
      type: Object,
    },
    student: {
      type: Object,
    },
    course: {
      type: Object,
    },
  });

  const addNewCharsVisible = ref(false);
  const addFamiliarCharsVisible = ref(false);
  const newCharsInput = ref('');
  const familiarCharsInput = ref('');
  const thesaurus = ref();

  // 移动到熟字本
  const moveToFamiliar = async (char: string) => {
    if (!thesaurus.value?.familiar.includes(char)) {
      thesaurus.value.familiar.push(char);
      thesaurus.value.unfamiliar = thesaurus.value.unfamiliar.filter((c) => c !== char);
    }
  };

  // 移动到生字本
  const moveToNew = async (char: string) => {
    if (!thesaurus.value?.unfamiliar.includes(char)) {
      thesaurus.value.unfamiliar.push(char);
      thesaurus.value.familiar = thesaurus.value.familiar.filter((c) => c !== char);
    }
  };

  // 从生字本移除
  const removeFromNew = async (char: string) => {
    thesaurus.value.unfamiliar = thesaurus.value.unfamiliar.filter((c) => c !== char);
  };

  // 从熟字本移除
  const removeFromFamiliar = async (char: string) => {
    thesaurus.value.familiar = thesaurus.value.familiar.filter((c) => c !== char);
  };

  const loadStudentThesaurus = async () => {
    try {
      const { data: res } = await request('/resourceRoom/thesaurus/findAll', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          studentId: props.record?.student.id,
        },
      });
      const [firstItem] = res.items;
      thesaurus.value = firstItem;
    } finally {
      /**/
    }
  };
  // 处理输入的文字
  const processText = (text: string) => {
    return Array.from(new Set(text.match(/[\u4e00-\u9fa5]/g) || []));
  };

  // 处理后的生字
  const processedNewChars = computed(() => processText(newCharsInput.value));

  // 处理后的熟字
  const processedFamiliarChars = computed(() => processText(familiarCharsInput.value));

  // 显示添加生字弹窗
  const showAddNewCharsModal = () => {
    addNewCharsVisible.value = true;
    newCharsInput.value = '';
  };

  // 显示添加熟字弹窗
  const showAddFamiliarCharsModal = () => {
    addFamiliarCharsVisible.value = true;
    familiarCharsInput.value = '';
  };

  // 添加生字
  const handleAddNewChars = async () => {
    const unfamiliarWords = thesaurus.value.unfamiliar;
    const words = new Set([...(processedNewChars.value || []), ...unfamiliarWords]);
    thesaurus.value.unfamiliar = [...words];
  };

  // 添加熟字
  const handleAddFamiliarChars = async () => {
    const familiarWords = thesaurus.value.familiar;
    const words = new Set([...(processedFamiliarChars.value || []), ...familiarWords]);
    thesaurus.value.familiar = [...words];
  };
  const handlePreOk = async () => {
    try {
      const { data: res } = await request(`/resourceRoom/thesaurus/${thesaurus.value.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'put',
        data: thesaurus.value,
      });
      Message.success('操作成功');
    } finally {
      /**/
    }
  };

  onMounted(async () => {
    if (props.record?.student.id) await loadStudentThesaurus();
  });
</script>

<style scoped>
  .books {
    padding: 20px;
    background-color: #f2f3f5;
    min-height: 100vh;
  }

  .books-card {
    background-color: #fff;
    border-radius: 4px;
  }

  .back-icon {
    font-size: 20px;
    cursor: pointer;
    color: #86909c;
  }

  .back-icon:hover {
    color: #165dff;
  }

  .books-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }

  .book-section {
    background-color: #f7f8fa;
    border-radius: 4px;
    padding: 16px;
  }

  .book-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .book-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    margin: 0;
  }

  .book-icon {
    font-size: 20px;
  }

  .book-icon.new {
    color: #ff7d00;
  }

  .book-icon.familiar {
    color: #00b42a;
  }

  .char-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 12px;
  }

  .char-item {
    position: relative;
    background-color: #fff;
    border: 1px solid #e5e6eb;
    border-radius: 4px;
    padding: 12px;
    text-align: center;
  }

  .char {
    font-size: 24px;
    line-height: 1;
  }

  .actions {
    position: absolute;
    top: 4px;
    right: 4px;
    display: none;
  }

  .char-item:hover .actions {
    display: flex;
  }

  .preview {
    margin-top: 16px;
    padding: 16px;
    background-color: #f7f8fa;
    border-radius: 4px;
  }

  .preview-title {
    margin-bottom: 8px;
    color: #86909c;
  }

  .preview-chars {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .preview-char {
    display: inline-block;
    padding: 4px 8px;
    background-color: #fff;
    border: 1px solid #e5e6eb;
    border-radius: 4px;
  }
</style>
