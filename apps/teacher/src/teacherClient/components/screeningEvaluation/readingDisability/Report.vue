<template>
  <a-modal
    :visible="visible"
    fullscreen
    :closable="false"
    cancel-text="返回"
    :on-before-ok="handlePreOk"
    @cancel="handleCancel"
  >
    <div class="report-container">
      <div class="report-content">
        <h1>
          <span v-if="props.record?.id">{{ props.record?.student.name + ' - ' }}</span>
          儿童汉语阅读障碍量表(DCCC)评估报告</h1
        >

        <div class="total-score">
          <h2>总体评估</h2>
          <div class="score-box"> 总分：{{ props.result?.total || props.record?.total }}</div>
        </div>

        <div class="dimensions-detail">
          <h2>各维度详情</h2>
          <div v-for="dimension in dimensionData" :key="dimension.key" class="dimension-section">
            <div class="dimension-header">
              <div class="dimension-title">
                <h3>{{ dimension.name }}</h3>
                <p class="dimension-desc">{{ dimension.description }}</p>
              </div>
            </div>

            <div class="dimension-stats">
              <div class="stat-box">
                <div class="stat-value">{{ dimension.score }}</div>
                <div class="stat-label">维度得分</div>
              </div>
              <div class="stat-box">
                <div class="stat-value">{{ dimension.itemCount }}</div>
                <div class="stat-label">包含题目</div>
              </div>
            </div>

            <div class="items-list">
              <div v-for="item in dimension.items" :key="item.id" class="item-row">
                <div class="item-main">
                  <span class="item-id">{{ item.id }}.</span>
                  <span class="item-content">{{ item.content }}</span>
                </div>
                <div class="item-score-box">
                  <span class="score-label">得分</span>
                  <span class="score-value">{{ item.score }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="actions">
          <a-button type="primary" @click="handlePrint">打印报告</a-button>
          <a-button v-if="!props.record?.id" @click="handleBack">返回评估</a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import questions from './questions';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    result: {
      type: Object,
    },
    isViewReport: {
      type: Boolean,
      default: false,
    },
    record: {
      type: Object,
    },
  });
  const emits = defineEmits(['update:modelValue']);
  const handleCancel = () => {
    emits('update:modelValue', false);
  };
  const handlePreOk = () => {
    emits('update:modelValue', false);
  };
  const dimensionNames = {
    visual: '视知觉障碍',
    auditory: '听知觉障碍',
    comprehension: '意义理解障碍',
    writing: '书写障碍',
    oral: '口语障碍',
    written: '书面表达障碍',
    habits: '不良阅读习惯',
    attention: '注意力障碍',
  };

  const dimensionDescriptions = {
    visual: '主要测查儿童对汉字字形的视觉加工、早期大脑发育和动作协调功能障碍。',
    auditory: '主要测查儿童对汉字语音的听觉加工和语音表达障碍。',
    comprehension: '主要测查儿童对包括字、词、句、段落和篇章等不同层次语义表达的获得和加工障碍。',
    writing: '主要测查儿童的书写流畅性和可辨认性差，反映其书写注意力集中和书写动作障碍。',
    oral: '主要测查儿童口语理解和口语表达障碍。',
    written: '主要测查儿童在书面语的使用和输出方面困难，反映儿童意义加工、书面词汇量缺失和书写技能的综合障碍。',
    habits: '主要测查儿童由于不良阅读习惯和环境导致对汉字水平形、音、义加工能力障碍。',
    attention: '主要测查儿童存在注意力缺陷，专注水平低导致汉字字形、字音、字义的加工障碍。',
  };
  const dimensionData = ref([]);
  const prepareDimensionData = (val) => {
    if (!val || !val.result) {
      Message.error('结果数据格式不正确');
      return;
    }
    dimensionData.value = Object.entries(val.result).map(([key, value]) => ({
      key,
      name: dimensionNames[key],
      description: dimensionDescriptions[key],
      score: value.score,
      itemCount: value.items.length,
      items: value.items.map((item) => ({
        id: item.id,
        content: questions[item.id - 1]?.content || '未知题目',
        score: item.score,
      })),
    }));
  };
  const reportResult = ref(null);
  const loadReport = async () => {
    if (!props.record?.id) return;
    try {
      const { data: res } = await request(`/resourceRoom/readingDisability/${props.record.id}`, {
        method: 'get',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      reportResult.value = res;
      prepareDimensionData(res);
    } finally {
      /**/
    }
  };
  onMounted(async () => {
    // 滚动到顶部
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
    if (!props.isViewReport) prepareDimensionData(props.result);
    else await loadReport();
  });
  const handlePrint = () => {
    window.print();
  };
  const handleBack = () => {
    emits('update:modelValue', false);
  };
</script>

<style scoped>
  .report-container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background-color: #fff;
  }

  .report-content {
    padding: 20px;
  }

  h1 {
    text-align: center;
    margin-bottom: 40px;
    color: #1d2129;
  }

  h2 {
    color: #1d2129;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e5e6eb;
  }

  .total-score {
    margin-bottom: 40px;
  }

  .score-box {
    padding: 20px;
    background-color: #f2f3f5;
    font-size: 18px;
    font-weight: bold;
    color: #1d2129;
  }

  .dimensions-detail {
    margin-bottom: 40px;
  }

  .dimension-section {
    margin-bottom: 30px;
    padding: 24px;
    background-color: #ffffff;
    border: 1px solid #e5e6eb;
  }

  .dimension-header {
    margin-bottom: 20px;
  }

  .dimension-title h3 {
    font-size: 18px;
    color: #1d2129;
    margin: 0;
  }

  .dimension-desc {
    margin: 8px 0 0 0;
    font-size: 14px;
    color: #4e5969;
    line-height: 1.6;
  }

  .dimension-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 24px;
  }

  .stat-box {
    padding: 16px 24px;
    background-color: #f7f8fa;
    border: 1px solid #e5e6eb;
    text-align: center;
    min-width: 120px;
  }

  .stat-value {
    font-size: 24px;
    font-weight: 600;
    color: #165dff;
    line-height: 1.4;
  }

  .stat-label {
    font-size: 14px;
    color: #4e5969;
    margin-top: 4px;
  }

  .items-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
  }

  .item-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f7f8fa;
    border: 1px solid #e5e6eb;
  }

  .item-main {
    display: flex;
    gap: 12px;
    flex: 1;
  }

  .item-id {
    color: #4e5969;
    font-weight: 500;
    min-width: 30px;
  }

  .item-content {
    color: #1d2129;
    flex: 1;
  }

  .item-score-box {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background-color: #ffffff;
    border: 1px solid #e5e6eb;
  }

  .score-label {
    font-size: 14px;
    color: #4e5969;
  }

  .score-value {
    font-size: 16px;
    font-weight: 600;
    color: #165dff;
  }

  .actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-top: 40px;
  }

  @media print {
    .actions {
      display: none;
    }

    .report-container {
      padding: 0;
    }

    .dimension-section {
      break-inside: avoid;
    }
  }

  @media screen and (max-width: 768px) {
    .dimension-stats {
      flex-wrap: wrap;
    }

    .stat-box {
      flex: 1;
      min-width: 100px;
    }

    .item-row {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .item-score-box {
      align-self: flex-end;
    }
  }
</style>
