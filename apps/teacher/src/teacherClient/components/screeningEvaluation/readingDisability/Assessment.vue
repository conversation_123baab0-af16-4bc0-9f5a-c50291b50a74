<template>
  <a-modal
    :visible="visible"
    fullscreen
    :closable="false"
    cancel-text="返回"
    :on-before-ok="handlePreOk"
    @cancel="handleCancel"
  >
    <div class="assessment-container">
      <div class="assessment-header">
        <h1>儿童汉语阅读障碍量表(DCCC)评估</h1>
      </div>

      <!-- 卡片容器 -->
      <div class="fixed left-5 top-45 transform w-[300px] bg-white rounded-lg shadow-lg z-50">
        <!-- 说明内容 -->
        <div class="instructions-content p-4 h-96 overflow-auto">
          <h3>评估说明</h3>
          <ul>
            <li>这是一份用于了解小学三到五年级儿童汉语阅读行为与习惯的调查表，由熟悉儿童情况的家长或老师填写。</li>
            <li
              >本表对所列的57个条目分别规定了5个等级：1分="从未出现"、2分="偶尔出现"、3分="有时出现"、4分="较常出现"、5分="经常出现"。
            </li>
            <li>请根据被评定的孩子的实际情况，选择你认为最接近的答案。</li>
            <li>每题只选一个答案。</li>
          </ul>
        </div>
      </div>
      <a-select
        v-model="selectedStudent"
        placeholder="请选择学生"
        class="fixed right-5 z-50 top-20"
        :options="options"
        :bordered="false"
        style="width: 300px"
      />
      <!-- 按钮区，独立于卡片之外 -->
      <div class="fixed right-5 bottom-36 z-50 flex space-x-4 items-center">
        <div class="flex space-x-2">
          <a-button class="drop-shadow-md" shape="round" title="返回顶部" @click="scrollToTop">
            <template #icon>
              <icon-double-up />
            </template>
          </a-button>
          <a-button class="drop-shadow-md" shape="round" title="前往底部" @click="scrollToBottom">
            <template #icon>
              <icon-double-down />
            </template>
          </a-button>
        </div>
        <a-button type="primary" shape="round" @click="findNextUnanswered">
          {{ completedCount }}/{{ questions.length }}
        </a-button>
      </div>

      <div ref="readingDisability" class="pt-5 pb-5">
        <a-form :model="formState" layout="vertical" @submit="handleSubmit">
          <div v-for="(question, index) in questions" :key="index" class="question-item" :data-question="index">
            <a-form-item>
              <template #label>
                <div class="question-label">
                  <span class="question-number">{{ index + 1 }}</span>
                  <span class="question-content">{{ question.content }}</span>
                </div>
              </template>
              <a-radio-group v-model="formState[index]" class="radio-group">
                <a-radio :value="1">从未出现</a-radio>
                <a-radio :value="2">偶尔出现</a-radio>
                <a-radio :value="3">有时出现</a-radio>
                <a-radio :value="4">较常出现</a-radio>
                <a-radio :value="5">经常出现</a-radio>
              </a-radio-group>
            </a-form-item>
          </div>

          <div class="submit-section">
            <a-button type="primary" html-type="submit" size="large"> 提交评估</a-button>
            <p v-if="!isFormComplete" class="submit-tip"> 请完成所有评估项目后提交 </p>
          </div>
        </a-form>
      </div>
    </div>
  </a-modal>
  <report v-if="reportVisible" v-model="reportVisible" :visible="reportVisible" :result="results" />
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import Report from './Report.vue';
  import questions from './questions';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    students: {
      type: Array,
    },
  });
  const emits = defineEmits(['update:modelValue']);
  const reportVisible = ref(false);

  const handleCancel = () => {
    emits('update:modelValue', false);
  };
  const handlePreOk = () => {
    emits('update:modelValue', false);
  };
  const readingDisability = ref();

  // 滚动到顶部
  const scrollToTop = () => {
    if (readingDisability.value) {
      readingDisability.value.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };
  // 滚动到底部
  const scrollToBottom = () => {
    if (readingDisability.value) {
      readingDisability.value.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
  };

  const formState = ref({});

  const isFormComplete = computed(() => {
    return Object.keys(formState.value).length === questions.length;
  });

  const completedCount = computed(() => {
    return Object.keys(formState.value).length;
  });

  const findNextUnanswered = () => {
    for (let i = 0; i < questions.length; i += 1) {
      if (!formState.value[i]) {
        const element = document.querySelector(`[data-question="${i}"]`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          element.classList.add('highlight-animation');
          setTimeout(() => {
            element.classList.remove('highlight-animation');
          }, 5000);
          break;
        }
      }
    }
  };
  const dimensions = {
    visual: [1, 2, 5, 6, 7, 22, 55],
    auditory: [8, 11, 17, 21, 23, 56],
    comprehension: [10, 26, 29, 32, 33, 44, 46, 50, 51],
    writing: [9, 16, 20, 28, 39, 48, 54],
    oral: [15, 27, 30, 36, 38, 40],
    written: [19, 31, 35, 41, 52, 53, 57],
    habits: [3, 37, 42, 43, 47, 49],
    attention: [4, 14, 18, 24, 25, 34, 45],
  };
  const total = ref(0);

  const calculateResults = (answers) => {
    const dimensionsResult = {};

    // 遍历每个维度
    // eslint-disable-next-line no-restricted-syntax
    for (const [key, questionIds] of Object.entries(dimensions)) {
      let dimensionScore = 0;
      const items = [];
      // 计算每个维度的得分和收集题目信息
      // eslint-disable-next-line no-restricted-syntax
      for (const id of questionIds) {
        const score = answers[id - 1] || 0;
        dimensionScore += score;
        items.push({
          id,
          score,
        });
      }
      // 累加总分
      total.value += dimensionScore;
      // 保存维度结果
      dimensionsResult[key] = {
        score: dimensionScore,
        items,
      };
    }
    return {
      total,
      result: dimensionsResult,
    };
  };

  const results = ref();
  const options = ref([]);
  const selectedStudent = ref(null);
  const enableSubmit = ref(true);
  const handleSubmit = () => {
    try {
      if (!selectedStudent.value) {
        Message.warning('请选择学生');
        return;
      }
      if (!enableSubmit.value) return;

      results.value = calculateResults(formState.value);
      request('/resourceRoom/readingDisability', {
        method: 'POST',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: {
          total: total.value,
          result: results.value.result,
          student: {
            id: selectedStudent.value,
          },
        },
      });
      Message.success('保存成功');
      enableSubmit.value = false;
    } finally {
      if (!enableSubmit.value) reportVisible.value = true;
    }
  };

  onMounted(() => {
    props.students.forEach((student) => {
      options.value.push({
        label: student.name,
        value: student.id,
      });
    });
  });
</script>

<style scoped>
  .assessment-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f7fa;
    min-height: 100vh;
  }

  .assessment-header {
    margin-bottom: 40px;
  }

  .assessment-header h1 {
    margin: 0;
    padding: 20px 0;
    color: #1d2129;
    font-size: 24px;
    text-align: center;
    font-weight: bold;
  }

  /* 移动端适配 */
  @media screen and (max-width: 768px) {
    .assessment-container {
      padding: 10px;
    }

    .assessment-header h1 {
      font-size: 20px;
    }
  }

  .question-container {
    padding: 20px 0;
  }

  .question-item {
    margin-bottom: 24px;
    padding: 20px;
    background-color: #ffffff;
    border: 1px solid #e5e6eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition:
      border-color 0.3s ease,
      box-shadow 0.3s ease;
  }

  .question-item:hover {
    border-color: #165dff;
  }

  .question-label {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
  }

  .question-number {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
    color: #4e5969;
    font-size: 15px;
    background-color: #f2f3f5;
    border-radius: 16px;
    flex-shrink: 0;
  }

  .question-content {
    font-size: 16px;
    color: #1d2129;
    line-height: 1.6;
    font-weight: 500;
  }

  .radio-group {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    padding: 8px 0;
  }

  :deep(.arco-radio) {
    padding: 6px 16px;
    border: 1px solid #e5e6eb;
    border-radius: 0;
    transition: all 0.2s ease;
  }

  :deep(.arco-radio:hover) {
    border-color: #165dff;
    background-color: #f2f3f5;
  }

  :deep(.arco-radio-checked) {
    background-color: #f2f3f5;
    border-color: #165dff;
  }

  .submit-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 40px;
    padding: 20px;
  }

  .submit-tip {
    margin-top: 12px;
    color: #86909c;
    font-size: 14px;
  }

  .instructions-content {
    padding: 20px;
  }

  .instructions-content h3 {
    margin: 0 0 16px 0;
    color: #1d2129;
    font-size: 16px;
    font-weight: 500;
  }

  .instructions-content ul {
    margin: 0;
    padding-left: 20px;
  }

  .instructions-content li {
    margin-bottom: 12px;
    color: #4e5969;
    font-size: 14px;
    line-height: 1.6;
  }

  /* 移动端适配 */
  @media screen and (max-width: 1400px) {
    .floating-instructions {
      position: static;
      width: auto;
      margin: 20px auto;
      transform: none;
    }
  }

  .progress-button :deep(.arco-btn) {
    padding: 6px 20px;
    font-size: 16px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  /* 移动端适配 */
  @media screen and (max-width: 768px) {
    .progress-button {
      right: 50%;
      transform: translateX(50%);
    }
  }

  @keyframes borderBlink {
    0% {
      border-color: #165dff;
      box-shadow: 0 0 0 0 rgba(22, 93, 255, 0.2);
    }
    25% {
      border-color: #165dff;
      box-shadow: 0 0 0 6px rgba(22, 93, 255, 0.2);
    }
    50% {
      border-color: #165dff;
      box-shadow: 0 0 0 0 rgba(22, 93, 255, 0.2);
    }
    75% {
      border-color: #165dff;
      box-shadow: 0 0 0 6px rgba(22, 93, 255, 0.2);
    }
    100% {
      border-color: #165dff;
      box-shadow: 0 0 0 0 rgba(22, 93, 255, 0.2);
    }
  }

  .highlight-animation {
    animation: borderBlink 3s ease-in-out 2; /* 每次动画3秒，重复2次，总共6秒 */
    border-color: #165dff !important;
  }
</style>
