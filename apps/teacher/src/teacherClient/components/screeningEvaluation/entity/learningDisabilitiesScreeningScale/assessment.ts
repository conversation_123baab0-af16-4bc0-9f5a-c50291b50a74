import { defineStore } from 'pinia';
import type { AssessmentResult } from './assessmentTypes';

export const useAssessmentStore = defineStore('assessment', {
  state: () => ({
    assessmentResult: null as AssessmentResult | null,
  }),

  actions: {
    setAssessmentResult(result: AssessmentResult) {
      this.assessmentResult = result;
      localStorage.setItem('assessment-result', JSON.stringify(result));
    },

    resetAssessment() {
      this.assessmentResult = null;
      localStorage.removeItem('assessment-result');
    },

    loadSavedResult() {
      const saved = localStorage.getItem('assessment-result');
      if (saved) {
        this.assessmentResult = JSON.parse(saved);
      }
    },
  },
});
