<template>
  <a-card v-if="schema">
    <template #title>
      <table-action
        v-if="tableRef"
        class="w-full"
        component-size="mini"
        :table="tableRef"
        :schema="schema"
        @row-action="handleRowAction"
      >
        <template #title>送教档案管理</template>
        <template #extra-actions></template>
      </table-action>
    </template>
    <crud-table
      ref="tableRef"
      :schema="schema"
      class="mt-2"
      :default-query-params="{ sort: '-id' }"
      :visible-columns="columns"
      @row-action="handleRowAction"
    >
      <template #custom-column-createdBy="{ record }">
        <span>{{ record.createdBy.name }}</span>
      </template>
      <template #custom-column-finished="{ record }">
        <span
          class="w-full h-full px-2 py-1 rounded-lg text-xs font-bold"
          :class="record?.finished ? 'text-green-500' : ' text-red-500 cursor-pointer'"
          @click="handleViewInfo(record)"
        >
          {{ record?.finished ? '已完成' : '进行中' }}
        </span>
      </template>
    </crud-table>
  </a-card>
  <add
    v-if="addVisible"
    v-model="addVisible"
    :visible="addVisible"
    :students="students"
    :schema="schema"
    :record="records"
    :type="type"
    @update:model-value="flush"
  />
  <detail
    v-if="detailVisible"
    v-model="detailVisible"
    :visible="detailVisible"
    :record="records"
    @update:model-value="flush"
  />
</template>

<script setup lang="ts">
  import { CrudTable, TableAction } from '@repo/ui/components/table';
  import { onMounted, ref, h } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { Message, Modal } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import Add from '@/teacherClient/components/teaching/teachingPlan/add.vue';
  // import Detail from '@/teacherClient/components/teaching/teachingPlan/detail.vue';
  import Detail from '@repo/components/d2dEduShare/detail.vue';
  import { RenderContent } from '@arco-design/web-vue/es/_utils/types';

  const currentItem = ref(null);
  const detailVisible = ref(false);
  const addVisible = ref(false);
  const schema = ref(null);
  const tableRef = ref(null);
  const records = ref(null);
  const students = ref([]);
  const type = ref('add');

  const columns = [
    'student',
    'gradeClass',
    'finished',
    'period',
    'chargeUser',
    'createdDate',
    'createdBy',
    'collaborators',
    // 'submitStatus',
  ];
  const flush = async () => {
    await tableRef.value.loadData();
  };
  const loadingStudents = async () => {
    try {
      const res = await request('/resourceRoom/student', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'GET',
      });
      students.value = res.data.items;
    } catch (error) {
      /**/
    }
  };
  const handleExportSelected = async (exportType: string) => {
    Message.info('正在导出，请稍后在右上角导出结果中查看');
    let ids = [];
    if (Array.isArray(records.value)) ids = records.value;
    else ids.push(records.value.id);
    try {
      await request(`/resourceRoom/d2dEducation/batchExport?type=${exportType}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'POST',
        data: ids,
      });
    } catch (e) {
      /**/
    }
  };

  const handleRowAction = async (action: any, record: any) => {
    records.value = record;
    switch (action.key) {
      case 'exportArchive':
        await handleExportSelected('source');
        break;
      case 'exportArchiveMerge':
        await handleExportSelected('merge');
        break;
      case 'placeOnFile': {
        // 归档
        const loading = Message.loading('归档中...');
        await request(`/resourceRoom/d2dEducation/finish/${record.id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'PUT',
        }).then(() => {
          loading.close();
          Message.success('操作成功');
          flush();
        });
        break;
      }
      case 'removeArchive': {
        // 解除归档
        const loading = Message.loading('解除归档中...');
        await request(`/resourceRoom/d2dEducation/unfinish/${record.id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'PUT',
        }).then(() => {
          loading.close();
          Message.success('操作成功');
          flush();
        });
        break;
      }
      case 'add':
        type.value = 'add';
        records.value = null;
        addVisible.value = true;
        break;
      case 'edit':
        type.value = 'edit';
        addVisible.value = true;
        break;
      case 'editEducationArchive': // 编辑档案
        detailVisible.value = true;
        break;
      default:
        break;
    }
  };

  const loadSendRecord = async (sendEduId: number) => {
    const { data } = await request(`/resourceRoom/d2dEducationRecord/findByD2dEduId/${sendEduId}`, {
      method: 'get',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    return data;
  };
  const statusInfoCatch = ref<RenderContent>({});

  const handleViewInfo = async (record: any): string => {
    if (record?.finished) {
      /**/
    } else {
      // 加载对应的record info such as parent signature and  submitStatus,
      if (!statusInfoCatch.value?.[record.id]) {
        const { items: sendRecordList } = await loadSendRecord(record.id);
        const unsignedList = sendRecordList.filter(
          (item) => !(item?.parentSignature?.url || item?.parentSignature?.udf1),
        );

        const content = () =>
          h(
            'div',
            {
              class: 'p-6 bg-white rounded-xl shadow-lg border border-gray-100 space-y-5',
            },
            unsignedList.length
              ? [
                  h('div', { class: 'flex items-center text-amber-600 text-xl font-semibold' }, [
                    h('i', { class: 'i-heroicons-exclamation-triangle-solid w-6 h-6 mr-2 text-amber-500' }),
                    `有 ${unsignedList.length} 条送教记录待家长签字确认`,
                  ]),
                  h(
                    'ul',
                    { class: 'space-y-3' },
                    unsignedList.map((item, index) =>
                      h(
                        'li',
                        {
                          class:
                            'flex items-start bg-red-50 border-l-4 border-red-400 px-4 py-2 rounded-md shadow-sm text-red-700 text-sm leading-snug',
                        },
                        `${index + 1}、${item?.date || '日期不详'} 的送教记录家长未签字确认`,
                      ),
                    ),
                  ),
                ]
              : [
                  h(
                    'div',
                    {
                      class:
                        'flex items-center justify-center text-green-700 text-lg font-medium py-4 bg-green-50 rounded-md',
                    },
                    [
                      h('i', {
                        class: 'i-heroicons-check-circle-solid w-6 h-6 mr-2 text-green-600',
                      }),
                      '所有送教记录已签字确认',
                    ],
                  ),
                ],
          );

        statusInfoCatch.value[record.id] = content();
      }

      Modal.open({
        // title: () => h('span', { class: 'text-sm' }, `${record.student.name} - ${record.period}`),
        content: statusInfoCatch.value[record.id],
        footer: false,
        closable: false,
        bodyStyle: {
          padding: 0,
          margin: 0,
        },
      });
    }
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/d2dEducation');
    await loadingStudents();
  });
</script>

<style lang="scss" scoped>
  @use '@/assets/module/submittableRowColorful.scss' as *;
</style>
