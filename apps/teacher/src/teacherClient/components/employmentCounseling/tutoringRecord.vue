<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import dayjs from 'dayjs';
  import isBetween from 'dayjs/plugin/isBetween';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    students: {
      type: Array,
    },
    record: {
      type: Object,
    },
  });
  const emits = defineEmits(['update:modelValue']);
  const studentOptions = ref([]);
  const formData = ref({});
  const planList = ref([]);
  const planOptions = ref([]);
  const size = 'mini';
  const tableData = ref<any[]>([]);

  dayjs.extend(isBetween);
  const isInRange = computed(() => {
    const currentDate = dayjs();
    const startDate = dayjs(props.record.dateRange[0], 'YYYY-MM-DD'); // 起始日期
    const endDate = dayjs(props.record.dateRange[1], 'YYYY-MM-DD'); // 结束日期
    return currentDate.isBetween(startDate, endDate, 'day', '[]'); // 使用 '[]' 来包含边界
  });

  const loadDataPlan = async () => {
    if (props.record.id) {
      try {
        const res = await request(`/resourceRoom/employmentServicePlan/findByServiceId/${props.record.id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'GET',
        });
        planList.value = res.data.items;
        planList.value.forEach((item: any) => {
          planOptions.value.push({
            label: item.target,
            value: item.id,
          });
        });
      } catch (e) {
        /**/
      }
    }
  };
  const loadDataRecord = async () => {
    if (props.record.id) {
      try {
        const res = await request(`/resourceRoom/employmentServiceRecord/findByEmploymentService/${props.record.id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'GET',
        });
        tableData.value = res.data.items;
      } catch (e) {
        /**/
      }
    }
  };

  const handleDel = async (record) => {
    if (!isInRange.value) {
      Message.error('超出时间范围不能操作');
      return;
    }
    const res = await request(`/resourceRoom/employmentServiceRecord/${record.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'delete',
    }).then(() => {
      loadDataRecord();
    });
  };

  const currentPlan = ref(null);
  const handleChange = (val: number) => {
    if (val) currentPlan.value = planList.value.find((item) => item.id === val);
    else currentPlan.value = null;
  };

  const columns = ref([
    { title: '所属计划', dataIndex: 'employmentServicePlan.target', slotName: 'plan', width: 200 },
    { title: '领域', dataIndex: 'field', slotName: 'field', width: 200 },
    { title: '辅导日期', dataIndex: 'tutoringDate', slotName: 'target', width: 200 },
    { title: '辅导教师', dataIndex: 'teacher', slotName: 'teacher' },
    { title: '创建日期', dataIndex: 'createdDate', slotName: 'createdDate' },
    { title: '操作', slotName: 'operation', align: 'center', width: 150 },
  ]);
  const handleAdd = async () => {
    if (!isInRange.value) {
      Message.error('超出时间范围不能操作');
      return;
    }
    if (currentPlan.value === null) {
      Message.warning('请先选择计划');
      return;
    }
    const { data: res } = await request(`/resourceRoom/employmentServiceRecord`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'POST',
      data: {
        employmentService: { id: props.record.id },
        employmentServicePlan: { id: currentPlan.value.id },
      },
    });
    tableData.value.push({
      employmentServicePlan: { ...currentPlan.value },
      ...res,
    });
  };
  const danger = {
    /*    backgroundColor: 'rgba(255,236,236,0.79)',
    placeholderColor: '#ff5f5f',
    color: '#ffa1a1', */
    backgroundColor: 'rgba(223,223,223,0.71)',
    color: '#000000',
  };
  const ordinary = {
    backgroundColor: 'rgba(223,223,223,0.71)',
    color: '#000000',
  };
  const handleCancel = () => {
    emits('update:modelValue', false);
  };
  const handlePreOk = async () => {
    if (!isInRange.value) {
      Message.error('超出时间范围不能操作');
      return;
    }
    await request(`/resourceRoom/employmentServiceRecord/save`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'post',
      data: tableData.value,
    }).then(() => {
      emits('update:modelValue', false);
    });
  };
  onMounted(async () => {
    await loadDataPlan();
    await loadDataRecord();
  });
</script>

<template>
  <a-modal :closable="false" :on-before-ok="handlePreOk" :visible="visible" width="60%" @cancel="handleCancel">
    <template #title>
      <div class="flex justify-between items-center w-full">
        <span class="flex-grow text-center">{{ record.student.name }}-辅导记录</span>
        <span class="w-40">
          <a-select
            :options="planOptions"
            placeholder="请选择计划"
            size="mini"
            allow-clear
            allow-search
            :style="currentPlan != null ? ordinary : danger"
            @change="handleChange"
          />
        </span>
        <a-button type="outline" class="ml-10 mr-8" :size="size" :disabled="!isInRange" @click="handleAdd">
          <icon-plus />
          添加
        </a-button>
      </div>
    </template>
    <a-table :columns="columns" :data="tableData" :bordered="{ cell: true }" column-resizable>
      <template #plan="{ record }">
        <span v-if="record?.employmentServicePlan.target">{{ record.employmentServicePlan.target }}</span>
        <span v-else> - </span>
      </template>
      <template #field="{ record }">
        <a-textarea v-model="record.field" placeholder="请输入领域" />
      </template>
      <template #target="{ record }">
        <a-date-picker v-model="record.tutoringDate" />
      </template>
      <template #teacher="{ record }">
        <a-textarea v-model="record.teacher" placeholder="请输入教师" />
      </template>
      <template #createdDate="{ record }">
        <span>{{ record.createdDate.split(' ')[0] }}</span>
      </template>
      <template #operation="{ record }">
        <a-button v-if="false" status="normal" class="mr-2" :size="size" @click="handleDel(record)">查看</a-button>
        <a-button status="danger" :size="size" @click="handleDel(record)">删除</a-button>
      </template>
    </a-table>
  </a-modal>
</template>

<style scoped lang="scss"></style>
