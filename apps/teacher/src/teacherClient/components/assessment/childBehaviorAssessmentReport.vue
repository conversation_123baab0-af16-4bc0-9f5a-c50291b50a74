<template>
  <a-drawer
    v-bind="$attrs"
    :title="`${currentRow.student.name} 儿心评估报告`"
    :width="800"
    :before-close="handleClose"
    hide-cancel
    @open="handleOpen"
  >
    <div id="container"></div>
    <div v-if="currentRow" id="reportPrintArea" class="print-visible report-wrapper">
      <div class="title">发育评估报告</div>
      <div class="content">
        <div class="block-title bg-blue-300">基本资料</div>
        <table>
          <tbody>
            <tr>
              <th>姓名</th>
              <td>{{ currentRow.student.name }}</td>
              <th>性别</th>
              <td>{{ currentRow.student.gender }}</td>
            </tr>
            <tr>
              <th>出生日期</th>
              <td>{{ currentRow.student.birthday }}</td>
              <th>实际月龄</th>
              <td>{{ currentRow.realAge }} 月</td>
            </tr>
            <tr>
              <th>评测时间</th>
              <td>{{ currentRow.evaluateDate.substr(0, 10) }}</td>
              <th>测查者</th>
              <td>{{ currentRow.evaluatePerson }}</td>
            </tr>
          </tbody>
        </table>

        <div class="block-title">评测结果</div>
        <table>
          <tbody>
            <tr>
              <th colspan="3">发育商（综合）：{{ currentRow.dqResult }}</th>
              <th colspan="3">智龄：{{ currentRow.mentalAgeResult }}</th>
            </tr>
            <tr>
              <th width="16.65%">评测项目</th>
              <th v-for="d in domains" :key="d" width="16.67%">{{ d }}</th>
            </tr>
            <tr>
              <th>能区智龄</th>
              <td v-for="d in domains" :key="d">
                {{ currentRow.mentalAgeResultDetail[d] }}
              </td>
            </tr>
            <tr>
              <th>发育商(单项)</th>
              <td v-for="d in domains" :key="d">
                {{ currentRow.dqResultDetail[d] }}
              </td>
            </tr>
          </tbody>
        </table>

        <div class="chart-wrapper">
          <div class="chart-item">
            <div class="chart-title">五大能区实龄与智龄比较</div>
            <div :id="`cbar-left-${currentRow.id}`" class="chart print-hide"></div>
            <img id="cbar-left-image" class="image print-visible-only" />
          </div>

          <div class="chart-item">
            <div class="chart-title">五大能区发育商情况</div>
            <div :id="`cbar-right-${currentRow.id}`" class="chart print-hide"></div>
            <img id="cbar-right-image" class="image print-visible-only" />
          </div>
        </div>

        <div class="text-left p-4 mt-4 border border-slate-100 bg-slate-50 rounded-xl">
          {{ currentRow.resultComment }}
          <div class="text-right"> 评价人员： {{ currentRow.evaluatePerson }} </div>
        </div>
      </div>

      <div class="footer">
        注：测试结果一般只反应儿童当时的受试情况，反应儿童目前的发育水平，不能据此预测将来的智力水平。
      </div>
    </div>

    <template #footer>
      <div class="w-full flex justify-between">
        <a-button type="outline" size="small" @click="handlePrint">
          <template #icon>
            <IconPrinter />
          </template>
          打印报告
        </a-button>
        <a-button type="primary" size="small" @click="handleClose">关闭</a-button>
      </div>
    </template>
  </a-drawer>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted, defineProps, defineEmits, nextTick } from 'vue';
  import { VuePrintNext } from 'vue-print-next';
  import { Column, Radar } from '@antv/g2plot';

  // 定义组件的 Props 类型
  interface Student {
    name: string;
    gender: string;
    birthday: string;
  }

  interface CurrentRow {
    id: string;
    student: Student;
    realAge: number;
    evaluateDate: string;
    evaluatePerson: string;
    dqResult: string;
    mentalAgeResult: string;
    dqResultDetail: Record<string, number>;
    mentalAgeResultDetail: Record<string, number>;
    resultComment: string;
  }

  const domains = ['大运动', '精细动作', '适应能力', '语言', '社会行为'];

  // 接收组件的 Props
  const props = defineProps<{
    currentRow: CurrentRow | null;
  }>();

  // 触发父组件事件
  const emit = defineEmits<{
    (e: 'update:value', val: boolean): void;
    (e: 'close'): void;
  }>();

  // 定义 loading 状态
  const loading = ref(false);

  // 计算属性
  const dialogVisible = computed({
    get() {
      return !!props.currentRow;
    },
    set(val: boolean) {
      emit('update:value', val);
    },
  });

  // 处理关闭事件
  const handleClose = () => {
    emit('close');
  };

  // 打印功能
  const handlePrint = () => {
    new VuePrintNext({
      el: `#reportPrintArea`,
      zIndex: 9999,
      hide: '.no-print',
    });
  };

  // 渲染左侧图表
  const renderLeftChart = () => {
    const data = domains
      .map((domain) => {
        return {
          domain,
          month: props.currentRow?.realAge ?? 0,
          monthType: '实际月龄',
        };
      })
      .concat(
        domains.map((domain) => {
          return {
            domain,
            month: props.currentRow?.mentalAgeResultDetail[domain] ?? 0,
            monthType: '能区智龄',
          };
        }),
      );

    const chartId = `cbar-left-${props.currentRow?.id}`;
    const imageId = `cbar-left-image`;

    const stackedColumnPlot = new Column(chartId, {
      data,
      isGroup: true,
      xField: 'domain',
      yField: 'month',
      seriesField: 'monthType',
      color: ['#1ca9e6', '#f88c24'],
      marginRatio: 0.1,
      legend: { position: 'bottom' },
      label: {
        position: 'middle',
        layout: [{ type: 'interval-adjust-position' }, { type: 'interval-hide-overlap' }, { type: 'adjust-color' }],
      },
    });

    stackedColumnPlot.render();

    setTimeout(() => {
      const canvas = document.querySelector(`#${chartId} canvas`);
      const image = document.querySelector(`#${imageId}`);
      if (canvas && image) {
        image.src = canvas.toDataURL('image/png');
      }
    }, 1000);
  };

  // 渲染右侧图表
  const renderRightChart = () => {
    const data = domains.map((domain) => ({
      item: domain,
      score: props.currentRow?.dqResultDetail[domain] ?? 0,
    }));

    const chartId = `cbar-right-${props.currentRow?.id}`;
    const imageId = `cbar-right-image`;

    const radarPlot = new Radar(document.getElementById(chartId), {
      data,
      xField: 'item',
      yField: 'score',
      label: {},
      meta: {
        score: {
          alias: '发育商',
          min: 0,
          max: 150,
        },
      },
      xAxis: {
        line: null,
        tickLine: null,
        grid: {
          line: { style: { lineDash: null } },
        },
      },
      yAxis: {
        line: null,
        visible: false,
        tickLine: null,
        grid: { line: { type: 'line', style: { lineDash: null } } },
      },
      point: { size: 2 },
      area: {},
    });

    radarPlot.render();

    setTimeout(() => {
      const canvas = document.querySelector(`#${chartId} canvas`);
      const image = document.querySelector(`#${imageId}`);
      if (canvas && image) {
        image.src = canvas.toDataURL('image/png');
      }
    }, 1000);
  };

  const handleOpen = () => {
    nextTick(() => {
      renderLeftChart();
      renderRightChart();
    });
  };
</script>

<style lang="scss" scoped>
  .report-wrapper {
    text-align: center;
    .title {
      font-size: 22px;
      font-weight: 600;
      padding: 16px;
    }
    .content {
      padding: 20px;
      .block-title {
        font-weight: bold;
        font-size: 18px;
        padding: 8px 0;
      }
      table {
        width: 100%;
        margin: 16px 0;
        th,
        td {
          padding: 8px 16px;
          border: 1px solid #ccc;
        }
      }
      .footer {
        font-size: 14px;
        padding: 12px 0;
      }
    }
  }

  .chart-wrapper {
    display: flex;
    justify-content: space-between;
    .chart-item {
      width: 48%;
      .chart-title {
        text-align: center;
        font-size: 16px;
        padding: 8px;
      }
      .chart {
        width: 100%;
        height: 300px;
      }
    }
  }

  .footer {
    margin-top: 20px;
    text-align: center;
  }

  .print-visible-only {
    display: none;
  }

  @media print {
    .print-hide {
      display: none;
    }
    .print-visible {
      display: block;
    }
    .print-visible-only {
      display: block;
    }
  }
</style>
