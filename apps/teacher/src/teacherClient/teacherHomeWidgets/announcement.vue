<script lang="ts" setup>
  import { vue3ScrollSeamless } from 'vue3-scroll-seamless';
  import dayjs from 'dayjs';
  import { ref } from 'vue';
  import { request } from '@repo/infrastructure/request';

  const props = defineProps({
    dataList: {
      type: Array as PropType<any[]>,
    },
  });

  const currentItem = ref<any>(null);
  const viewModalVisible = ref(false);

  const handleViewDetail = async (item: any) => {
    const { data } = await request(`/guardian/announcement/${item.id}`);
    currentItem.value = data;
    viewModalVisible.value = true;
  };

  const classOptions = {
    limitMoveNum: 2,
    direction: 2,
  };
</script>

<template>
  <div class="demo">
    <vue3ScrollSeamless class="scroll-wrap" :class-options="classOptions" :data-list="dataList">
      <ul class="ui-wrap">
        <li v-for="(item, i) in dataList" :key="i" class="li-item" @click="() => handleViewDetail(item)">
          <a-tooltip :content="item.title">
            <IconSend />
            [{{ dayjs(item.createdDate).format('YYYY-MM-DD') }}]
            <span>{{ item.title }}</span>
          </a-tooltip>
        </li>
      </ul>
    </vue3ScrollSeamless>

    <a-modal v-model:visible="viewModalVisible" :width="800" simple hide-cancel>
      <template #title>
        <div>公告详情</div>
      </template>
      <div v-if="currentItem">
        <div class="text-lg font-medium">{{ currentItem.title }}</div>
        <a-divider :margin="10" />
        <div v-html="currentItem.content"></div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped>
  .demo {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  .scroll-wrap {
    height: 30px;
    width: 300px;
    overflow: hidden;
  }
  .ui-wrap {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
  }
  .li-item {
    padding: 0;
    margin-top: 0;
    margin-left: 20px;
    width: 300px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    cursor: pointer;
  }
</style>
