<script setup lang="ts">
  // import EvaluateVersion2 from '@repo/components/src/resourceRoom/evaluateVersion2.vue';
  import EvaluateVersion2 from '@repo/components/resourceRoom/evaluateVersion2.vue';
  import { getOrgNature } from '@repo/components/utils/utils';
  import { onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    dataList: {
      type: Array,
    },
  });
  const evaluateVisible = ref(false);
  const fusionSchool = ref<Record<string, any>>();

  const loadFusionSchool = async () => {
    const data = await request('/resourceCenter/fusionSchool/findByBranchOfficeId', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
    });
    fusionSchool.value = data.data;
  };
  const evaluationIndicator = ref();
  const loadEvaluationIndicators = async () => {
    try {
      const res = await request('/resourceCenter/evaluationCriterion/findNotSubmit', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
        params: {
          nature: getOrgNature(),
        },
      });
      evaluationIndicator.value = res.data;
    } catch (e) {
      /**/
    }
  };
  const flush = async () => {
    await loadFusionSchool();
    await loadEvaluationIndicators();
  };
  const currentEvaluation = ref();
  const handleClick = (item: any) => {
    /**/
    evaluateVisible.value = true;
    currentEvaluation.value = item;
  };
  onMounted(async () => {
    await loadFusionSchool();
    await loadEvaluationIndicators();
  });
</script>

<template>
  <a-card v-if="evaluationIndicator?.length > 0" size="mini">
    <template #title>常规巡检</template>
    <div v-for="item in evaluationIndicator" :key="item.id" class="pr-2">
      <a-link class="text-ellipsis w-full" @click="() => handleClick(item)">
        <div style="width: 100%">
          {{ item.name }}
        </div>
      </a-link>
    </div>
  </a-card>

  <EvaluateVersion2
    v-model="evaluateVisible"
    :visible="evaluateVisible"
    :fusion-school="fusionSchool"
    :evaluation-indicator="currentEvaluation"
    :type="'teacher'"
    @update:model-value="flush"
  />
</template>

<style scoped lang="scss"></style>
