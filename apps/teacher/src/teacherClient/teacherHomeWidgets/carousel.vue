<template>
  <a-carousel v-if="dataList?.length" class="h-48 w-72 bg-white" indicator-type="line" auto-play>
    <a-carousel-item v-for="(image, idx) in dataList" :key="idx">
      <img :src="image.image" alt="image.title" />
    </a-carousel-item>
  </a-carousel>
</template>

<script lang="ts" setup>
  const props = defineProps({
    dataList: Array,
  });
</script>

<style scoped>
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
</style>
