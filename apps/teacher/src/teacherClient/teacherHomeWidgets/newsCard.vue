<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import dayjs from 'dayjs';

  const props = defineProps({
    dataList: {
      type: Array,
      default: () => [],
    },
  });

  const viewModalVisible = ref(false);
  const currentItem = ref<any>({});

  const lists = computed(() => {
    return props.dataList?.slice(0, 4) || [];
  });

  const handleShowDetail = async (item) => {
    const { data } = await request(`/guardian/news/${item.id}`);
    currentItem.value = data;
    viewModalVisible.value = true;
  };
</script>

<template>
  <a-card v-if="dataList?.length" size="mini" title="新闻资讯" class="flex-1">
    <div v-for="item in lists" :key="item.id" class="pr-2">
      <a-link class="text-ellipsis overflow-hidden truncate w-full" @click="() => handleShowDetail(item)">{{
        item.title
      }}</a-link>
    </div>
  </a-card>

  <a-modal v-model:visible="viewModalVisible" :width="900" simple hide-cancel>
    <template #title>
      <div>新闻详情</div>
    </template>
    <div v-if="currentItem">
      <div class="text-lg font-medium">
        [{{ dayjs(currentItem.createdDate).format('YYYY-MM-DD') }}]
        {{ currentItem.title }}
      </div>
      <a-divider :margin="10" />
      <div v-html="currentItem.content"></div>
    </div>
  </a-modal>
</template>

<style scoped lang="scss">
  .arco-link {
    justify-content: flex-start;
  }
</style>
