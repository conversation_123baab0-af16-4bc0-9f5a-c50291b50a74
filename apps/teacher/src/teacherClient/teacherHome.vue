<script setup lang="ts">
  import { useUserStore } from '@repo/infrastructure/store';
  import { onMounted, ref } from 'vue';
  // import TeacherLessonSchedule from '@repo/components/teacher/teacherLessonSchedule.vue';
  import teacherScheduleRelatedToLesson from '@repo/components/teacher/teacherScheduleRelatedToLesson.vue';
  import { request } from '@repo/infrastructure/request';
  import { guardianWeappInit } from '@repo/infrastructure/openapi/guardianController';
  import { PROJECT_URLS } from '@repo/env-config';
  import Announcement from '@/teacherClient/teacherHomeWidgets/announcement.vue';
  import Carousel from '@/teacherClient/teacherHomeWidgets/carousel.vue';
  import NewsCard from '@/teacherClient/teacherHomeWidgets/newsCard.vue';
  import PaperCompetitionCard from '@/teacherClient/teacherHomeWidgets/paperCompetitionCard.vue';
  import { getAvailableQuestionnaireForms } from '@repo/infrastructure/openapi/questionnaireFormController';
  import QuestionnaireFormCard from '@/teacherClient/teacherHomeWidgets/questionnaireFormCard.vue';
  import SupervisionEvaluationCard from '@/teacherClient/teacherHomeWidgets/supervisionEvaluationCard.vue';

  const userStore = useUserStore();
  const { userInfo } = userStore;

  const widgetData = ref<any>({
    newsList: [],
    announcementList: [],
    carouselList: [],

    paperCompetitionList: [],
    questionnaireFormList: [],
  });

  /**
   * {
   *   CarouselList,
   *   NewsList,
   *   Announcement
   * }
   */
  const loadNews = async () => {
    const { data } = await guardianWeappInit({
      visibleIn: 'teacher',
    });

    widgetData.value = data;
  };
  const loadPaperCompetition = async () => {
    const { data } = await request('/paper/paperCompetition', {
      params: {
        published: true,
        limit: 5,
        sort: '-id',
      },
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    widgetData.value.paperCompetitionList = data.items || [];
  };

  const loadQuestionnaireForm = async () => {
    const { data } = await getAvailableQuestionnaireForms();

    widgetData.value.questionnaireFormList = data || [];
  };

  const loadData = async () => {
    await Promise.all([await loadNews(), await loadPaperCompetition(), await loadQuestionnaireForm()]);
  };

  // const unSubmitIepNum = ref(0);
  // const unSubmitIep = async () => {
  //   const res = await request('/paper/paperCompetition/getNumNotSubmitIep', {
  //     baseURL: PROJECT_URLS.MAIN_PROJECT_API,
  //   });
  //   unSubmitIepNum.value = res.data;
  // };

  onMounted(async () => {
    await loadData();
    // await unSubmitIep();
  });
</script>

<template>
  <div class="flex gap-2">
    <div class="flex-1">
      <div class="flex flex-col gap-2">
        <a-card size="small">
          <div class="flex items-center justify-between gap-10">
            <div>
              <strong>{{ userInfo.name }}</strong
              >， 欢迎来到 {{ userInfo.branchOffice?.name }}
            </div>
            <!--<div> 未提交IEP: {{ unSubmitIepNum }} 份</div>-->
            <div class="flex-1">
              <announcement v-if="widgetData.announcementList?.length" :data-list="widgetData.announcementList" />
            </div>
          </div>
        </a-card>
        <div class="bg-white">
          <a-card title="我的课表">
            <!--<teacher-lesson-schedule v-if="userInfo?.id" mode="teacher" :teacher="userInfo" />-->
            <teacherScheduleRelatedToLesson v-if="userInfo?.id" mode="teacher" :teacher="userInfo" />
          </a-card>
        </div>

        <div class="flex gap-2">
          <carousel :data-list="widgetData.carouselList" />
          <news-card :data-list="widgetData.newsList" />
        </div>
      </div>
    </div>
    <div class="w-96 flex flex-col gap-2">
      <paper-competition-card :data-list="widgetData.paperCompetitionList" />
      <questionnaire-form-card :data-list="widgetData.questionnaireFormList" />
      <supervision-evaluation-card :data-list="null" />
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
