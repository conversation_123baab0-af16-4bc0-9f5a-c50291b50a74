<script setup lang="ts">
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { defineAsyncComponent, onMounted, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import subModulesComponents from '@/teacherClient/pages';

  const route = useRoute();
  const menuStore = useUserMenuStore();
  const menuInfo = ref<any>(menuStore.getCurrentTeacherMenuInfo(route));
  const router = useRouter();
  const ready = ref(false);

  const subModuleComponent = ref<any>(null);

  const getSubModuleComponent = () => {
    const currentMenuInfo = menuStore.getCurrentTeacherMenuInfo(route);
    const paths = [];
    if (currentMenuInfo.app) {
      paths.push(currentMenuInfo.app.key);
    }
    if (currentMenuInfo?.module) {
      paths.push(currentMenuInfo.module.key);
    }

    if (currentMenuInfo?.subModules?.length) {
      paths.push(...currentMenuInfo.subModules);
    }

    const path = `./pages/${paths.join('/')}.vue`;
    if (subModulesComponents[path]) {
      return defineAsyncComponent(subModulesComponents[path]);
    }

    throw new Error(`Module not found: ${path}`);
  };

  onMounted(async () => {
    if (!menuInfo.value.subModules?.length && menuInfo.value.module?.children?.length > 0) {
      await router.push(menuInfo.value.module.children[0].link as string);
    }

    subModuleComponent.value = getSubModuleComponent();

    ready.value = true;
  });

  watch(
    () => route.path,
    () => {
      menuInfo.value = menuStore.getCurrentTeacherMenuInfo(route);
      subModuleComponent.value = getSubModuleComponent();
    },
  );
</script>

<template>
  <component :is="subModuleComponent" class="flex-1" />
</template>

<style scoped lang="scss"></style>
