<template>
  <a-card v-if="schema">
    <template #title>
      <table-action
        v-if="tableRef"
        class="w-full"
        component-size="mini"
        :table="tableRef"
        :schema="schema"
        :visible-components="visibleComponents"
        @row-action="handleRowAction"
      >
        <template #title>就业服务</template>
        <template #extra-actions></template>
        <template #supplementary-button="{ size }">
          <a-button type="primary" :size="size" @click="handleAdd">
            <icon-plus />
            新增
          </a-button>
        </template>
      </table-action>
    </template>
    <crud-table
      ref="tableRef"
      :schema="schema"
      class="mt-2"
      :default-query-params="{ sort: '-id' }"
      :visible-columns="columns"
      @row-action="handleRowAction"
    >
      <template #custom-column-age="{ record }">
        <span>{{ record.student.age }}</span>
      </template>
      <template #custom-column-disorders="{ record }">
        <span>{{ record.student.disorders }}</span>
      </template>
      <template #custom-column-dateRange="{ record }">
        <span>{{ record.dateRange[0] }} / {{ record.dateRange[0] }}</span>
      </template>
    </crud-table>
  </a-card>
  <add
    v-if="addVisible"
    v-model="addVisible"
    :visible="addVisible"
    :students="students"
    :record="records"
    @update:model-value="flush"
  />
  <AddPlan
    v-if="addPlanVisible"
    v-model="addPlanVisible"
    :visible="addPlanVisible"
    :students="students"
    :record="records"
  />
  <TutoringRecord
    v-if="tutoringRecordVisible"
    v-model="tutoringRecordVisible"
    :visible="tutoringRecordVisible"
    :students="students"
    :record="records"
  />
  <viewRecord
    v-if="viewRecordVisible"
    v-model="viewRecordVisible"
    :visible="viewRecordVisible"
    :students="students"
    :record="records"
  />
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { CrudTable, TableAction } from '@repo/ui/components/table';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import Add from '@/teacherClient/components/employmentCounseling/add.vue';
  import AddPlan from '@/teacherClient/components/employmentCounseling/addPlan.vue';
  import TutoringRecord from '@/teacherClient/components/employmentCounseling/tutoringRecord.vue';
  import ViewRecord from '@/teacherClient/components/employmentCounseling/viewRecord.vue';

  const columns = [
    'student',
    'age',
    'disorders',
    'supervisionPlan',
    'target',
    'guideTeacher',
    'companyName',
    'dateRange',
    'createdDate',
  ];
  const schema = ref(null);
  const tableRef = ref(null);
  const visibleComponents = ref(['quickSearch', 'refresh', 'layout']);
  const addVisible = ref(false);
  const addPlanVisible = ref(false);
  const tutoringRecordVisible = ref(false);
  const viewRecordVisible = ref(false);

  const records = ref<Record<string, any>>();
  const students = ref([]);

  const flush = async () => {
    await tableRef.value.loadData();
  };
  const handleAdd = () => {
    addVisible.value = true;
  };
  const handleAddPlan = () => {
    addPlanVisible.value = true;
  };
  const handleTutoringRecord = () => {
    tutoringRecordVisible.value = true;
  };

  const deleteData = async (record: any) => {
    const ids = ref<number[]>([]);
    if (Array.isArray(record)) ids.value = Object.values(record);
    else ids.value.push(record.id);
    await request(`/resourceRoom/employmentService/batch/${ids.value}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'delete',
    });
    await flush();
  };

  const handleRowAction = async (action: any, record?: any) => {
    records.value = record;
    switch (action.key) {
      case 'viewRecord':
        viewRecordVisible.value = true;
        break;
      case 'edit':
        handleAdd();
        break;
      case 'tutoringProgram':
        handleAddPlan();
        break;
      case 'record':
        handleTutoringRecord();
        break;
      case 'delete':
        await deleteData(record);
        break;
      default:
        break;
    }
  };
  const loadingStudents = async () => {
    try {
      const res = await request('/resourceRoom/student', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'GET',
      });
      students.value = res.data.items;
    } catch (error) {
      /**/
    }
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/employmentService');
    await loadingStudents();
  });
</script>

<style scoped lang="scss"></style>
