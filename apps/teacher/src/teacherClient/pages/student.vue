<script setup lang="ts">
  import StudentList from '@repo/components/student/studentList.vue';
  import GradeClassSelect from '@repo/components/student/gradeClassSelect.vue';
  import { onMounted, ref } from 'vue';
  import { useUserStore } from '@repo/infrastructure/store';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const studentListRef = ref<any>(null);
  const currentClassInfo = ref<any>(undefined);
  const userStore = useUserStore();

  const additionalQueryParams = ref({});

  const handleGradeChange = (id, gradeClassInfo) => {
    studentListRef.value?.tableRef().resetPage();
    if (id === -1) {
      currentClassInfo.value = null;
      additionalQueryParams.value = {
        fromSubgroup: true,
        pageSize: 999,
      };
      studentListRef.value?.tableRef().loadData({
        ...additionalQueryParams.value,
      });
    } else {
      additionalQueryParams.value = {};
      currentClassInfo.value = gradeClassInfo;
      studentListRef.value?.tableRef().loadData({
        gradeClass: id,
      });
    }
  };
  const currentGroup = ref();
  const handleSelectGroup = (group: any) => {
    currentGroup.value = group;
    currentClassInfo.value = null;
    additionalQueryParams.value = {
      fromSubgroup: true,
      cluster: group?.id,
      pageSize: 999,
    };
    studentListRef.value?.tableRef().loadData({
      ...additionalQueryParams.value,
    });
  };

  const visibleColumns = ref([
    'name',
    'symbol',
    'nation',
    'fusionSchool',
    'gradeClass',
    'status',
    'gender',
    'age',
    'birthday',
    'disorders',
  ]);

  const { userInfo } = userStore;
  const groups = ref<any>();
  const loadGroupInfo = async () => {
    const { data } = await request('/resourceRoom/subgroup/findByIds', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'put',
      data: userInfo?.subgroupIds || [],
    });
    groups.value = data.items;
  };

  onMounted(async () => {
    await loadGroupInfo();
  });
</script>

<template>
  <student-list
    ref="studentListRef"
    :default-query-params="{
      from: 'teacher',
      orgNature: userStore.getUserNature(),
      ...additionalQueryParams,
    }"
    module-path="/teacher/student"
  >
    <template #extra-actions>
      <grade-class-select class="min-w-[120px]" @change="handleGradeChange" />
    </template>
    <template #table-action-append>
      <div v-if="currentClassInfo" class="py-2 flex gap-2 flex-wrap">
        <a-tag color="#165dff" size="small">
          {{ currentClassInfo.name }} 班主任: {{ currentClassInfo.managerUser?.name || '无' }}
        </a-tag>
        <a-tag v-for="item in currentClassInfo.teacherList" :key="item.id" color="arcoblue" size="small">
          {{ item.udf1 }}:
          {{ item.name }}
        </a-tag>
      </div>
      <div v-else-if="additionalQueryParams?.fromSubgroup" class="py-2 flex gap-2 flex-wrap cursor-pointer">
        <a-tag
          v-for="group in groups"
          :key="group?.id"
          :color="currentGroup?.id === group?.id ? '#168cff' : 'gray'"
          size="small"
          @click="handleSelectGroup(group)"
        >
          {{ group?.name }}
        </a-tag>
      </div>
    </template>
  </student-list>
</template>

<style scoped lang="scss"></style>
