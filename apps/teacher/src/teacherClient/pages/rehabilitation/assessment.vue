<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useUserStore } from '@repo/infrastructure/store';

  const schema = ref(null);
  const ready = ref(false);
  const { userInfo } = useUserStore();

  const queryParams = {
    sort: '-id',
  };

  const defaultEditData = ref({});

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/rehabilitation/rehabilitationAssessment');
    ready.value = true;
  });
</script>

<template>
  <table-with-modal-form
    v-if="schema && ready"
    module-name="康复评估"
    :schema="schema"
    :default-query-params="queryParams"
    :default-edit-value="defaultEditData"
    :visible-columns="['institution', 'student', 'assessmentDate', 'participants', 'attachments', 'createdBy']"
  />
</template>

<style scoped lang="scss"></style>
