<template>
  <!--=============-->
  <a-card v-if="schema">
    <template #title>
      <table-action
        v-if="tableRef"
        class="w-full"
        component-size="mini"
        :table="tableRef"
        :schema="schema"
        :visible-components="visibleComponents"
        @row-action="handleRowAction"
      >
        <template #title>{{ title }}</template>
        <template #extra-actions></template>
        <template #supplementary-button="{ size }">
          <a-button type="outline" :size="size" @click="handleShowSelectFusionSchool">新增督导评分</a-button>
        </template>
      </table-action>
    </template>
    <crud-table
      ref="tableRef"
      :schema="schema"
      class="mt-2"
      :row-class="getSubmittableRowClass"
      :default-query-params="{ sort: '-id' }"
      :visible-columns="columns"
      @row-action="handleRowAction"
    />
  </a-card>

  <a-modal :visible="newSupervisionRatingVisible" :closable="false" :footer="false" title="新建督导评分">
    <a-select placeholder="请选择学校" :options="fusionSchoolOptions" class="mb-2" @change="handelChangeFusionSchool" />
    <a-select placeholder="请选择指标" :options="evaluationIndicatorOptions" @change="handelChangeEvaluation" />
    <div class="flex justify-end items-end mt-2">
      <a-button size="mini" @click="handelCancel">取消</a-button>
      <a-button type="primary" size="mini" class="ml-2" @click="handelOk">确认</a-button>
    </div>
  </a-modal>
  <EvaluateVersion2
    v-model="evaluateVisible"
    :visible="evaluateVisible"
    :fusion-school="fusionSchool"
    :evaluation-indicator="evaluationIndicator"
    :type="'teacher'"
  />
  <!--=============-->
</template>

<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { CrudTable, TableAction } from '@repo/ui/components/table';
  import { getSubmittableRowClass } from '@repo/components/utils/collaborate';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { request } from '@repo/infrastructure/request';
  import EvaluateVersion2 from '@repo/components/resourceRoom/evaluateVersion2.vue';

  const tableRef = ref(null);
  const schema = ref(null);
  const visibleComponents = ref(['quickSearch', 'refresh', 'layout']);
  const records = ref();
  const newSupervisionRatingVisible = ref(false);
  const evaluateVisible = ref(false);

  const columns = [
    'id',
    'trainingType',
    'ageBracket',
    'student',
    'trainingTimes',
    'assessmentTimes',
    'disorders',
    'createdDate',
  ];
  const fusionSchool = ref();
  const fusionSchoolOptions = ref([]);
  const loadFusionSchool = async () => {
    const { data } = await request('/resourceCenter/fusionSchool', {
      params: {
        pageSize: 999,
        page: 1,
      },
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    if (data.items.length > 0) {
      data.items.forEach((item) => {
        fusionSchoolOptions.value.push({
          label: item.name,
          value: item.id,
        });
      });
    }
  };

  const evaluationIndicator = ref();
  const evaluationIndicatorOptions = ref([]);
  const loadEvaluationIndicators = async () => {
    try {
      const res = await request('/resourceCenter/evaluationCriterion/findByCompanyId', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
      });
      res.data.items.forEach((item) => {
        evaluationIndicatorOptions.value.push({
          label: item.name,
          value: item.id,
        });
      });
    } catch (e) {
      /**/
    }
  };

  const handelChangeFusionSchool = (val) => {
    if (val) {
      const item = fusionSchoolOptions.value.find((option) => option.value === val);
      fusionSchool.value = {
        name: item.label,
        id: item.value,
      };
    }
  };

  const handelChangeEvaluation = (val) => {
    if (val) {
      const item = evaluationIndicatorOptions.value.find((option) => option.value === val);
      evaluationIndicator.value = {
        name: item.label,
        id: item.value,
      };
    }
  };
  const handleShowSelectFusionSchool = () => {
    newSupervisionRatingVisible.value = true;
  };
  const handelCancel = () => {
    newSupervisionRatingVisible.value = false;
  };
  const handelOk = () => {
    // 保存一个新的 ... 评估学校+ 指标  || 现在的话其实是在发布指标的时候就创建当前管辖指标的对应评估数据，然后让教师填写
    newSupervisionRatingVisible.value = false;
    evaluateVisible.value = true;
  };

  const flush = async () => {
    setTimeout(() => {
      tableRef.value.loadData();
    }, 500);
  };

  const deleteData = async (record: any) => {
    const ids = ref<number[]>([]);
    if (Array.isArray(record)) ids.value = Object.values(record);
    else ids.value.push(record.id);
    // => 这里ids 需要处理成{1,2,3,4}
    await request(`/resourceRoom/attentionTrainingRecord/batch/${ids.value}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'delete',
    });
    await flush();
  };

  const handleRowAction = async (action: any, record?: any) => {
    records.value = record;
    switch (action.key) {
      case 'add':
        break;
      case 'edit':
        break;
      case 'view':
        break;
      case 'delete':
        // await deleteData(record);
        break;
      case 'training':
        break;
      case 'assessment':
        break;
      default:
        break;
    }
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/attentionTrainingRecord');
    await loadFusionSchool();
    await loadEvaluationIndicators();
  });
</script>
