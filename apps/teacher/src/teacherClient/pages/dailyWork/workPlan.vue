<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';

  const schema = ref(null);

  const queryParams = {
    sort: '-id',
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/teacher/teacherWorkPlan');
  });
</script>

<template>
  <table-with-modal-form
    v-if="schema"
    module-name="教师工作计划"
    :schema="schema"
    :default-query-params="queryParams"
    :visible-columns="['year', 'timePeriod', 'type', 'attachments', 'createdDate', 'createdBy']"
  />
</template>
