<template>
  <a-card v-if="schema">
    <template #title>
      <table-action
        v-if="tableRef"
        class="w-full"
        component-size="mini"
        :table="tableRef"
        :schema="schema"
        :visible-components="visibleComponents"
        @row-action="handleRowAction"
      >
        <template #title>行为管理</template>
        <template #extra-actions></template>
        <template #supplementary-button="{ size }">
          <AiButton tooltip="AI 行为分析" style="padding: 4px 8px 4px 8px" @ok="handleOk">
            <template #default>
              <span>AI 行为分析</span>
            </template>
          </AiButton>
          <a-button :size="size" type="primary" @click="handelAddEvent"> 新增事件</a-button>
          <a-button :size="size" @click="viewIntervention"> 行为干预记录</a-button>
        </template>
      </table-action>
    </template>
    <crud-table
      ref="tableRef"
      :schema="schema"
      class="mt-2"
      :row-class="getSubmittableRowClass"
      :default-query-params="{ sort: '-id' }"
      :visible-columns="columns"
      @row-action="handleRowAction"
    />
    <create-behavior-modal
      v-model="createVisible"
      :schema="schema"
      :records="records"
      @reset-record="resetRecord"
      @flush="flush"
    />
    <execute-policy-modal v-model="executePolicyVisible" :record="records" />
    <intervention-record-modal v-model="interventionVisible" />
    <start-diagnosis
      v-if="startDiagnosisVisible"
      v-model:visible="startDiagnosisVisible"
      :base-info="null"
      :records="records"
      @flush="flush"
    />
    <edit-behavior-analysis v-model="editBehaviorVisible" :record="records" @flush="flush" />
    <aiDialogBox
      ref="aiDialogRef"
      v-model:visible="chatModalVisible"
      :response="chatResponse"
      :session-id="sessionId"
      @send="handleChat"
      @reset="sessionId = ''"
    >
      <template #toolBar>
        <div class="flex justify-between items-center px-6 rounded-full bg-gray-100 py-2">
          <a-tooltip content="AI 对话分享二维码">
            <icon-qrcode size="15" class="hover:text-blue-400" @click="handleOpenShareQrc" />
          </a-tooltip>
        </div>
      </template>
    </aiDialogBox>
    <a-modal
      v-model:visible="aiChatQrcVisible"
      :footer="null"
      :closable="false"
      :width="360"
      centered
      class="qrcode-modal"
    >
      <template #title>
        <div class="modal-header">
          <span class="text-gray-600">扫码试用</span>
        </div>
      </template>

      <div class="flex justify-center">
        <div class="qrcode-card">
          <vue-qrcode
            :value="url"
            :options="{
              width: 220,
              color: {
                dark: '#1a1a1a',
                light: '#ffffff',
              },
            }"
          />
          <div class="w-full flex flex-col justify-center text-center text-gray-500">
            <p>请使用手机微信扫描二维码加入</p>
            <p>或点击按钮复制链接进行分享</p>
          </div>
          <a-button class="w-full mt-2" type="outline" size="mini" shape="round" @click="handleCopy">
            <template #icon>
              <icon-link />
            </template>
            复制链接
          </a-button>
        </div>
      </div>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
  import { CrudTable, TableAction } from '@repo/ui/components/table';
  import { computed, onMounted, ref, watch } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { getSubmittableRowClass } from '@repo/components/utils/collaborate';
  import InterventionRecordModal from '@/teacherClient/pages/teaching/teachingImpl/behavior/InterventionRecordModal.vue';
  import StartDiagnosis from '@/teacherClient/pages/teaching/teachingImpl/behavior/startDiagnosis.vue';
  import CreateBehaviorModal from '@/teacherClient/pages/teaching/teachingImpl/behavior/createBehaviorModal.vue';
  import ExecutePolicyModal from '@/teacherClient/pages/teaching/teachingImpl/behavior/executePolicyModal.vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import EditBehaviorAnalysis from '@/teacherClient/pages/teaching/teachingImpl/behavior/editBehaviorAnalysis.vue';
  import AiButton from '@repo/components/common/aiButton.vue';
  import aiDialogBox from '@repo/components/common/aiDialogBox.vue';
  import VueQrcode from '@chenfengyuan/vue-qrcode';
  import { getToken } from '@repo/infrastructure/auth';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@repo/infrastructure/store';
  import AiDialogBox from '@repo/components/common/aiDialogBox.vue';

  // const currentItem = ref(null);
  const schema = ref(null);
  const tableRef = ref(null);
  const userStore = useUserStore();
  const { userInfo } = userStore;

  const createVisible = ref(false);
  const interventionVisible = ref(false);
  const startDiagnosisVisible = ref(false);
  const executePolicyVisible = ref(false);
  const editBehaviorVisible = ref(false);
  const records = ref<Record<string, any> | null>(null);
  const columns = ['id', 'studentName', 'event', 'createdDate', 'status', 'collaborators', 'submitStatus'];

  const visibleComponents = ref(['quickSearch', 'refresh', 'layout']);

  const resetRecord = () => {
    records.value = null;
  };
  const flush = async () => {
    await tableRef.value.loadData();
  };
  const handelAddEvent = () => {
    records.value = null;
    createVisible.value = true;
  };
  const viewIntervention = () => {
    interventionVisible.value = true;
  };
  const deleteData = async (record) => {
    const ids = ref([]);
    if (Array.isArray(record)) ids.value = Object.values(record);
    else ids.value.push(record.id);
    await request(`/resourceRoom/behaviorRecordAnalysis/batch`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'delete',
      data: ids.value,
    });
    await flush();
  };
  const handleRowAction = async (action: any, record?: any) => {
    records.value = record;
    switch (action.key) {
      case 'view':
        break;
      case 'edit':
        editBehaviorVisible.value = true;
        break;
      case 'delete':
        await deleteData(record);
        break;
      case 'startDiagnosis':
        startDiagnosisVisible.value = true;
        break;
      case 'continueDiagnosis':
        startDiagnosisVisible.value = true;
        break;
      case 'addEvent':
        createVisible.value = true;
        break;
      case 'executePolicy':
        executePolicyVisible.value = true;
        break;
      default:
        break;
    }
  };

  const aiChatQrcVisible = ref(false);
  const handleOpenShareQrc = () => {
    aiChatQrcVisible.value = true;
  };
  const url = computed(() => {
    return `${PROJECT_URLS.MAIN_PROJECT}/module/aiChat.html?token=${getToken()}&shearUserId=${userInfo?.id}`;
  });
  const chatModalVisible = ref(false);
  const handleOk = () => {
    chatModalVisible.value = true;
  };

  const aiDialogRef = ref(null);

  const sessionId = ref();

  const chatResponse = ref('');

  // const requestWholeChat = async (msg: string) => {
  //   const { data: res } = await request(`/resourceRoom/behaviorAnalysis/chat/turns`, {
  //     baseURL: PROJECT_URLS.MAIN_PROJECT_API,
  //     timeout: 60000,
  //     method: 'put',
  //     headers: {
  //       'X-DashScope-SSE': 'enable',
  //     },
  //     params: {
  //       message: msg,
  //       sessionId,
  //     },
  //   });
  //   chatResponse.value = res;
  //   sessionId = res?.output?.sessionId || '';
  // };

  const handleCopy = () => {
    navigator.clipboard.writeText(url.value).then(() => {
      Message.clear('top');
      Message.success('已复制');
    });
  };

  const requestStreamChat = async (msg: string) => {
    chatResponse.value = '';
    const requestUrl = new URL(`${PROJECT_URLS.MAIN_PROJECT_API}/resourceRoom/behaviorAnalysis/chat/streamCall`);
    requestUrl.searchParams.append('message', msg);
    if (sessionId.value && sessionId.value !== '') requestUrl.searchParams.append('sessionId', sessionId.value);

    const response = await fetch(requestUrl.toString(), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Authorization': `Bearer ${getToken()}`,
        'LoginSource': 'PC',
        'Accept-Language': 'zh-CN',
        'X-User-Type': 'System',
        'Request-Client-Role': 'Company',
      },
    });

    const reader = response.body?.getReader();
    const decoder = new TextDecoder('utf-8');

    let partial = '';
    while (true) {
      // eslint-disable-next-line no-await-in-loop
      const { value, done } = await reader.read();
      if (done) {
        aiDialogRef.value?.saveChatHistory();
        aiDialogRef.value?.reset();
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      partial += chunk;

      const lines = partial.split('\n');

      partial = lines.pop() || '';

      // eslint-disable-next-line no-restricted-syntax
      for (const line of lines) {
        if (line.startsWith('data:')) {
          const jsonText = line.slice(5).trim();
          try {
            const json = JSON.parse(jsonText);
            const text = json.output?.text || '';
            sessionId.value = json.output?.sessionId || '';
            chatResponse.value += text;
          } catch (err) {
            console.error('⚠️ JSON 解析失败：', jsonText);
          }
        }
      }
    }
  };

  const handleChat = async (msg: string) => {
    // await requestWholeChat(msg);
    await requestStreamChat(msg);
  };

  onMounted(async () => {
    sessionId.value = localStorage.getItem(`behavior_chat_sessionId`) || '';
    schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/behaviorRecordAnalysis');
  });

  watch(
    () => sessionId.value,
    (newVal) => {
      if (newVal) {
        localStorage.setItem(`behavior_chat_sessionId`, newVal);
      }
    },
    { deep: true },
  );
</script>

<style lang="scss" scoped>
  @use '@/assets/module/submittableRowColorful.scss' as *;
</style>
