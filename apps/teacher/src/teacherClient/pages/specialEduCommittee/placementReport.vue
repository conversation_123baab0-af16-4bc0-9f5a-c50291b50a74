<template>
  <a-card v-if="schema">
    <template #title>
      <table-action
        v-if="tableRef"
        class="w-full"
        component-size="mini"
        :table="tableRef"
        :schema="schema"
        @row-action="handleRowAction"
      >
        <template #title>安置报告</template>
      </table-action>
    </template>
    <crud-table
      ref="tableRef"
      :schema="schema"
      class="mt-2"
      :default-query-params="{ sort: '-id', notImportedFromExternal: '1' }"
      :visible-columns="columns"
      @row-action="handleRowAction"
    />
    <add v-if="createVisible" v-model:visible="createVisible" :record="currentItem" @flush="handleFlush" />
    <View v-if="viewVisible" v-model:visible="viewVisible" :record="currentItem" :schema="schema" />
  </a-card>
</template>

<script setup lang="ts">
  import { CrudTable, TableAction } from '@repo/ui/components/table';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { getSubmittableRowClass } from '@repo/components/utils/collaborate';
  import add from '@/teacherClient/components/placementReport/add.vue';
  import View from '@/teacherClient/components/placementReport/view.vue';

  const chartVisible = ref(false);
  const currentItem = ref(null);
  const schema = ref(null);
  const tableRef = ref(null);
  const tasksRef = ref(null);
  const createVisible = ref(false);
  const viewVisible = ref(false);
  const columns = ['student', 'student.age', 'student', 'student.status', 'createdDate', 'createdBy'];

  const handleFlush = async () => {
    await tableRef.value.loadData();
  };

  const handleRowAction = async (action: any, record?: any) => {
    let task: any;
    currentItem.value = record;
    switch (action.key) {
      case 'add':
      case 'edit':
        createVisible.value = true;
        break;
      case 'viewReport':
        viewVisible.value = true;
        break;
      case 'delete':
        task = tasksRef.value.tasks?.find((item) => item.resultIds.includes(record.id));
        if (task) {
          tasksRef.value.handleSetFinished(task);
        }
        break;
      default:
        break;
    }
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/specialCommittee/placementReport');
  });
</script>

<style lang="scss" scoped>
  @use '@/assets/module/submittableRowColorful.scss' as *;
</style>
