<template>
  <a-card v-if="schema">
    <template #title>
      <table-action
        v-if="tableRef"
        class="w-full"
        component-size="mini"
        :table="tableRef"
        :schema="schema"
        :visible-components="visibleComponents"
        @row-action="handleRowAction"
      >
        <template #title>{{ title }}</template>
        <template #extra-actions></template>
        <template #supplementary-button="{ size }">
          <a-button type="primary" :size="size" @click="handleAdd"
            ><template #icon><icon-plus /></template>新增</a-button
          >
        </template>
      </table-action>
    </template>
    <crud-table
      ref="tableRef"
      :schema="schema"
      class="mt-2"
      :default-query-params="{ sort: '-id' }"
      :visible-columns="columns"
      @row-action="handleRowAction"
    >
    </crud-table>
  </a-card>
  <ScoringSystem
    v-if="scoringSystemVisible"
    v-model="scoringSystemVisible"
    :visible="scoringSystemVisible"
    :students="students"
    @update:model-value="flush"
  />
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';

  import ScoringSystem from '@/teacherClient/components/screeningEvaluation/ScoringSystem.vue';

  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { CrudTable, TableAction } from '@repo/ui/components/table';
  import { SchemaHelper } from '@repo/infrastructure/schema';

  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    trainingType: {
      type: String,
      default: '',
    },
  });

  const columns = ['student', 'gender', 'age', 'disorders', 'score', 'state', 'createdDate'];
  const schema = ref(null);
  const tableRef = ref(null);
  const visibleComponents = ref(['quickSearch', 'refresh', 'layout']);
  const scoringSystemVisible = ref(false);

  const records = ref<Record<string, any>>();
  const students = ref([]);

  const flush = async () => {
    await tableRef.value.loadData();
  };
  const handleAdd = () => {
    scoringSystemVisible.value = true;
  };

  const deleteData = async (record: any) => {
    const ids = ref<number[]>([]);
    if (Array.isArray(record)) ids.value = Object.values(record);
    else ids.value.push(record.id);
    await request(`/resourceRoom/neuralDiscrimination/batch/${ids.value}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'delete',
    });
    await flush();
  };

  const handleRowAction = async (action: any, record?: any) => {
    records.value = record;
    switch (action.key) {
      case 'view':
        break;
      case 'delete':
        await deleteData(record);
        break;
      default:
        break;
    }
  };
  const loadingStudents = async () => {
    try {
      const res = await request('/resourceRoom/student', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'GET',
      });
      students.value = res.data.items;
    } catch (error) {
      /**/
    }
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/neuralDiscrimination');
    await loadingStudents();
  });
</script>

<style scoped lang="scss"></style>
