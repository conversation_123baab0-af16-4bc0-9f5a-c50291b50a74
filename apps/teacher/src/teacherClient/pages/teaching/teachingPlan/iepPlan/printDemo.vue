<script setup lang="ts">
  import { computed, inject, onMounted, PropType, ref } from 'vue';

  const props = defineProps({
    record: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const defaultEditData = {
    healthSelfCare: {},
    mainDiversion: '',
    eduAnalysis: {},
    development: {},
    behavioralChallenges: '',
  };

  const emits = defineEmits(['update:record']);
  const modelValue = ref(null);

  const SchemaHelper = inject('SchemaHelper');
  const request = inject('request');
  const PROJECT_URLS = inject('PROJECT_URLS');
  const CrudForm = inject('CrudForm');
  const cloneDeep = inject('cloneDeep');
  const extend = inject('extend');

  const schema = ref<any>(null);

  const OPTIONS_CONFIG = {
    isNeedOption: ['无需', '需要'],
    isNormalOption: ['正常', '异常'],
    isDifficultyOption: ['较好', '困难'],
    isGoodOption: ['良好', '不佳'],
    healthStatusOptions: ['健康，很少生病', '心理状况与同龄人相当', '较常生病', '体弱多病（常缺席）'],
    othersOptions: ['依医生指示固定用药', '目前至医疗院所接受固定治疗', '使用辅具', '其他'],
    studyOptions: [
      '学习能力与一般学生相当或良好',
      '学习速度明显比同龄人缓慢',
      '注意力短暂，不易持续专心于任何活动',
      '容易受干扰而分心',
      '固执化，做某一事时不管其他目标',
      '可以记住刚学的东西，几天后容易忘记',
      '记不住当天老师或父母的交代',
      '说话或做事凌乱，没有重点与组织',
      '对抽象或较复杂符号或词汇的理解能力差',
      '推理能力弱',
      '迁移能力弱',
    ],
    communicateAndUnderstand: [
      '听话理解能力差，常抓不到他人说话重点',
      '听懂日常生活词语，但无法理解抽象内容',
      '大部分话语须加上手势或动作才能理解',
      '无法理解他人说话，只能仿说',
      '容易误解别人说话的意思',
    ],
    communicateAndExpress: [
      '说话不清楚、构音差，一般人不容易听得懂',
      '说话高低音、音质差、音量大小失调',
      '口吃或说话费力',
      '口语能力表达差，无法清楚交代事情',
      '回答问题所使用的词汇，简短有限',
      '经常重复简单的词汇或短句',
      '不会主动表达自己的需求',
    ],
    otherCommunicateWay: ['无口语，可使用肢体、手势沟通等', '使用替代性沟通辅具', '其他'],
    action: [
      '能独立行动',
      '动作协调能力良好',
      '动作协调能力稍有问题，但并不影响学习',
      '使用拐杖、支架、轮椅等辅助器具后，并不影响学习',
    ],
    otherAction: [
      '手眼协调能力弱（例如：写字能不能写进田字格；涂色超出格子等）',
      '视动协调能力弱（例如：走路容易摔跤）',
      '双侧协调能力弱（例如：做操同手同脚）',
      '空间方向辨识有困难',
      '操作工具(执笔、剪刀等)有困难',
      '独自行动有问题，动作协调差，需要行动训练或复健',
      '没有行动和动作协调能力，严重影响生活或学习，需要他人长期协助或复健训练',
    ],
  };
  const abilityItems = {
    healthSelfCare: {
      key: 'healthSelfCare',
      type: 'radio',
      title: '健康与自理',
      items: [
        { key: 'healthStatus', type: 'radio', label: '健康状况', optionKey: 'healthStatusOptions' },
        { key: 'sensoryPerception', type: 'radio', label: '感官知觉', optionKey: 'isNormalOption' },
        { key: 'grossMovements', type: 'radio', label: '粗大动作', optionKey: 'isNormalOption' },
        { key: 'fineMotor', type: 'radio', label: '精细动作', optionKey: 'isNormalOption' },
        { key: 'selfCare', type: 'radio', label: '生活自理', optionKey: 'isNormalOption' },
        { key: 'others', type: 'checkbox', label: '其他选项', optionKey: 'othersOptions' },
      ],
    },
    cognition: {
      key: 'cognition',
      type: 'radio',
      title: '认知',
      items: [{ key: 'studyStatus', type: 'checkbox', label: '学习能力', optionKey: 'studyOptions' }],
    },
    communicate: {
      key: 'communicate',
      type: 'radio',
      title: '沟通能力',
      items: [
        { key: 'studyStatus', type: 'checkbox', label: '优势', options: ['语言表达能力良好'] },
        {
          key: 'communicateLevel',
          type: 'title',
          label: '限制',
          children: [
            {
              key: 'communicateAndUnderstand',
              type: 'checkbox',
              label: '沟通理解',
              optionKey: 'communicateAndUnderstand',
            },
            { key: 'communicateAndExpress', type: 'checkbox', label: '沟通表达', optionKey: 'communicateAndExpress' },
            { key: 'otherCommunicateWay', type: 'checkbox', label: '其他沟通方式', optionKey: 'otherCommunicateWay' },
          ],
        },
      ],
    },
    society: {
      key: 'society',
      type: 'radio',
      title: '社会适应',
      items: [
        { key: 'emotionUnderstand', type: 'radio', hasRemark: true, label: '情绪理解', optionKey: 'isNormalOption' },
        { key: 'emotionExpress', type: 'radio', hasRemark: true, label: '情绪表达', optionKey: 'isDifficultyOption' },
        { key: 'emotionControl', type: 'radio', hasRemark: true, label: '情绪控制', optionKey: 'isDifficultyOption' },
        { key: 'interpersonal', type: 'radio', hasRemark: true, label: '人际关系', optionKey: 'isNormalOption' },
        { key: 'adaptation', type: 'radio', hasRemark: true, label: '环境适应', optionKey: 'isNormalOption' },
        { key: 'ruleFollow', type: 'radio', hasRemark: true, label: '规则遵守', optionKey: 'isNormalOption' },
        { key: 'character', type: 'radio', hasRemark: true, label: '性格与品格', optionKey: 'isGoodOption' },
        { key: 'problemSolve', type: 'radio', hasRemark: true, label: '问题解决', optionKey: 'isGoodOption' },
        { key: 'hobbies', type: 'radio', hasRemark: true, label: '兴趣爱好', optionKey: 'isGoodOption' },
      ],
    },
    action: {
      key: 'action',
      type: 'radio',
      title: '知觉动作',
      items: [
        { key: 'perceptionActionChecked', type: 'radio', label: '', optionKey: 'action' },
        { key: 'otherActionExplanation', type: 'radio', label: '其他表现说明', optionKey: 'otherAction' },
      ],
    },
  };

  const lessonItems = [
    { name: '语文', key: 'chinese' },
    { name: '数学', key: 'math' },
    { name: '体育', key: 'port' },
    { name: '艺术', key: 'art' },
    { name: '劳动', key: 'labor' },
  ];

  const analysis = {
    mainDiversion: {
      label: '主要障碍',
      key: 'mainDiversion',
    },
    eduAnalysis: {
      label: '教育康复需求分析',
      key: 'eduAnalysis',
      items: [
        { label: '健康与自理', key: 'healthSelfCare' },
        { label: '认知', key: 'cognition' },
        { label: '沟通能力', key: 'communicationSkills' },
        { label: '社会适应', key: 'socialAdaptation' },
        { label: '知觉动作', key: 'perceptionAction' },
        { label: '语文', key: 'language' },
        { label: '数学', key: 'mathematics' },
        { label: '体育', key: 'physicalEducation' },
        { label: '艺术', key: 'art' },
        { label: '劳动', key: 'labor' },
      ],
    },
    development: {
      label: '学习优势与发展潜力',
      key: 'learningStrengthsAndPotential',
    },
    behavioralChallenges: {
      label: '挑战性行为',
      key: 'behavioralChallenges',
    },
  };

  onMounted(async () => {});

  const baseInfoItems = [
    { label: '学生', key: 'student.name', span: 1 },
    { label: '性别', key: 'student.gender', span: 1 },
    { label: '年龄', key: 'student.age', span: 1 },
    { label: '障碍类型', key: 'student.disorders', span: 1 },
    { label: '安置形式', key: 'additionalData.placementType', span: 1 },
  ];

  const familyCondition = [
    {
      label: '居住条件',
      key: 'livingCondition',
      options: [
        { label: '与奶奶住一屋', value: 1 },
        { label: '有独立寝室', value: 2 },
        { label: '有起居室 ', value: 3 },
        { label: '有独立客厅', value: 4 },
        { label: '有阳台', value: 5 },
        { label: '有独立餐厅', value: 6 },
        { label: '有学习空间 ', value: 7 },
        { label: '有自己的活动空间', value: 8 },
      ],
      span: 2,
    },
    {
      label: '住房类型',
      key: 'livingHouseType',
      options: [
        { label: '平房', value: 1 },
        { label: '楼房 ', value: 2 },
        { label: '自建房  ', value: 3 },
        { label: '其他', value: 4 },
      ],
      span: 2,
    },
    {
      label: '居住小环境',
      key: 'smallLivingEnv',
      options: [
        { label: '绿地', value: 1 },
        { label: '休闲地', value: 2 },
        { label: '临街', value: 3 },
        { label: '其他', value: 4 },
      ],
      span: 2,
    },
    {
      label: '居住大环境',
      key: 'livingEnv',
      options: [
        { label: '市区', value: 1 },
        { label: '城郊', value: 2 },
        { label: '农村', value: 3 },
        { label: '其他', value: 4 },
      ],
      span: 2,
    },
    {
      label: '社区环境',
      key: 'communityEnv',
      options: [
        { label: '健身设施', value: 1 },
        { label: '医疗', value: 2 },
        { label: '超市', value: 3 },
        { label: '其他', value: 4 },
      ],
      span: 2,
    },
    { label: '教养方式', key: 'teachingType', span: 2 },
    { label: '家庭常用语言', key: 'firstLanguage', span: 2 },
    { label: '生长史', key: 'growthHistory', span: 2 },
    { label: '教育康复史', key: 'educationalHistory', span: 2 },
  ];

  const getDataByKey = (item: any, keyItem: any): any => {
    const keys: string[] = keyItem.key.split('.');
    let data = keys.reduce((acc, key) => (acc && acc[key] !== undefined ? acc[key] : undefined), item);
    if (keyItem?.options || keyItem.optionKey) {
      const option = keyItem?.options || OPTIONS_CONFIG[keyItem.optionKey];
      const type = typeof data;
      if (type === 'number') {
        data = [data];
      }
      const select = option.filter((op, index) => data?.includes(index + 1));

      return (
        select
          ?.map((se: any) => {
            return se?.label || se;
          })
          .join('，') || data
      );
    }
    return data;
  };
  const VuePrintNext = inject('VuePrintNext');
  const handlePrint = async () => {
    try {
      // eslint-disable-next-line no-new
      new VuePrintNext({
        el: `#print-area`,
        popTitle: '报告',
        zIndex: 9999,
        printMode: 'popup',
        hide: '.no-print',
      });
    } catch (error) {
      /**/
    }
  };
  defineExpose({
    handlePrint,
  });
</script>

<template>
  <div id="print-area" style="width: 208mm; margin: 0 auto; display: flex; flex-direction: column">
    <a-descriptions :column="4" title="基本信息" bordered>
      <a-descriptions-item
        v-for="(baseInfoItem, baseIndex) in baseInfoItems"
        :key="baseIndex"
        :label="baseInfoItem.label"
        :span="baseInfoItem.span || 1"
      >
        {{ getDataByKey(record, baseInfoItem) }}
      </a-descriptions-item>
      <a-descriptions-item label="家庭成员" :span="4">
        <span v-for="(member, index) in record?.familyMembers" :key="index" class="mr-2">
          {{ `${member?.name}（${member?.relationship}）` }}
        </span>
      </a-descriptions-item>
    </a-descriptions>

    <a-descriptions :column="4" title="家庭情况" bordered>
      <a-descriptions-item
        v-for="(conditionItem, conditionIndex) in familyCondition"
        :key="conditionIndex"
        :label="conditionItem.label"
        :span="conditionItem?.span || 1"
      >
        {{ getDataByKey(record?.additionalData, conditionItem) }}
      </a-descriptions-item>

      <a-descriptions-item v-if="false" label="  家长期望与需求" :span="2">
        {{ record.student.diversion }}
      </a-descriptions-item>
    </a-descriptions>
    <a-descriptions title="" bordered :column="4" class="mt-6">
      <a-descriptions-item :span="4" layout="inline-vertical" label="能力现状分析">
        <div class="border flex justify-center items-center">
          <div class="flex justify-center items-center w-1/5">领域</div>
          <div class="flex justify-center items-center w-2/5">现状描述</div>
          <div class="flex justify-center items-center w-2/5">优弱势分析</div>
        </div>
        <div v-for="(ability, abilityIndex) in abilityItems" :key="abilityIndex" class="flex justify-start">
          <div class="w-1/5 border flex items-center justify-center">{{ ability.title }}</div>
          <div class="w-2/5 border p-2">
            <div
              v-for="(abilityIts, itemsIndex) in ability.items"
              :key="itemsIndex"
              class="p-1 bg-gray-50 rounded mb-2"
            >
              {{ abilityIts.label }}:&nbsp;&nbsp; {{ getDataByKey(record?.additionalData[ability.key], abilityIts) }}
            </div>
          </div>
          <div class="w-2/5 border p-2 flex flex-col justify-center">
            <div> 优势：{{ record?.additionalData[ability.key]?.advantages }}</div>
            <div> 弱势：{{ record?.additionalData[ability.key]?.weak }}</div>
          </div>
        </div>
      </a-descriptions-item>
    </a-descriptions>
    <a-descriptions title="" bordered :column="4">
      <a-descriptions-item :span="4" label="学业水平分析">
        <div v-for="(lesson, lessonIndex) in lessonItems" :key="lessonIndex">
          <div class="font-bold mb-2 mt-4">{{ lesson.name }}</div>
          <div class="border flex justify-center items-center p-2">
            <div class="w-1/5 flex justify-center items-center">领域</div>
            <div class="w-2/5 flex justify-center items-center">能力现状</div>
            <div class="w-2/5 flex justify-center items-center">优弱势分析</div>
          </div>
          <div class="border">
            <div v-if="record?.additionalData[lesson.key]?.length">
              <div
                v-for="(lessonDetails, lesDetailsIndex) in record?.additionalData[lesson.key]"
                :key="lesDetailsIndex"
                class="flex justify-start"
              >
                <div class="w-1/5 border flex justify-center items-center p-2">{{ lessonDetails?.field }}</div>
                <div class="w-2/5 border flex justify-center items-start p-2">{{ lessonDetails?.ability }}</div>
                <div class="w-2/5 border flex justify-center items-start p-2">{{ lessonDetails?.status }}</div>
              </div>
            </div>
            <a-empty v-else />
          </div>
        </div>
      </a-descriptions-item>
    </a-descriptions>
    <a-descriptions :column="4" bordered title="综合分析/探讨">
      <a-descriptions-item v-for="(item, analysisIndex) in analysis" :key="analysisIndex" :span="4" :label="item.label">
        <div v-if="!item.items">
          {{ record.additionalData[item.key] }}
        </div>
        <div v-else>
          <div v-for="(analysisItem, analysisIdx) in item.items" :key="analysisIdx" class="p-1 bg-gray-50 rounded mb-2">
            <span class="mr-2">{{ `${analysisItem.label}：` }}</span>
            <span>{{ record?.additionalData?.[item.key]?.[analysisItem.key] }}</span>
          </div>
        </div>
      </a-descriptions-item>
    </a-descriptions>
  </div>
</template>

<style scoped lang="scss"></style>
