<script setup lang="ts">
  import { Line } from '@antv/g2plot';
  import { VuePrintNext } from 'vue-print-next';
  import { ref, onMounted, computed, onBeforeUnmount } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });
  const emits = defineEmits(['update:modelValue']);
  const bodyStyle = {
    height: '100%',
    overflow: 'auto',
  };
  const size = 'mini';

  const handlePrint = () => {
    try {
      // eslint-disable-next-line no-new
      new VuePrintNext({
        el: `#print-area`,
        popTitle: '报告',
        zIndex: 9999,
        printMode: 'popup',
        hide: '.no-print',
      });
    } catch (error) {
      /**/
    }
  };
  const filterParams = ref<Record<string, any>>({
    time: '按天看',
  });
  const cycleOptions = [
    { label: '按天看', value: '按天看' },
    { label: '按周看', value: '按周看' },
    { label: '按月看', value: '按月看' },
  ];

  const resetData = () => {
    filterParams.value = {
      time: '按天看',
      hopeEvent: null,
    };
  };
  const handlePreOk = () => {
    resetData();
    emits('update:modelValue', false);
  };
  const handleCancel = () => {
    resetData();
    emits('update:modelValue', false);
  };
  const students = ref();
  const loadStudents = async () => {
    try {
      const res = await request('/resourceRoom/student', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'GET',
      });
      students.value = res.data.items;
      filterParams.value.studentId = students.value[0].id;
    } catch (error) {
      /**/
    }
  };
  const searchData = ref<Record<string, any>>({});
  let line: Line | null = null;

  const averages = ref<Record<string, any>>({});
  const average = (items: any) => {
    averages.value = {};
    averages.value = items.reduce((acc, item) => {
      if (!acc[item.stage]) {
        acc[item.stage] = [];
        acc[item.stage].totalRate = 0;
        acc[item.stage].totalHopeRate = 0;
        acc[item.stage].length = 0;
      }
      acc[item.stage].push(item);
      acc[item.stage].totalRate += item.rate;
      acc[item.stage].totalHopeRate += item.hopeRate;
      acc[item.stage].length += 1;
      return acc;
    }, {});
  };
  const average2 = (items: any) => {
    if (!Array.isArray(items)) return;
    averages.value = {};
    items.forEach((item) => {
      const stage = item.stage.split('-');
      if (!averages.value[stage[0]]) {
        averages.value[stage[0]] = {
          totalRate: 0,
          totalHopeRate: 0,
          length: 0,
        };
      }
      if (stage[1] === '问题行为') {
        averages.value[stage[0]].totalRate += item.rate;
      } else averages.value[stage[0]].totalHopeRate += item.rate;

      averages.value[stage[0]].length += 1;
    });
  };

  const lines = (list: any) => {
    const data = ref([]);
    /*    if (Array.isArray(list) && list.length > 0) {
      data.value = list.flatMap((item) => {
        const { time, rate, hopeRate, stage } = item;
        return [
          { time, rate: hopeRate, stage: `${stage}-问题行为` },
          { time, rate, stage: `${stage}-目标行为` },
        ];
      });
      console.log(data.value);
      average(list);
    } */
    if (!Array.isArray(list)) return;
    average2(list);
    if (line !== null) line.destroy();
    line = new Line('container', {
      // data: data.value,
      data: list,
      xField: 'time',
      yField: 'rate',
      seriesField: 'stage',

      color: ['#1979C9', '#D62A0D', '#FAA219', '#8e2ab8', '#62ff83', '#ff6090'],
    });
    line.render();
  };
  const unit = ref(filterParams.value.time.substring(1, 2));

  const search = async () => {
    await request('/resourceRoom/behaviorRecordAnalysis/statisticalAnalysis', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'put',
      data: filterParams.value,
    }).then((res) => {
      searchData.value = res.data;
      // eslint-disable-next-line prefer-destructuring
      filterParams.value.hopeEvent = Object.keys(res.data)[0];
      lines(Object.values(res.data)[0]);
      unit.value = filterParams.value.time.substring(1, 2);
    });
  };
  const handleChange = (val: string) => {
    lines(searchData.value[val]);
  };
  const handleClear = (type: string) => {
    if (type === 'student') delete filterParams.value.studentId;
    if (type === 'hopeEvent') delete filterParams.value.hopeEvent;
  };
  onMounted(async () => {
    await loadStudents();
    await search();
  });
  onBeforeUnmount(() => {
    if (line !== null) line.destroy();
  });

  const stages = [
    { label: '基线期', key: 'totalRate' },
    { label: '干预期', key: 'totalHopeRate' },
    { label: '维持期', key: 'totalRate' },
  ];
  const cardClass = 'bg-gray-50 rounded w-60 h-full inline-block border border-slate-100 shadow hover:shadow-md';
</script>

<template>
  <a-modal
    title="综合干预统计"
    :closable="false"
    fullscreen
    :visible="visible"
    :on-before-ok="handlePreOk"
    :body-style="bodyStyle"
    @cancel="handleCancel"
  >
    <div id="print-area" class="w-full h-full p-2">
      <div class="flex items-center gap-5 mb-2">
        <a-select
          v-model="filterParams.studentId"
          :size="size"
          placeholder="选择查看学生"
          class="flex-1 max-w-44"
          allow-search
          @change="filterParams.hopeEvent = null"
          @clear="handleClear('student')"
        >
          <a-option v-for="student in students" :key="student.id" :value="student.id">{{ student.name }}</a-option>
        </a-select>
        <a-range-picker v-model="filterParams.range" class="flex-1 max-w-80" :size="size" />
        <a-select
          v-model="filterParams.time"
          :size="size"
          placeholder="请选择查看周期"
          class="flex-1 max-w-44"
          :options="cycleOptions"
        />
        <a-select
          v-model="filterParams.hopeEvent"
          :size="size"
          placeholder="按目标行为"
          class="flex-1 max-w-80"
          allow-clear
          allow-search
          @clear="handleClear('hopeEvent')"
          @change="handleChange"
        >
          <a-option v-for="(behavior, index) in Object.keys(searchData)" :key="index" :value="behavior">
            {{ behavior }}
          </a-option>
        </a-select>
        <a-button class="ml-2" :size="size" type="outline" @click="search">
          <icon-search />
          搜索
        </a-button>
        <a-button class="ml-2" :size="size" type="outline" @click="handlePrint">
          <icon-export />
          导出
        </a-button>
        <div class="flex-1"></div>
      </div>
      <div id="container" class="w-full h-4/5 pb-5"></div>

      <div class="w-full pb-2 p-5 pl-20 pr-20 flex justify-between h-32">
        <div v-for="(i, index) in stages" :key="index" :class="cardClass" class="p-3">
          <div class="font-bold">{{ i.label }}</div>
          <div
            v-for="(title, num) in ['问题行为平均次数:', '目标行为平均次数:']"
            :key="num"
            class="text-gray-500 text-xs mt-1 font-bold"
          >
            {{ title }}&nbsp;&nbsp;
            {{
              averages[i.label]?.[i.key] ? ((averages[i.label]?.[i.key] / averages[i.label]?.length) * 2).toFixed(1) : 0
            }}
            次 &nbsp;/{{ unit }}
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
