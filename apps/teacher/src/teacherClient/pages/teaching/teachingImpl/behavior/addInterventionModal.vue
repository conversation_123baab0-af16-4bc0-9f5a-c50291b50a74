<script setup lang="ts">
  import { ref, watch, onMounted } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import dayjs from 'dayjs';

  const props = defineProps({
    record: {
      type: Object,
    },
    currentRecord: {
      type: Object,
    },
    addExecuteVisible: {
      type: Boolean,
    },
  });

  const emit = defineEmits(['update']);

  const excuteEntity = ref({
    braId: props.record?.id,
    birsId: props.currentRecord?.id,
    studentId: props.record?.studentId,
    studentName: props.record?.studentName,
    targetBehavior: props.currentRecord?.hopeTarget,
    place: '',
    timeDate: '',
    happenTimes: 1,
    hopeHappenTimes: 1,
    executeInfo: '',
  });
  /*  watch(
    () => props.record,
    async (newRecord) => {
      if (newRecord) {
        excuteEntity.value.braId = newRecord.id || '';
        excuteEntity.value.studentId = newRecord.studentId || '';
        excuteEntity.value.studentName = newRecord.studentName || '';
      }
    },
  );

  watch(
    () => props.currentRecord,
    (newCurrentRecord) => {
      if (newCurrentRecord) {
        excuteEntity.value.birsId = newCurrentRecord.id || '';
        excuteEntity.value.targetBehavior = newCurrentRecord.hopeTarget || '';
      }
    },
  ); */

  const resetData = () => {
    excuteEntity.value.place = '';
    excuteEntity.value.timeDate = '';
    excuteEntity.value.happenTimes = 1;
    excuteEntity.value.executeInfo = '';
  };

  const handelOk = () => {
    try {
      request(`/resourceRoom/behavioralInterventionRecord`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'post',
        data: excuteEntity.value,
      });
      Message.success('提交成功');
      resetData();
      emit('update', false);
    } catch (e) {
      // Message.error(e.message);
    }
  };

  const handelCancel = () => {
    resetData();
    emit('update', false);
  };
  const disabledDate = (current) => {
    // '[]' 表示闭区间
    // 返回 true 表示该日期禁用
    return !dayjs(current).isBetween(props.currentRecord?.timeRange[0], props.currentRecord?.timeRange[1], null, '[]');
  };
  const markedDates = ref([]);
  const highlightStyle = {
    border: '1px solid rgb(var(--arcoblue-6))',
  };
  const getCellStyle = (date) => {
    const time = dayjs(date).format('YYYY-MM-DD');
    return markedDates.value.includes(time) ? highlightStyle : {};
  };
  const loadHighlightDate = async () => {
    if (props.record?.id)
      await request(`/resourceRoom/behavioralInterventionRecord/getHighlightTime`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
        params: {
          targetBehavior: props.currentRecord.hopeTarget,
          braid: props.record?.id,
        },
      }).then((res) => {
        markedDates.value = res.data.items;
      });
  };

  onMounted(async () => {
    await loadHighlightDate();
  });
</script>

<template>
  <a-modal
    v-if="addExecuteVisible"
    :visible="addExecuteVisible"
    ok-text="提交"
    :closable="false"
    :on-before-ok="handelOk"
    @cancel="handelCancel"
  >
    <a-card title="目标策略方法信息">
      <a-form class="space-y-4">
        <a-form-item label="目标行为">
          <a-input :model-value="currentRecord?.hopeTarget" :disabled="true" />
        </a-form-item>
        <a-form-item label="策略">
          <a-input :model-value="currentRecord?.strategyContent" :disabled="true" />
        </a-form-item>
        <a-form-item label="方法">
          <a-input :model-value="currentRecord?.strategyMethods" :disabled="true" />
        </a-form-item>
        <a-divider />
        <a-form-item label="时间">
          <a-date-picker v-model="excuteEntity.timeDate" style="width: 200px" :disabled-date="disabledDate">
            <template #cell="{ date }">
              <div class="arco-picker-date">
                <div class="arco-picker-date-value" :style="getCellStyle(date)">
                  {{ date.getDate() }}
                </div>
              </div>
            </template>
          </a-date-picker>
        </a-form-item>
        <a-form-item label="目标行为" class="flex items-center">
          <a-input-number v-model="excuteEntity.happenTimes" min="1" class="flex-1 mr-2" />
          <span>次/天</span>
        </a-form-item>
        <a-form-item label="问题行为" class="flex items-center">
          <a-input-number v-model="excuteEntity.hopeHappenTimes" min="1" class="flex-1 mr-2" />
          <span>次/天</span>
        </a-form-item>
        <a-form-item label="地点">
          <a-input v-model="excuteEntity.place" placeholder="请输入地点" />
        </a-form-item>
        <a-form-item label="执行策略">
          <a-textarea v-model="excuteEntity.executeInfo" placeholder="请输入策略" />
        </a-form-item>
      </a-form>
    </a-card>
  </a-modal>
</template>

<style scoped lang="scss"></style>
