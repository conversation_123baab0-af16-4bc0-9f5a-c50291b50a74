<script setup lang="ts">
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { ref, computed } from 'vue';

  const props = defineProps({
    selectVisible: Boolean,
    type: {
      type: String,
    },
  });
  const emits = defineEmits(['handelOk', 'handelCancel']);

  const paths = {
    happenEvent: '/resourceRoom/BehaviorProblemDict',
    hopeEvent: '/resourceRoom/behaviorTargetDict',
    strategy: '/resourceRoom/behaviorStrategyDict/findAll',
  };
  const selectData = ref([]);
  const type = computed(() => props.type);
  const handelOpen = async () => {
    await request(paths[type.value], {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    })
      .then((res) => {
        selectData.value = res.data.items;
        console.log(selectData.value);
      })
      .catch((error) => {
        /**/
      });
  };

  const columns = computed(() => {
    if (type.value === 'hopeEvent') {
      return [
        { title: '分类', dataIndex: 'targetType' },
        { title: '内容', dataIndex: 'targetContent' },
        { title: '创建时间', dataIndex: 'createdDate' },
      ];
    }
    if (type.value === 'happenEvent') {
      return [
        { title: '分类', dataIndex: 'problemType' },
        { title: '内容', dataIndex: 'problemContent' },
        { title: '创建时间', dataIndex: 'createdDate' },
      ];
    }
    return [
      { title: '分类', dataIndex: 'type', width: 240 },
      { title: '内容', dataIndex: 'strategyDictContent', slotName: 'strategyDictContent' },
      { title: '操作', slotName: 'operation', width: 180, align: 'center' },
    ];
  });

  const selectedKeys = ref([]);

  const rowSelection = ref({
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
  });

  const selectedRows = computed(() => {
    return selectData.value.filter((item) => selectedKeys.value.includes(item.id));
  });

  const resetData = () => {
    selectData.value = [];
    selectedKeys.value = [];
  };

  const handelOk = () => {
    emits('handelOk', selectedRows.value);
    resetData();
  };
  const handelCancel = () => {
    emits('handelCancel');
    resetData();
  };
  const spanMethod = (line: any) => {
    if (line.columnIndex !== 0) return {};
    const currentRow = line.rowIndex;
    const currentData = selectData.value[currentRow];
    if (line.columnIndex === 0) {
      let rowspan = 1;
      for (let i = currentRow + 1; i < selectData.value.length; i += 1) {
        if (selectData.value[i].type === currentData.type) {
          rowspan += 1;
        } else {
          break;
        }
      }
      if (rowspan > 1) {
        return { rowspan, colspan: 1 };
      }
    }
    return {};
  };

  const handleDel = async (record: any) => {
    await request(`/resourceRoom/behaviorStrategyDict/${record.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'delete',
    }).then(() => {
      handelOpen();
    });
  };

  // 诊断策略
  const saveData = async (record) => {
    await request(`/resourceRoom/behaviorStrategyDict/save`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'put',
      data: {
        id: record.id,
        title: record.strategyDictContent,
        parentId: record.parentId,
      },
    }).then((res) => {
      record.id = res.data.id;
    });
  };

  const handleAdd = async (record, rowIndex) => {
    const data = ref({ id: null, parentId: record.parentId, type: record.type, strategyDictContent: '' });
    await saveData(data.value);
    selectData.value.splice(rowIndex + 1, 0, {
      ...data.value,
    });
  };
  const shouldDel = (record: any): boolean => {
    const list = selectData.value.filter((item) => record.type === item.type);
    return !(list.length > 1);
  };
</script>

<template>
  <a-modal
    v-if="props.selectVisible"
    :visible="props.selectVisible"
    :closable="false"
    fullscreen
    title="引用将会覆盖原来的数据，请慎重"
    ok-text="引用"
    @ok="handelOk"
    @cancel="handelCancel"
    @open="handelOpen"
  >
    <a-table
      v-model:selected-keys="selectedKeys"
      :pagination="false"
      row-key="id"
      :data="selectData"
      :columns="columns"
      column-resizable
      :bordered="{ cell: true }"
      :row-selection="rowSelection"
    >
      <!--      :span-method="spanMethod"-->

      <template v-for="(i, index) in columns" :key="index" #[i.slotName]="{ record, rowIndex }">
        <a-textarea v-if="i.slotName !== 'operation'" v-model="record[i.dataIndex]" @change="saveData(record)" />
        <div v-if="i.slotName === 'operation'">
          <a-button size="mini" type="outline" class="mr-2" @click="handleAdd(record, rowIndex)">新增</a-button>
          <a-popconfirm type="error" content="确认删除？" @ok="handleDel(record)">
            <a-button size="mini" :disabled="shouldDel(record)">删除</a-button>
          </a-popconfirm>
        </div>
      </template>
    </a-table>
  </a-modal>
</template>

<style scoped lang="scss"></style>
