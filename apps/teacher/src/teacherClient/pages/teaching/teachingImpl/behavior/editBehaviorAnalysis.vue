<script setup lang="ts">
  import { computed, watch, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    record: {
      type: Object,
    },
  });

  const emit = defineEmits(['update:modelValue', 'flush']);
  const visible = computed({
    get: () => props.modelValue,
    set: (value) => {
      emit('update:modelValue', value);
    },
  });
  const records = ref({ ...props.record }); // 直接复制对象

  const handelPreOk = async () => {
    await request('/resourceRoom/behaviorRecordAnalysis', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'put',
      data: records.value,
    });
    emit('flush');
    visible.value = false;
  };

  const handelCancel = () => {
    visible.value = false;
  };

  watch(
    () => props.record,
    (newValue) => {
      records.value = { ...newValue };
      records.value.createdDate = '';
      records.value.modifiedDate = '';
    },
    { immediate: true },
  );
</script>

<template>
  <a-modal :visible="visible" :closable="false" title="修改" :on-before-ok="handelPreOk" @cancel="handelCancel">
    <a-form>
      <a-form-item label="姓名">
        <a-input v-model="records.studentName" :disabled="true" />
      </a-form-item>
      <a-form-item label="综合描述">
        <a-textarea v-model="records.event" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style scoped lang="scss"></style>
