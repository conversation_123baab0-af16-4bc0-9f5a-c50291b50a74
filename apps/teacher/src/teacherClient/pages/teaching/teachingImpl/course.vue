<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import TeachingArchiveModal from '@repo/components/course/teachingArchive/teachingArchiveModal.vue';
  import TeacherQuestionLibraryModal from '@repo/components/teacher-question-library/teacherQuestionLibraryModal.vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { onMounted, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useUserStore } from '@repo/infrastructure/store';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const schema = ref(null);
  const router = useRouter();
  const route = useRoute();
  const currentCourse = ref<any>(null);
  const detailVisible = ref(false);
  const questionLibVisible = ref(false);
  const morning = ref();
  const afternoon = ref();

  const handleRowAction = (action, row) => {
    switch (action.key) {
      case 'mission':
        router.push({
          path: `/teacher/teaching/teachingImpl/course/mission`,
          query: {
            ...route.query,
            courseId: row.id,
            morningCount: morning.value,
            afternoonCount: afternoon.value,
          },
        });
        break;
      case 'archive':
        currentCourse.value = row;
        detailVisible.value = true;
        break;
      case 'questionLib':
        currentCourse.value = row;
        questionLibVisible.value = true;
        break;
      default:
        break;
    }
  };

  const userStore = useUserStore();
  const orgNature = userStore.getUserNature();

  const loadClass = async () => {
    const { data } = await request('/teacher/timetable/my', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    if (!data || !data.assignments?.length) {
      return;
    }
    morning.value = data?.gradeConditions?.reduce((acc, item) => Math.max(acc, item.morningCount), 0);
    afternoon.value = data?.gradeConditions?.reduce((acc, item) => Math.max(acc, item.afternoonCount), 0);
  };

  onMounted(async () => {
    const rawSchema = SchemaHelper.getInstanceByApi('/course/course');
    schema.value = {
      ...rawSchema,
      requestApi: {
        list: '/course/course/my-course',
      },
    };
    await loadClass();
  });
</script>

<template>
  <table-with-modal-form
    v-if="schema"
    module-name="我教的课"
    :schema="schema"
    :default-edit-value="{ orgNature }"
    :visible-columns="['name', 'category', 'gradePeriod', 'grade', 'period', 'createdDate', 'description']"
    @row-action="handleRowAction"
  />

  <teaching-archive-modal v-model="detailVisible" :course-id="currentCourse?.id" />
  <teacher-question-library-modal v-model:visible="questionLibVisible" :course="currentCourse" />
</template>
