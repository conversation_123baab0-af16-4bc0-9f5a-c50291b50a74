<!--  <div class="flex justify-end items-end mb-2">
  <a-button type="primary" status="success" :size="size" class="mr-2" @click="handelTraining">训练</a-button>
  <a-button type="primary" :size="size" class="mr-2" @click="handelAddAssessment">新增评估</a-button>
  <a-input-search :style="{ width: '200px' }" placeholder="输入搜索关键字" :loading="false" :size="size" />
</div>
<a-table :columns="columns" :data="tableData" row-key="index" column-resizable :bordered="{ cell: true }"></a-table> -->
<template>
  <a-card v-if="schema">
    <template #title>
      <table-action
        v-if="tableRef"
        class="w-full"
        component-size="mini"
        :table="tableRef"
        :schema="schema"
        :visible-components="visibleComponents"
        @row-action="handleRowAction"
      >
        <template #title>{{ title }}</template>
        <template #extra-actions></template>
        <template #supplementary-button="{ size }">
          <a-button type="primary" status="success" :size="size" @click="handelTraining">训练</a-button>
        </template>
      </table-action>
    </template>
    <crud-table
      ref="tableRef"
      :schema="schema"
      class="mt-2"
      :row-class="getSubmittableRowClass"
      :default-query-params="{ sort: '-id' }"
      :visible-columns="columns"
      @row-action="handleRowAction"
    >
      <template #custom-column-setting="{ record }">
        <span v-for="item in record.setting">
          <span>{{ displaySetting(item) + '、' }}</span>
        </span>
      </template>
    </crud-table>
  </a-card>

  <stroopEffectTrainingModal v-model:visible="trainingModalVisible" :students="students" @start-test="handleStart" />
  <stroopEffectTestComponent
    v-if="startTrainingVisible"
    v-model:visible="startTrainingVisible"
    :config="currentConfig"
    :record="records"
    @show-results="handleShowResult"
  />
  <stroopEffectResultComponent
    v-if="resultComponentVisible"
    v-model:visible="resultComponentVisible"
    :config="currentConfig"
    :test-data="currentTestResult"
    @clear-record="
      () => {
        records = null;
        flush();
      }
    "
  />
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';

  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { getSubmittableRowClass } from '@repo/components/utils/collaborate';
  import { CrudTable, TableAction } from '@repo/ui/components/table';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import stroopEffectTrainingModal from '@/teacherClient/components/attentionTraining/components/stroopEffectTrainingModal.vue';
  import stroopEffectTestComponent from '@/teacherClient/components/attentionTraining/components/stroopEffectTestComponent.vue';
  import stroopEffectResultComponent from '@/teacherClient/components/attentionTraining/components/stroopEffectResultComponent.vue';

  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    trainingType: {
      type: String,
      default: '',
    },
  });

  const columns = ['id', 'student', 'disorders', 'age', 'gender', 'setting', 'createdDate'];
  const schema = ref(null);
  const tableRef = ref(null);
  const visibleComponents = ref(['quickSearch', 'refresh', 'layout']);

  const trainingModalVisible = ref(false);
  const startTrainingVisible = ref(false);
  const resultComponentVisible = ref(false);

  const records = ref<Record<string, any>>();
  const students = ref([]);

  const flush = async () => {
    setTimeout(() => {
      tableRef.value.loadData();
    }, 500);
  };

  const displaySetting = (item: any) => {
    switch (item) {
      case 'manual':
        return '手动答题';
      case 'oral':
        return '口答模式';
      case 'easy':
        return '初级';
      case 'medium':
        return '中级';
      case 'high':
      case 'hard':
        return '高级';
      case 'low':
        return '初级';
      default:
        return item;
    }
  };
  const handelTraining = () => {
    records.value = null;
    trainingModalVisible.value = true;
  };
  const currentConfig = ref(null);
  const handleStart = (val: any) => {
    currentConfig.value = val;
    trainingModalVisible.value = false;
    startTrainingVisible.value = true;
  };
  const currentTestResult = ref(null);

  const handleShowResult = (val: any) => {
    currentTestResult.value = val;
    startTrainingVisible.value = false;
    resultComponentVisible.value = true;
  };

  const handleRowAction = async (action: any, record?: any) => {
    records.value = record;
    switch (action.key) {
      case 'add':
        break;
      case 'edit':
        break;
      case 'view':
        break;
      case 'delete':
        break;
      case 'training':
        currentConfig.value = {
          ...record.setting,
          student: record.student.id,
        };
        startTrainingVisible.value = true;
        break;
      case 'assessment':
        startTrainingVisible.value = true;
        break;
      default:
        break;
    }
  };
  const loadingStudents = async () => {
    try {
      const res = await request('/resourceRoom/student', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'GET',
      });
      students.value = res.data.items;
    } catch (error) {
      /**/
    }
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/attentionTraining/stroopEffect');
    await loadingStudents();
  });
</script>

<style scoped lang="scss"></style>
