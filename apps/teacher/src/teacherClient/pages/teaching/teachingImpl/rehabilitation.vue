<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import TeachingArchiveModal from '@repo/components/rehabCourse/teachingArchive/teachingArchiveModal.vue';
  import TeacherQuestionLibraryModal from '@repo/components/teacher-question-library/teacherQuestionLibraryModal.vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { onMounted, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useUserStore } from '@repo/infrastructure/store';

  const schema = ref(null);
  const router = useRouter();
  const route = useRoute();
  const currentCourse = ref<any>(null);
  const detailVisible = ref(false);
  const questionLibVisible = ref(false);

  const handleRowAction = (action, row) => {
    switch (action.key) {
      case 'mission':
        router.push({
          path: `/teacher/teaching/teachingImpl/rehabilitation/design`,
          query: {
            ...route.query,
            courseId: row.id,
          },
        });
        break;
      case 'archive':
        currentCourse.value = row;
        detailVisible.value = true;
        break;
      case 'questionLib':
        currentCourse.value = row;
        questionLibVisible.value = true;
        break;
      default:
        break;
    }
  };

  const userStore = useUserStore();
  const orgNature = userStore.getUserNature();

  onMounted(async () => {
    const rawSchema = SchemaHelper.getInstanceByApi('/course/rehab-course');
    schema.value = {
      ...rawSchema,
      requestApi: {
        list: '/course/rehab-course/my-course',
      },
    };
  });
</script>

<template>
  <table-with-modal-form
    v-if="schema"
    module-name="康复设计"
    :schema="schema"
    :default-edit-value="{ orgNature }"
    :visible-columns="['description', 'category', 'gradePeriod', 'trainingForm', 'createdDate']"
    @row-action="handleRowAction"
  />

  <teaching-archive-modal v-model="detailVisible" :course-id="currentCourse?.id" />
  <teacher-question-library-modal v-model:visible="questionLibVisible" :course="currentCourse" />
</template>
