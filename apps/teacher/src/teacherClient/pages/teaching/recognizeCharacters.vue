<template>
  <table-with-modal-form
    v-if="schema"
    ref="tableRef"
    :module-name="title"
    :schema="schema"
    :visible-columns="columns"
    :visible-component="['quickSearch', 'refresh', 'layout', 'recycleBin']"
    :table-action-visible="false"
    @row-action="handleRowAction"
  >
    <template #action-bar>
      <div class="flex space-x-2 mr-2">
        <a-button v-if="false" size="mini" type="primary" @click="handleAdd">新增</a-button>
        <a-button size="mini" status="success" type="primary" @click="assessmentVisible = true">
          <icon-play-circle />
          开始评估
        </a-button>
      </div>
    </template>
  </table-with-modal-form>
  <a-modal v-model:visible="assessmentVisible" title="新增评估" :on-before-ok="handleAddPreOk">
    <a-form ref="formRef" :model="formData">
      <a-form-item :rules="[{ required: true, message: '请选择课程' }]" label="请选择课程" field="selectCourse">
        <a-select v-model="formData.selectCourse" allow-clear allow-search @change="handelCourseChange">
          <a-option v-for="course in courseList" :key="course.id" :value="course.id">
            {{ `${course.gradePeriod} - ${schoolCourseMap[course.category]?.name}` }}
          </a-option>
        </a-select>
      </a-form-item>
      <a-form-item :rules="[{ required: true, message: '请选择章节' }]" label="请选择章节" field="selectChapter">
        <a-tree-select
          v-model="formData.selectChapter"
          :data="chapterList"
          :field-names="{
            key: 'id',
            value: 'id',
            title: 'name',
            children: 'children',
          }"
          :filter-tree-node="filterTreeNode"
          placeholder="请选择章节"
          allow-search
        />
      </a-form-item>
      <a-form-item :rules="[{ required: true, message: '请选择学生' }]" label="请选择学生" field="selectStudent">
        <a-select
          v-model="formData.selectStudent"
          :options="studentList"
          allow-search
          :field-names="{ label: 'name', value: 'id' }"
          @change="handleStudentChange"
        />
      </a-form-item>
    </a-form>
  </a-modal>
  <AssessmentView
    v-if="startAssessmentVisible"
    v-model:visible="startAssessmentVisible"
    :form-data="formData"
    :student="currentStudent"
    :course="currentCourse"
    @update:test-result="handleViewReport"
  />
  <AssessmentReport
    v-if="reportVisible"
    v-model:visible="reportVisible"
    :test-result="testResult"
    :student="currentStudent"
  />
  <CharacterBooks v-if="booksVisible" v-model:visible="booksVisible" :record="records" />
</template>

<script setup lang="ts">
  import { onMounted, ref, watch } from 'vue';

  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import useChapterStore from '@repo/components/store/chapterInfoByCourseStore';
  import AssessmentView from '@/teacherClient/components/screeningEvaluation/recognizeCharacters/AssessmentView.vue';
  import AssessmentReport from '@/teacherClient/components/screeningEvaluation/recognizeCharacters/AssessmentReport.vue';
  import CharacterBooks from '@/teacherClient/components/screeningEvaluation/recognizeCharacters/CharacterBooks.vue';

  const props = defineProps({
    title: {
      type: String,
      default: '',
    },
    trainingType: {
      type: String,
      default: '',
    },
    students: {
      type: Array,
    },
  });

  const columns = [
    'student',
    'student.gender',
    'student.age',
    'student.disorders',
    'category',
    'spendTime',
    'createdDate',
  ];
  const schema = ref(null);
  const tableRef = ref(null);
  const assessmentVisible = ref(false);
  const startAssessmentVisible = ref(false);
  const reportVisible = ref(false);
  const booksVisible = ref(false);
  const formRef = ref(null);
  const records = ref<Record<string, any>>();
  const store = useChapterStore();

  const flush = async () => {
    await tableRef.value.loadData();
  };
  const handleAdd = () => {
    assessmentVisible.value = true;
  };

  const deleteData = async (record: any) => {
    const ids = ref<number[]>([]);
    if (Array.isArray(record)) ids.value = Object.values(record);
    else ids.value.push(record.id);
    await request(`/resourceRoom/recognizeCharactersResult/batch/${ids.value}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'delete',
    });
    await flush();
  };
  const testResult = ref();

  const handleRowAction = async (action: any, record?: any) => {
    records.value = record;
    switch (action.key) {
      case 'viewRecords':
        testResult.value = record;
        reportVisible.value = true;
        break;
      case 'viewThesaurus':
        booksVisible.value = true;
        break;
      case 'delete':
        await deleteData(record);
        break;
      case 'add':
        handleAdd();
        break;
      default:
        break;
    }
  };
  const formData = ref({
    selectCourse: null,
    selectChapter: null,
    selectStudent: null,
  });

  const filterTreeNode = (searchValue, nodeData) => {
    return nodeData.name.toLowerCase()?.indexOf(searchValue.toLowerCase()) > -1;
  };

  const getSemesterOrder = (semester: string): number => {
    const [year, season] = semester.split('年');
    const seasonValue = season.includes('秋季') ? 0.5 : 0; // 秋季学期为 0.5，春季学期为 0
    return parseFloat(year) + seasonValue;
  };
  const courseList = ref<any[]>([]);
  const loadMyCourse = async () => {
    try {
      const { data: res } = await request('/course/course/my-course', {
        params: {
          page: 1,
          pageSize: 999,
        },
      });
      courseList.value = res.list.sort((a, b) => {
        const orderA = getSemesterOrder(a.gradePeriod);
        const orderB = getSemesterOrder(b.gradePeriod);
        return orderA - orderB;
      });
    } finally {
      /**/
    }
  };
  const schoolCourseMap = ref<Record<string, any>>({});

  const loadSchoolCourse = async () => {
    try {
      const { data: res } = await request('/teacher/schoolCourse', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          page: 1,
          pageSize: 999,
        },
      });
      res.items.forEach((item) => {
        schoolCourseMap.value[item.id] = item; // 将数据存储为普通对象
      });
    } finally {
      /**/
    }
  };
  const studentList = ref(null);
  const loadStudent = async () => {
    try {
      const { data: res } = await request('/resourceRoom/student', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      studentList.value = res.items;
    } finally {
      /**/
    }
  };
  const handleAddPreOk = async (done: any) => {
    const errors = await formRef.value.validate();
    if (!errors) {
      done(true);
      startAssessmentVisible.value = true;
    } else {
      done(false);
    }
  };
  const chapterList = ref([]);
  const currentCourse = ref(null);
  const currentStudent = ref(null);
  const handleStudentChange = (val: number) => {
    const [student] = studentList.value.filter((item) => item.id === val);
    currentStudent.value = student;
  };

  const handelCourseChange = (val: number) => {
    const [course] = courseList.value.filter((item) => item.id === val);
    currentCourse.value = course;
  };

  const handleViewReport = (result: any) => {
    startAssessmentVisible.value = false;
    reportVisible.value = true;
    testResult.value = result;
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/recognizeCharactersResult');
    await Promise.all([loadMyCourse(), loadSchoolCourse(), loadStudent()]);
  });
  watch(
    () => formData.value.selectCourse,
    async (courseId) => {
      formData.value.selectChapter = null;
      if (courseId) chapterList.value = await store.getChaptersByCourseId(courseId);
    },
  );
</script>

<style scoped lang="scss"></style>
