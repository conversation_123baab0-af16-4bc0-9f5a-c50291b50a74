:deep {
  .submitted-row .arco-table-td {
    background-color: rgba(var(--green-1), .3);

    &:last-child,
    &:nth-child(1),
    &:nth-child(2) {
      background: #fff;
    }
  }

  .rejected-row .arco-table-td {
    background-color: rgba(var(--red-1), .3);
    &:last-child,
    &:nth-child(1),
    &:nth-child(2) {
      background: #fff;
    }
  }

  //.approved-row .arco-table-td {
  //  background-color: rgba(247, 254, 231, 0.3);
  //  &:last-child,
  //  &:nth-child(1),
  //  &:nth-child(2) {
  //    background: #fff;
  //  }
  //}
}