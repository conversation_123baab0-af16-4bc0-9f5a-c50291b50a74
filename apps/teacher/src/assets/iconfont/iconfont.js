;(window._iconfont_svg_string_4362711 =
  '<svg><symbol id="icon-rengongzhinengdanao" viewBox="0 0 1024 1024"><path d="M196.1 989.7c-9.1 0-17.6-3.5-24-9.9-6.3-6.3-9.9-15-9.9-24v-236c0-1.9-1.4-3.5-3.3-3.8-39.1-5.1-108.4-19.7-133.7-60.7-12.4-20.1-13.5-44.2-3.1-67.7 27.2-61.2 72.5-105.4 97-126.2 0.7-0.6 1.2-1.5 1.3-2.4 8.9-71.3 71.3-426.8 428.7-426.8 194.6 0 351.9 88.1 420.8 235.5 73.4 157.3 30.3 352.4-118.3 535.4-0.6 0.7-0.8 1.5-0.8 2.4V950c0 18.7-15.2 33.9-33.9 33.9S783 968.7 783 950V791.8c0-8 2.8-15.7 7.9-21.8C929.1 604.9 972 432.1 908.6 296.2c-57.5-123-191.8-196.4-359.4-196.4-336.2 0-361.8 364.6-362.8 380.1-0.7 10.1-5.7 19.4-13.9 25.4-6.3 4.8-60.7 47.3-88.4 109.7-0.4 1-0.4 2.1 0 3 7.1 16.8 65.6 31.1 113.6 33.5 18.1 0.9 32.2 15.7 32.3 33.8V918c0 2.1 1.7 3.8 3.8 3.8h189.3c18.7 0 33.9 15.2 33.9 33.9s-15.2 33.9-33.9 33.9h-227z" fill="#333333" ></path><path d="M583.9 687.3c-14.3 0-27.1-9-31.9-22.4l-21.3-62.3c-0.5-1.5-2-2.6-3.6-2.6H415.2c-1.6 0-3.1 1-3.6 2.5L389 665.4c-5.1 12.8-17.5 21.1-31.4 21.1-3.9 0-7.8-0.7-11.5-2-17.2-6.2-26.4-24.7-20.8-42.2l116.3-323.6c4.8-13.4 17.6-22.4 31.9-22.4 14.6 0.1 27.4 9.3 32 22.9l110.7 323.7c5.7 17.4-3.9 36.6-21.3 42.5-3.6 1.3-7.2 1.9-11 1.9z m-111.4-247c-1.6 0-3.1 1-3.6 2.5L438.7 527c-0.4 1.2-0.2 2.5 0.5 3.5s1.9 1.6 3.1 1.6h59c1.2 0 2.4-0.6 3.1-1.6 0.7-1 0.9-2.3 0.5-3.5l-28.8-84.2c-0.5-1.5-1.9-2.5-3.6-2.5z m225 244.5c-18.7 0-33.9-14.7-33.9-32.7V329.3c0-18 15.2-32.7 33.9-32.7s33.9 14.7 33.9 32.7v322.9c-0.1 18-15.3 32.6-33.9 32.6z" fill="#333333" ></path></symbol><symbol id="icon-shuju" viewBox="0 0 1024 1024"><path d="M338.6 834.7h133.3V546H338.6v288.7zM383 590.4h44.4v199.9H383V590.4zM538.5 834.7h133.3v-422H538.5v422z m44.4-377.5h44.4v333.1h-44.4V457.2zM738.4 190.6v644.1h133.3V190.6H738.4z m88.8 599.7h-44.4V235.1h44.4v555.2zM138.7 834.7H272V346.1H138.7v488.6z m44.4-444.2h44.4v399.8h-44.4V390.5z" fill="#4B5E6D" ></path><path d="M463.6 490.3h-77.1c-1.1 0-2-0.9-2-2v-77.1c0-1.1 0.9-2 2-2h77.1c1.1 0 2 0.9 2 2v77.1c0 1.1-0.9 2-2 2z" fill="#FFFFFF" ></path><path d="M425 510.6c-33.5 0-60.8-27.3-60.8-60.8S391.5 389 425 389s60.8 27.3 60.8 60.8-27.2 60.8-60.8 60.8z m0-81.2c-11.2 0-20.3 9.1-20.3 20.3 0 11.2 9.1 20.3 20.3 20.3s20.3-9.1 20.3-20.3c0-11.2-9.1-20.3-20.3-20.3z" fill="#FF8D0A" ></path></symbol><symbol id="icon-qiehuanyonghu" viewBox="0 0 1024 1024"><path d="M759 335c0-137-111-248-248-248S263 198 263 335c0 82.8 40.6 156.2 103 201.2-0.4 0.2-0.7 0.3-0.9 0.4-44.7 18.9-84.8 46-119.3 80.6-34.5 34.5-61.5 74.7-80.4 119.5C146.9 780.5 137 827 136 874.8c-0.1 4.5 3.5 8.2 8 8.2h59.9c4.3 0 7.9-3.5 8-7.8 2-77.2 32.9-149.5 87.6-204.3C356 614.2 431 583 511 583c137 0 248-111 248-248zM511 507c-95 0-172-77-172-172s77-172 172-172 172 77 172 172-77 172-172 172z m105 221h264c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H703.5l47.2-60.1c1.1-1.4 1.7-3.2 1.7-4.9 0-4.4-3.6-8-8-8h-72.6c-4.9 0-9.5 2.3-12.6 6.1l-68.5 87.1c-4.4 5.6-6.8 12.6-6.8 19.8 0.1 17.7 14.4 32 32.1 32z m240 64H592c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h176.5l-47.2 60.1c-1.1 1.4-1.7 3.2-1.7 4.9 0 4.4 3.6 8 8 8h72.6c4.9 0 9.5-2.3 12.6-6.1l68.5-87.1c4.4-5.6 6.8-12.6 6.8-19.8-0.1-17.7-14.4-32-32.1-32z" fill="#1296db" ></path></symbol><symbol id="icon--wenjianjia" viewBox="0 0 1024 1024"><path d="M182.9 694.4h-64.1c-4.4 0-6.6 4.4-6.6 6.6 0 4.4 4.4 6.6 8.8 6.6h64.1c2.2 0 4.4 0 4.4-2.2 2.2-2.2 2.2-4.4 2.2-6.6-2.2 0-4.4-4.4-8.8-4.4zM426 694.4H231.5c-4.4 0-6.6 4.4-6.6 6.6 0 4.4 4.4 6.6 6.6 6.6H426c2.2 0 4.4 0 4.4-2.2 2.2-2.2 2.2-4.4 2.2-6.6 0.1 0-2.1-4.4-6.6-4.4z" fill="#FFFFFF" ></path><path d="M837.1 188.2H463.6l-46.4-61.9c-4.4-4.4-8.8-8.8-15.5-8.8H189.5C123.2 117.5 68 170.5 68 239v459.7c0 66.3 53 121.6 121.6 121.6h649.8c66.3 0 121.6-53 121.6-121.6v-389c-2.3-66.2-57.5-121.5-123.9-121.5z" fill="#429BCF" ></path><path d="M114.4 208.1h775.8v68.5H114.4z" fill="#FFFFFF" ></path><path d="M861.5 254.5H461.4l-48.6-66.3c-4.4-4.4-11.1-8.8-17.7-8.8H165.2c-70.7 0-130.4 57.5-130.4 130.4v495.1c0 70.7 57.5 130.4 130.4 130.4h696.2c70.7 0 130.4-57.5 130.4-130.4v-420c0.1-72.9-59.6-130.4-130.3-130.4z" fill="#83C6EF" ></path><path d="M990.1 364.2c-10.1-62.8-64.7-109.7-128.6-109.7H461.4l-48.6-66.3c-4.4-4.4-11.1-8.8-17.7-8.8H165.2c-70.7 0-130.4 57.5-130.4 130.4v54.4h955.3z" fill="#D2EDF7" ></path><path d="M990.1 364.2c-10.1-62.8-64.7-109.7-128.6-109.7H461.4l-48.6-66.3c-4.4-4.4-11.1-8.8-17.7-8.8H165.2c-70.7 0-130.4 57.5-130.4 130.4v54.4h955.3z" fill="#D2EDF7" ></path></symbol><symbol id="icon-wenbenwendang-txt" viewBox="0 0 1024 1024"><path d="M967.111111 281.6V910.222222c0 62.862222-50.915556 113.777778-113.777778 113.777778H170.666667c-62.862222 0-113.777778-50.915556-113.777778-113.777778V113.777778c0-62.862222 50.915556-113.777778 113.777778-113.777778h514.844444L967.111111 281.6z" fill="#6D9FE5" ></path><path d="M685.511111 167.822222V0L967.111111 281.6H799.288889c-62.862222 0-113.777778-50.915556-113.777778-113.777778" fill="#4B80CB" ></path><path d="M344.177778 485.575111h312.888889V426.666667h-312.888889zM471.153778 770.019556h58.908444v-284.444445h-58.908444z" fill="#FFFFFF" ></path></symbol><symbol id="icon-tupian" viewBox="0 0 1024 1024"><path d="M0 0h1024v1024H0z" fill="#FFFFFF" ></path><path d="M665.6 0l256 256v716.8c0 28.16-23.04 51.2-51.2 51.2H153.6c-28.16 0-51.2-23.04-51.2-51.2V51.2c0-28.16 23.04-51.2 51.2-51.2h512z m7.68 307.2h-332.8c-10.24 0-17.92 2.56-23.04 10.24s-10.24 15.36-10.24 28.16v332.8c0 10.24 2.56 20.48 10.24 28.16 7.68 7.68 15.36 10.24 25.6 10.24h335.36c5.12 0 10.24 0 15.36-2.56 5.12-2.56 7.68-5.12 12.8-7.68s5.12-7.68 7.68-12.8c2.56-5.12 2.56-7.68 2.56-12.8V353.28c0-12.8-2.56-25.6-10.24-33.28-5.12-7.68-17.92-12.8-33.28-12.8z m-7.68 64v99.84c-20.48 0-38.4 5.12-53.76 10.24-15.36 5.12-28.16 12.8-40.96 23.04-12.8 7.68-23.04 17.92-33.28 28.16-10.24 10.24-20.48 17.92-30.72 28.16s-20.48 15.36-33.28 20.48c-12.8 5.12-25.6 7.68-40.96 7.68-10.24 0-17.92-2.56-28.16-5.12-7.68-2.56-15.36-7.68-23.04-10.24s-12.8-10.24-17.92-12.8c-5.12-5.12-7.68-10.24-10.24-12.8v-174.08c0-2.56 0-7.68 2.56-10.24 2.56-2.56 5.12-5.12 7.68-5.12h281.6c2.56 0 5.12 2.56 7.68 5.12 12.8 2.56 12.8 5.12 12.8 7.68zM448 409.6c-5.12 0-10.24 0-15.36 2.56s-7.68 5.12-12.8 7.68-5.12 7.68-7.68 12.8c-2.56 5.12-2.56 10.24-2.56 15.36 0 5.12 0 10.24 2.56 15.36 2.56 5.12 5.12 7.68 7.68 12.8s7.68 5.12 12.8 7.68c5.12 2.56 10.24 2.56 15.36 2.56 10.24 0 20.48-2.56 28.16-10.24 7.68-7.68 10.24-15.36 10.24-28.16 0-5.12 0-10.24-2.56-15.36-2.56-5.12-5.12-7.68-7.68-12.8s-7.68-5.12-12.8-7.68c-5.12-2.56-10.24-2.56-15.36-2.56z" fill="#FF7743" ></path><path d="M665.6 0l256 256H665.6z" fill="#FFA480" ></path></symbol><symbol id="icon-zip" viewBox="0 0 1024 1024"><path d="M832 1024H192c-105.6 0-192-86.4-192-192V192c0-105.6 86.4-192 192-192h640c105.6 0 192 86.4 192 192v640c0 105.6-86.4 192-192 192z" fill="#1FB8EC" ></path><path d="M832 1024H192c-105.6 0-192-86.4-192-192V512h1024v320c0 105.6-86.4 192-192 192z" fill="#51BC74" ></path><path d="M0 368h1024v288H0z" fill="#FF7461" ></path><path d="M368 0h288v1024h-288z" fill="#F19725" ></path><path d="M656 688h-288c-19.2 0-32-12.8-32-32v-288c0-19.2 12.8-32 32-32h288c19.2 0 32 12.8 32 32v288c0 19.2-12.8 32-32 32z m-256-64h224v-224h-224v224z" fill="#FFFFFF" ></path></symbol><symbol id="icon-pdf" viewBox="0 0 1024 1024"><path d="M863.5648 1004.4672H159.744c-31.0016 0-56.32-25.2928-56.32-56.32V75.52c0-31.0272 25.3184-56.32 56.32-56.32h488.6272l271.488 279.8336v649.216a56.3968 56.3968 0 0 1-56.32 56.2176z" fill="#FF5562" ></path><path d="M647.5264 19.0976a17.152 17.152 0 0 0-1.1008 5.8112v218.1376c0 31.0272 25.2928 56.32 56.32 56.32h217.1392v-0.4352L648.3968 19.0976h-0.8704z" fill="#FFBBC0" ></path><path d="M682.9312 632.2432a164.5824 164.5824 0 0 1-91.6224-36.864c-51.1488 10.6752-99.9936 27.392-147.6096 47.616-38.0672 69.12-75.008 103.5264-105.9328 103.5264-5.9392 0-14.3104-1.2032-19.0464-4.736-14.2848-5.9392-21.4272-20.224-21.4272-33.3312 0-10.6752 2.4064-42.9056 120.2176-92.8256a831.9488 831.9488 0 0 0 65.4592-154.7776c-15.5136-29.7984-47.616-103.5008-24.9856-140.4672 7.168-14.3104 22.656-21.4528 38.0672-20.224 11.8784 0 24.96 5.9136 32.128 16.5888 16.5888 22.656 15.5136 70.1696-5.9392 139.264 20.224 36.864 46.4128 71.3984 77.312 99.9936 26.1888-4.736 52.3776-8.3712 78.5408-8.3712 58.3168 1.2032 66.6624 28.5952 65.4592 45.2096 1.3312 39.3984-39.168 39.3984-60.6208 39.3984z m-348.672 77.312l3.5072-1.2032c17.8176-5.9392 30.8992-19.0208 41.6768-34.56-19.0208 7.168-34.5344 19.1488-45.2096 35.7632z m165.4272-373.76h-3.5328c-1.2032 0-3.5072 0-4.7104 1.2032-4.736 21.4528-1.2288 43.9808 7.1424 63.1296a108.8 108.8 0 0 0 1.1008-64.3584z m8.3456 180.9408l-1.2032 2.4064-1.2032-1.2032c-10.6752 28.5952-23.7568 57.088-36.864 84.48l2.432-1.2032v2.4064c27.392-9.5744 57.088-19.0208 84.48-24.96l-1.2032-1.2032h3.5072c-19.0208-19.0464-35.7376-40.4992-49.92-60.7232z m170.1888 65.4336c-10.6752 0-21.4528 0-32.128 2.432 11.8784 5.9392 24.96 8.3712 36.864 9.5744 8.3456 1.2032 17.792 0 24.96-2.432 0-3.6352-4.736-9.5744-29.696-9.5744z" fill="#FFFFFF" ></path></symbol><symbol id="icon-exl" viewBox="0 0 1024 1024"><path d="M512.283 482.043"  ></path><path d="M291.878 774.006V314.831h459.176v459.176H291.878z m413.26-413.258H337.796v275.504h367.342V360.748zM495.956 544.419l86.734-117.343 30.609 45.916 30.614-10.203 20.406 122.447H368.41l76.525-71.426 51.021 30.609z m-86.734-76.532c-20.405 0-35.712-15.302-35.712-30.609 0-15.307 15.307-30.609 35.712-30.609 20.406 0 35.714 15.302 35.714 30.609-0.001 15.307-15.308 30.609-35.714 30.609z m0 0" fill="#FFFFFF" ></path><path d="M133.782 1.036c-15.45 0-30.901 5.114-36.048 15.334-10.298 10.221-15.45 25.556-15.45 35.776v919.962c0 10.227 5.152 25.556 15.45 35.776 10.298 10.226 20.598 15.334 36.048 15.334h757.003c10.304 0 25.75-5.108 36.048-15.334 10.305-10.221 15.45-25.55 15.45-35.776V292.36L648.75 1.036H133.782z m0 0" fill="#5ACC9B" ></path><path d="M937.138 292.36H695.101c-15.45 0-25.75-5.113-36.048-15.334-10.303-10.222-15.45-20.442-15.45-35.776V1.036L937.138 292.36z m0 0" fill="#BDEBD7" ></path><path d="M468.51 537.68L355.22 379.245h82.394l72.096 112.438 77.242-117.55H664.2L545.758 537.68l123.593 173.774h-82.399L509.71 588.79l-82.394 122.663h-82.399L468.51 537.68z m0 0" fill="#FFFFFF" ></path></symbol><symbol id="icon-word" viewBox="0 0 1024 1024"><path d="M160 32c-12 0-24.8 4.8-33.6 14.4S112 68 112 80v864c0 12 4.8 24.8 14.4 33.6C136 987.2 148 992 160 992h704c12 0 24.8-4.8 33.6-14.4C907.2 968 912 956 912 944V304L640 32H160z" fill="#6CCBFF" ></path><path d="M912 304H688c-12 0-24.8-4.8-33.6-14.4-9.6-8.8-14.4-21.6-14.4-33.6V32l272 272z" fill="#C4EAFF" ></path><path d="M280 385.6h64.8l64.8 244h0.8l71.2-244H544l72 244 65.6-244H744L648 700h-64.8L512 458.4h-0.8l-72 240.8h-64.8L280 385.6z" fill="#FFFFFF" ></path></symbol><symbol id="icon-mp3-1" viewBox="0 0 1024 1024"><path d="M137.902164 0.00045a48.572979 48.572979 0 0 0-35.588984 15.293993 53.964976 53.964976 0 0 0-15.272993 35.639985v917.411596a48.706979 48.706979 0 0 0 15.272993 35.639985 49.970978 49.970978 0 0 0 35.589984 15.292993h746.335672a48.639979 48.639979 0 0 0 35.589985-15.292993 50.369978 50.369978 0 0 0 15.272993-35.639985V288.717323L646.727941 0.00045H137.902164z" fill="#FF5562" ></path><path d="M935.101814 288.717323H697.655918c-27.821988-0.666-50.226978-23.07599-50.927977-50.933977V0.00045l288.373873 288.716873z" fill="#FFBBC0" ></path><path d="M678.133927 487.465236c0.42-2.200999 0.42-4.453998 0-6.649997V358.532292a18.099992 18.099992 0 0 0-5.908997-12.763994 15.922993 15.922993 0 0 0-13.613994-3.393998l-265.599884 74.740967a15.420993 15.420993 0 0 0-12.681994 15.293993v292.105872a90.39396 90.39396 0 0 0-34.794985-6.655997 106.192953 106.192953 0 0 0-27.954988 3.394998c-43.289981 11.903995-70.379969 45.87998-61.951972 77.331966 6.644997 24.601989 32.275986 39.894982 64.475971 39.894983a161.209929 161.209929 0 0 0 28.020988-3.389999c38.978983-11.037995 64.409972-39.095983 63.544972-67.09197a12.457995 12.457995 0 0 0 0.865-5.984998V571.776199l233.266897-65.361972v144.358937a90.65496 90.65496 0 0 0-34.794985-6.650997 106.096953 106.096953 0 0 0-28.021987 3.393998c-43.227981 11.899995-70.384969 45.87498-61.885973 77.260966 6.640997 24.672989 32.204986 39.899982 64.470972 39.899983a105.901953 105.901953 0 0 0 27.955987-3.393999c38.179983-10.239996 63.609972-38.230983 63.609972-66.49397a12.170995 12.170995 0 0 0 0.866-5.912998l0.133-201.409911z m-265.599883 50.933977v-56.852975l233.394897-65.094971v56.918975l-233.394897 65.023971z" fill="#FFFFFF" ></path></symbol><symbol id="icon-ppt" viewBox="0 0 1024 1024"><path d="M160 32c-12.0064 0-24.8064 4.8-33.6 14.4-8.8064 9.6-14.4 21.6064-14.4 33.6v864c0 12.0064 4.8 24.8064 14.4 33.6 9.6 9.6 21.6064 14.4 33.6 14.4h704c12.0064 0 24.8064-4.8 33.6-14.4 9.6-9.6 14.4-21.6064 14.4-33.6v-640L640 32H160z" fill="#FF8976" ></path><path d="M912 304h-224c-12.0064 0-24.8064-4.8-33.6-14.4C644.8 280.7936 ************ 640 256V32l272 272z" fill="#FFD0C8" ></path><path d="M385.6 385.6h176c70.4 0 92.8 47.2064 92.8 97.6 0 48-28.0064 96.8064-92.0064 96.8064h-116.7872v119.9872h-60.0064V385.6zM445.6064 531.2h96.7936c34.4064 0 52.8-10.4064 52.8-47.2064 0-38.4-24.8064-48-48-48h-101.6064V531.2z" fill="#FFFFFF" ></path></symbol></svg>'),
  (function (l) {
    var t = (t = document.getElementsByTagName('script'))[t.length - 1],
      c = t.getAttribute('data-injectcss'),
      t = t.getAttribute('data-disable-injectsvg')
    if (!t) {
      var h,
        a,
        i,
        e,
        o,
        n = function (t, c) {
          c.parentNode.insertBefore(t, c)
        }
      if (c && !l.__iconfont__svg__cssinject__) {
        l.__iconfont__svg__cssinject__ = !0
        try {
          document.write(
            '<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>'
          )
        } catch (t) {
          console && console.log(t)
        }
      }
      ;(h = function () {
        var t,
          c = document.createElement('div')
        ;(c.innerHTML = l._iconfont_svg_string_4362711),
          (c = c.getElementsByTagName('svg')[0]) &&
            (c.setAttribute('aria-hidden', 'true'),
            (c.style.position = 'absolute'),
            (c.style.width = 0),
            (c.style.height = 0),
            (c.style.overflow = 'hidden'),
            (c = c),
            (t = document.body).firstChild
              ? n(c, t.firstChild)
              : t.appendChild(c))
      }),
        document.addEventListener
          ? ~['complete', 'loaded', 'interactive'].indexOf(document.readyState)
            ? setTimeout(h, 0)
            : ((a = function () {
                document.removeEventListener('DOMContentLoaded', a, !1), h()
              }),
              document.addEventListener('DOMContentLoaded', a, !1))
          : document.attachEvent &&
            ((i = h),
            (e = l.document),
            (o = !1),
            F(),
            (e.onreadystatechange = function () {
              'complete' == e.readyState && ((e.onreadystatechange = null), d())
            }))
    }
    function d() {
      o || ((o = !0), i())
    }
    function F() {
      try {
        e.documentElement.doScroll('left')
      } catch (t) {
        return void setTimeout(F, 50)
      }
      d()
    }
  })(window)
