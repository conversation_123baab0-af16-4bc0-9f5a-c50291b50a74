<template>
  <div>
    <!--header-->
    <div class="header">
      <div class="time">
        {{ currentTime }}
      </div>
      <div class="title" :style="rawData.companyName.length < 10 ? 'letter-spacing: 10px' : ''">
        {{ rawData.companyName }}
      </div>
    </div>
    <!--left-->
    <div class="main-wrap flex">
      <div class="left ml">
        <div class="card-view mt" style="height: 300px">
          <div class="title text-center cursor-pointer" @click="requestData">学生信息统计</div>
          <div class="content" style="padding: 20px">
            <student-statistics-bar :data="rawDatas" />
          </div>
        </div>

        <div class="card-view mt" style="height: 350px">
          <div class="title text-center">单位信息统计</div>
          <div class="content" style="padding: 20px; height: 200px">
            <unit-info-statistics-bar :raw-data="rawDatas" />
          </div>
        </div>

        <div class="card-view mt clickable flex-1" style="height: 300px; overflow: hidden">
          <div class="title text-center">资源教室督导考核</div>
          <div class="content p-5" style="height: 250px">
            <ResourceRoomAssessmentBar :data="rawDatas" />
          </div>
        </div>
      </div>
      <!--middle-->
      <div class="middle">
        <div class="first-block flex">
          <div class="card-view" style="height: 300px; flex: 1">
            <div class="title text-center">残疾障碍</div>
            <div class="content p-5" style="padding: 20px">
              <disabilityDisordersBar :raw-data="rawDatas" />
            </div>
          </div>
        </div>
        <div class="card-view mt" style="height: 300px">
          <div class="title text-center">特殊需要</div>
          <div class="content p-5">
            <special-needs-bar :raw-data="rawDatas" />
          </div>
        </div>
        <div class="card-view mt" style="flex: 1; height: 300px">
          <div class="title text-center">特殊儿童发展趋势</div>
          <div class="content p-5 mb-2">
            <development-trends-bar :raw-data="rawDatas" />
          </div>
        </div>
      </div>
      <!--right-->
      <div class="right mr">
        <div class="card-view mt" style="height: 100%; width: 500px">
          <div class="title text-center">残障学生“一人一案”预警</div>
          <div class="content p-5">
            <one-person-one-case-bar :raw-data="rawDatas" @show="handleListModalVisible" />
          </div>
        </div>
      </div>
    </div>
    <list-modal
      :title="listModal.title"
      :raw-data="rawData"
      :value="listModal.visible"
      :modal-config="listModal"
      :width="listModal.width || '80%'"
      @update:model-value="handleListModalVisible"
    />
  </div>
</template>

<script>
  import ListModal from '@/screen/templates/default/listModal.vue';
  import StudentStatisticsBar from '@/screen/templates/default/components/newScreenComponents/studentStatisticsBar.vue';
  import UnitInfoStatisticsBar from '@/screen/templates/default/components/newScreenComponents/unitInfoStatisticsBar.vue';
  import ResourceRoomAssessmentBar from '@/screen/templates/default/components/newScreenComponents/resourceRoomAssessmentBar.vue';
  import OnePersonOneCaseBar from '@/screen/templates/default/components/newScreenComponents/onePersonOneCaseBar.vue';
  import disabilityDisordersBar from '@/screen/templates/default/components/newScreenComponents/disabilityDisordersBar.vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { G2 } from '@antv/g2plot';
  import SpecialNeedsBar from '@/screen/templates/default/components/newScreenComponents/specialNeedsBar.vue';
  import DevelopmentTrendsBar from '@/screen/templates/default/components/newScreenComponents/developmentTrendsBar.vue';
  import { request } from '@repo/infrastructure/request';
  import dayjs from 'dayjs';

  const { registerTheme } = G2;

  export default {
    name: 'Index',
    components: {
      DevelopmentTrendsBar,
      SpecialNeedsBar,
      OnePersonOneCaseBar,
      ResourceRoomAssessmentBar,
      UnitInfoStatisticsBar,
      StudentStatisticsBar,
      ListModal,
      disabilityDisordersBar,
    },
    props: {
      rawData: {
        type: Object,
        required: true,
      },
      rawDatas: {
        type: Object,
        required: true,
      },
    },
    data() {
      return {
        currentTime: '',
        listModal: {
          visible: false,
          title: '',
        },
        rawDataes: {},
        width: document.documentElement.clientWidth,
        height: document.documentElement.clientHeight,
      };
    },
    mounted() {
      this.currentTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
      setInterval(() => {
        this.currentTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
      }, 1000);
    },
    methods: {
      handleListModalVisible() {
        this.listModal = {
          visible: false,
          title: '',
        };
      },
      showListModal(type) {
        this.listModal = {
          visible: true,
          type,
          ...this.modals[type],
        };
      },
      async requestData() {
        const { data: res } = await request('/resourceCenter/screenStatistics/screenVersionTow', {
          params: {
            securityCode: this.globalData.securityCode,
          },
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        });
        this.width = res.screenConfig?.screenWidth || this.width;
        this.height = res.screenConfig?.screenHeight || this.height;
        this.rawDataes = res;
      },
    },
  };
</script>

<style lang="scss">
  .ScreenAdapter {
    padding: 0;
    margin: 0;
    font-size: 16px;
    background: #141d37;
    color: #fff;
  }
</style>

<style lang="scss" scoped>
  $mainGradient: linear-gradient(rgb(25, 37, 62) 14%, rgb(34, 62, 97) 100%);
  $margin: 16px;
  .flex {
    display: flex;
  }

  .ml {
    margin-left: $margin;
  }

  .mr {
    margin-right: $margin;
  }

  .mt {
    margin-top: $margin;
  }

  .mb {
    margin-bottom: $margin;
  }

  .header {
    height: 80px;
    background-image: url('./assets/images/title-bg.png');
    background-size: contain;
    margin: 0 auto;
    background-repeat: no-repeat;
    width: 80%;
    position: relative;

    .time {
      position: absolute;
      left: -10%;
      top: 10px;
    }

    .title {
      text-align: center;
      font-size: 38px;
      font-weight: 500;
      line-height: 80px;
      letter-spacing: 2px;
    }
  }

  .mini-card {
    text-align: center;
    height: 138px;
    width: 250px;
    background: $mainGradient;

    .title {
      font-size: 32px;
      margin: 12px 0 16px;
    }

    .num {
      font-size: 38px;
      color: rgb(255, 185, 0);
    }
  }

  .card-view {
    box-shadow: rgba(255, 255, 255, 0) 0px 0px 1px;
    background: linear-gradient(rgb(25, 37, 62) 3.14%, rgb(34, 62, 97) 100%);
    display: flex;
    flex-direction: column;

    .title {
      background: linear-gradient(90deg, rgba(0, 95, 201, 0.5) 0%, rgba(34, 62, 97, 0) 100%);
      height: 40px;
      padding-left: $margin;
      line-height: 40px;
      font-size: 22px;
    }

    .content {
      flex: 1;
    }
  }

  .main-wrap {
    padding: 0 16px;
    height: calc(100% - 80px);
    margin-top: -20px;

    .left {
      height: 100%;
      display: flex;
      flex-direction: column;
      width: 516px;
      flex-wrap: nowrap;
      margin-left: $margin;

      .first-block {
        text-align: center;
        display: flex;
      }
    }

    .middle {
      display: flex;
      flex-direction: column;
      padding-top: 36px;
      flex: 1;
      margin: 0 $margin;

      .first-right {
        width: 350px;

        .mini-card {
          width: 100%;
          margin-top: 16px;
          display: flex;
          justify-content: space-around;
          height: 75px;
          line-height: 75px;

          .title {
            margin: 0;
          }

          .num {
            color: rgb(13, 224, 157);
          }

          &:first-child {
            margin: 0;
            height: 96px;
            line-height: 96px;
            font-size: 36px;

            .num {
              color: rgb(255, 185, 0);
            }
          }
        }
      }

      .second-block {
        display: flex;

        .left-item {
          height: 180px;
          width: 350px;
          justify-content: space-around;
          align-items: center;
          background: $mainGradient;
        }

        .card-view {
          height: 180px;
          flex: 1;
        }
      }
    }

    .right {
      margin-right: $margin;
      display: flex;
      flex-direction: column;
    }
  }

  .clickable {
    cursor: pointer;
    transition: all linear 0.2s;

    &:hover {
      box-shadow: 0 0 20px 10px rgba(255, 255, 255, 0.2);
    }
  }
</style>

<style>
  body,
  html {
    background: #141d37;
  }

  .print-visible-only {
    display: none !important;
  }
</style>
