<template>
  <div id="userTitleBar"></div>
</template>

<script>
import { Bar } from '@antv/g2plot'

export default {
  name: 'userTitleBar',
  props: {
    rawData: {
      type: Array,
      required: true,
    },
  },
  mounted() {
    const indexes = [
      '正高级教师',
      '高级教师',
      '一级教师',
      '二级教师',
      '三级教师',
      '其他',
    ]
    const data = this.rawData.sort((a, b) => {
      return indexes.indexOf(a.udf1) - indexes.indexOf(b.udf1)
    })
    const bar = new Bar('userTitleBar', {
      data,
      appendPadding: 20,
      isGroup: true,
      height: 470,
      xField: 'id',
      yField: 'udf1',
      seriesField: 'remark',
      theme: 'giant-screen',
      legend: {
        position: 'bottom',
      },
      xAxis: {
        label: {
          style: {
            fontSize: 14,
            color: '#ffffff',
          },
        },
        tickLine: false,
        grid: false,
      },
      label: {
        autoHide: true,
        autoRotate: true,
        size: 50,
      },
      tickLine: false,
      grid: false,
    })

    bar.render()
  },
}
</script>
