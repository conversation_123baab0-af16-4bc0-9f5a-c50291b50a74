<template>
  <div class="flex-container">
    <div id="specialNeedsBar" class="special-needs-bar"></div>
    <div id="specialNeedsBar2" class="special-needs-bar2"></div>
  </div>
</template>

<script>
  import { Line, Bar } from '@antv/g2plot';

  export default {
    name: 'SpecialNeedsBar',
    props: {
      rawData: {
        type: Array,
      },
    },
    mounted() {
      this.renderChart();
      this.pei();
    },
    methods: {
      renderChart() {
        const data = Object.entries(this.rawData.specialNeedStatistics.disordersMap).map(([k, v]) => ({
          type: k,
          count: v,
        }));

        const barPlot = new Bar('specialNeedsBar', {
          data,
          xField: 'count',
          yField: 'type',
          seriesField: 'type',
          legend: false,
          meta: {
            count: {
              alias: '数量',
            },
          },
          xAxis: {
            label: {
              style: {
                // fill: '#6ce5ff', // 修改为你想要的颜色
              },
            },
          },
          yAxis: {
            label: {
              style: {
                // fill: '#6ce5ff', // 修改为你想要的颜色
                fill: '#b0b0b0', // 设置标签文本的颜色
              },
            },
          },
        });

        barPlot.render();
      },
      pei() {
        const requiredConditions = ['亚斯伯格症', '学习困难', '情绪行为障碍', '注意力缺陷多动症'];
        const yearAndStatistics = this.rawData?.specialNeedStatistics?.yearAndStatistics;

        if (!yearAndStatistics || typeof yearAndStatistics !== 'object') {
          console.error('数据格式不正确或为空');
          return;
        }

        const transformedData = [];

        // eslint-disable-next-line guard-for-in,no-restricted-syntax
        for (const year in yearAndStatistics) {
          const statistics = yearAndStatistics[year];
          let otherCount = 0;

          // 遍历条件并构建数据
          // eslint-disable-next-line no-restricted-syntax
          for (const condition of requiredConditions) {
            transformedData.push({
              year,
              name: condition,
              gdp: statistics[condition] || 0, // 如果没有该条件，则默认为0
            });
          }

          // 计算其他条件的数量
          // eslint-disable-next-line no-restricted-syntax
          for (const condition in statistics) {
            if (!requiredConditions.includes(condition)) {
              otherCount += statistics[condition];
            }
          }

          // 添加 "其他" 的数据
          transformedData.push({
            year,
            name: '其他',
            gdp: otherCount,
          });
        }

        console.log(transformedData, '转换后的数据'); // 输出转换后的数据

        const linePlot = new Line('specialNeedsBar2', {
          data: transformedData,
          xField: 'year',
          yField: 'gdp',
          seriesField: 'name',
          yAxis: {
            label: {
              formatter: (v) => `${v}`, // 根据需要格式化
            },
          },
          legend: false, // 关闭图例显示
          smooth: true,
          area: {
            style: {
              fillOpacity: 0.15,
            },
          },
          animation: {
            appear: {
              animation: 'wave-in',
              duration: 3000,
            },
          },
        });

        linePlot.render();
      },
    },
  };
</script>

<style scoped>
  .flex-container {
    display: flex;
    width: 100%;
    height: 100%;
  }

  .special-needs-bar {
    flex: 5;
    height: 100%;
    margin-right: 1.25rem;
  }

  .special-needs-bar2 {
    flex: 5;
    height: 100%;
    margin-left: 1.25rem;
  }
</style>
