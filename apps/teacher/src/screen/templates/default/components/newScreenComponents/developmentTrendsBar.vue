<template>
  <div class="flex-container">
    <div id="developmentTrendsBar" class="development-trends-bar"></div>
  </div>
</template>

<script>
  import { Line } from '@antv/g2plot';

  export default {
    name: 'DevelopmentTrendsBar',
    props: {
      rawData: {
        type: Array,
      },
    },
    mounted() {
      this.pei();
    },
    methods: {
      pei() {
        const data = [];

        // 遍历 developmentTrends 数据
        this.rawData.developmentTrends.forEach((item) => {
          const { year, obstacleCount, disabledCount } = item;

          // 添加障碍数量的数据
          data.push({
            date: year,
            series: '障碍数量',
            value: obstacleCount,
          });

          // 添加残疾数量的数据
          data.push({
            date: year,
            series: '残疾数量',
            value: disabledCount,
          });
        });

        const linePlot = new Line('developmentTrendsBar', {
          data,
          xField: 'date',
          yField: 'value',
          seriesField: 'series',
          label: {
            position: 'top', // 标签位置
            style: {
              fill: '#ffffff', // 标签颜色
              fontSize: 12, // 字体大小
            },
            formatter: (datum) => `${datum.value}`,
          },
          yAxis: {
            label: {
              formatter: (v) => `${v}`, // 根据需要格式化y轴标签
            },
          },
          xAxis: {
            label: {
              style: {
                fill: '#b0b0b0', // 设置标签文本的颜色
              },
            },
          },
          legend: {
            position: 'top',
            marker: {
              symbol: 'circle', // 图例标记的形状
            },
            itemName: {
              style: {
                fill: '#ffffff', // 设置图例文本的颜色
              },
            },
          },
          smooth: false,
          area: {
            style: {
              fillOpacity: 0.1, // 填充透明度
            },
          },
          animation: {
            appear: {
              animation: 'wave-in',
              duration: 3000,
            },
          },
        });

        linePlot.render();
      },
    },
  };
</script>

<style scoped>
  .flex-container {
    display: flex;
    width: 100%;
    height: 100%;
  }

  .development-trends-bar {
    width: 100%;
    height: 100%;
  }
</style>
