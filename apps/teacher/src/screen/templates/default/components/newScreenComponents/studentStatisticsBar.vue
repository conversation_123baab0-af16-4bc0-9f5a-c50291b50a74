<template>
  <div class="flex-container">
    <div v-for="(item, index) in menu" :key="index" class="cursor-pointer" @click="checked(index)">
      <div class="skewed-text">{{ item.name }}</div>
      <div class="num-text">{{ item?.num !== 0 ? item.num : item.count }}</div>
      <div v-show="item.showUnderLine" class="under-line"></div>
    </div>
  </div>

  <a-divider />
  <div class="flex-container">
    <div v-for="(v, k) in menu[currentIndex]?.details" :key="k" class="flex-item">
      <div class="inner-box">
        <div class="title" style="padding: 4px 0 4px 0">{{ k }}</div>
        <a-divider class="divider"></a-divider>
        <div class="number">
          <span class="number-value">{{ v }}</span>
          <span class="number-unit"> 人</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { Column } from '@antv/g2plot';

  export default {
    name: 'StudentStatisticsBar',
    props: {
      data: {
        type: Array,
      },
    },
    data() {
      return {
        plot: null,
        colors: ['#0468b5', '#fff'],
        menu: [],
        currentIndex: 0,
      };
    },
    mounted() {
      this.data.studentInfoStatistics.forEach((item) => {
        this.menu.push({
          name: item.classification,
          count: Object.values(item.typeAndNum).reduce((acc, num) => acc + num, 0),
          showUnderLine: false,
          details: item.typeAndNum,
          num: item.num,
        });
        this.menu[0].showUnderLine = true;
        this.menu[0].count = this.data.studentCount;
      });
    },
    methods: {
      checked(index) {
        this.currentIndex = index;
        this.menu.forEach((item) => {
          item.showUnderLine = false;
        });
        this.menu[index].showUnderLine = true;
      },
    },
  };
</script>

<style scoped>
  /* 定义自定义样式 */
  .flex-container {
    display: flex;
    justify-content: space-around;
    text-align: center;
    font-weight: bold;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .skewed-text {
    color: white;
    transform: skewX(2deg);
  }

  .num-text {
    font-size: 2.25rem; /* 1.5rem * 1.5 for text-4xl */
    color: #38b2ac; /* teal-400 */
    font-style: italic;
  }

  .under-line {
    width: 100%;
    border: 1px solid #38b2ac; /* teal-400 */
  }

  .hidden {
    display: none;
  }

  /* 外层容器 - Flex 布局，水平分布 */
  .flex-container {
    display: flex;
    justify-content: space-around;
  }

  /* 每个子项 - 设置为自适应宽度，居中显示，白色文字，加粗，外边距 */
  .flex-item {
    flex: 1;
    text-align: center;
    color: white;
    font-weight: bold;
    margin: 0.25rem; /* m-1 = 0.25rem */
  }

  /* 内部盒子 - 边框2px，天蓝色边框，设置高度，圆角 */
  .inner-box {
    border: 2px solid #0ea5e9; /* sky-600 */
    height: 100%;
    border-radius: 0.375rem; /* rounded-md */
  }

  /* 标题样式 */
  .title {
    font-size: 1rem;
    font-weight: normal;
  }

  /* 边界线样式 - 天蓝色背景 */
  .divider {
    margin: 0;
    background-color: #0ea5e9; /* sky-600 */
  }

  /* 数字部分 - 设置黄色字体 */
  .number {
    color: #d97706; /* yellow-500 */
  }

  /* 数字部分的字体大小和加粗 */
  .number-value {
    font-size: 1.5rem; /* text-2xl */
    font-weight: bold;
  }

  /* 小字 "人" - 设置字体颜色为白色，字体大小12px */
  .number-unit {
    color: white;
    font-size: 12px;
  }
</style>
