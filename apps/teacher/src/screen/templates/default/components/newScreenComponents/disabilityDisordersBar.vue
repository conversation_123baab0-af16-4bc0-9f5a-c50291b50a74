<template>
  <div class="flex-container">
    <div id="studentAgeBar" class="student-age-bar"></div>
    <div id="studentAgeBar2" class="student-age-bar2"></div>
  </div>
</template>

<script>
  import { Column, Pie } from '@antv/g2plot';

  export default {
    name: 'StudentAgeBar',
    props: {
      rawData: {
        type: Array,
      },
    },
    mounted() {
      this.renderChart();
      this.pei();
    },
    methods: {
      renderChart() {
        const data = Object.entries(this.rawData.disabilityStatistics.gradeStatistics).map(([k, v]) => ({
          type: k,
          count: v,
        }));
        const columnPlot = new Column('studentAgeBar', {
          data,
          xField: 'type',
          yField: 'count',
          color: '#6ad5c1',
          xAxis: {
            label: {
              autoHide: true,
              autoRotate: false,
            },
          },
          yAxis: {
            nice: true, // 自动调整轴
            max: Math.max(...data.map((item) => item.count)) + 10, // 手动设置最大值
          },
          meta: {
            count: {
              alias: '数量',
            },
          },
          columnWidthRatio: 0.5,
          label: {
            position: 'top',
            style: {
              fill: '#ffffff',
            },
            formatter: (datum) => String(datum.count),
            offset: 10, // 向上偏移标签位置
          },
          marginRatio: 0.2, // 调整柱子间距
        });

        columnPlot.render();
      },
      pei() {
        const data = Object.entries(this.rawData.disabilityStatistics.disorders).map(([k, v]) => ({
          type: k,
          count: v,
        }));
        const piePlot = new Pie('studentAgeBar2', {
          appendPadding: 10,
          data,
          colorField: 'type',
          angleField: 'count',
          radius: 1,
          innerRadius: 0.6,
          label: {
            type: 'inner',
            offset: '-50%',
            content: '{value}',
            style: {
              textAlign: 'center',
              fontSize: 14,
              color: '#50a8ff',
            },
          },
          legend: {
            position: 'right',
            itemName: {
              style: {
                fill: '#ffffff', // 图例中文字颜色
              },
            },
          },
          interactions: [{ type: 'element-selected' }, { type: 'element-active' }],
          statistic: {
            title: false,
            content: {
              style: {
                whiteSpace: 'pre-wrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                color: '#ffffff',
              },
              content: ' ',
            },
          },
        });

        piePlot.render();
      },
    },
  };
</script>

<style scoped>
  .flex-container {
    display: flex;
    width: 100%;
    height: 100%;
  }

  .student-age-bar {
    flex: 4;
    height: 100%;
    margin-right: 0.5rem;
  }

  .student-age-bar2 {
    flex: 6;
    height: 100%;
  }
</style>
