<template>
  <span class="text-cyan-600">
    年度
    <a-select
      v-model="currentTerm"
      :options="options"
      size="mini"
      style="width: 100px; background-color: transparent; color: #40ffd3; border: 1px solid #37a8ff; border-radius: 8px"
      @change="onTermChange"
    />
  </span>
  <span style="float: right; margin-right: 20px">{{ currentTerm }}年度</span>
  <div id="resourceRoomAssessment" style="height: 80%; width: 100%; margin-top: 12px" />
</template>

<script>
  import { Column } from '@antv/g2plot';

  export default {
    name: 'ResourceRoomAssessmentBar',
    props: {
      data: {
        type: Object,
        required: true,
      },
      term: {
        type: String,
        default: new Date().getFullYear().toString(),
      },
    },
    data() {
      return {
        plot: null,
        currentTerm: this.term,
        terms: [],
        options: [],
      };
    },
    mounted() {
      this.loadData();
    },
    methods: {
      loadData() {
        const evaluations = this.data.resourceRoomEvaluation[this.currentTerm];
        if (evaluations) {
          this.terms = Object.entries(evaluations).map(([k, v]) => ({
            type: k,
            count: v,
          }));
          this.options = Object.keys(this.data.resourceRoomEvaluation);
          // 在数据加载完成后渲染图表
          this.renderChart();
        }
      },
      renderChart() {
        if (this.plot) {
          this.plot.destroy(); // 如果已经存在的图表，先销毁
        }
        this.plot = new Column('resourceRoomAssessment', {
          data: this.terms,
          xField: 'type',
          yField: 'count',
          xAxis: {
            label: {
              autoHide: true, // 自动隐藏
              autoRotate: true, // 自动旋转
              style: {
                fontSize: 10, // 减小字体大小
                fill: '#b0b0b0', // 设置标签文本的颜色
              },
            },
          },
          yAxis: {
            nice: true, // 自动调整轴
            max: Math.max(...this.terms.map((item) => item.count)) + 5, // 手动设置最大值
          },
          smooth: true,
          columnWidthRatio: 0.3,
          meta: {
            type: {
              alias: '类型',
            },
            count: {
              alias: `${this.currentTerm}年度考核`,
            },
          },
          label: {
            position: 'top',
            style: {
              fill: '#ffffff',
            },
            formatter: (datum) => String(datum.count),
            offset: 10, // 向上偏移标签位置
          },
        });
        this.plot.render();
      },

      onTermChange(newTerm) {
        this.currentTerm = newTerm; // 更新当前学期
        this.loadData(); // 重新加载数据并渲染图表
      },
    },
  };
</script>
