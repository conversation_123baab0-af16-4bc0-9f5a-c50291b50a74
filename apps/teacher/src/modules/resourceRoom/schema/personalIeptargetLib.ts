import { CustomSchema } from '@repo/infrastructure/types';
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';
import useCommonStore from '@repo/infrastructure/utils/store';
import { defineAsyncComponent, Ref } from 'vue';

const mainColumns = ['content', 'teachingScene'];

const PersonalIepTargetLib: CustomSchema = {
  api: '/resourceRoom/personalIepTargetLibrary',
  quickSearchProps: {
    enabled: true,
    fields: mainColumns,
  },
  rowActions: [
    {
      key: 'search',
      label: '检索',
      expose: true,
      icon: 'icon-filter',
      btnProps: {
        status: 'success',
        type: 'dashed',
      },
    },
  ],
  importable: {
    enabled: true,
    columns: mainColumns,
  },
  exportable: {
    enabled: true,
    columns: mainColumns,
  },
  fieldsMap: {
    content: {
      inputWidget: 'textareaInput',
    },
    // individualizedEducationTarget: {
    //   key: 'individualizedEducationTarget',
    //   visibleInForm: false,
    //   inputWidget: defineAsyncComponent(
    //     () => import('@repo/components/iep/components/longTermTargetAssociationInput.vue'),
    //   ),
    //   inputWidgetProps: {},
    //   displayProps: {
    //     toDisplay(value, record) {
    //       return value?.content ?? '';
    //     },
    //   },
    // },
    customCriterionDetail: {
      visibleInForm: true,
      inputWidget: defineAsyncComponent(
        () => import('@repo/components/iep/components/longTermTargetAssociationInput.vue'),
      ),
      inputWidgetProps: {},
      displayProps: {
        toDisplay(value, record) {
          return value?.name ?? '';
        },
      },
    },

    dateRange: {
      inputWidget: 'dateRangeInput',
      displayProps: {
        toDisplay: (val: any) => {
          return val?.join(' ~ ');
        },
      },
    },
    customCriterion: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        onValueChange: (value: any, record: any) => {
          record.value.customCriterionDetail = null;
        },
        allowCreate: false,
        placeholder: '请选择关联评估',
        allowSearch: true,
        valueType: (val: any, options: any) => {
          return {
            id: val,
          };
        },
        getOptions: async () => {
          const res = await request('/evaluation/customCriterion', {
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            method: 'get',
          });
          return res.data.items.map((item) => ({
            label: item.name,
            value: item.id,
          }));
        },
      },
      displayProps: {
        toDisplay: async (value, record) => {
          const store = useCommonStore({
            api: '/evaluation/customCriterion',
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            queryParams: {
              institutionList: 1,
            },
          });
          const raw = await store.getList();

          if (Array.isArray(value)) {
            return raw
              .filter((item) => value.includes(item.id))
              .map((item) => item.name)
              .join(',');
          }
          return '';
        },
      },
    },
    preScore: {
      inputWidgetProps: {
        mode: 'button',
        style: {
          width: '120px',
        },
      },
    },
  },
};

export default { PersonalIepTargetLib };
