import { CustomSchema } from '@repo/infrastructure/types';
import { getCollaborateRowAction, getCollaboratorField, getDataSubmitAction } from '@repo/components/utils/collaborate';
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';
import useCommonStore from '@repo/infrastructure/utils/store';

const resourceRoomEvent: CustomSchema = {
  api: '/resourceRoom/resourceRoomEvent',
  rowActions: [getCollaborateRowAction('ResourceRoomEvent'), getDataSubmitAction('/resourceRoom/resourceRoomEvent')],
  fieldsMap: {
    ...getCollaboratorField(),
    attachments: {
      inputWidget: 'uploadInput',
      displayProps: {
        component: 'AttachmentsPreviewDisplay',
      },
    },
    record: {
      inputWidget: 'richInput',
      displayProps: {
        detailSpan: 2,
      },
    },
    /*    participants: {
      inputWidget: 'textareaInput',
      inputWidgetProps: {
        maxLength: 9999,
      },
      displayProps: {
        detailSpan: 2,
      },
    }, */
    // participants
    participantsIds: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        allowCreate: false,
        multiple: true,
        placeholder: '请选择参会人员',
        allowSearch: true,
        getOptions: async () => {
          const res = await request('/org/companyUser', {
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            method: 'get',
          });
          return res.data.items.map((item) => ({
            label: item.name,
            value: item.id,
          }));
        },
      },
      displayProps: {
        toDisplay: async (value, record) => {
          const store = useCommonStore({
            api: '/org/companyUser/allTeachers',
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            queryParams: {
              institutionList: 1,
            },
          });
          const raw = await store.getList();

          if (Array.isArray(value)) {
            return raw
              .filter((item) => value.includes(item.id))
              .map((item) => item.name)
              .join(',');
          }
          return '';
        },
      },
    },
    result: {
      inputWidget: 'textareaInput',
      inputWidgetProps: {
        maxLength: 9999,
      },

      displayProps: {
        detailSpan: 2,
      },
    },
    tag: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        allowCreate: true,
        options: [
          '融合教育宣讲会记录',
          '融合教育个案会记录',
          '融合教育校本培训记录',
          '融合教育课题讨论记录',
          '融合教育常规检查记录',
          '融合教育新闻宣传记录',
          '融合教育讲师授课记录',
          '其他融合教育活动记录',
          '工作会',
          '教学研讨',
          '培训',
          '论文评比',
          '优质课比赛',
          '对外交流',
          '文件',
          '其他',
        ],
      },
    },
    targetContent: {
      inputWidget: 'textareaInput',
      inputWidgetProps: {
        maxLength: 9999,
      },
      displayProps: {
        detailSpan: 2,
      },
    },
  },
};

export default { resourceRoomEvent };
