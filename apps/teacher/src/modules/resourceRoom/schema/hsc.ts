import { CustomSchema } from '@repo/infrastructure/types';
import { PROJECT_URLS } from '@repo/env-config';
import { defineAsyncComponent } from 'vue';
import { getCollaborateRowAction, getCollaboratorField, getDataSubmitAction } from '@repo/components/utils/collaborate';
import { useUserStore } from '@repo/infrastructure/store';
import { omit } from 'lodash';

const performanceColumns = [
  { key: 'domain', label: '领域', inputWidget: 'textareaInput', width: 160 },
  { key: 'performance', label: '表现', inputWidget: 'textareaInput' },
];
const problemColumns = [
  { key: 'mainProblem', label: '主要问题', inputWidget: 'textareaInput' },
  { key: 'detail', label: '具体问题', inputWidget: 'textareaInput' },
];
const solutionColumns = [
  { key: 'mainProblem', label: '主要问题', inputWidget: 'textareaInput' },
  { key: 'solution', label: '解决办法', inputWidget: 'textareaInput' },
  { key: 'personInCharge', label: '负责人', inputWidget: 'textareaInput', width: 150 },
];

const hscSchema: CustomSchema = {
  api: '/resourceRoom/homeSchoolConnection',
  formViewProps: {
    colSpan: 6,
  },
  quickSearchProps: {
    enabled: true,
    fields: ['student.name', 'guardianName', 'teacher'],
    placeholder: '按学生、家长、联系教师搜索',
  },
  rowActions: [
    getCollaborateRowAction('HomeSchoolConnection'),
    getDataSubmitAction('/resourceRoom/homeSchoolConnection'),
  ],
  detailViewProps: {
    printTitle: () => {
      const school = useUserStore().getUserFusionSchool();
      return `${school.name}入户家访记录表`;
    },
  },
  fieldsMap: {
    ...getCollaboratorField(),
    student: {
      valueType: 'Foreign',
      foreignField: {
        api: '/resourceRoom/student',
        apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
        preload: true,
      },
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/components/student/studentDetailButton.vue')),
      },
      inputWidgetProps: {
        allowClear: true,
        allowSearch: true,
        placeholder: '请选择学生',
        // disabled: true,
        valueType: (value: any, computedOptions: any[]) => {
          const selected = computedOptions.find((item) => item.raw?.id === value);
          if (!selected) {
            return undefined;
          }
          return {
            ...selected.raw,
          };
        },
      },
    },
    performancesAtSchool: {
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        colSpan: 24,
        columns: performanceColumns,
      },
      displayProps: {
        detailSpan: 24,
        component: 'ListTableDisplay',
        columns: performanceColumns,
      },
    },
    performancesAtHome: {
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        colSpan: 24,
        columns: performanceColumns,
      },
      displayProps: {
        detailSpan: 24,
        component: 'ListTableDisplay',
        columns: performanceColumns,
      },
    },
    problems: {
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        colSpan: 24,
        columns: problemColumns,
      },
      displayProps: {
        detailSpan: 24,
        component: 'ListTableDisplay',
        columns: problemColumns,
      },
    },
    solutions: {
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        colSpan: 24,
        columns: solutionColumns,
      },
      displayProps: {
        detailSpan: 24,
        component: 'ListTableDisplay',
        columns: solutionColumns,
      },
    },
    connectionDate: {
      inputWidgetProps: {
        disabledDate: (current: any) => {
          return current && current > new Date();
        },
      },
    },
  },
};

export default { hscSchema };
