import { CustomSchema } from '@repo/infrastructure/types';
import { getCollaborateRowAction, getCollaboratorField, getDataSubmitAction } from '@repo/components/utils/collaborate';

const awards: CustomSchema = {
  api: '/teacher/awardRecord',
  rowActions: [
    getCollaborateRowAction('AwardRecord'),
    getDataSubmitAction('/teacher/awardRecord', {
      disabled: (record: any) => {
        return !record.finished || (record.submitStatus !== 'Draft' && record.submitStatus !== 'Rejected');
      },
    }),
  ],
  fieldsMap: {
    ...getCollaboratorField(),
    attachments: {
      inputWidget: 'uploadInput',
      displayProps: {
        component: 'AttachmentsPreviewDisplay',
      },
    },
    awardLevel: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        allowCreate: true,
        placeholder: '请选择或输入',
        options: ['校级', '区级', '市级', '省级', '国家级'],
      },
    },
    level: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        allowCreate: true,
        placeholder: '请选择或输入',
        options: ['一等奖', '二等奖', '三等奖'],
      },
    },
    type: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        allowCreate: true,
        placeholder: '请选择或输入',
        options: ['融合教育教学成果获奖', '学生参与相关活动获奖'],
      },
    },
  },
};

export default { awards };
