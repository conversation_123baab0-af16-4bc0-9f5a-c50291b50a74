import { CustomSchema } from '@repo/infrastructure/types';

const teacherWorkPlan: CustomSchema = {
  api: '/teacher/teacherWorkPlan',
  fieldsMap: {
    content: {
      inputWidget: 'richInput',
      displayProps: {
        detailSpan: 2,
      },
    },
    attachments: {
      inputWidget: 'uploadInput',
      displayProps: {
        component: 'AttachmentsPreviewDisplay',
      },
    },
    timePeriod: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        allowCreate: true,
        placeholder: '请选择或输入',
        options: ['全年', '春季', '秋季'],
      },
    },
    type: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        allowCreate: true,
        placeholder: '请选择或输入',
        options: ['全校融合教育工作', '相关部门融合教育', '资源教室工作计划', '班主任工作计划'],
      },
    },
  },
};

export default { teacherWorkPlan };
