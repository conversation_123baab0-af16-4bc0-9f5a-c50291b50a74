<template>
  <teleport to="body">
    <div v-if="dialogVisible" class="browser-check-overlay">
      <a-modal
        v-model:visible="dialogVisible"
        :show-close="false"
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        title="浏览器提示"
        :render-to-body="false"
      >
        <div class="tips-wrapper">
          <strong>请不要在微信或Safari中使用本系统，可能存在兼容问题</strong>
          <p>为了更好的体验，请选择 Edge、Chrome或360浏览器登录系统</p>
          <a href="https://www.microsoft.com/zh-cn/edge/download">点此下载 Edge 浏览器</a>
          <div style="margin-top: 10px"> * 如果您使用Windows 11系统，可以直接使用Edge浏览器，无需下载 </div>
          <div v-if="isSafari">
            <small> * 如果您已安装 Edge、Chrome、360等现代浏览器，请复制以下链接到浏览器打开： </small>
            <div id="url" style="margin-top: 10px">
              <a-input :model-value="url" size="small" />
            </div>
          </div>
        </div>
      </a-modal>
      <div v-if="isWeixin" class="right-top-btn">
        或者点击上方按钮
        <div>在默认浏览器中打开</div>
      </div>
    </div>
  </teleport>
</template>

<script>
  export default {
    name: 'BrowserCheck',
    data() {
      return {
        isWeixin: false,
        isSafari: false,
        url: window.location.href,
      };
    },
    computed: {
      dialogVisible() {
        return this.isWeixin || this.isSafari;
      },
    },
    mounted() {
      const ua = navigator.userAgent.toLowerCase();
      this.isWeixin = ua.indexOf('micromessenger') !== -1;
      this.isSafari = ua.indexOf('safari') !== -1 && ua.indexOf('chrome') === -1;
    },
  };
</script>

<style lang="scss" scoped>
  .browser-check-active {
    isolation: isolate;
    z-index: 9000 !important;
  }
  @keyframes bounce {
    0%,
    20%,
    50%,
    80%,
    100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }
  .tips-wrapper {
    text-align: center;
    a {
      color: #409eff;
    }
  }
  .right-top-btn {
    position: fixed;
    top: 0;
    right: 8px;
    background-color: #f5f5f5;
    padding: 5px 10px;
    font-size: 12px;
    color: #666;
    z-index: 9990;
    text-align: center;
    border-radius: 0 0 16px 16px;
    animation: bounce 3s infinite;
  }
</style>
