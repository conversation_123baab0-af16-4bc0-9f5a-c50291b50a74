import _, { isArray, isPlainObject } from 'lodash';
import { useDataStructureStore } from '@repo/infrastructure/store';
import dayjs from 'dayjs';

export class DataStructure {
  dataStructure;

  dsMap = {};

  instance;

  enumMap = {};

  preLoadedForeign;

  alreadyPreLoadedForeignApi;

  private static instance: any;

  constructor() {
    this.dataStructure = {};
    this.instance = null;
    this.preLoadedForeign = {};
    this.alreadyPreLoadedForeignApi = [];

    DataStructure.instance = this;
  }

  static getInstance() {
    if (!DataStructure.instance) {
      DataStructure.instance = new DataStructure();
    }
    return DataStructure.instance;
  }

  async load(force, callback) {
    if (!Object.keys(this.dataStructure).length || force === true) {
      const store = useDataStructureStore();
      const res = await store.initDataStructure();

      DataStructure.getInstance().dataStructure = res;

      Object.keys(res).forEach((k) => {
        const map = {};
        const resK = res[k];
        Object.keys(resK).forEach((item) => {
          const resKItem = resK[item];
          map[resKItem.key] = resKItem;
        });
        this.dsMap[k] = map;
      });

      typeof callback === 'function' && callback(res);
    }
  }

  get(api) {
    return this.dataStructure[api] || [];
  }

  getMap(api) {
    return this.dsMap[api] || {};
  }

  setAlreadyLoadedApi(api) {
    this.alreadyPreLoadedForeignApi.push(api);
  }

  isAlreadyLoadedApi(api) {
    return this.alreadyPreLoadedForeignApi.indexOf(api) >= 0;
  }

  setPreLoadedForeign(api, data) {
    this.preLoadedForeign[api] = data;
  }

  getPreLoadedForeign(api) {
    return this.preLoadedForeign[api];
  }

  // eslint-disable-next-line class-methods-use-this
  getEnumDisplay(api, field, value) {
    const me = DataStructure.getInstance();
    const enumName = `${api}_${field}`;
    const config = me.getMap(api)[field];

    if (!config || !config.staticStore || config.staticStore.length < 1) {
      return value;
    }

    if (!me.enumMap[enumName] || Object.keys(me.enumMap[enumName]).length === 0) {
      me.enumMap[enumName] = {};
      config.staticStore.forEach((item) => {
        me.enumMap[enumName][item.id] = item.name;
      });
    }

    return me.enumMap[enumName][value];
  }

  // eslint-disable-next-line class-methods-use-this
  getDisplay(column, value, row) {
    if (typeof column.getDisplay === 'function') {
      return column.getDisplay(value, row);
    }
    if (isArray(value)) {
      return value;
    }
    if (isPlainObject(value)) {
      return column.labelField ? value[column.labelField] || value.name || value.title : value.name || value.title;
    }

    return value;
  }

  // eslint-disable-next-line class-methods-use-this
  getFormSubmitValue(data, model) {
    const result = {};
    const raw = data || {};

    Object.keys(raw).forEach((field) => {
      const columnConfigs = model.getFields(true)[field] || {};
      let value = raw[field];

      switch (columnConfigs.inputControl) {
        case 'region':
          if (_.isArray(value)) {
            columnConfigs.inputControlProps = columnConfigs.inputControlProps || {};
            if (columnConfigs.inputControlProps.multiple) {
              value = Array.from(value, (item) => {
                return item.join(' ');
              }).join(',');
            } else {
              value = value.join(' ');
            }
          }
          break;
        case 'date':
          if (_.isArray(value)) {
            value = value.map((item) => {
              return dayjs(item).format('yyyy-MM-DD HH:mm:ss');
            });
          } else {
            value = dayjs(value).format('yyyy-MM-DD HH:mm:ss');
          }
          break;
        case 'select':
          if (columnConfigs.type === 'ENUM') {
            // eslint-disable-next-line no-nested-ternary
            value = value ? (value.id === undefined ? value : value.id) : null;
          }
          if (value === '') {
            value = null;
          }
          break;
        case 'combo':
          if (value === '') {
            value = null;
          }
          break;
        default:
          break;
      }

      if (typeof columnConfigs.getSubmitValue === 'function') {
        result[field] = columnConfigs.getSubmitValue(value, raw);
      } else {
        result[field] = value;
      }
    });

    return result;
  }

  setMap(api, map) {
    this.dsMap[api] = map;
  }
}

export const dsInstance = DataStructure.getInstance();
