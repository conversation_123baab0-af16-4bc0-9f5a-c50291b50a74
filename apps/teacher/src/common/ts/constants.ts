import { PROJECT_URLS as urls, ENV } from '@repo/env-config'

export default {
  LOCALE_CACHE_KEY: 'arco-locale',
  DEFAULT_DATE_FORMAT: 'MM-DD HH:mm',
  FULL_DATE_FORMAT: 'YYYY-MM-DD HH:mm:ss',
  TOKEN_CACHE_KEY: 'token',
  USER_ID_CACHE_KEY: 'userId',
  FROM_MAIN_PROJECT_PATH_CACHE_KEY: 'fromMain',
}

export const PROJECT_URLS: {
  GO_PROJECT_API: string
  GO_PROJECT: string
  MAIN_PROJECT_API: string
  WS_URL: string
  env: any
} = {
  MAIN_PROJECT_API: urls.MAIN_PROJECT_API,
  GO_PROJECT_API: urls.GO_PROJECT_API,
  GO_PROJECT: urls.GO_PROJECT,
  WS_URL: urls.WS_URL,
  env: ENV,
}

export const ROLES = {
  MANAGER: 'CENTER_MANAGER',
  TEACHER: 'TEACHER',
  SCHOOL_MANAGER: 'SCHOOL_MANAGER',
}

export const DEFAULT_LIST_FIELDS: string[] = [
  'enabled',
  'createdBy',
  'createdAt',
  'modifiedBy',
  'modifiedAt',
]

export const COURSE_CATEGORIES: string[] = ['生活语文', '生活数学', '生活适应']

export const COURSE_GRADES: string[] = [
  '一年级',
  '二年级',
  '三年级',
  '四年级',
  '五年级',
  '六年级',
]

export const COURSE_PERIODS: string[] = ['上册', '下册']
