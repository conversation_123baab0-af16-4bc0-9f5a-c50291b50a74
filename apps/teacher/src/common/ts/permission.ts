import { toRaw } from 'vue';
import AntPathMatcher from '@maxbilbow/ant-path-matcher';
import { useUserStore } from '@repo/infrastructure/store';
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';

const matcher = AntPathMatcher();

export const getUserInfo = () => {
  const userStore = useUserStore();
  return userStore.userInfo;
};

export const setUserInfo = (info: any) => {
  const userStore = useUserStore();
  userStore.setInfo(toRaw(info));
};

export const loadCurrentUser = async (redirect?: boolean, options: any) => {
  try {
    // const { data: userInfo } = await request('/org/session/me', {
    //   baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    //   ...(options || {}),
    // });
    // const { data: sysRoles }: any = await request('/org/sysRole', {
    //   baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    //   ...(options || {}),
    // });

    const [{ data: userInfo }, { data: sysRoles }] = await Promise.all([
      request('/org/session/me', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        ...(options || {}),
      }),
      request('/org/sysRole', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        ...(options || {}),
      }),
    ]);

    localStorage.setItem('allSysRoles', JSON.stringify(sysRoles.items || []));
    setUserInfo(userInfo);
    return userInfo;
  } catch (e) {
    if (redirect !== false) {
      console.log(e);
      // clearToken();
      // window.top.location.href = '/';
    }
  }
  return null;
};

export const getAllSysRoles = () => {
  const raw = localStorage.getItem('allSysRoles');
  if (!raw) {
    return null;
  }
  return JSON.parse(raw);
};

export const isAuthorized = (permission: string, grantedPermissions?: string[]) => {
  if (!permission) {
    return true;
  }
  const userInfo = getUserInfo();
  // if (userInfo.su) {
  //   return true;
  // }
  if (!grantedPermissions) {
    grantedPermissions = userInfo?.authorities;
  }

  return !!(
    grantedPermissions?.includes(permission) ||
    grantedPermissions?.some((p) => p.indexOf(permission) === 0 || matcher.match(p, permission))
  );
};

export const isAnyAuthorized = (permissions: string[], grantedPermissions?: string[]) => {
  if (!permissions || !permissions?.length) {
    return true;
  }
  return permissions?.some((p) => isAuthorized(p, grantedPermissions));
};
