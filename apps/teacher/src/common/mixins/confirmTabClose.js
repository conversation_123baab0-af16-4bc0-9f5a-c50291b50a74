export default {
  beforePageLeave(tab, type) {
    // 浏览器窗口刷新或者关闭时，支持的浏览器会展示确认消息
    if (type === 'unload') {
      return `您在“${tab.title}”页面的更改尚未完成，是否要离开？`
    }

    // 离开类型
    const action = {
      close: '关闭',
      refresh: '刷新',
      replace: '替换',
      leave: '离开',
    }[type]

    const msg = `您确认要${action} “${tab.title}” 吗？`

    // 此处使用了 Element 的 confirm 组件
    // 需将 closeOnHashChange 配置为 false，以避免路由切换导致确认框关闭
    return this.$confirm(msg, '提示', { closeOnHashChange: false })
  },
}
