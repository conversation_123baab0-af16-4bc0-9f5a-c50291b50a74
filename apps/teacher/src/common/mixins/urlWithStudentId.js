import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';

export default {
  data() {
    return {
      studentInfo: {},
    };
  },

  methods: {
    async getStudentInfo(id) {
      id = id || this.$route.query.studentId;
      const { data: data } = await request(`/resourceRoom/student/${id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      if (data && data.id) {
        this.studentInfo = data;
        this.theTableTitle = data.name + this.tableTitleSuffix;
      }
    },
  },
};
