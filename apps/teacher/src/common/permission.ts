import { isArray } from 'lodash';
import { toRaw } from 'vue';
import { useUserStore } from '@repo/infrastructure/store';
import { clearToken } from '@repo/infrastructure/auth';
import { PROJECT_URLS } from '@repo/env-config';
import { request } from '@repo/infrastructure/request';
import { Modal } from '@arco-design/web-vue';
import { clearClientToken, clientRedirectTo, getElectronApi, isElectron } from '@repo/infrastructure/electron';
// import CompanyUserModel from '@/modules/org/model/CompanyUserModel';
// import { isAnyAuthorized, isAuthorized } from '@/common/ts/permission';

// export const hasModulePerm = window.$vueApp.directive('hasModulePerm', {
//   mounted(el, binding) {
//     const companyModel = CompanyUserModel.getInstance();
//     const res = companyModel.isModuleAuthorized(binding.value);
//     if (!res) {
//       el.remove();
//     }
//   },
// });

// export const permission = window.$vueApp.directive('permission', {
//   mounted(el, binding) {
//     if (isArray(binding.value) && !isAnyAuthorized(binding.value)) {
//       el.remove();
//     } else if (!isAuthorized(binding.value)) {
//       el.remove();
//     }
//   },
// });

// export const anyPermission = window.$vueApp.directive('permission', {
//   mounted(el, binding) {
//     if (isArray(binding.value) && !isAnyAuthorized(binding.value)) {
//       el.remove();
//     } else if (!isAuthorized(binding.value)) {
//       el.remove();
//     }
//   },
// });

// export const hasModulePerm = {
//   beforeMount: (el: any, binding: any) => {
//     const companyModel = CompanyUserModel.getInstance();
//     const res = companyModel.isModuleAuthorized(binding.value);
//     if (!res) {
//       el.remove();
//     }
//   },
// };
//
// export const permission = {
//   beforeMount: (el: any, binding: any) => {
//     if (isArray(binding.value) && !isAnyAuthorized(binding.value)) {
//       el.remove();
//     } else if (!isAuthorized(binding.value)) {
//       el.remove();
//     }
//   },
// };
//
// export const anyPermission = {
//   beforeMount: (el: any, binding: any) => {
//     if (isArray(binding.value) && !isAnyAuthorized(binding.value)) {
//       el.remove();
//     } else if (!isAuthorized(binding.value)) {
//       el.remove();
//     }
//   },
// };

export const getUserInfo = (): any => {
  const userStore = useUserStore();
  return userStore.userInfo;
};

export const setUserInfo = (info: any) => {
  const userStore = useUserStore();
  userStore.setInfo(info);
};

export const loadCurrentUser = async () => {
  try {
    const [{ data: userInfo }, { data: sysRoles }] = await Promise.all([
      request('/org/session/me', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      }),
      request('/org/sysRole', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      }),
    ]);

    localStorage.setItem('allSysRoles', JSON.stringify(sysRoles.items));
    setUserInfo(toRaw(userInfo));
    return userInfo;
  } catch (e) {
    // clearToken();
    // window.top.location.href = '/';
    Modal.error({
      hideCancel: true,
      title: '获取用户信息失败',
      content: '获取用户信息失败，请重新登录',
      onOk: () => {
        clearClientToken();
        if (isElectron) {
          getElectronApi().send('relaunchToLogin');
          return;
        }
        window.top.location.href = '/?current-user-error';
      },
    });
  }
  return null;
};

export const getAllSysRoles = () => {
  const raw = localStorage.getItem('allSysRoles');
  if (!raw) {
    return null;
  }
  return JSON.parse(raw);
};
