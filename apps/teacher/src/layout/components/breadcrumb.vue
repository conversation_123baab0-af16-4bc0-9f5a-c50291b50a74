<script setup lang="ts">
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';

  const menuStore = useUserMenuStore();
  const route = useRoute();
  const router = useRouter();
  const breadCrumbs = ref([]);

  const go = (path) => {
    router.push(path);
  };

  watch(
    () => route.path,
    () => {
      breadCrumbs.value = [];
      const menuInfo = menuStore.getCurrentTeacherMenuInfo(route);
      if (menuInfo.app) {
        breadCrumbs.value.push(menuInfo.app);
      }
      if (menuInfo.module) {
        breadCrumbs.value.push(menuInfo.module);
      }
      if (menuInfo.subModules) {
        let children = menuInfo.module?.children || [];
        // eslint-disable-next-line no-restricted-syntax
        for (const subModule of menuInfo.subModules) {
          const child = children.find((item) => item.key === subModule);
          if (child) {
            breadCrumbs.value.push(child);
            children = child.children || [];
          } else {
            break;
          }
        }
      }
    },
    { immediate: true },
  );
</script>

<template>
  <a-breadcrumb>
    <a-breadcrumb-item class="cursor-pointer" @click="() => go('/')">
      <a-tooltip content="工作台">
        <IconHome />
      </a-tooltip>
    </a-breadcrumb-item>
    <a-breadcrumb-item v-for="item in breadCrumbs" :key="item.key">
      {{ item.label }}
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>

<style scoped lang="scss"></style>
