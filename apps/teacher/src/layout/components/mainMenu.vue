<script setup lang="ts">
  import { useRouter } from 'vue-router';
  import { isString } from 'lodash';

  defineProps({
    menus: {
      type: Array,
      required: true,
    },
  });

  const router = useRouter();

  const handleMenuClick = (menu: any) => {
    if (menu.link && !menu.children?.length) {
      router.push(menu.link);
    }
  };
</script>

<template>
  <div v-for="m in menus" :key="m.key">
    <a-menu-item v-if="!m.children?.length" :key="m.permission" @click="() => handleMenuClick(m)">
      <template #icon>
        <i v-if="m.icon && isString(m.icon) && m.icon.indexOf('icon-') == 0" class="iconfont" :class="m.icon"></i>
        <component :is="m.icon" v-else-if="m.icon" />
      </template>
      {{ m.label }}
    </a-menu-item>
    <a-sub-menu v-else :key="m.permission">
      <template #icon>
        <i v-if="m.icon && isString(m.icon) && m.icon.indexOf('icon-') == 0" class="iconfont" :class="m.icon"></i>
        <IconMenu v-else />
      </template>
      <template #title>
        {{ m.label }}
      </template>
      <main-menu :menus="m.children" />
    </a-sub-menu>
  </div>
</template>

<style scoped lang="scss"></style>
