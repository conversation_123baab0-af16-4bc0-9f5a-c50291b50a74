import { getURLParams } from '@/common/utils.ts';
import { loadCurrentUser } from '@/common/permission';

const appInit = async () => {
  let userInfo;
  const { register, code, bo } = getURLParams();
  if (!register && !code && !bo) {
    userInfo = await loadCurrentUser();
  }

  // const { proxy: app } = getCurrentInstance();
  // app.config.globalProperties = userInfo;
  return {
    userInfo,
  };
};

export default appInit();
