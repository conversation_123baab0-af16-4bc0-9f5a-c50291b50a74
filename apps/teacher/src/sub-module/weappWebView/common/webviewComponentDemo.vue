<script setup lang="ts">
  import { computed, ref, inject } from 'vue';

  const watchDebounced = inject('watchDebounced') as any;
  const DISORDER_TYPES = inject('DISORDER_TYPES');
  const nationsList = inject('nationsList');
  const Message = inject('Message') as any;

  const checkIdCardAvailable = inject('checkIdCardAvailable') as any;
  // const uploader = inject('Uploader') as any;
  const captcha = inject('Captcha');

  const props = defineProps({
    record: {
      type: Object,
      default: () => ({}),
    },
    loading: {
      type: Boolean,
      default: false,
    },
    submitted: {
      type: Boolean,
      default: false,
    },
  });

  const diseaseTypes = ['无', '癫痫', '哮喘', '心脏病', '其他'];
  const relationShips = ['父亲', '母亲', '爷爷', '奶奶', '外公', '外婆', '其他亲属', '其他非亲属'];
  const resettlementType = ['普通学校随班', '普通学校特教班', '特教学校', '送教上门'];
  const familyTeachingType = ['民主型', '放任型', '溺爱型'];

  const emits = defineEmits(['back', 'update:record', 'submit']);
  const infoFormRef = ref<any>(null);

  const attachments = ref([]);

  const student = computed({
    get: () => props.record,
    set: (val) => {
      emits('update:record', {
        ...val,
        attachments: [
          {
            type: 'Other',
            attachments: attachments.value,
          },
        ],
      });
    },
  });

  const idCardNoChecked = computed(() => {
    if (!student.value.idCardNo) {
      return false;
    }

    return checkIdCardAvailable(student.value.idCardNo);
  });

  const rules = {
    'name': [
      { required: true, message: '请输入姓名', trigger: 'blur' },
      { min: 2, max: 10, message: '姓名长度在 2 到 10 个字符', trigger: 'blur' },
    ],
    'idCardNo': [
      { required: true, message: '请输入身份证号', trigger: 'blur' },
      {
        validator: async (value, cb) => {
          if (!checkIdCardAvailable(value)) {
            cb('请输入正确的身份证号');
          }
          cb();
        },
        trigger: 'blur',
      },
    ],
    'nation': [{ required: true, message: '请选择民族', trigger: 'blur' }],
    'gender': [{ required: true, message: '请选择性别', trigger: 'blur' }],
    'birthday': [{ required: true, message: '请选择出生日期', trigger: 'blur' }],
    'additionalData.currentAddress': [{ required: true, message: '请输入现居住地', trigger: 'blur' }],
    'guardian': [{ required: true, message: '请输入家长姓名', trigger: 'blur' }],
    'familyMembers': [{ required: true, message: '请输入家庭成员', trigger: 'blur' }],
    'guardianPhone': [
      { required: true, message: '请输入家长电话', trigger: 'blur' },
      { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
    ],
    'additionalData.registeredAddress': [{ required: true, message: '请输入户籍所在地' }],
    'additionalData.disorders': [{ required: true, message: '请选择残疾类型' }],
    'disabilityLevel': [{ required: true, message: '请选择残疾等级' }],
    'disabilityCertificateNo': [{ required: true, message: '请输入残疾证号' }],
    'additionalData.disease': [{ required: true, message: '请选择是否患有疾病' }],
    'additionalData.otherDisease': [{ required: true, message: '如有其他疾病请输入', trigger: 'blur' }],
    // 'additionalData.allergyHistory': [{ required: true, message: '请选择是否有过敏史' }],
    'additionalData.allergySource': [{ required: true, message: '如有过敏史请输入过敏源' }],
    // 'additionalData.medicineHistory': [{ required: true, message: '请选择是否有长期用药' }],
    'additionalData.medicine': [{ required: true, message: '如有长期用药请输入药物名称' }],
    'additionalData.medicineSideEffect': [{ required: true, message: '请输入药物副作用' }],
    // 'additionalData.enjoyAidPolicy': [{ required: true, message: '请选择是否享受救助政策' }],
    'additionalData.enjoyAidPolicyDescription': [{ required: true, message: '如享受救助政策请输入' }],
    'additionalData.commuteMethod': [{ required: true, message: '请选择上下学方式' }],
    'additionalData.resettlementType': [{ required: true, message: '请选择安置意向' }],
    'additionalData.familyTeachingType': [{ required: true, message: '请选择教养方式' }],
    'additionalData.communityEnvironment': [{ required: true, message: '请输入社区环境描述' }],
    'additionalData.activityDescription': [{ required: true, message: '请输入活动情况描述' }],
  };

  const handleGoHome = () => {
    emits('back');
  };

  const token = inject('token');

  const handleSubmit = async () => {
    const res = await infoFormRef.value.validate();
    if (res) {
      return;
    }
    const validFamilyMembers = student.value.familyMembers.filter((fm: any) => fm.name && fm.relationship);
    if (validFamilyMembers.length === 0) {
      Message.error('请至少添加一名家庭成员');
    }
    emits('submit');
  };

  const handleAddNewFamilyMember = () => {
    student.value.familyMembers = student.value.familyMembers || [];
    student.value.familyMembers.push({
      relation: '',
      name: '',
    });
  };

  const handleRemoveFamilyMember = (idx: number) => {
    student.value.familyMembers.splice(idx, 1);
  };

  watchDebounced(
    () => student.value.idCardNo,
    async (idCardNo) => {
      if (idCardNo?.length === 18) {
        const year = idCardNo.substring(6, 10);
        const month = idCardNo.substring(10, 12);
        const day = idCardNo.substring(12, 14);
        const gender = Number(idCardNo.substring(16, 17)) % 2 === 0 ? '女' : '男';

        student.value = {
          ...student.value,
          gender,
          birthday: `${year}-${month}-${day}`,
        };
      }
    },
    { debounce: 500 },
  );
</script>

<template>
  <div>
    <div v-if="submitted">
      <div class="flex items-center justify-center h-full mt-10">
        <a-result status="success" title="提交成功">
          <template #subtitle>
            审核结果将在3个工作日内以短信通知发送给您，请注意查收。
            <div>您也可以通过本小程序中【我的】页面查看进度</div>
          </template>
          <template #extra>
            <a-space>
              <a-button type="primary" @click="handleGoHome">返回首页</a-button>
            </a-space>
          </template>
        </a-result>
      </div>
    </div>
    <div v-else>
      <div class="bg-slate-50 border rounded border-slate-100 m-2 p-2 leading-7"> 请您认真填写以下各项内容 </div>
      <a-form ref="infoFormRef" :model="student" layout="inline" :label-col-style="{ width: '115px' }" :rules="rules">
        <div class="px-2">
          <a-divider>基本信息</a-divider>
          <a-form-item label="姓名" field="name">
            <a-input v-model.trim="student.name" :disabled="student.id" />
          </a-form-item>
          <a-form-item label="身份证号" field="idCardNo">
            <a-input v-model.trim="student.idCardNo" />
          </a-form-item>
          <a-form-item label="民族" class="flex-1" field="nation">
            <a-select v-model="student.nation" :options="nationsList" allow-search />
          </a-form-item>
          <div v-if="idCardNoChecked">
            <a-form-item label="性别" field="gender">
              <a-radio-group v-model="student.gender">
                <a-radio value="男">男</a-radio>
                <a-radio value="女">女</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="出生日期" field="birthday">
              <a-date-picker v-model="student.birthday" type="date" />
            </a-form-item>
          </div>
          <div class="flex gap-2">
            <a-form-item label="独生子女" field="additionalData.onlyChild">
              <a-switch v-model="student.additionalData.onlyChild" unchecked-text="否" checked-text="是" />
            </a-form-item>
            <a-form-item label="单亲" field="additionalData.singleParent">
              <a-switch v-model="student.additionalData.singleParent" unchecked-text="否" checked-text="是" />
            </a-form-item>
          </div>
          <a-form-item label="主要照顾者" field="guardian">
            <a-input v-model.trim="student.guardian" placeholder="请填写主要照顾者姓名" />
          </a-form-item>
          <a-form-item label="联系电话" field="guardianPhone">
            <a-input v-model.trim="student.guardianPhone" placeholder="请填写主要照顾者电话" />
          </a-form-item>
          <a-form-item label="户籍所在地" field="additionalData.registeredAddress">
            <a-textarea v-model.trim="student.additionalData.registeredAddress" />
          </a-form-item>
          <a-form-item label="现居住地址" field="additionalData.currentAddress">
            <a-textarea v-model.trim="student.additionalData.currentAddress" />
          </a-form-item>
          <a-form-item label="残疾类型" field="additionalData.disorders">
            <a-select v-model="student.additionalData.disorders" :options="DISORDER_TYPES" allow-search multiple />
          </a-form-item>
          <a-form-item label="残疾等级" field="disabilityLevel">
            <a-select v-model="student.disabilityLevel" :options="['一级', '二级', '三级', '四级']" />
          </a-form-item>
          <a-form-item label="残疾证号" field="disabilityCertificateNo">
            <a-input v-model.trim="student.disabilityCertificateNo" />
          </a-form-item>
          <a-form-item label="有无自伤史" field="additionalData.selfHarmHistory">
            <a-switch v-model="student.additionalData.selfHarmHistory" unchecked-text="无" checked-text="有" />
          </a-form-item>
          <a-form-item label="患有疾病" field="additionalData.disease">
            <a-select v-model="student.additionalData.disease" :options="diseaseTypes" multiple />
          </a-form-item>
          <a-form-item v-if="student.additionalData.disease?.includes('其他')" field="additionalData.otherDisease">
            <a-input v-model.trim="student.additionalData.otherDisease" placeholder="请输入其他疾病" />
          </a-form-item>
          <a-form-item label="过敏史" field="additionalData.allergyHistory">
            <a-switch v-model="student.additionalData.allergyHistory" unchecked-text="无" checked-text="有" />
          </a-form-item>
          <a-form-item v-if="student.additionalData.allergyHistory" field="additionalData.allergySource" label="过敏源">
            <a-textarea v-model.trim="student.additionalData.allergySource" placeholder="请输入过敏源" />
          </a-form-item>
          <a-form-item label="长期用药" field="additionalData.medicineHistory">
            <a-switch v-model="student.additionalData.medicineHistory" unchecked-text="无" checked-text="有" />
          </a-form-item>
          <a-form-item v-if="student.additionalData.medicineHistory" field="additionalData.medicine" label="服用药物">
            <a-input v-model.trim="student.additionalData.medicine" placeholder="请输入服用药物" />
          </a-form-item>
          <a-form-item
            v-if="student.additionalData.medicineHistory"
            field="additionalData.medicineSideEffect"
            label="副作用"
          >
            <a-textarea v-model.trim="student.additionalData.medicineSideEffect" placeholder="用药副作用" />
          </a-form-item>
          <a-form-item label="享受救助政策" field="additionalData.enjoyAidPolicy">
            <a-switch v-model="student.additionalData.enjoyAidPolicy" unchecked-text="否" checked-text="是" />
          </a-form-item>
          <a-form-item
            v-if="student.additionalData.enjoyAidPolicy"
            field="additionalData.enjoyAidPolicyDescription"
            label="政策说明"
          >
            <a-textarea
              v-model.trim="student.additionalData.enjoyAidPolicyDescription"
              placeholder="请输入享受救助政策"
            />
          </a-form-item>
          <a-form-item label="上下学方式" field="additionalData.commuteMethod">
            <a-select v-model="student.additionalData.commuteMethod" :options="['家长接送', '自行上下学']" />
          </a-form-item>

          <a-divider class="!mt-8">家庭成员情况</a-divider>
          <a-form-item label="家庭成员" field="familyMembers" required>
            <div>
              <div>
                <a-button size="mini" @click="handleAddNewFamilyMember">
                  <template #icon>
                    <IconPlus />
                  </template>
                  添加家庭成员
                </a-button>
              </div>
              <div v-for="(fm, idx) in student.familyMembers" :key="idx" class="mt-2 border-b-slate-100 pb-2 border-b">
                <div class="flex gap-2">
                  <a-select
                    v-model="fm.relationship"
                    allow-search
                    allow-create
                    :options="relationShips"
                    placeholder="关系(可输入)"
                  />
                  <a-input v-model.trim="fm.name" placeholder="姓名" />
                </div>
                <a-input v-model.trim="fm.remark" class="mt-2" placeholder="身份证号" />
                <a-input v-model.trim="fm.phone" class="mt-2" placeholder="联系电话" />
                <a-input v-model.trim="fm.jobTitle" class="mt-2" placeholder="工作单位" />
                <div class="flex gap-2 items-center">
                  <a-select
                    v-model="fm.education"
                    :options="['小学', '初中', '高中', '中专', '大专', '本科', '研究生']"
                    allow-create
                    allow-search
                    class="mt-2 flex-1"
                    placeholder="文化程度(选择或输入)"
                  />
                  <a-button type="outline" status="danger" @click="() => handleRemoveFamilyMember(idx)">
                    <template #icon>
                      <IconDelete />
                    </template>
                  </a-button>
                </div>
              </div>
            </div>
          </a-form-item>
          <a-form-item label="安置意向" field="additionalData.resettlementType">
            <a-select v-model="student.additionalData.resettlementType" :options="resettlementType" multiple />
          </a-form-item>
          <a-form-item label="教养方式" field="additionalData.familyTeachingType">
            <a-select v-model="student.additionalData.familyTeachingType" :options="familyTeachingType" />
          </a-form-item>

          <a-divider class="!mt-8">社区环境</a-divider>
          <a-form-item label="社区环境描述" field="additionalData.communityEnvironment">
            <a-textarea v-model.trim="student.additionalData.communityEnvironment" class="h-40" auto-size />
          </a-form-item>
          <a-form-item label="活动情况描述" field="additionalData.activityDescription">
            <a-textarea
              v-model.trim="student.additionalData.activityDescription"
              class="h-60"
              placeholder="请输入学生在社区中活动情况描述"
            />
          </a-form-item>
        </div>

        <div class="flex gap-2 w-full flex-col">
          <captcha id="resettlement_apply" scene-id="xxg85bsj" :handler="handleSubmit" />
          <a-button
            id="ali-captcha-button-resettlement_apply"
            class="w-full mt-2"
            size="large"
            type="primary"
            :loading="loading"
          >
            <template #icon>
              <IconCheck />
            </template>
            提交安置申请
          </a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
