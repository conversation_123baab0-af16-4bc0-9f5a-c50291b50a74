<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { AttachmentToUpload, getOssProcessor, IAttachmentProcessor } from '@repo/infrastructure/upload';
  import { useRoute } from 'vue-router';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { setHtmlTitle } from '@repo/ui/components/utils/utils';
  import { clearToken, getToken } from '@repo/infrastructure/auth';

  // 用于存储视频流
  const videoRef = ref<HTMLVideoElement | null>(null);
  const canvasRef = ref<HTMLCanvasElement | null>(null);

  // 用于存储拍照后的图片数据
  const photoData = ref<string | null>();

  // 是否翻转摄像头
  const isFlipped = ref(false);

  // 初始化摄像头
  /*  const initCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });
      if (videoRef.value) {
        videoRef.value.srcObject = stream;
      }
    } catch (error) {
      Message.warning('无法访问摄像头，请确保已授予摄像头权限。');
    }
  }; */

  const isFront = ref(false);
  const initCamera = async () => {
    try {
      const facingMode = isFront.value ? 'user' : 'environment';
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode,
        },
        audio: false,
      });

      if (videoRef.value) {
        videoRef.value.srcObject = stream;
      }
    } catch (error) {
      Message.warning('无法访问摄像头，请确保已授予摄像头权限。');
    }
  };

  // 拍照
  const takePhoto = () => {
    if (videoRef.value && canvasRef.value) {
      const context = canvasRef.value.getContext('2d');
      if (context) {
        // 设置画布尺寸与视频帧一致
        canvasRef.value.width = videoRef.value.videoWidth;
        canvasRef.value.height = videoRef.value.videoHeight;

        // 如果需要翻转，先水平翻转画布
        if (isFlipped.value) {
          context.translate(canvasRef.value.width, 0);
          context.scale(-1, 1);
        }

        // 将视频帧绘制到画布上
        context.drawImage(videoRef.value, 0, 0, canvasRef.value.width, canvasRef.value.height);

        // 将画布内容转换为图片数据（Base64）
        photoData.value = canvasRef.value.toDataURL('image/jpeg', 0.7);
      }
    }
  };

  const base64ToBlob = async (base64: string): Blob => {
    const arr = base64.split(',');
    const mime = arr[0].match(/:(.*?);/)?.[1];
    if (!mime) {
      Message.error('文件转换失败');
      throw new Error('无法解析 MIME 类型');
    }

    const bstr = atob(arr[1]);
    const u8arr = new Uint8Array(bstr.length);

    for (let i = 0; i < bstr.length; i += 1) {
      u8arr[i] = bstr.charCodeAt(i);
    }

    return new Blob([u8arr], { type: mime });
  };

  // 重拍
  const retakePhoto = async () => {
    if (videoRef.value && videoRef.value.srcObject) {
      const stream = videoRef.value.srcObject as MediaStream;
      stream.getTracks().forEach((track) => track.stop());
      videoRef.value.srcObject = null;
    }
    photoData.value = null;
    await initCamera();
  };

  /*  // 翻转摄像头
  const toggleFlip = () => {
    isFlipped.value = !isFlipped.value;
  }; */

  const handleFrontOrBack = async () => {
    isFront.value = !isFront.value;
    await initCamera();
  };

  const route = useRoute();
  const recordId = Number(route.query.id);
  const record = ref();
  const d2d = ref<any>({}); // 送教计划

  // 获取格式化日期
  const getFormattedDate = (): string => {
    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    return `${year}-${month}-${day}-${hours}-${minutes}`;
  };

  // 保存送教记录
  const saveRecord = async (name: string, url: string) => {
    if (!record.value.attachments.length) record.value.attachments = [];
    record.value.attachments.push({ name, udf1: url });

    await request({
      url: `/resourceRoom/sendEducationRecord/${record.value.id}`,
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'put',
      data: record.value,
      headers: {
        'Login-Source': 'WeApp',
      },
    });
    await initCamera();
  };

  const oss: IAttachmentProcessor = getOssProcessor();

  // 上传文件
  const handleUpload = async (file: File, fileName: string) => {
    try {
      Message.loading('上传中...');
      const token = getToken();

      const config = {};
      config.headers = {
        ...{
          'Login-Source': 'WeApp',
        },
      };
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      const url = await oss.uploadSimply({ file }, '', config);

      await Promise.all([saveRecord(fileName, url), retakePhoto()]);

      Message.success('上传成功！');
    } catch (error) {
      Message.error(error || '上传失败，请重试。');
    }
  };

  // 上传照片到服务器
  const uploadPhoto = async () => {
    if (!photoData.value) {
      Message.warning('请先拍照');
      return;
    }
    try {
      const fileName: string = `${d2d.value?.student?.name || ''}sendRecordPhoto_${getFormattedDate()}.png`;

      const blobData = await base64ToBlob(photoData.value);
      const file = new File([blobData], fileName, { type: 'image/jpeg' });

      await handleUpload(file, fileName);
    } catch (error) {
      Message.error('上传失败，请检查网络连接。');
    }
  };

  onMounted(async () => {
    const [{ data }] = await Promise.all([
      request({
        url: `/resourceRoom/sendEducationRecord/${recordId}`,
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        headers: {
          'Login-Source': 'WeApp',
        },
      }),
    ]);
    record.value = data;
    d2d.value = data.sendEducationPlan || {};

    await initCamera();
    setHtmlTitle(`${data?.student?.name || ''} 送教计划`);
  });
</script>

<template>
  <div class="w-full h-screen flex flex-col">
    <div class="flex-1 relative">
      <video
        v-if="!photoData"
        ref="videoRef"
        autoplay
        class="w-full h-full object-cover"
        :class="{ 'transform scale-x-[-1]': isFlipped }"
      >
      </video>

      <img
        v-else
        :src="photoData"
        alt="Captured Photo"
        class="w-full h-full object-cover"
        :class="{ 'transform scale-x-[-1]': isFlipped }"
      />
    </div>

    <div class="p-4 bg-gray-100 flex justify-center space-x-4">
      <template v-if="!photoData">
        <button class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600" @click="takePhoto"> 拍照 </button>

        <button class="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600" @click="handleFrontOrBack">
          {{ '翻转摄像头' }}
        </button>
      </template>

      <template v-else>
        <button class="px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600" @click="retakePhoto"> 重拍 </button>

        <button class="px-6 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600" @click="uploadPhoto">
          上传
        </button>
      </template>
    </div>

    <canvas ref="canvasRef" class="hidden"></canvas>
  </div>
</template>

<style scoped lang="scss">
  /* 自定义样式 */
  .hidden {
    display: none;
  }
</style>
