<script setup lang="ts">
  import { ref, onMounted, computed } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import Uploader from '@repo/ui/components/upload/uploader.vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import CameraUploader from '@/sub-module/weappWebView/component/CameraUploader.vue';
  import { IconSwap, IconCamera, IconRight, IconClose, IconFile } from '@arco-design/web-vue/es/icon';
  import { Message, Modal } from '@arco-design/web-vue';
  import nativeCameraTrigger from '@/sub-module/weappWebView/component/nativeCameraTrigger.vue';
  import getFileType from '@repo/ui/components/utils/getFileType';

  const route = useRoute();
  const router = useRouter();
  const d2dId = route.query?.d2dId ? Number(route.query.d2dId) : null; // 1589;
  const attachmentId = route.query?.id ? Number(route.query.id) : null; // 32;
  const isParent = route.query?.isParent ? route.query.isParent === 'true' : false;

  const d2dEdu = ref();

  const headers = computed(() => {
    return {
      'Login-Source': isParent ? 'GuardianWeapp' : 'WeApp',
    };
  });

  // const userStore = useUserStore();
  const previewVisible = ref(false);

  const uploadedList = ref([]);
  const currentAttachment = ref();

  const handleView = (i: any) => {
    currentAttachment.value = i;
    previewVisible.value = true;
  };
  const uploadVisible = ref(false);
  const takePhotoVisible = ref(false);
  const handleReplaceAttachment = () => {
    uploadVisible.value = true;
  };
  const handleTakePhoto = () => {
    takePhotoVisible.value = true;
  };

  const handleUpload = async () => {
    const uploadRecourse = uploadedList.value[uploadedList.value.length - 1];

    const result = {
      id: uploadRecourse?.id || null,
      name: uploadRecourse?.name,
      udf1: uploadRecourse?.udf1 || uploadRecourse?.url,
      numUdf1: 1,
    };

    // replace attachment | if not exits with a default docTemplate
    d2dEdu.value?.docTemplates.forEach((d) => {
      if (d.id === attachmentId) {
        // d.attachment = result;
        d.attachment.numUdf1 = 1;
        if (!d.others) d.others = [];
        d.others.push(result);
      }
    });
    uploadedList.value = [];
  };

  const handleUploadSuccess = (name: string, imageUrl: string) => {
    uploadedList.value.push({ name, udf1: imageUrl });
    handleUpload();
  };

  const change = (results: any) => {
    uploadedList.value.push({ name: results?.fileName, udf1: results?.url });
    handleUpload();
  };

  const currentDoc = computed(() => {
    return d2dEdu.value?.docTemplates.find((d) => d.id === attachmentId);
  });
  const loadD2dEducation = async () => {
    const { data } = await request(`/resourceRoom/d2dEducation/${d2dId}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      headers: headers.value,
    });
    d2dEdu.value = data;
  };

  const btnLoading = ref(false);
  // save d2dEducation data
  const handleSave = async () => {
    btnLoading.value = true;
    await request(`/resourceRoom/d2dEducation/${d2dEdu.value.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'put',
      data: d2dEdu.value,
      headers: headers.value,
    });
    btnLoading.value = false;
    Message.success('保存成功');
  };

  const handleGoBack = () => {
    if (window.history.state.back !== null) {
      router.go(-1);
    } else {
      // @ts-ignore
      // eslint-disable-next-line no-undef
      wx.miniProgram.navigateBack();
    }
  };
  const handleDel = async (index: number) => {
    Modal.confirm({
      title: '确认删除？',
      content: '是否删除当前文件？',
      onOk: async () => {
        currentDoc.value.others.splice(index, 1);
      },
    });
  };

  const navigateToPreview = (url: string, name: string) => {
    const encodeUrlSafely = (originalUrl: string) => {
      try {
        const urlObj = new URL(originalUrl);
        const pathSegments = urlObj.pathname.split('/').filter((segment) => segment !== '');
        const encodedPathSegments = pathSegments.map((segment) => encodeURIComponent(segment));
        urlObj.pathname = `/${encodedPathSegments.join('/')}`;

        const { searchParams } = urlObj;
        const newParams = new URLSearchParams();
        // eslint-disable-next-line no-restricted-syntax
        for (const [key, value] of searchParams.entries()) {
          newParams.append(encodeURIComponent(key), encodeURIComponent(value));
        }
        urlObj.search = newParams.toString();

        return urlObj.toString();
      } catch (e) {
        return originalUrl;
      }
    };
    // @ts-ignore
    // eslint-disable-next-line no-undef
    if (typeof wx !== 'undefined' && wx?.miniProgram) {
      const fileType = getFileType(url);
      const encodedUrl = encodeUrlSafely(url);
      // @ts-ignore
      // eslint-disable-next-line no-undef
      wx.miniProgram.navigateTo({
        url: `/pages/attachment/docPreviewByLink?url=${encodedUrl}&type=${fileType}&name=${name}`,
        fail: (ignore) => {
          console.log(ignore);
        },
      });
    }
  };

  const ready = ref(false);
  onMounted(async () => {
    if (d2dId) await loadD2dEducation();
    ready.value = true;
  });
</script>

<template>
  <div v-if="ready" class="text-lg h-full text-black border mt-2 relative">
    <div class="p-2 mt-2">
      <div class="flex flex-wrap gap-2">
        <div class="w-[calc(33.333%-8px)]">
          <div
            class="relative pt-[100%] rounded overflow-hidden"
            @click="navigateToPreview(currentDoc.attachment?.udf1, currentDoc.attachment?.name)"
          >
            <div class="absolute inset-0 flex justify-center items-center flex-col">
              <div
                style="background-color: #f8f8f8; color: #999"
                class="w-full h-full border-[2px] flex-col shadow border-gray-300 flex justify-center items-center rounded-[12px] border-dashed"
              >
                <icon-file size="30" class="mb-2" />
                <span class="text-xs line-clamp-2 px-2">{{ currentDoc.attachment?.name }}</span>
              </div>
            </div>
          </div>
        </div>

        <div v-for="(i, imgDex) in currentDoc.others" :key="imgDex" class="w-[calc(33.333%-8px)]">
          <div class="relative pt-[100%] rounded shadow overflow-hidden bg-gray-100" @click="handleView(i)">
            <icon-close
              v-if="!isParent"
              style="color: red"
              class="top-[5px] right-[5px] z-50 absolute text-red-500 w-[20px] h-[20px] bg-red-200/80 p-0.5 rounded-full overflow-hidden"
              @click.stop="handleDel(imgDex)"
            />
            <div
              v-if="getFileType(i?.udf1 || i.url) !== 'image'"
              class="absolute inset-0 w-full h-full flex flex-col justify-center items-center bg-gray-100 text-gray-400"
              style="border: rgba(128, 128, 128, 0.58) 1px dashed"
            >
              <icon-file size="40" />
              <span class="text-base line-clamp-2">{{ i?.name }}</span>
            </div>
            <img
              v-else
              :src="i?.udf1 || i?.url"
              :alt="i?.name"
              class="absolute inset-0 w-full h-full object-cover text-gray-400"
              style="border: rgba(128, 128, 128, 0.58) 1px dashed"
            />
          </div>
        </div>

        <div v-if="!isParent" class="w-[calc(33.333%-8px)]">
          <div class="relative pt-[100%] rounded overflow-hidden">
            <div class="absolute inset-0 flex justify-center items-center flex-col">
              <nativeCameraTrigger :is-parent="isParent" @change="change" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <a-modal
      v-model:visible="previewVisible"
      :title="false"
      :footer="false"
      :closable="false"
      :mask-style="{ backgroundColor: 'rgba(0,0,0,0.82)' }"
      fullscreen
      :body-style="{
        width: '100%',
        height: '100%',
        padding: 0,
        margin: 0,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'rgba(0,0,0,0.82)',
      }"
    >
      <div class="w-full h-full flex items-center justify-center" @click="previewVisible = false">
        <img
          :src="currentAttachment?.udf1 || currentAttachment?.url"
          style="width: 100%; height: 100%; object-fit: contain"
          alt="image"
        />
      </div>
    </a-modal>
    <a-modal v-model:visible="uploadVisible" fullscreen :on-before-ok="handleUpload">
      <uploader v-model="uploadedList" sub-folder="" :multiple="false" :accept="'*'" />
    </a-modal>

    <a-modal
      v-if="takePhotoVisible"
      v-model:visible="takePhotoVisible"
      fullscreen
      :render-to-body="false"
      :closable="false"
      :footer="false"
      :body-style="{ padding: 0, width: '100%', height: '100%', overflow: 'auto' }"
      :on-before-ok="handleUpload"
    >
      <CameraUploader @upload-success="handleUploadSuccess" @close="takePhotoVisible = false" />
    </a-modal>

    <div class="fixed bottom-0 left-0 w-full my-1 z-50">
      <div class="flex justify-between space-x-2 px-2">
        <a-button shape="round" size="large" type="outline" class="w-1/2" @click="handleGoBack">返回</a-button>
        <a-button
          :loading="btnLoading"
          size="large"
          shape="round"
          type="outline"
          status="success"
          class="w-1/2"
          @click="handleSave"
        >
          保存
        </a-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
