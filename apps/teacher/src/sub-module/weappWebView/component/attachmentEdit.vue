<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import Uploader from '@repo/ui/components/upload/uploader.vue';
  import { Modal, Message } from '@arco-design/web-vue';
  import CameraUploader from '@/sub-module/weappWebView/component/CameraUploader.vue';
  import AttachmentPreviewModal from '@repo/ui/components/data-display/attachmentPreviewModal.vue';
  import { IconCamera, IconSwap, IconClose, IconFile } from '@arco-design/web-vue/es/icon';
  import getFileType from '@repo/ui/components/utils/getFileType';
  import nativeCameraTrigger from '@/sub-module/weappWebView/component/nativeCameraTrigger.vue';

  const props = defineProps({
    description: {
      type: String,
    },
    type: {
      type: String,
      default: 'attachment',
    },
    filesList: {
      type: Array,
    },
    item: {
      type: Object,
    },
    isParent: {
      type: Boolean,
      default: false,
    },
  });

  const emits = defineEmits(['closeModal', 'close']);

  const filesList: any = ref(props.filesList);
  const item: any = computed(() => props.item);

  // const userStore = useUserStore();
  const previewVisible = ref(false);

  const uploadedList = ref([]);

  const currentAttachment = ref();
  const handleView = (doc: any) => {
    const url = doc?.url || doc?.udf1 || doc.attachment?.url || doc.attachment?.udf1;
    const type = computed(() => {
      const suffix = url?.split('.')?.pop().toLowerCase();
      if (['png', 'gif', 'webp', 'svg', 'ico', 'apng', 'jpg', 'jpeg'].includes(suffix)) {
        return 'img';
      }
      if (['pdf', 'doc', 'xlsx', 'docx'].includes(suffix)) {
        return 'doc';
      }
      return 'doc';
    });
    if (type.value === 'doc') {
      Message.info('当前文档暂不支持预览');
    } else {
      currentAttachment.value = doc;
      previewVisible.value = true;
    }
  };
  const uploadVisible = ref(false);
  const takePhotoVisible = ref(false);

  const handleTakePhoto = () => {
    takePhotoVisible.value = true;
  };

  const handleUpload = async () => {
    const currentFiles = filesList.value;
    const newList = uploadedList.value.map((source) => ({
      id: source?.id,
      name: source?.name,
      udf1: source?.url || source?.udf1,
    }));

    filesList.value = [...currentFiles, ...newList];

    uploadedList.value = [];
    uploadVisible.value = false;
  };

  const handleDel = async (index: number) => {
    Modal.confirm({
      title: '确认删除？',
      content: '是否删除当前文件？',
      onOk: async () => {
        filesList.value.splice(index, 1);
      },
    });
  };

  const ready = ref(false);
  onMounted(async () => {
    ready.value = true;
  });
  const uploadedImageUrl = ref<string | null>(null);

  const handleUploadSuccess = (name: string, imageUrl: string) => {
    uploadedImageUrl.value = imageUrl;
    uploadedList.value.push({ name, udf1: imageUrl });
    handleUpload(); // 上传成功更新...
  };

  const change = (results: any) => {
    uploadedImageUrl.value = results?.url;
    uploadedList.value.push({ name: results?.fileName, udf1: results?.url });
    handleUpload();
  };

  const handleCameraError = (message: string, detail?: any) => {};

  const handleClose = () => {
    takePhotoVisible.value = false;
  };
  const handleCloseModal = (type: string = 'close') => {
    if (type === 'close') {
      emits('close');
    } else {
      emits('closeModal', filesList.value);
    }
  };
</script>

<template>
  <div class="w-full h-full overflow-y-scroll text-lg text-black">
    <div class="flex justify-start text-black mt-2 relative">
      <div class="p-2 mt-2 mb-[30px] overflow-auto w-full">
        <!--图片类-->
        <div class="flex flex-wrap gap-2">
          <div v-for="(i, imgDex) in filesList" :key="imgDex" class="w-[calc(33.333%-8px)]">
            <div class="relative pt-[100%] rounded shadow overflow-hidden bg-gray-100" @click="handleView(i)">
              <icon-close
                v-if="!isParent"
                style="color: red"
                class="top-[5px] right-[5px] z-50 absolute text-red-500 w-[20px] h-[20px] bg-red-200/80 p-0.5 rounded-full overflow-hidden"
                @click.stop="handleDel(imgDex)"
              />
              <div
                v-if="getFileType(i?.udf1 || i.url) !== 'image'"
                class="absolute inset-0 w-full h-full flex flex-col justify-center items-center bg-gray-100 text-gray-400"
                style="border: rgba(128, 128, 128, 0.58) 1px dashed"
              >
                <icon-file size="40" />
                <span class="text-base line-clamp-2">{{ i?.name }}</span>
              </div>
              <img
                v-else
                :src="i?.udf1 || i?.url"
                :alt="i?.name"
                class="absolute inset-0 w-full h-full object-cover text-gray-400"
                style="border: rgba(128, 128, 128, 0.58) 1px dashed"
              />
            </div>
          </div>
          <div v-if="!isParent && false" class="w-[calc(33.333%-8px)]">
            <div
              class="relative pt-[100%] rounded shadow overflow-hidden bg-gray-50 border-dashed border border-black"
              @click="handleTakePhoto"
            >
              <div class="absolute inset-0 flex justify-center items-center flex-col">
                <icon-camera size="30" />
                <div>拍照</div>
              </div>
            </div>
          </div>
          <div v-if="!isParent" class="w-[calc(33.333%-8px)]">
            <div class="relative pt-[100%] rounded overflow-hidden">
              <div class="absolute inset-0 flex justify-center items-center flex-col">
                <nativeCameraTrigger :is-parent="isParent" @change="change" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <a-modal
        v-model:visible="previewVisible"
        :title="false"
        :footer="false"
        :closable="false"
        :mask-style="{ backgroundColor: 'rgb(0,0,0)' }"
        fullscreen
        :body-style="{
          width: '100%',
          height: '100%',
          padding: 0,
          margin: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'rgb(0,0,0)',
        }"
      >
        <div class="w-full h-full flex items-center justify-center" @click="previewVisible = false">
          <img
            :src="currentAttachment?.udf1 || currentAttachment?.url"
            style="width: 100%; height: 100%; object-fit: contain"
            alt="image"
          />
        </div>
      </a-modal>

      <a-modal
        v-if="uploadVisible"
        v-model:visible="uploadVisible"
        fullscreen
        :on-before-ok="handleUpload"
        :render-to-body="false"
      >
        <uploader
          v-model="uploadedList"
          sub-folder=""
          :multiple="item?.multiple || false"
          :accept="item?.accept || '*'"
        />
      </a-modal>

      <a-modal
        v-if="takePhotoVisible"
        v-model:visible="takePhotoVisible"
        fullscreen
        :render-to-body="false"
        :closable="false"
        :footer="false"
        :body-style="{ padding: 0, width: '100%', height: '100%', overflow: 'auto' }"
        :on-before-ok="handleUpload"
      >
        <CameraUploader @upload-success="handleUploadSuccess" @error="handleCameraError" @close="handleClose" />
      </a-modal>

      <div class="fixed bottom-0 left-0 w-full px-2 py-1 bg-white shadow-lg z-50">
        <div class="w-full flex justify-between space-x-4">
          <a-button
            status="danger"
            size="large"
            type="outline  w-full"
            shape="round"
            @click="handleCloseModal('close')"
          >
            关闭
          </a-button>

          <a-button type="outline  w-full" size="large" shape="round" @click="handleCloseModal('confirm')">
            确定
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
