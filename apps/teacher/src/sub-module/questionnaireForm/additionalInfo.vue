<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';

  const props = defineProps({
    additionalInfo: {
      type: Object as PropType<any>,
    },
    form: {
      type: Object as PropType<any>,
    },
  });

  const emit = defineEmits(['update:answer']);

  const formData = computed({
    get: () => {
      return props.additionalInfo;
    },
    set: (val) => {
      emit('update:answer', val);
    },
  });

  const loading = ref(false);
  const size = 'size';
  const formRef = ref();
  const validateRes = async () => {
    const res = await formRef.value.validate();
    return !(res && Object.keys(res).length > 0);
  };
  defineExpose({ validateRes });
</script>

<template>
  <div class="w-auto sm:!rounded-lg shadow-xl !border-0 form-wrapper !p-2 h-full">
    <div class="box">
      <a-form ref="formRef" auto-label-width :model="formData">
        <a-form-item
          v-if="form.additionalInfo.includes('authorName')"
          label="姓名"
          field="authorName"
          :rules="{ required: true, message: '姓名不能为空' }"
        >
          <a-input v-model="formData.authorName" placeholder="请输入姓名" :size="size" />
        </a-form-item>
        <a-form-item
          v-if="form.additionalInfo.includes('boName')"
          label="单位"
          field="authorBoName"
          :rules="{ required: true, message: '单位不能为空' }"
        >
          <a-input
            v-model="formData.authorBoName"
            show-word-limit
            :max-length="50"
            placeholder="请输入单位名"
            :size="size"
          />
        </a-form-item>
        <a-form-item
          v-if="form.additionalInfo.includes('authorPhoneNumber')"
          label="手机号"
          field="authorPhoneNumber"
          :rules="[
            { required: true, message: '请输入手机号码', trigger: 'blur' },
            {
              match: /^1[3-9]\d{9}$/,
              message: '手机号码格式不正确',
              trigger: 'blur',
            },
          ]"
        >
          <a-input-number
            v-model="formData.authorPhoneNumber"
            show-word-limit
            placeholder="请输入手机号"
            :size="size"
            :max-length="11"
          />
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<style scoped lang="scss">
  .box {
    display: inline-block;
    width: auto;
    margin-bottom: 20px;
    border-radius: 4px;
    padding: 1px 4px;
    &:hover {
    }
  }

  :deep(.arco-form-item) {
    margin: 1px !important;
    padding: 0 !important;
  }
</style>
