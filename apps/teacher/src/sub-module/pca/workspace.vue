<script setup lang="ts">
  import { inject, onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message, Modal } from '@arco-design/web-vue';
  import dayjs from 'dayjs';
  import { sum } from 'lodash';
  import CompetitionDisplayCourse from '@/sub-module/pca/components/competitionDisplayCourse.vue';
  import CompetitionDisplayPaper from '@/sub-module/pca/components/competitionDisplayPaper.vue';
  import UsageTimer from '@repo/components/common/usageTimer.vue';
  import { secondsFormatter } from '@repo/ui/components/utils/utils';

  const splitSize = ref(800);
  const params = inject('params');
  const token = inject('token');
  const ready = ref(false);
  const saving = ref(false);
  const competition = ref<any>(null);
  const currentPaper = ref<any>(null);
  const paperDetailLoading = ref(false);
  const paperScore = ref<any>([]);

  const initPaperScore = () => {
    const paper = currentPaper.value;
    if (paper?.scores?.length) {
      paperScore.value = paper.scores;
      return;
    }
    const scores: any[] = [];
    competition.value.criterionList.forEach((item, i) => {
      scores[i] = [];
      item.children?.forEach(() => {
        scores[i].push(undefined);
      });
    });

    paperScore.value = scores;
  };

  const loadCompetitionInfo = async () => {
    const { data } = (await request('/paper/expertClient/competitionInfo', {
      params: {
        competitionId: params.value.id,
        token: token.value,
      },
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    })) as any;

    competition.value = data;

    const [startTime, endTime] =
      competition.value.assessmentDateRange?.map((item) => {
        return new Date(item);
      }) || [];

    if (!startTime || !endTime) {
      await Modal.error({
        title: '提示',
        content: '评审尚未开始，请联系管理员',
        onOk: () => {
          window.close();
        },
      });
    } else if (startTime && startTime > new Date()) {
      await Modal.info({
        title: '提示',
        content: `本次评审将于 ${dayjs(startTime).format('YYYY-MM-DD HH:mm')} 开始，请耐心等待 :)`,
        onOk: () => {
          window.close();
        },
      });
      throw new Error('评审已结束');
    } else if (endTime && endTime < new Date()) {
      await Modal.info({
        title: '提示',
        content: '本次评审已结束，非常感谢您的参与与付出 :)',
        onOk: () => {
          window.close();
        },
      });
    } else {
      ready.value = true;
    }
  };

  const handleSwitch = async (item: any) => {
    paperDetailLoading.value = true;
    try {
      const { data } = await request(`/paper/expertClient/paperInfo/${item.id}`, {
        params: {
          token: token.value,
        },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });

      currentPaper.value = {
        ...data,
        scores: item.scores,
        assessmentStatus: item.assessmentStatus,
        timeSpend: item.timeSpend || 0,
      };

      initPaperScore();
    } finally {
      paperDetailLoading.value = false;
    }
  };

  const handleSave = async (submit?: boolean) => {
    saving.value = true;

    try {
      const unScored = paperScore.value.some((item) => {
        return item.some((score) => score === undefined);
      });

      if (unScored) {
        Message.error('请完成所有评分项');
        saving.value = false;
        return;
      }

      await request('/paper/expertClient/savePaperScore', {
        method: 'POST',
        params: {
          token: token.value,
        },
        data: {
          competitionId: competition.value.id,
          entryId: currentPaper.value.id,
          scores: paperScore.value,
          submitStatus: submit ? 'Submitted' : 'Draft',
          timeSpend: currentPaper.value.timeSpend,
        },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });

      currentPaper.value.scores = paperScore.value;

      if (submit) {
        currentPaper.value.assessmentStatus = 'Submitted';
      }

      competition.value.paperList = competition.value.paperList.map((item: any) => {
        if (item.id === currentPaper.value.id) {
          return {
            ...item,
            scores: paperScore.value,
            assessmentStatus: submit ? 'Submitted' : 'Draft',
          };
        }
        return item;
      });

      initPaperScore();

      Message.success(submit ? '提交成功' : '暂存成功');
    } finally {
      saving.value = false;
    }
  };

  const handleRollBackSubmit = async () => {
    try {
      await request('/paper/expertClient/rollBackSubmit', {
        method: 'get',
        params: {
          token: token.value,
          competitionId: competition.value.id,
          entryId: currentPaper.value.id,
        },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      currentPaper.value.assessmentStatus = 'Draft';
      await loadCompetitionInfo();
      await handleSwitch(currentPaper.value);
      Message.success('操作成功');
    } finally {
      /**/
    }
  };

  onMounted(async () => {
    if (!params.value.id || !token.value) {
      throw new Error('缺少必要参数');
    }
    await loadCompetitionInfo();
  });
</script>

<template>
  <a-layout v-if="competition && ready">
    <div style="background-color: white; margin: 0; padding: 0; height: 100vh">
      <a-layout-header
        class="bg-white h-12 flex items-center justify-between w-full px-4"
        style="display: flex; justify-content: center; border-bottom: solid gray 1px; align-items: center"
      >
        <div style="width: 100%; padding: 2px 4px; margin-top: 6px"> 欢迎您 {{ competition?.expert?.name }} </div>

        <div class="subject text-lg" style="width: 100%; padding: 8px; font-weight: 700; font-size: 24px">
          {{ competition.name }}
        </div>
      </a-layout-header>
      <a-layout-content class="p-2 flex gap-2" style="padding: 4px">
        <a-split class="left-wrapper bg-white rounded overflow-x-hidden" :size="140">
          <template #first>
            <a-empty v-if="!competition.paperList?.length" description="暂无作品" />
            <div class="py-2 left">
              <div
                v-for="(item, i) in competition.paperList"
                :key="item.id || i"
                :class="['paper-item', { 'paper-item--selected': currentPaper?.id === item.id }]"
                @click="() => handleSwitch(item)"
              >
                <div class="paper-item__left-content">
                  <span class="paper-item__number"> 作品 {{ i + 1 }} </span>
                  <div class="paper-item__status-icon">
                    <IconCheck v-if="item.assessmentStatus === 'Submitted'" class="status-icon--submitted" />
                    <IconSave v-else-if="item.assessmentStatus === 'Draft'" class="status-icon--draft" />
                    <IconQuestion v-else class="status-icon--pending" />
                  </div>
                </div>
              </div>
            </div>
          </template>
          <template #second>
            <a-card class="flex-1 rounded" title="作品信息" size="small" :bordered="false">
              <template #title>
                <div style="display: flex">
                  <a-space v-if="currentPaper && currentPaper.assessmentStatus !== 'Submitted'">
                    <small>* 请您在评审时，始终保持此页面为活动状态</small>
                    <usage-timer v-model="currentPaper.timeSpend" />
                  </a-space>

                  <a-tag
                    v-else-if="currentPaper && currentPaper?.assessmentStatus !== 'Draft'"
                    size="small"
                    color="green"
                  >
                    评审用时：{{ secondsFormatter(currentPaper.timeSpend) }}
                  </a-tag>
                </div>
              </template>
              <a-split v-model:size="splitSize" class="full-height-wrapper">
                <template #first>
                  <a-spin class="w-full" :loading="paperDetailLoading" style="width: 100%">
                    <a-empty v-if="!currentPaper" description="请先在左侧选择作品" />
                    <div v-else>
                      <competition-display-course v-if="competition.type === '课例类'" :raw="currentPaper" />
                      <competition-display-paper v-else-if="competition.type === '论文类'" :raw="currentPaper" />
                    </div>
                  </a-spin>
                </template>
                <template v-if="currentPaper" #second>
                  <div v-for="(item, i) in competition?.criterionList" :key="i" style="padding: 0 6px">
                    <div class="ml-2 py-2 flex justify-between gap-2">
                      <div> {{ i + 1 }}. {{ item.criterion }} </div>
                      <div>
                        <a-tag color="blue" size="small">小计: {{ sum(paperScore[i]) }}</a-tag>
                      </div>
                    </div>
                    <a-table size="mini" :data="item?.children" :pagination="false" class="ml-2 mb-2">
                      <template #columns>
                        <a-table-column data-index="criterion" title="评估标准" />
                        <a-table-column data-index="score" title="得分" :width="100">
                          <template #cell="{ rowIndex }">
                            <a-input-number
                              v-model="paperScore[i][rowIndex]"
                              size="mini"
                              :min="0"
                              :step="0.5"
                              :precision="1"
                            />
                          </template>
                        </a-table-column>
                        <a-table-column data-index="recommendScore" :width="60" title="参考" />
                      </template>
                    </a-table>
                  </div>

                  <a-space
                    v-if="!currentPaper.assessmentStatus || currentPaper.assessmentStatus === 'Draft'"
                    style="padding: 2px 6px"
                    class="ml-2"
                  >
                    <a-button size="mini" :loading="saving" @click="() => handleSave(false)">
                      <template #icon>
                        <IconSave />
                      </template>
                      暂存
                    </a-button>
                    <a-popconfirm content="确定要提交这个评分吗？提交之后不可修改！" @ok="() => handleSave(true)">
                      <a-button type="primary" size="mini" :loading="saving">
                        <template #icon>
                          <IconCheck />
                        </template>
                        提交
                      </a-button>
                    </a-popconfirm>
                  </a-space>
                  <a-button
                    v-if="currentPaper.assessmentStatus"
                    size="mini"
                    style="margin-left: 6px"
                    @click="handleRollBackSubmit"
                  >
                    撤回提交
                  </a-button>
                </template>
              </a-split>
            </a-card>
          </template>
        </a-split>
      </a-layout-content>
    </div>
  </a-layout>
</template>

<style scoped lang="scss">
  .left-wrapper {
    height: calc(100vh - 70px);
  }
  .full-height-wrapper {
    height: calc(100vh - 135px);
  }
  .space-between {
    justify-content: space-between;
  }
  .left {
    height: 100%;
    overflow: auto;
    .nav {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
  }

  .paper-item {
    position: relative; /* 为指示条定位提供参考 */
    display: flex;
    align-items: center;
    justify-content: space-between; /* 内容左右对齐 */
    padding: 12px 16px; /* 垂直和水平内边距 */
    margin-bottom: 8px; /* 列表项之间的间距 */
    background-color: #ffffff; /* 默认白色背景 */
    border-left: 4px solid transparent; /* 默认透明左边框 */
    border-radius: 6px; /* 轻微圆角 */
    cursor: pointer;
    transition: all 0.2s ease-in-out; /* 所有属性的平滑过渡 */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08); /* 柔和的阴影 */
  }

  /* 悬停效果 */
  .paper-item:hover {
    background-color: #f5f5f5; /* 悬停时浅灰色背景 */
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1); /* 悬停时更明显的阴影 */
  }

  /* 选中状态的样式 */
  .paper-item--selected {
    background-color: #e0f2fe; /* 选中时浅蓝色背景 */
    border-left-color: #2196f3; /* 选中时蓝色左边框 */
    color: #1a237e; /* 选中时深蓝色文本 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12); /* 选中时更强的阴影 */
  }

  /* 左侧内容布局 */
  .paper-item__left-content {
    display: flex;
    align-items: center;
    gap: 12px; /* 作品序号和图标之间的间距 */
  }

  /* 作品序号文本样式 */
  .paper-item__number {
    font-size: 14px;
    font-weight: 500; /* 中等粗细 */
    color: inherit; /* 继承父元素的颜色 (根据选中状态变化) */
    white-space: nowrap; /* 防止文字换行 */
  }

  /* 状态图标容器 */
  .paper-item__status-icon {
    display: flex; /* 确保图标居中 */
    align-items: center;
    font-size: 18px; /* 图标大小 */
  }

  /* 具体状态图标颜色 */
  .status-icon--submitted {
    color: #4caf50; /* 绿色 */
  }

  .status-icon--draft {
    color: #2196f3; /* 蓝色 */
  }

  .status-icon--pending {
    color: #f44336; /* 红色 */
  }
</style>
