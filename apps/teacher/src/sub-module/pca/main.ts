// eslint-disable-next-line import/order
import App from './container.vue';
// eslint-disable-next-line import/order
import router from './router';
import { createApp } from 'vue';
import ArcoVueIcon from '@arco-design/web-vue/es/icon';
import '@arco-design/web-vue/dist/arco.css';
import { createPinia } from 'pinia';
import '@repo/infrastructure/iconfont-online-css';
import '@repo/infrastructure/iconfont';
import { Message } from '@arco-design/web-vue';

const pinia = createPinia();
const app = createApp(App);
app.use(pinia as any);
app.use(ArcoVueIcon as any);
app.use(router);
app.mount('#app-submodule-pca');

// @ts-ignore
window.handleRequestError = (msg) => {
  Message.error(msg);
};

export default app;
