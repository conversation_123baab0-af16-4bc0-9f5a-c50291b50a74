<script setup lang="ts">
  import Captcha from '@repo/components/common/captcha.vue';
  import { computed, PropType, ref } from 'vue';
  import SendVerifyCode from '@repo/components/common/sendVerifyCode.vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { useRouter } from 'vue-router';

  const props = defineProps({
    competitionInfo: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const router = useRouter();
  const loading = ref(false);
  const formData = ref<any>({});
  const sendVerifyCodeRef = ref(null);
  const mobileChecked = computed(() => {
    return /^1[3456789]\d{9}$/.test(formData.value.mobile);
  });

  const handleSendCode = async () => {
    await sendVerifyCodeRef.value?.sendVerifyCode();
  };

  const handleLogin = async () => {
    const { data: token } = await request('/paper/expertClient/authorize', {
      method: 'POST',
      data: {
        name: formData.value.mobile,
        udf1: formData.value.code,
        id: props.competitionInfo.id,
      },
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    router.push({
      path: '/workspace',
      query: {
        token,
      },
    });
  };
</script>

<template>
  <div class="login-wrapper">
    <div class="container">
      <div class="title text-white text-center">
        <div class="font-medium">
          {{ competitionInfo.udf1 }}
        </div>
        <div class="text-xl">
          {{ competitionInfo.name }}
        </div>
      </div>
      <a-card class="bg-white rounded-xl mx-auto mt-8" title="评审专家登录">
        <a-form :model="formData" auto-label-width>
          <a-form-item label="手机号码">
            <a-input v-model.trim="formData.mobile" placeholder="请输入手机号码">
              <template #prefix>
                <IconMobile />
              </template>
            </a-input>
          </a-form-item>
          <a-form-item label="验证码">
            <a-input v-model.trim="formData.code" placeholder="短信验证码">
              <template #prefix>
                <IconMessage />
              </template>
            </a-input>
            <send-verify-code
              id="ali-captcha-button-app_expert_login"
              ref="sendVerifyCodeRef"
              :mobile="formData.mobile"
              :handler="() => {}"
              type="Login"
            />
          </a-form-item>
          <captcha id="app_expert_login" scene-id="99kcupos" :slide-style="{ width: 270 }" :handler="handleSendCode" />
          <a-button
            class="w-full mt-2 py-8"
            type="primary"
            size="large"
            :loading="loading"
            :disabled="!mobileChecked || !formData.code"
            @click="handleLogin"
            >登录</a-button
          >
        </a-form>
      </a-card>
    </div>
  </div>
</template>

<style scoped lang="scss">
  .login-wrapper {
    padding: 20px;
    width: 100%;
    position: absolute;
    top: 50%;
    transform: translate(0%, -70%);
  }
  .container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
  }

  .title {
    letter-spacing: 0.2em;
    font-size: 28px;
    line-height: 180%;
    color: white;
    text-align: center;
  }

  .arco-card {
    width: 400px;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 10px;
    padding: 10px 20px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  }
</style>
