<script setup lang="ts">
  import { ref, onMounted, computed } from 'vue';
  import childrenBehaviorAssessmentForm from '@/teacherClient/components/placementAssessment/childrenBehaviorAssessmentForm.vue';
  import ReportPage from '@/teacherClient/components/placementAssessment/ReportPage.vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const queryParams = {
    sort: '-id',
  };
  const GuId = ref<any>();
  const token = ref<any>();
  const student = ref();
  const loadStudentInfo = async (guid: string) => {
    const { data } = await request(`/resourceRoom/student/getByGuid/${guid}`, {
      method: 'GET',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    student.value = data;
  };
  onMounted(async () => {
    const url = window.location.href;
    const urlParam = new URLSearchParams(new URL(url).search);
    GuId.value = urlParam.get('guid');
    token.value = urlParam.get('token');
    await loadStudentInfo(GuId.value);
  });

  const props = defineProps({
    title: {
      type: String,
    },
  });

  const editVisible = ref(true);
  const reportVisible = ref(false);
  const currentRecord = computed(() => {
    return {
      general: { student: { id: student.value?.id }, filledBy: null, filledDate: null },
      socialAbility: {
        sportsActivities: { timeSpent: '' },
        organizations: {},
        relationship: { siblings: '' },
        hobbies: { timeSpent: '' },
        jobs: { list: '' },
        friends: { count: '' },
        academics: {
          inSchool: '',
          subjects: {
            others: {},
          },
        },
      },
      behaviorProblems: [],
      student: { ...student.value },
    };
  });
  const handleSubmit = (val: any) => {
    // currentRecord.value = val?.data;
    editVisible.value = false;
    reportVisible.value = true;
  };
</script>

<!--儿童行为量表（CBCL）评估-->
<template>
  <childrenBehaviorAssessmentForm
    v-if="student"
    v-model:visible="editVisible"
    :assessment-data="currentRecord"
    view-type="QRC"
    :token="token"
    @submit="handleSubmit"
  />
  <ReportPage v-if="reportVisible" v-model:visible="reportVisible" :assessment-data="currentRecord" view-type="QRC" />
</template>

<style scoped lang="scss"></style>
