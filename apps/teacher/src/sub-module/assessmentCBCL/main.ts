// eslint-disable-next-line import/order
import App from './container.vue';
// eslint-disable-next-line import/order
import { createApp } from 'vue';
import ArcoVueIcon from '@arco-design/web-vue/es/icon';
import '@arco-design/web-vue/dist/arco.css';
import '@repo/infrastructure/iconfont-online-css';
import '@repo/infrastructure/iconfont';
import { Message } from '@arco-design/web-vue';
import { createPinia } from 'pinia';

const pinia = createPinia();
const app = createApp(App);
app.use(pinia);
app.use(ArcoVueIcon);
app.mount('#app-assessment-cbcl');

// @ts-ignore
window.handleRequestError = (msg) => {
  Message.error(msg);
};

export default app;
