<script lang="ts" setup>
  import { computed, onMounted, provide, ref } from 'vue';
  import { useRoute } from 'vue-router';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';
  import { PROJECT_URLS } from '@repo/env-config';

  const route = useRoute();

  const params = ref<any>({});
  const token = computed(() => route.query.token);

  provide('params', params);
  provide('token', token);

  const bodyStyle = {
    overflow: 'auto',
  };

  const formData = ref({
    name: '',
    phoneNumber: '',
    branchOfficeName: '',
    sysUserId: null,
  });

  const formRef = ref(null);
  const meetingId = ref<any>();
  const title = ref<any>();
  const isSignIn = ref<boolean>();
  const expiry = ref();

  const orgType = ref<string>('inside');
  const orgRange = computed(() => (orgType.value === 'inside' ? '内' : '外'));

  const visible = ref(false);
  const currentAccounts = ref();
  const handleChooseAccount = (accounts: any) => {
    visible.value = true;
    currentAccounts.value = accounts;
  };
  const isFailed = ref(false);

  const handleSubmit = async () => {
    if (isFailed.value) {
      Message.info('二维码已失效，请扫描最新二维码');
      return false;
    }
    const errors = await formRef.value.validate();
    if (!errors) {
      try {
        const { data: res } = await request('/resourceCenter/conferenceRecord/checkin', {
          method: 'post',
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          data: {
            ...formData.value,
            isSignIn: isSignIn.value,
            conferenceId: meetingId.value,
          },
        });
        if (!res?.items) Message.success(isSignIn.value ? '签到成功' : '签退成功');
        else {
          handleChooseAccount(res.items);
        }
      } catch (e) {
        Message.error(e.message);
      }
    } else {
      console.log(errors);
    }
    return true;
  };
  const currentIndex = ref(0);
  const handleSelect = (index: number, id: number) => {
    currentIndex.value = index;
    formData.value.sysUserId = id;
  };
  const handlePreOk = async () => {
    if (formData.value.sysUserId) await handleSubmit();
    visible.value = false;
  };
  const searchLoading = ref(false);
  const branchOptions = ref([]);
  const handleSearch = async (keyword: any) => {
    try {
      searchLoading.value = true;
      const { data: res } = await request('/org/branchOffice/searchByKeyWord', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          keyword,
        },
      });
      branchOptions.value = res?.items.map((b) => ({ label: b?.name, value: b?.name })) || [];
    } finally {
      searchLoading.value = false;
    }
  };

  onMounted(() => {
    const url = window.location.href;
    const urlParam = new URLSearchParams(new URL(url).search);
    meetingId.value = urlParam.get('meetingId');
    title.value = urlParam.get('title');
    expiry.value = Number(urlParam.get('expiry'));
    isSignIn.value = urlParam.get('isSignIn') !== 'false';
    if (Date.now() > parseInt(expiry.value, 10)) {
      isFailed.value = true;
      Message.info('二维码已失效，请扫描最新二维码');
    }
  });
</script>

<template>
  <div class="container">
    <span class="title-style">{{ title }}</span>
    <div class="second-box-style">
      <a-form ref="formRef" auto-label-width :model="formData" @submit="handleSubmit">
        <a-form-item field="name" :rules="[{ required: true, message: '姓名不能为空' }]">
          <template #label>
            <span class="form-item-label-style">姓&nbsp;&nbsp;&nbsp;名</span>
          </template>
          <a-input v-model="formData.name" class="input-style" placeholder="请输入姓名" />
        </a-form-item>
        <a-form-item field="phoneNumber" :rules="[{ required: true, message: '手机号不能为空' }]">
          <template #label>
            <span class="form-item-label-style">手机号</span>
          </template>
          <a-input v-model="formData.phoneNumber" class="input-style" placeholder="请输入手机号" />
        </a-form-item>
        <a-form-item field="branchOfficeName" :rules="[{ required: false, message: '' }]">
          <template #label>
            <span class="form-item-label-style">单位名</span>
          </template>
          <a-select
            v-if="orgType === 'inside'"
            v-model="formData.branchOfficeName"
            allow-search
            placeholder="请搜索并选择单位"
            :loading="searchLoading"
            :options="branchOptions"
            @search="handleSearch"
          />
          <a-input
            v-else
            v-model="formData.branchOfficeName"
            class="input-style flex-grow"
            placeholder="请输入单位名"
          />
        </a-form-item>
        <a-form-item>
          <template #label>
            <span class="form-item-label-style flex">区&nbsp;&nbsp;&nbsp;{{ orgRange }}</span>
          </template>
          <a-switch
            v-model="orgType"
            type="round"
            checked-value="outside"
            unchecked-value="inside"
            @change="formData.branchOfficeName = null"
          >
            <template #unchecked> 区内 </template>
            <template #checked> 区外 </template>
          </a-switch>
        </a-form-item>
        <a-form-item>
          <a-button class="submit-btn-style" type="primary" html-type="submit">
            {{ isSignIn ? '签到' : '签退' }}
          </a-button>
        </a-form-item>
      </a-form>
    </div>
    <a-modal
      v-model:visible="visible"
      hide-cancel
      :body-style="bodyStyle"
      title="请选择签到账户"
      fullscreen
      :on-before-ok="handlePreOk"
    >
      <div class="account-container">
        <div
          v-for="(item, index) in currentAccounts"
          :key="index"
          class="account-item"
          :class="currentIndex === index ? 'hasBorder' : ''"
          @click="handleSelect(index, item.id)"
        >
          <div class="account-name">{{ item?.name }}</div>
          <div class="account-details">
            <a-tag class="account-username">{{ item?.username }}</a-tag>
            <a-tag class="account-mobile">{{ item?.mobile }}</a-tag>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style lang="scss">
  html,
  body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }

  .container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2px;
    width: 80%;
    margin: 15% auto auto auto;
  }

  .second-box-style {
    width: 100%;
    padding: 2px;
  }

  .input-style {
    border-radius: 5px;
  }

  .form-item-label-style {
    color: #2c2c2c;
  }

  .submit-btn-style {
    width: 100%;
    border-radius: 4px;
    background-color: #50a8ff !important;
  }

  .title-style {
    margin-bottom: 12px;
    max-width: 80%;
    font-weight: 700;
  }

  //
  .hasBorder {
    border: 1px solid #0079ff;
  }

  :root {
    --primary-color: #88c1ff;
    --background-color: rgba(220, 220, 220, 0.27);
    --border-radius: 4px;
    --padding: 10px;
    --gap: 10px;
    --hover-background: rgba(220, 220, 220, 0.4);
  }

  .account-container {
    width: 100%;
    cursor: pointer;
  }

  .account-item {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    background-color: var(--background-color);
    border-radius: var(--border-radius);
    padding: var(--padding);
    margin-bottom: 8px;
    transition: background-color 0.3s ease;
  }

  .account-item:hover {
    background-color: var(--hover-background);
  }

  .account-name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
  }

  .account-details {
    display: flex;
    gap: var(--gap);
    font-size: 14px;
    color: #666;
  }

  .account-username {
    font-weight: 400;
  }

  .account-mobile {
    font-weight: 400;
  }
</style>
