import { createApp } from 'vue';
import ArcoVueIcon from '@arco-design/web-vue/es/icon';
import '@arco-design/web-vue/dist/arco.css';
import '@repo/infrastructure/iconfont-online-css';
import '@repo/infrastructure/iconfont';
import { createPinia } from 'pinia';

import App from './container.vue';

const pinia = createPinia();
const app = createApp(App);
app.use(pinia as any);
app.use(ArcoVueIcon);
app.mount('#app-submodule-editStudentInfo');

export default app;
