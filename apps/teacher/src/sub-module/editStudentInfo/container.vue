<script lang="ts" setup>
  import StudentInfoCards from '@repo/components/student/StudentInfoCards.vue';
  import { ref, onMounted, provide } from 'vue';

  const studentId = ref(false);
  const token = ref(null);
  const nature = ref(null);

  onMounted(() => {
    const url = window.location.href;
    const urlParam = new URLSearchParams(new URL(url).search);
    studentId.value = urlParam.get('studentId');
    nature.value = urlParam.get('nature');
    token.value = urlParam.get('token');
  });

  provide('token', token);
</script>

<template>
  <StudentInfoCards :student-id="studentId" :nature="nature" />
</template>

<style lang="scss">
  @tailwind base;
  @tailwind components;
  @tailwind utilities;

  html,
  body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }
</style>
