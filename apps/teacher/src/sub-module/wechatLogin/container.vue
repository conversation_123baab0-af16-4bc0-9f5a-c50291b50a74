<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import LoginBind from '@/sub-module/wechatLogin/components/loginBind.vue';
  import { Modal } from '@arco-design/web-vue';
  import { PROJECT_URLS } from '@repo/env-config';

  const params = ref<any>({});
  const allowOrigin = ['loginBind', 'userBind'];

  const bindWechat = async () => {};

  onMounted(async () => {
    const urlParams = new URLSearchParams(window.location.search);

    params.value.origin = urlParams.get('origin') || 'loginBind';
    params.value.code = urlParams.get('code');
    params.value.state = urlParams.get('state');

    if (!allowOrigin.includes(params.value.origin)) {
      params.value.origin = 'loginBind';
    }

    if (!params.value.code) {
      Modal.error({
        title: '登录失败',
        content: '登录失败，请重试',
        onOk: () => {
          window.top.location.href = PROJECT_URLS.MAIN_PROJECT;
        },
      });
    }

    if (params.value.origin === 'userBind') {
      await bindWechat();
      Modal.success({
        title: '用户绑定',
        content: '您已成功绑定微信，以后可使用微信扫码登录',
        onOk: () => {
          window.top.location.href = PROJECT_URLS.MAIN_PROJECT;
        },
      });
    }
  });
</script>

<template>
  <login-bind v-if="params.origin === 'loginBind'" :code="params.code" />
</template>

<style scoped lang="scss"></style>
