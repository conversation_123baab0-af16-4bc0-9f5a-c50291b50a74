<template>
  <div class="lock-wrapper">
    <div class="title">
      {{ shareLinkInfo.title }}
    </div>
    <div class="password">
      <a-input v-model="password" placeholder="请输入查看密码" />
      <a-button type="primary" class="ml-2" @click="handleCheckPassword">查看</a-button>
    </div>
    <div class="meta">有效期至：{{ shareLinkInfo.endTime }}</div>
  </div>
</template>

<script lang="ts" setup>
  import { PropType, ref } from 'vue';
  import { Message } from '@arco-design/web-vue';

  defineProps({
    shareLinkInfo: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  });

  const emit = defineEmits(['checkpass']);
  const password = ref('');

  const handleCheckPassword = () => {
    if (password.value.length < 5) {
      Message.error('请输入正确的查看密码');
      return;
    }
    emit('checkpass', password.value);
  };
</script>

<style lang="scss" scoped>
  $width: 500px;
  $height: 200px;
  .lock-wrapper {
    width: $width;
    height: $height;
    background: #fff;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    .title {
      font-size: 18px;
      font-weight: 400;
      text-align: center;
    }

    .password {
      display: flex;
      align-items: center;
      justify-content: center;
      .el-input {
        width: 200px;
      }
      :deep input {
        text-align: center;
        letter-spacing: 3px;
        font-size: 20px;
      }
    }

    .meta {
      font-size: 14px;
      color: #999;
      text-align: center;
    }
  }
</style>
