<script lang="ts" setup>
  import { computed, onMounted, provide, ref } from 'vue';
  import { useRoute } from 'vue-router';
  import ShareLock from '@/sub-module/d2dEduShare/shareLock.vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import sendStatistics from '@repo/components/d2dEduShare/sendStatistics.vue';
  import sendStatisticsSimple from '@repo/components/d2dEduShare/sendStatisticsSimple.vue';
  // import sendStatistics from './sendStatistics.vue';
  import { getURLParams } from '@/common/utils';

  const route = useRoute();

  const urlParams = getURLParams();
  const params = ref<any>({});
  const token = computed(() => route.query.token);

  provide('params', params);
  provide('token', token);
  const viewMode = ref('shareLock');
  const shareLinkInfo = ref({
    linkCode: '',
    codeChecked: false,
  });
  const shareLinkCode = ref();

  const loadSharedLinkInfo = async (shareCode) => {
    viewMode.value = 'shareLock';
    const { data } = await request(`/statistics/statisticsShareLink/getInfo/${shareCode}`, {
      method: 'get',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    shareLinkInfo.value = { ...shareLinkInfo, ...data };
  };

  const handleCheckPassword = async (password) => {
    console.log('handleCheckPassword', password);
    try {
      // const res = await axios.post(`/statistics/statisticsShareLink/checkPassword/${shareLinkInfo.value.linkCode}`, {
      //   name: password,
      // });
      const res = await request(`/statistics/statisticsShareLink/checkPassword/${shareLinkInfo.value.linkCode}`, {
        method: 'post',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: {
          name: password,
        },
      });
      if (res) {
        shareLinkCode.value = password;
        shareLinkInfo.value.codeChecked = true;
        viewMode.value = 'statistics';
      }
    } catch (e) {
      // $message.error('密码错误');
    }
  };

  onMounted(() => {
    const url = window.location.href;
    const urlParam = new URLSearchParams(new URL(url).search);
    shareLinkInfo.value.linkCode = urlParam.get('sc');
    loadSharedLinkInfo(shareLinkInfo.value.linkCode);
    // Object.keys(urlParams).forEach((key) => {
    //   if (urlParams[key]) {
    //     params.value[key] = urlParams[key];
    //   } else {
    //     params.value.id = key;
    //   }
    // });
  });
</script>

<template>
  <share-lock
    v-if="viewMode === 'shareLock' && shareLinkInfo?.id"
    :share-link-info="shareLinkInfo"
    @checkpass="handleCheckPassword"
  />
  <sendStatistics v-else :share-link-info="shareLinkInfo" />
  <!--这个需要判断是简单模式or 文档模式-->
  <sendStatisticsSimple v-if="true" :share-link-info="shareLinkInfo" />
</template>

<style lang="scss">
  html,
  body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }

  body {
    background-image: url('/assets/global-bg.svg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .main-wrapper {
    width: 98vw;
    height: 98vh;
    margin: 1vh auto;
  }
</style>
