<template>
  <div v-if="ready">
    <router-view />
  </div>
</template>

<script>
  import { setToken } from '@repo/infrastructure/auth';
  import ComConfigure from '@/common/configure';
  import { useSchemaStore, useTeacherStore, useUserStore } from '@repo/infrastructure/store';
  import useCommonStore from '@repo/infrastructure/utils/store';
  import useSchoolCourseStore from '@repo/components/store/schoolCourseStore';
  import { DataStructure } from '@/common/dataStructure';
  import { getURLParams } from '@/common/utils';
  import { getUserInfo } from '@/common/ts/permission';
  import { loadCurrentUser } from '@/common/permission';

  const urlParams = getURLParams();
  export default {
    name: 'App',
    components: {},
    data() {
      return {
        loginStatus: null,
        roleType: urlParams.role === 'Manager' ? 'Manager' : 'Company',

        firstLoading: true,
        ready: false,
        ds: DataStructure.getInstance(),
        layout: 'default',
        layoutComponent: null,
      };
    },
    watch: {
      $route(to) {
        this.layout = to.meta.layout || 'default';
      },
    },
    async beforeMount() {
      if (urlParams.token) {
        setToken(urlParams.token);
        window.location.href = window.location.origin + window.location.pathname;
      }

      const appInit = async () => {
        let userInfo;
        const { register, code, bo } = getURLParams();
        if (!register && !code && !bo) {
          userInfo = await loadCurrentUser();
        }

        // const { proxy: app } = getCurrentInstance();
        // app.config.globalProperties = userInfo;
        return {
          userInfo,
        };
      };

      await appInit(this);
      await this.loginUserInit();

      const user = getUserInfo();
      if (user) {
        this.globalData.user = user;
      }

      document.getElementById('global-loading-cover').style.display = 'none';

      this.ready = true;
    },
    methods: {
      async loginUserInit() {
        this.menuCollapsed = localStorage.getItem('mainMenuCollapsed') === 'true';
        if (urlParams.platform === 'tv' && urlParams.deviceId) {
          this.showMenu = false;
        }

        const schemaStore = useSchemaStore();
        const teacherStore = useTeacherStore();
        const userStore = useUserStore();
        const boStore = useCommonStore({
          api: '/org/branchOffice/simpleList',
        });
        const teachersStore = useCommonStore({
          api: '/org/companyUser/allTeachers',
        });
        const schoolCourseStore = useSchoolCourseStore();

        await Promise.all([
          await DataStructure.getInstance().load(false),
          await ComConfigure.getInstance().load(true),
          await schemaStore.fetchSchemaList(),
          await boStore.getList(),
          await teacherStore.getTeachersListByNature(userStore.getUserNature()),
          await teachersStore.getList(),
          await schoolCourseStore.getSchoolCourses(),
        ]);

        this.ds.branchOfficeList = await boStore.getList();
        this.ds.branchOfficeMap = await boStore.getMap();
      },
    },
  };
</script>

<style lang="scss">
  @use '../assets/iconfont/iconfont.css' as *;
  @tailwind base;
  @tailwind components;
  @tailwind utilities;
</style>
