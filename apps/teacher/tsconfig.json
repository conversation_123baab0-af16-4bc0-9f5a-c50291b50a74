{
  "compilerOptions": {
    "noEmitOnError": false,
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "noImplicitAny": false,
    "strict": false,
    "jsx": "preserve",
    "jsxFactory": "h",
    "jsxFragmentFactory": "Fragment",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
    },
    "lib": ["es2020", "dom"],
    "skipLibCheck": true,
    "types": ["vite/client", "node"],
    "experimentalDecorators": true
  },
  "include": [
    "src/**/*.ts",
    "src/*",
    "src/**/*",
    "src/**/*.vue",
    "../../packages/infrastructure/src/openapi/typings.d.ts",
    "../../packages/infrastructure/typings.d.ts",
    "@repo/infrastructure/types"
  ],
  "exclude": ["node_modules"]
}
