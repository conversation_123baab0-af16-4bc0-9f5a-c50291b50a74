"use strict";var extendStatics=function(e,t){return(extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)};function __extends(e,t){function n(){this.constructor=e}extendStatics(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var ECbConfigEventNames,EOfficeTypes,EModeTypes,EFullScreenStatus,__assign=function(){return(__assign=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var s in t=arguments[n])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e}).apply(this,arguments)};function __awaiter(e,t,n,i){return new(n||(n=Promise))(function(s,r){function o(e){try{c(i.next(e))}catch(e){r(e)}}function a(e){try{c(i.throw(e))}catch(e){r(e)}}function c(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(o,a)}c((i=i.apply(e,t||[])).next())})}function __generator(e,t){var n,i,s,r,o={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(r){return function(a){return function(r){if(n)throw new TypeError("Generator is already executing.");for(;o;)try{if(n=1,i&&(s=2&r[0]?i.return:r[0]?i.throw||((s=i.return)&&s.call(i),0):i.next)&&!(s=s.call(i,r[1])).done)return s;switch(i=0,s&&(r=[2&r[0],s.value]),r[0]){case 0:case 1:s=r;break;case 4:return o.label++,{value:r[1],done:!1};case 5:o.label++,i=r[1],r=[0];continue;case 7:r=o.ops.pop(),o.trys.pop();continue;default:if(!(s=(s=o.trys).length>0&&s[s.length-1])&&(6===r[0]||2===r[0])){o=0;continue}if(3===r[0]&&(!s||r[1]>s[0]&&r[1]<s[3])){o.label=r[1];break}if(6===r[0]&&o.label<s[1]){o.label=s[1],s=r;break}if(s&&o.label<s[2]){o.label=s[2],o.ops.push(r);break}s[2]&&o.ops.pop(),o.trys.pop();continue}r=t.call(e,o)}catch(e){r=[6,e],i=0}finally{n=s=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,a])}}}!function(e){e.refreshToken="api.getToken"}(ECbConfigEventNames||(ECbConfigEventNames={})),function(e){e.unknown="unknown",e.spreadsheet="s",e.writer="w",e.presentation="p",e.pdf="f",e.otl="o",e.dbt="d",e.ksheet="k"}(EOfficeTypes||(EOfficeTypes={})),function(e){e.nomal="nomal",e.simple="simple",e.embed="embed"}(EModeTypes||(EModeTypes={})),function(e){e[e.requestFullscreen=1]="requestFullscreen",e[e.exitFullscreen=0]="exitFullscreen"}(EFullScreenStatus||(EFullScreenStatus={}));var agent=window.navigator.userAgent.toLowerCase(),isIos=/iPhone|iPod|iPad/i.test(agent);function isPlainObject(e){if(!e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function isType(e,t){return{}.toString.call(e)==="[object "+t+"]"}function dispatchFullScreenChange(e){["fullscreen","fullscreenElement"].forEach(function(t){Object.defineProperty(document,t,{get:function(){return!!e.status},configurable:!0})});var t=new CustomEvent("fullscreenchange");document.dispatchEvent(t)}var Iframe=function(){function e(e,t,n,i,s,r,o){var a=this;void 0===s&&(s=!0),void 0===r&&(r=!1),void 0===o&&(o={}),this.instance=e,this.instanceId=t,this.url=n,this.mount=i,this.isListenResize=s,this.addedStyles=r,this.customAttrs=o,this.handleResize=function(){var e=a.mount,t=a.mountResizeObserver,n=e.clientHeight,i=e.clientWidth;0!==n||0!==i||t?0===n&&0===i||!t||(t.disconnect(),t=null):window.ResizeObserver&&(t=new ResizeObserver(function(e){a.handleResize()})).observe(e),a.iframe.style.cssText+="height: "+e.clientHeight+"px; width: "+e.clientWidth+"px"},this.iframe=this.initIframe()}return e.prototype.initIframe=function(){var e=this,t=this.mount,n=document.createElement("iframe");n.classList.add("web-office-iframe");var i=this.mergeAttrs({id:"office-iframe-"+this.instanceId,src:this.url,scrolling:"no",frameborder:"0",allowfullscreen:"allowfullscreen",webkitallowfullscreen:"true",mozallowfullscreen:"true",allow:"clipboard-read; clipboard-write"});for(var s in t?(i.style="width: "+t.clientWidth+"px; height: "+t.clientHeight+"px;",this.isListenResize&&window.addEventListener("resize",this.handleResize)):((t=document.createElement("div")).classList.add("web-office-default-container"),this.addedStyles||this.addStylesheetRules(".web-office-default-container {position: absolute; padding: 0;  margin: 0; width: 100vw; height: 100vh; left: 0; top: 0;}"),document.body.appendChild(t),i.style="width: 100vw; height: 100vh;"),isIos&&window.visualViewport&&this.isListenResize&&window.visualViewport.addEventListener("resize",function(){var t=window.visualViewport,n=t.width,i=t.height,s=document.body.clientHeight-e.iframe.clientHeight;e.instance.sendMsgToWps({eventName:"visualViewportResize",data:{width:n,height:i-s}})}),i)n.setAttribute(s,i[s]);return t.appendChild(n),n.destroy=function(){window.removeEventListener("resize",e.handleResize),n.parentNode.removeChild(n),n=null,e.mountResizeObserver&&(e.mountResizeObserver.disconnect(),e.mountResizeObserver=null)},n},e.prototype.destroy=function(){this.iframe.parentNode.removeChild(this.iframe),window.removeEventListener("resize",this.handleResize)},e.prototype.addStylesheetRules=function(e){var t=document.createElement("style");document.head.appendChild(t);var n=t.sheet;n.insertRule(e,n.cssRules.length)},e.prototype.mergeAttrs=function(e){var t=this.customAttrs;if(t&&Object.keys(t).length&&t.allow){var n=t.allow instanceof Array?t.allow:t.allow.split(";");n.unshift("clipboard-write"),n.unshift("clipboard-read"),t.allow=n.join(";"),Object.assign(e,t)}return e},e}(),Message=function(){function e(){this.HANDLE_LIST=[]}return e.prototype.add=function(e){this.HANDLE_LIST.push(e),window.addEventListener("message",e,!1)},e.prototype.remove=function(e){var t=this.HANDLE_LIST.indexOf(e);t>=0&&this.HANDLE_LIST.splice(t,1),window.removeEventListener("message",e,!1)},e.prototype.empty=function(){for(;this.HANDLE_LIST.length;){var e=this.HANDLE_LIST.shift();window.removeEventListener("message",e,!1)}},e.prototype.parse=function(e){try{return"object"==typeof e?e:e?JSON.parse(e):e}catch(t){return e}},e}(),Base=function(){function e(e,t){var n=this;this.collectObjIdHandlers=new Set,this.polyfillApi=["ExportAsFixedFormat","GetOperatorsInfo","ImportDataIntoFields","ReplaceText","ReplaceBookmark","GetBookmarkText","GetComments"],this.getId=function(){return""},this.setterCallbacks={idMap:{}},this.sendMsgToWps=function(){},this.objId=0,this.origin="",this.apiChannel=function(e,t,i,s){return __awaiter(n,void 0,void 0,function(){var n,r,o,a,c,l,u,d,f,p=this;return __generator(this,function(h){switch(h.label){case 0:return n=this.getId(),a=new Promise(function(e,t){r=e,o=t}),c={},t.args?[4,this.reduceArgs(t.args)]:[3,2];case 1:l=h.sent(),u=l[0],d=l[1],t.args=u,c=d,h.label=2;case 2:return e!==this.prefix+"setter"?[3,4]:[4,this.handleApiSetter(t)];case 3:h.sent(),h.label=4;case 4:return f=function(){var t=function(a){return __awaiter(p,void 0,void 0,function(){var l,u,d;return __generator(this,function(f){switch(f.label){case 0:return this.instance.originWhiteList.includes(a.origin)||this.origin===a.origin?(l=this.message.parse(a.data)).eventName===this.prefix+"callback"&&l.callbackId&&c[l.callbackId]?[4,c[l.callbackId].apply(c,l.data.args)]:[3,2]:[2];case 1:u=f.sent(),this.sendMsgToWps({result:u,eventName:this.prefix+"callback.reply",callbackId:l.callbackId}),f.label=2;case 2:return l.eventName===e+".reply"&&l.msgId===n&&(l.error?((d=new Error("")).stack=l.error+"\n"+i,s&&s(),o(d)):r(l.result),this.message.remove(t)),[2]}})})};return p.message.add(t),a},this.handleSendApiChannel([{eventName:e,data:t,msgId:n},f]),[2,a]}})})},this.handleApiSetter=function(e){return __awaiter(n,void 0,void 0,function(){var t,n,i,s,r,o,a,c,l,u,d=this;return __generator(this,function(f){switch(f.label){case 0:return t=function(){return Object.keys(d.setterCallbacks.idMap).find(function(e){return d.setterCallbacks.idMap[e]===i+":"+n})},n=e.prop,i=e.parentObjId,s=e.value,[4,this.reduceArgs([s])];case 1:return r=f.sent(),o=r[0],a=r[1],e.value=o[0],c=Object.keys(a)[0],l=this.setterCallbacks[i],null===s&&l&&l[n]&&((u=t())&&delete this.setterCallbacks.idMap[u],delete l[n],Object.keys(l).length||delete this.setterCallbacks[i],Object.keys(this.setterCallbacks.idMap).length||this.message.remove(this.setterCallbackSubscribe.bind(this))),c&&(Object.keys(this.setterCallbacks.idMap).length||this.message.add(this.setterCallbackSubscribe.bind(this)),this.setterCallbacks[i]||(this.setterCallbacks[i]={}),this.setterCallbacks[i][n]={callbackId:c,callback:a[c]},(u=t())&&delete this.setterCallbacks.idMap[u],this.setterCallbacks.idMap[c]=i+":"+n),[2]}})})},this.prefix=t,this.origin=e.origin,this.sendMsgToWps=e.sendMsgToWps.bind(e),this.getId=e.getId.bind(e),this.message=e.message,this.sdkInstanceId=e.instanceId,this.instance=e}return e.prototype.destroyApplication=function(){this.collectObjIdHandlers=new Set,this.objId=0},e.prototype.subEventHandle=function(e,t){var n=this,i={};this.message.add(function(e){return __awaiter(n,void 0,void 0,function(){var t,n,s,r,o,a;return __generator(this,function(c){switch(c.label){case 0:return this.instance.originWhiteList.includes(e.origin)||this.origin===e.origin?(t=this.message.parse(e.data),n=t.sdkInstanceId,t.eventName===this.prefix+"event"&&Number(n)===this.sdkInstanceId&&t.data?(s=t.data,r=s.eventName,o=s.data,(a=i[r])?[4,a(o)]:[3,2]):[3,2]):[2];case 1:c.sent(),c.label=2;case 2:return[2]}})})});var s=function(s){var r=t[s];Object.defineProperty(e,r,{set:function(e){i[r]=e,n.sendMsgToWps({eventName:n.prefix+"event.register",data:{eventName:r,register:!!e,objId:n.objId+=1},sdkInstanceId:n.sdkInstanceId})}})};for(var r in t)s(r)},e.prototype.mixinProto=function(e,t){Object.assign(e,t)},e.prototype.makeCollectObjIdHandle=function(e){var t=this;return function(){var n=[],i=function(e){n.push(e)};return t.collectObjIdHandlers.add(i),{End:function(){e(n),t.collectObjIdHandlers.delete(i)}}}},e.prototype.assign=function(e,t,n){for(var i=this,s=t.slice(0),r=function(){var t=s.shift();!t.alias&&~o.polyfillApi.indexOf(t.prop)&&s.push(__assign(__assign({},t),{alias:t.prop+"Async"})),Object.defineProperty(e,t.alias||t.prop,{get:function(){var s=1===t.cache,r=s&&e["__"+t.prop+"CacheValue"];if(!r){var o=i.getError(),a=i.createObjId(s),c=function(){for(var s,r=[],a=0;a<arguments.length;a++)r[a]=arguments[a];return void 0!==t.caller?(s={objId:i.createObjId()},i.assign(s,n[t.caller],n)):s={},i.wrapper(c,s,i.prefix+"caller",{obj:c,args:r,parentObjId:e.objId,objId:s.objId,prop:t.prop},o),s};return c.objId=-1,void 0!==t.getter&&(c.objId=a,i.assign(c,n[t.getter],n)),i.wrapper(e,c,i.prefix+"getter",{parentObjId:e.objId,objId:c.objId,prop:t.prop},o,function(){delete e["__"+t.prop+"CacheValue"]}),s&&(e["__"+t.prop+"CacheValue"]=c),c}return r},set:function(n){var s=i.getError();return i.wrapper(e,{},i.prefix+"setter",{value:n,parentObjId:e.objId,objId:-1,prop:t.prop},s)}})},o=this;s.length;)r()},e.prototype.wrapper=function(e,t,n,i,s,r){var o,a=this,c=(e.done?e.done():Promise.resolve()).then(function(){return o||(o=a.apiChannel(n,i,s,r)),o});t.done=function(){return c},t.then=function(e,n){return i.objId>=0?(t.then=null,t.catch=null,c.then(function(){e(t)}).catch(function(e){return n(e)})):c.then(e,n)},t.catch=function(e){return c.catch(e)},t.Destroy=function(){return this.apiChannel(this.prefix+"free",{objId:t.objId},"")}},e.prototype.handleSendApiChannel=function(e){var t=e[0],n=e[1];"function"==typeof(t=__assign({},t)).data&&(t.data=t.data()),n(),this.sendMsgToWps(t)},e.prototype.setterCallbackSubscribe=function(e){return __awaiter(this,void 0,void 0,function(){var t,n,i,s,r,o,a,c,l,u;return __generator(this,function(d){switch(d.label){case 0:return this.instance.originWhiteList.includes(e.origin)||this.origin===e.origin?(t=this.message.parse(e.data),n=t.eventName,i=t.callbackId,s=t.data,i&&(r=this.setterCallbacks.idMap[i])?(o=r.split(":"),a=o[0],c=o[1],n===this.prefix+"callback"&&this.setterCallbacks[a]&&this.setterCallbacks[a][c]?[4,(u=this.setterCallbacks[a][c]).callback.apply(u,s.args)]:[3,2]):[3,2]):[2];case 1:l=d.sent(),this.sendMsgToWps({result:l,callbackId:i,eventName:this.prefix+"callback.reply"}),d.label=2;case 2:return[2]}})})},e.prototype.reduceArgs=function(e){return __awaiter(this,void 0,void 0,function(){var t,n,i,s,r,o,a,c,l,u,d;return __generator(this,function(f){switch(f.label){case 0:t={},n=[],i=e.slice(0),f.label=1;case 1:return i.length?(s=void 0,[4,i.shift()]):[3,13];case 2:return(r=f.sent())&&r.done?[4,r.done()]:[3,4];case 3:f.sent(),f.label=4;case 4:if(!isPlainObject(s))return[3,11];for(a in s={},o=[],r)o.push(a);c=0,f.label=5;case 5:return c<o.length?(l=o[c],u=r[l],/^[A-Z]/.test(l)?u&&u.done?[4,u.done()]:[3,7]:[3,8]):[3,10];case 6:f.sent(),f.label=7;case 7:u&&u.objId?u={objId:u.objId}:"function"==typeof u&&(d=this.getId(),t[d]=u,u={callbackId:d}),f.label=8;case 8:s[l]=u,f.label=9;case 9:return c++,[3,5];case 10:return[3,12];case 11:r&&r.objId?s={objId:r.objId}:"function"==typeof r&&void 0===r.objId?(d=this.getId(),t[d]=r,s={callbackId:d}):s=r,f.label=12;case 12:return n.push(s),[3,1];case 13:return[2,[n,t]]}})})},e.prototype.createObjId=function(e){return this.objId+=1,!e&&this.collectObjId(),this.objId},e.prototype.collectObjId=function(){var e=this;this.collectObjIdHandlers.forEach(function(t){return t(e.objId)})},e.prototype.getError=function(){var e=new Error("");return(e.stack||e.message||"").split("\n").slice(2).join("\n")},e}(),Application=function(e){function t(t,n,i){var s=e.call(this,t,i)||this,r={},o=t.officeType,a=n.Events,c=n.Enum,l=n.Props,u=l[0],d=l[1],f={objId:s.objId};switch(s.assign(f,u,d),f.Events=a,f.Enum=c,f.Sub={},r.Enum=f.Enum,r.Events=f.Events,r.Props=l,o){case EOfficeTypes.writer:r.WordApplication=t.WpsApplication=function(){return f};break;case EOfficeTypes.spreadsheet:r.ExcelApplication=t.EtApplication=function(){return f};break;case EOfficeTypes.presentation:r.PPTApplication=t.WppApplication=function(){return f};break;case EOfficeTypes.pdf:r.PDFApplication=function(){return f}}return r.Application=f,r.Free=function(e){return s.apiChannel(s.prefix+"free",{objId:e},"")},r.Stack=f.Stack=s.makeCollectObjIdHandle(function(e){s.apiChannel(s.prefix+"free",{objId:e},"")}),r.destroyApplication=function(){s.destroyApplication()},s.subEventHandle(f.Sub,a),s.mixinProto(t,r),s}return __extends(t,e),t}(Base),CommonApi=function(e){function t(t,n,i){var s=e.call(this,t,i)||this,r={},o=n.Events,a=n.Enum,c=n.Props,l=c[0],u=c[1],d={objId:s.objId};return s.assign(d,l,u),d.Events=o,d.Enum=a,d.Sub={},r.CommonEnum=d.Enum,r.CommonEvents=d.Events,r.CommonProps=c,r.CommonApi=d,r.CommonFree=function(e){return s.apiChannel(s.prefix+"free",{objId:e},"")},r.CommonStack=d.Stack=s.makeCollectObjIdHandle(function(e){s.apiChannel(s.prefix+"free",{objId:e},"")}),r.destroyCommonApp=function(){s.destroyApplication()},s.subEventHandle(d.Sub,o),s.mixinProto(t,r),s}return __extends(t,e),t}(Base);function mitt(e){return e=e||Object.create(null),{on:function(t,n){(e[t]||(e[t]=[])).push(n)},off:function(t,n){e[t]&&e[t].splice(e[t].indexOf(n)>>>0,1)},emit:function(t,n){(e[t]||[]).slice().map(function(e){e(n)}),(e["*"]||[]).slice().map(function(e){e(t,n)})}}}var WebOfficeSDK=function(){function e(t){var n=this;this.id=0,this.origin="",this.originWhiteList=[],this.realOrigin="",this.iframeWH=null,this.cbConfigsSub={},this.readyEventNames=[{event:"ready",callback:function(){n.handleBaseReady()},after:!0},{event:"open.result"},{event:"api.ready",callback:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var i=e[0];return new Application(n,i,"api."),n.Application}},{event:"commonApi.ready",callback:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var i=e[0];return new CommonApi(n,i,"commonApi."),n.CommonApi}}],this.cbConfigs=["refreshToken"],this.onEventNames={"open.result":"fileOpen","tab.switch":"tabSwitch","file.saved":"fileStatus",error:"error",stage:"stage"},this.polyfillConfigName=[["wpsOptions","wordOptions"],["etOptions","excelOptions"],["wppOptions","pptOptions"]],this.flag={advancedApiReadySended:!1,advancedApiReadySendedJust:!1,commonApiReadySended:!1,commonApiReadySendedJust:!1},this.baseReadyPromise=Promise.resolve(),this.fileOpenPromise=Promise.resolve(),this.advancedReadyPromise=Promise.resolve(),this.commonApiPromise=Promise.resolve(),this.iframeInstance=null,this.officeType="",this.url="",this.version="2.0.5",this.iframeReady=!1,this.forceIframeResize=function(){n.iframeInstance&&n.iframeInstance.handleResize()},this.isOtherEvent=function(e){return void 0!==e&&e!==n.instanceId},this.handleBaseReady=function(){n.sendMsgToWps({eventName:"setConfig",data:__assign(__assign({},n.sdkConfig),{version:n.version})}),n.tokenData&&n.setToken(__assign(__assign({},n.tokenData),{hasRefreshTokenConfig:!!n.sdkConfig.refreshToken})),n.flag.advancedApiReadySended&&n.sendMsgToWps({eventName:"api.ready"}),n.flag.commonApiReadySended&&n.sendMsgToWps({eventName:"commonApi.ready"}),n.iframeReady=!0},this.handleFullscreenChange=function(){var e={status:EFullScreenStatus.requestFullscreen};document.fullscreenElement?e.status=EFullScreenStatus.requestFullscreen:e.status=EFullScreenStatus.exitFullscreen,n.sendMsgToWps({data:e,eventName:"fullscreenchange"})},this.instanceId=e.instanceId,e.instanceId+=1,this.message=new Message,this.mittInstance=mitt(),this.handleConfig(t)}return e.config=function(t){return void 0===t&&(t={}),e.singleInstance||(e.singleInstance=new e(t)),e.singleInstance},e.prototype.setToken=function(e){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(t){switch(t.label){case 0:return[4,this.basicReady()];case 1:return t.sent(),this.tokenData=e,this.sendMsgToWps({eventName:"setToken",data:e}),[2]}})})},e.prototype.advancedApiReady=function(){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(e){switch(e.label){case 0:return this.flag.advancedApiReadySendedJust?[3,2]:(this.flag.advancedApiReadySendedJust=!0,[4,this.fileOpenPromise]);case 1:e.sent(),this.flag.advancedApiReadySended||(this.flag.advancedApiReadySended=!0,this.sendMsgToWps({eventName:"api.ready"})),e.label=2;case 2:return[4,this.advancedReadyPromise];case 3:return[2,e.sent()]}})})},e.prototype.commonApiReady=function(){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(e){switch(e.label){case 0:return this.flag.commonApiReadySendedJust?[3,2]:(this.flag.commonApiReadySendedJust=!0,[4,this.basicReady()]);case 1:e.sent(),this.flag.commonApiReadySended||(this.flag.commonApiReadySended=!0,this.sendMsgToWps({eventName:"commonApi.ready"})),e.label=2;case 2:return[4,this.commonApiPromise];case 3:return[2,e.sent()]}})})},e.prototype.destroy=function(){this.iframeInstance.destroy(),this.message.empty(),this.destroyApplication&&this.destroyApplication(),this.destroyCommonApp&&this.destroyCommonApp(),this.removeFullscreenEventListener()},e.prototype.on=function(e,t){return __awaiter(this,void 0,void 0,function(){var n;return __generator(this,function(i){switch(i.label){case 0:return[4,this.basicReady()];case 1:return i.sent(),n=e,"fullscreenChange"===e&&(n="fullscreenchange"),this.handleBasicEvent(n,"on"),this.mittInstance.on(e,t),[2]}})})},e.prototype.off=function(e,t){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(n){switch(n.label){case 0:return[4,this.basicReady()];case 1:return n.sent(),this.handleBasicEvent(e,"off"),this.mittInstance.off(e,t),[2]}})})},e.prototype.handleConfig=function(e){var t,n=this;void 0===e&&(e={});try{var i=this.userConfHandler(e),s=i.subscriptions,r=void 0===s?{}:s,o=i.mount,a=void 0===o?null:o,c=i.url,l=i.originWhiteList;this.origin=(c.match(/https*:\/\/[^\/]+/g)||[])[0],this.originWhiteList=l||[];var u=this.readyEventNames.map(function(e){return n.makeReady(e)}),d=u[0],f=u[1],p=u[2],h=u[3];this.baseReadyPromise=d,this.fileOpenPromise=f,this.advancedReadyPromise=p,this.commonApiPromise=h,this.iframeWH=a?{width:a.clientWidth+"px",height:a.clientHeight+"px"}:{width:"100vw",height:"100vh"},delete i.mount,c&&delete i.url,this.url=c,delete i.subscriptions,this.sdkConfig=i,this.iframeInstance=new Iframe(this,this.instanceId,c,a,null===(t=i.isListenResize)||void 0===t||t,!1,{allow:i.attrAllow}),this.iframe=this.iframeInstance.iframe,this.listener(r)}catch(e){return console.error(e),null}},e.prototype.listener=function(e){var t=this;this.message.add(function(n){return __awaiter(t,void 0,void 0,function(){var t,i,s,r,o,a,c,l,u;return __generator(this,function(d){return t=this.message.parse(n.data),i=t.eventName,s=void 0===i?"":i,r=t.data,o=void 0===r?null:r,a=t.url,c=void 0===a?null:a,l=t.sdkInstanceId,this.isOtherEvent(l)?[2]:-1!==["wps.jssdk.api"].indexOf(s)?[2]:((u=this.handleListenEvent(s,o))?u():Object.keys(this.onEventNames).includes(s)&&("open.result"===s&&(this.officeType=o.fileInfo.officeType),this.mittInstance.emit(this.onEventNames[s],o)),"function"==typeof e[s]&&e[s](this,c||o),[2])})})})},e.prototype.handleListenEvent=function(e,t){var n,i=this;return((n={"api.scroll":function(){return window.scrollTo(t.x,t.y)},"event.callback":function(){return __awaiter(i,void 0,void 0,function(){var e,n,i,s,r,o,a;return __generator(this,function(c){return e=t.eventName,n=t.data,i=e,"fullScreenChange"===e&&(i="fullscreenChange"),((null===(o=this.sdkConfig.commonOptions)||void 0===o?void 0:o.isBrowserViewFullscreen)||(null===(a=this.sdkConfig.commonOptions)||void 0===a?void 0:a.isParentFullscreen))&&"fullscreenchange"===i&&(s=n.status,r=n.isDispatchEvent,this.sdkConfig.commonOptions.isBrowserViewFullscreen?this.iframeWH&&function(e,t,n,i){0===e?t.style="position: static; width: "+n.width+"; height: "+n.height:1===e&&(t.style="position: absolute; width: 100%; height: 100%"),i&&dispatchFullScreenChange({status:e})}(s,this.iframe,this.iframeWH,r):this.sdkConfig.commonOptions.isParentFullscreen&&function(e,t){if(0===e){var n=document,i=n.exitFullscreen||n.mozCancelFullScreen||n.msExitFullscreen||n.webkitCancelFullScreen||n.webkitExitFullscreen;i.call(document)}else if(1===e){var s=t.requestFullscreen||t.mozRequestFullScreen||t.msRequestFullscreen||t.webkitRequestFullscreen;s.call(t)}}(s,this.iframe)),this.mittInstance.emit(i,n),[2]})})}})[ECbConfigEventNames.refreshToken]=function(){return __awaiter(i,void 0,void 0,function(){var t,n;return __generator(this,function(i){switch(i.label){case 0:t={token:!1},i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.cbConfigsSub.refreshToken()];case 2:return t=i.sent(),[3,4];case 3:return n=i.sent(),console.error("refreshToken: "+(n||"fail to get")),[3,4];case 4:return this.sendMsgToWps({eventName:e+".reply",data:t}),[2]}})})},n)[e]},e.prototype.basicReady=function(){return this.baseReadyPromise},e.prototype.userConfHandler=function(e,t){var n=this;void 0===t&&(t=!0);var i=__assign({},e),s=i.headers,r=void 0===s?{}:s,o=i.subscriptions,a=void 0===o?{}:o,c=i.commonOptions,l=i.url,u=void 0===l?"":l,d=i.wpsUrl,f=void 0===d?"":d,p=i.mode,h=void 0===p?EModeTypes.nomal:p,m=i.debug,v=i.disablePlugins,b=i.hideGuide,g=i.readOnly;return Object.assign(i,this.handleHeadersAndSubscriptionsConfig(r,a,t)),i.url=this.handleUrlConfig(c,h,m,u,f,v,b,g),c&&(c.isParentFullscreen||c.isBrowserViewFullscreen)&&document.addEventListener("fullscreenchange",this.handleFullscreenChange),this.polyfillConfigName.map(function(e){var t=e[0],n=e[1];i[n]&&(i[t]=i[n])}),this.cbConfigs.map(function(e){var t=i[e];t&&isType(t,"Function")&&(n.cbConfigsSub[e]=t,i[e]={eventName:ECbConfigEventNames[e]})}),i.commandBars&&this.handleCommandBarsConfig(i.commandBars,!1),__assign({},i)},e.prototype.handleBasicEvent=function(e,t){if(!["error","fileOpen"].includes(e)){var n={eventName:"basic.event",data:{eventName:e,action:t}};this.sendMsgToWps(n)}},e.prototype.removeFullscreenEventListener=function(){document.removeEventListener("fullscreenchange",this.handleFullscreenChange)},e.prototype.handleHeadersAndSubscriptionsConfig=function(e,t,n){var i=e.backBtn,s=void 0===i?{}:i,r=e.shareBtn,o=void 0===r?{}:r,a=e.otherMenuBtn,c=void 0===a?{}:a,l=[["wpsconfig_back_btn",s],["wpsconfig_share_btn",o],["wpsconfig_other_menu_btn",c]],u=[];return c.items&&Array.isArray(c.items)&&c.items.forEach(function(e,t){void 0===e&&(e={}),"custom"===e.type&&u.push(["wpsconfig_other_menu_btn_"+t,e])}),l.concat(u).forEach(function(e){var i,s;i=e[1],s=e[0],i.subscribe&&"function"==typeof i.subscribe&&(i.callback=s,t[s]=i.subscribe,n&&delete i.subscribe)}),{}},e.prototype.handleUrlConfig=function(e,t,n,i,s,r,o,a){var c=[];c.push("sdkId="+this.instanceId),t===EModeTypes.simple||e&&!1===e.isShowTopArea?c.push("simple","hidecmb"):t===EModeTypes.embed&&c.push("simple=1","hidecmb=1","embed=1"),r&&c.push("disablePlugins"),o&&c.push("hideguide"),a&&c.push("readonly"),n&&c.push("debugger");var l=i||s;return l&&c.length&&(l=l+(l.indexOf("?")>=0?"&":"?")+c.join("&")),l},e.prototype.handleCommandBarsConfig=function(e,t){void 0===t&&(t=!0);var n=e.map(function(e){var t=e.attributes;if(!Array.isArray(t)){var n=[];for(var i in t)if(t.hasOwnProperty(i)){var s={name:i,value:t[i]};n.push(s)}e.attributes=n}return e});return t&&this.sendMsgToWps({data:n,eventName:"setCommandBars"}),n},e.prototype.sendMsgToWps=function(e){var t,n,i=__assign(__assign({},e),{sdkInstanceId:this.instanceId});null===(n=null===(t=this.iframe)||void 0===t?void 0:t.contentWindow)||void 0===n||n.postMessage(JSON.stringify(i),this.realOrigin)},e.prototype.makeReady=function(e){var t=this,n=e.event,i=e.callback,s=e.after;return new Promise(function(e){var r=function(o){if(t.realOrigin=o.origin,t.originWhiteList.includes(o.origin)||t.origin===o.origin){var a=t.message.parse(o.data),c=a.eventName,l=a.data,u=a.sdkInstanceId;if(!t.isOtherEvent(u)&&c===n){var d=void 0;!s&&i&&(d=i(l)),e(d),s&&i&&i(l),"ready"!==n&&t.message.remove(r)}}};t.message.add(r)})},e.prototype.getId=function(){return this.id+=1,this.instanceId+"-"+this.id},e.instanceId=1,e}(),CommonApiMaps={executeCommandBar:"ExecuteCommandBar",off:"Off",save:"Save",setCommandBars:"SetCommandBars",setCooperUserColor:"SetCooperUserColor",tabs:"Tabs",Stack:"Stack",WhenStacksEmpty:"WhenStacksEmpty"};function ProxyTabs(e){var t=this;return{switchTab:function(n){return __awaiter(t,void 0,void 0,function(){return __generator(this,function(t){switch(t.label){case 0:return[4,e.commonApiReady()];case 1:return t.sent(),[4,e.CommonApi.Tabs.SwitchTab(n.tabKey)];case 2:return[2,t.sent()]}})})},getTabs:function(){return __awaiter(t,void 0,void 0,function(){return __generator(this,function(t){switch(t.label){case 0:return[4,e.commonApiReady()];case 1:return t.sent(),[4,e.CommonApi.Tabs.GetTabs()];case 2:return[2,t.sent()]}})})}}}function CompatibleProxy(e,t){var n=this;return void 0===t&&(t={}),new Proxy(e,__assign(__assign({},{get:function(e,t){if(Object.keys(CommonApiMaps).includes(t)){var i=CommonApiMaps[t];return"Tabs"===i?ProxyTabs(e):e.CommonApi?Reflect.get(e.CommonApi,i):function(){return __awaiter(n,void 0,void 0,function(){var t;return __generator(this,function(n){switch(n.label){case 0:return[4,e.commonApiReady()];case 1:return n.sent(),[4,e.commonApiPromise];case 2:return n.sent(),"function"!=typeof e.CommonApi[i]?[3,4]:[4,e.CommonApi[i]()];case 3:return t=n.sent(),[3,5];case 4:t=e.CommonApi[i],n.label=5;case 5:return[2,t]}})})}}return Reflect.get(e,t)},set:function(e,t,n){return!!Reflect.has(e,t)&&Reflect.set(e,t,n)}}),t))}function addHooksFun(e,t){return __awaiter(this,void 0,void 0,function(){var n,i,s,r;return __generator(this,function(o){switch(o.label){case 0:return[4,e.commonApiReady()];case 1:for(i in o.sent(),n=[],t)n.push(i);s=0,o.label=2;case 2:if(!(s<n.length))return[3,11];switch(r=n[s],r){case"onHyperLinkOpen":return[3,3];case"onToast":return[3,5];case"getClipboardData":return[3,7]}return[3,9];case 3:return[4,e.CommonApi.Hooks.Add("hyperLink")];case 4:return o.sent().OnAction=t[r],[3,10];case 5:return[4,e.CommonApi.Hooks.Add("toast")];case 6:return o.sent().OnAction=t[r],[3,10];case 7:return[4,e.CommonApi.Hooks.Add("getClipboardData")];case 8:return o.sent().OnAction=t[r],[3,10];case 9:return[3,10];case 10:return s++,[3,2];case 11:return[2]}})})}var OfficeType,GATEWAY="https://o.wpsgo.com";!function(e){e.Spreadsheet="s",e.Writer="w",e.Presentation="p",e.Pdf="f",e.Otl="o",e.Dbt="d",e.KSheet="k"}(OfficeType||(OfficeType={}));var userOptionsConvert=function(e){var t=__assign({},e);t.token=t.token||t.fileToken;var n=t.appId,i=t.fileId,s=t.officeType,r=t.token,o=t.endpoint,a=t.customArgs,c=void 0===a?"":a;if(!Object.values(OfficeType).includes(s))throw Error("[WebOfficeSDK.init] officeType属性值错误，可选值参考WebOfficeSDK.OfficeType: "+JSON.stringify(OfficeType));var l=r?1:0,u={token:""};if("string"==typeof r?u.token=r:isPlainObject(r)&&(u=__assign({tokenData:u},r)),l&&!u.token)return console.error("[WebOfficeSDK.init] token设置无效"),t;if(t.url=o+"/office/"+s+"/"+i+"?_w_appid="+n+"&_w_tokentype="+l,isPlainObject(c)){var d=Object.entries(c).map(function(e){var t=e[0],n=e[1];return t+"="+encodeURIComponent(n)}).join("&");t.url=t.url+"&"+d}return __assign({},t)},SolutionSDK=function(e){function t(t){var n=e.call(this,t)||this;return t.token&&(isPlainObject(t.token)?n.setToken(__assign({},t.token)):n.setToken({token:t.token})),n.ApiEvent={AddApiEventListener:function(e,t){n.on(e,t)},RemoveApiEventListener:function(e,t){n.off(e,t)}},n}return __extends(t,e),t.init=function(e){var n=__assign({},e);if("string"==typeof n.mount){var i=document.querySelector(n.mount);i?n.mount=i:(console.warn("[WebOfficeSDK.init] mount挂载节点未找到"),delete n.mount)}if(n.url)throw Error("[WebOfficeSDK.init] 不支持传递url，请使用appId、fileId、officeType、token等参数初始化！");if(!n.appId||!n.fileId||!n.officeType)throw Error("[WebOfficeSDK.init] appId、fileId、officeType为必选项！");n.endpoint=n.endpoint||GATEWAY;var s=new t(userOptionsConvert(n));return addHooksFun(s,e),CompatibleProxy(s)},Object.defineProperty(t,"version",{get:function(){return"2.0.5"},enumerable:!1,configurable:!0}),t.prototype.ready=function(){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(e){switch(e.label){case 0:return[4,this.advancedApiReady()];case 1:return[2,e.sent()]}})})},t.OfficeType=Object.freeze({Spreadsheet:"s",Writer:"w",Presentation:"p",Pdf:"f",Otl:"o",KSheet:"ksheet",Dbt:"dbt"}),t}(WebOfficeSDK);window.WPS=SolutionSDK,module.exports=SolutionSDK;
