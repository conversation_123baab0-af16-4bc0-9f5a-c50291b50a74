{"name": "linch-se-teacher", "version": "2.1.52", "private": true, "scripts": {"dev": "vite --mode dev", "build": "vite build", "build:staging": "vite build --mode staging", "build:test": "vite build --mode test", "build:deploy": "vite build --mode=deploy", "build:whdev": "vite build --mode whdev", "build:whprod": "vite build --mode whprod", "build:v2cloud": "vite build --mode v2cloud", "build:prod": "vite build --mode production"}, "dependencies": {"@antv/g2plot": "^2.4.26", "@arco-plugins/vite-vue": "^1.4.5", "@chenfengyuan/vue-qrcode": "2", "@maxbilbow/ant-path-matcher": "^0.0.2", "@repo/components": "workspace:*", "@repo/config": "workspace:*", "@repo/env-config": "workspace:*", "@repo/infrastructure": "workspace:*", "@repo/lbs": "workspace:*", "@repo/rich-editor": "workspace:*", "@repo/ui": "workspace:*", "@rollup/plugin-dynamic-import-vars": "^2.1.2", "ali-oss": "^6.20.0", "axios": "^1.6.8", "core-js": "^3.8.3", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "sass": "^1.80.4", "sortablejs": "^1.15.1", "spark-md5": "^3.0.2", "tiny-emitter": "^2.1.0", "vue": "^3.0.0", "vue-axios": "^3.2.5", "vue-i18n": "^10.0.5", "vue-particles": "^1.0.9", "vue-print-next": "^1.0.8", "vue-router": "^4.2.5", "vue3-scroll-seamless": "^1.0.6"}, "devDependencies": {"@babel/core": "^7.12.16", "@typescript-eslint/eslint-plugin": "^7.8.0", "@typescript-eslint/parser": "^7.8.0", "@umijs/openapi": "^1.12.1", "@vitejs/plugin-vue": "^5.0.4", "@vue/cli-plugin-babel": "^5.0.1", "@vue/cli-service": "^5.0.1", "@vueuse/core": "^10.9.0", "autoprefixer": "^10.4.19", "electron": "^33.3.1", "eslint": "^8.57.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.26.0", "prettier": "^3.2.5", "sass-loader": "^7.0.3", "ts-loader": "^9.5.1", "typescript": "^5.4.5", "vite": "^6.0.7", "vite-plugin-sass": "^0.1.0", "vue-demi": "^0.14.7", "vue-eslint-parser": "^9.4.2", "vue-loader": "^17.4.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}