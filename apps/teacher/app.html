<!DOCTYPE html>
<html lang="">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate"/>
    <meta http-equiv="Pragma" content="no-cache"/>
    <meta http-equiv="Expires" content="0"/>
    <title>特殊教育资源中心服务平台</title> <!--牧云融教 - -->
    <script src="/js/wps/web-office-sdk-solution-v2.0.5.umd.js" defer></script>
    <script src="https://gosspublic.alicdn.com/aliyun-oss-sdk-6.18.1.min.js" defer></script>
    <style>
        @keyframes circle {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        #global-loading-cover {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100vw;
            height: calc(100vh - 55px);
            z-index: 1;
        }

        .global-loading-spin {
            border: 2px solid #f2f2f2;
            border-top: 2px solid #0e42d2;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: circle infinite 1.5s linear;
        }

        #app-main {
            position: relative;
            z-index: 3;
        }
        ::-webkit-scrollbar {
            height: 1px;
            width: 1px;
        }

        ::-webkit-scrollbar-thumb {
            background-color: #d3d3d3;
            border-radius: 10px;
            width: 1px;
        }

        ::-webkit-scrollbar-track {
            background-color: transparent;
        }

        * {
            scrollbar-width: thin;
            scrollbar-color: #d3d3d3 transparent;
        }
    </style>
</head>
<body>
<noscript>
    <strong>特殊教育资源中心服务平台</strong>
</noscript>
<div id="app-main"></div>
<div id="global-loading-cover">
    <div class="global-loading-spin"></div>
</div>
<script type="module" src="/src/app/main.ts"></script>
</body>
</html>
