import { resolve, join } from 'path';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { vitePluginForArco } from '@arco-plugins/vite-vue';

export default defineConfig({
  server: {
    port: 4233,
  },
  base: process.env.VITE_APP_TARGET_RELEASE === 'pc' ? './' : '/',
  plugins: [
    vue(),
    // @ts-ignore
    vitePluginForArco({
      style: 'css',
    }),
  ],
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
      scss: {
        api: 'modern-compiler',
      },
    },
  },
  resolve: {
    alias: {
      // 键必须以斜线开始和结束
      '@': resolve(__dirname, './src'),
    },
    extensions: ['.vue', '.js', '.ts', '.json'],
  },
  define: {
    'process.env': {
      PROXY_ENV: process.env.PROXY_ENV,
    },
  },
  build: {
    minify: true,
    sourcemap: false,
    outDir: 'dist',
    assetsDir: 'assets',
    cssCodeSplit: true,
    rollupOptions: {
      input: {
        login: join(__dirname, 'index.html'),
        app: join(__dirname, 'app.html'),
        screen: join(__dirname, 'screen.html'),
        subModulePca: join(__dirname, 'module/pca.html'),
        subModuleD2dEduShare: join(__dirname, 'module/d2dEduShare.html'),
        subModuleEditStudentInfo: join(__dirname, 'module/editStudentInfo.html'),
        subModuleMeetingCheckIn: join(__dirname, 'module/meetingCheckIn.html'),
        subModulePoster: join(__dirname, 'module/poster.html'),
        subModuleWechatLogin: join(__dirname, 'module/wechatLogin.html'),
        subModuleQuestionnaireForm: join(__dirname, 'module/questionnaireForm.html'),
        subModuleWeappWebView: join(__dirname, 'module/weappWebView.html'),
        subModuleAssessmentCBCL: join(__dirname, 'module/assessmentCBCL.html'),
        subModuleAiChat: join(__dirname, 'module/aiChat.html'),
      },
      output: {
        entryFileNames: 'modules/[name]_[hash].js',
        chunkFileNames: 'js/[name]_[hash].js',
        assetFileNames: 'assets/[name]_[hash].[ext]',
        manualChunks(id) {
          // let reg;
          // let match;
          if (id.toLowerCase().includes('hiprint')) {
            return 'hiPrint';
          }
          return undefined;
        },
      },
    },
  },
  envDir: '../../env',
});
