/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('@arco-design/web-vue')['Alert']
    AAvatar: typeof import('@arco-design/web-vue')['Avatar']
    AAvatarGroup: typeof import('@arco-design/web-vue')['AvatarGroup']
    ABadge: typeof import('@arco-design/web-vue')['Badge']
    ABreadcrumb: typeof import('@arco-design/web-vue')['Breadcrumb']
    ABreadcrumbItem: typeof import('@arco-design/web-vue')['BreadcrumbItem']
    AButton: typeof import('@arco-design/web-vue')['Button']
    AButtonGroup: typeof import('@arco-design/web-vue')['ButtonGroup']
    ACard: typeof import('@arco-design/web-vue')['Card']
    ACascader: typeof import('@arco-design/web-vue')['Cascader']
    ACheckbox: typeof import('@arco-design/web-vue')['Checkbox']
    ACheckboxGroup: typeof import('@arco-design/web-vue')['CheckboxGroup']
    ACol: typeof import('@arco-design/web-vue')['Col']
    ACollapse: typeof import('@arco-design/web-vue')['Collapse']
    ACollapseItem: typeof import('@arco-design/web-vue')['CollapseItem']
    ADatePicker: typeof import('@arco-design/web-vue')['DatePicker']
    ADescriptions: typeof import('@arco-design/web-vue')['Descriptions']
    ADescriptionsItem: typeof import('@arco-design/web-vue')['DescriptionsItem']
    ADgroup: typeof import('@arco-design/web-vue')['Dgroup']
    ADivider: typeof import('@arco-design/web-vue')['Divider']
    ADoption: typeof import('@arco-design/web-vue')['Doption']
    ADrawer: typeof import('@arco-design/web-vue')['Drawer']
    ADropdown: typeof import('@arco-design/web-vue')['Dropdown']
    ADropdownButton: typeof import('@arco-design/web-vue')['DropdownButton']
    ADsubmenu: typeof import('@arco-design/web-vue')['Dsubmenu']
    AEmpty: typeof import('@arco-design/web-vue')['Empty']
    AForm: typeof import('@arco-design/web-vue')['Form']
    AFormItem: typeof import('@arco-design/web-vue')['FormItem']
    AImage: typeof import('@arco-design/web-vue')['Image']
    AInput: typeof import('@arco-design/web-vue')['Input']
    AInputNumber: typeof import('@arco-design/web-vue')['InputNumber']
    AInputTag: typeof import('@arco-design/web-vue')['InputTag']
    ALayout: typeof import('@arco-design/web-vue')['Layout']
    ALayoutContent: typeof import('@arco-design/web-vue')['LayoutContent']
    ALayoutSider: typeof import('@arco-design/web-vue')['LayoutSider']
    ALink: typeof import('@arco-design/web-vue')['Link']
    AMenu: typeof import('@arco-design/web-vue')['Menu']
    AMenuItem: typeof import('@arco-design/web-vue')['MenuItem']
    AModal: typeof import('@arco-design/web-vue')['Modal']
    AOptgroup: typeof import('@arco-design/web-vue')['Optgroup']
    AOption: typeof import('@arco-design/web-vue')['Option']
    APopconfirm: typeof import('@arco-design/web-vue')['Popconfirm']
    APopover: typeof import('@arco-design/web-vue')['Popover']
    AProgress: typeof import('@arco-design/web-vue')['Progress']
    ARadio: typeof import('@arco-design/web-vue')['Radio']
    ARadioGroup: typeof import('@arco-design/web-vue')['RadioGroup']
    ARangePicker: typeof import('@arco-design/web-vue')['RangePicker']
    ARow: typeof import('@arco-design/web-vue')['Row']
    ASelect: typeof import('@arco-design/web-vue')['Select']
    ASkeleton: typeof import('@arco-design/web-vue')['Skeleton']
    ASkeletonLine: typeof import('@arco-design/web-vue')['SkeletonLine']
    ASpace: typeof import('@arco-design/web-vue')['Space']
    ASpin: typeof import('@arco-design/web-vue')['Spin']
    ASplit: typeof import('@arco-design/web-vue')['Split']
    ASubMenu: typeof import('@arco-design/web-vue')['SubMenu']
    ASwitch: typeof import('@arco-design/web-vue')['Switch']
    ATable: typeof import('@arco-design/web-vue')['Table']
    ATableColumn: typeof import('@arco-design/web-vue')['TableColumn']
    ATabPane: typeof import('@arco-design/web-vue')['TabPane']
    ATabs: typeof import('@arco-design/web-vue')['Tabs']
    ATag: typeof import('@arco-design/web-vue')['Tag']
    ATextarea: typeof import('@arco-design/web-vue')['Textarea']
    ATimeline: typeof import('@arco-design/web-vue')['Timeline']
    ATimelineItem: typeof import('@arco-design/web-vue')['TimelineItem']
    ATooltip: typeof import('@arco-design/web-vue')['Tooltip']
    ATree: typeof import('@arco-design/web-vue')['Tree']
    ATreeSelect: typeof import('@arco-design/web-vue')['TreeSelect']
    AUpload: typeof import('@arco-design/web-vue')['Upload']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
