import type { RouteRecordNormalized } from 'vue-router';

const moduleRoutes = import.meta.glob('../modules/*/routes.ts', { eager: true });
const modules = import.meta.glob('./modules/*.ts', { eager: true });
const externalModules = import.meta.glob('./externalModules/*.ts', {
  eager: true,
});
function formatModules(_modules: any, result: RouteRecordNormalized[]) {
  Object.keys(_modules).forEach((key) => {
    const defaultModule = _modules[key].default;
    if (!defaultModule) return;
    const moduleList = Array.isArray(defaultModule) ? [...defaultModule] : [defaultModule];
    result.push(...moduleList);
  });
  return result;
}

const moduleCustomRoutes: RouteRecordNormalized[] = formatModules(moduleRoutes, []);
const appDefineRoutes: RouteRecordNormalized[] = formatModules(modules, []).concat(moduleCustomRoutes);

export const appRoutes: RouteRecordNormalized[] = appDefineRoutes;
export const appExternalRoutes: RouteRecordNormalized[] = formatModules(externalModules, []);
