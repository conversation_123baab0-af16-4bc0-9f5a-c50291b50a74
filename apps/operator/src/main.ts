import { createApp } from 'vue';
import ArcoVue, { Message } from '@arco-design/web-vue';
import ArcoVueIcon from '@arco-design/web-vue/es/icon';
import { createPinia } from 'pinia';
import { axiosInstance } from '@repo/infrastructure/request';
import router from './router';
import App from './App.vue';
import '@arco-design/web-vue/dist/arco.css';
import '@repo/infrastructure/iconfont';
import './index.css';

const app = createApp(App);

app.use(ArcoVue, {});
app.use(ArcoVueIcon);

const piniaInstance = createPinia();
app.use(piniaInstance);

app.use(router);
// app.use(piniaInstance);
// app.use(globalComponents);
// app.use(directive);
// app.use(autoFocus);

router.beforeEach((to, from, next) => {
  if (to.meta?.title) {
    document.title = `${to.meta.title} | 牧云融教`;
  }
  next();
});

app.config.errorHandler = (err, vm, info) => {
  if (info && vm) {
    console.error(err);
    // @ts-ignore
    if (!err.message) {
      Message.error(`系统开小差了，请稍后再试～`);
    }
  }
};

// @ts-ignore
window.handleRequestError = (msg) => {
  Message.error(msg);
};

app.provide('router', router);

app.mount('#app');
export default app;
