<template>
  <div class="floating-menu">
    <div
      class="main-ball suspension"
      @click="toggleMenu"
      ref="mainBallRef"
      id="floating-menu"
    ></div>
    <!-- 菜单项 -->
    <div
      v-for="(item, index) in menuItems"
      :key="index"
      class="menu-item"
      :style="getMenuItemStyle(index)"
      @click="handleMenuItemClick(item)"
    >
      <iconfont :name="item.icon" :size="20" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { getElectronApi } from "@repo/infrastructure/electron";
import { Iconfont } from "@repo/infrastructure/ui";

const mainBallRef = ref<HTMLElement | null>(null);

// 菜单是否打开
const isMenuOpen = ref(false);
// 菜单项
const menuItems = [
  { icon: "a-beike1", path: "/widget/resources" },
  { icon: "kebiao", path: "/widget/timetable" },
  {
    icon: "h",
    handler: () => {
      confirm("确定要退出登录吗？") && getElectronApi().send("logout");
    },
  },
  { icon: "xuesheng", path: "/widget/classBehaviorRecord" },
  {
    icon: "guanli",
    handler: () => {
      getElectronApi().send("openTeacherClient");
    },
  },
];

// 切换菜单开关
const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value;
  mainBallRef.value?.focus();
};

const biasX = ref(0);
const biasY = ref(0);

const moveEvent = (e: any) => {
  getElectronApi().send(
    "hoverBallMove",
    e.screenX - biasX.value,
    e.screenY - biasY.value,
  );
};

const initSuspension = () => {
  const suspensionDom = document.getElementsByClassName("suspension")[0];
  suspensionDom.addEventListener("mousedown", function (e: any) {
    switch (e.button) {
      case 0:
        biasX.value = e.x;
        biasY.value = e.y;
        document.addEventListener("mousemove", moveEvent);
        break;
      case 2:
        getElectronApi().send("hoverBallRightClick");
        break;
    }
  });
  suspensionDom.addEventListener("mouseup", function () {
    biasX.value = 0;
    biasY.value = 0;
    document.removeEventListener("mousemove", moveEvent);
  });
};

onMounted(() => {
  initSuspension();

  window.onblur = () => {
    isMenuOpen.value = false;
  };
});

// 菜单项布局
const getMenuItemStyle = (index: number) => {
  const angle = (360 / menuItems.length) * index - 90; // 起始点从顶部开始
  const radius = isMenuOpen.value ? 70 : 0; // 菜单项距离主球的半径
  const x = radius * Math.cos((angle * Math.PI) / 180);
  const y = radius * Math.sin((angle * Math.PI) / 180);
  return {
    transform: `translate(${x + 10}px, ${y - 58}px) scale(${isMenuOpen.value ? 1 : 0.5})`,
    opacity: isMenuOpen.value ? 1 : 0,
    // transition: `transform 0.4s ease, opacity 0.4s ease`,
    transition: "all 0.3s ease",
    zIndex: isMenuOpen.value ? 1000 : -1,
  };
};

// 菜单项点击事件
const handleMenuItemClick = async (item: any) => {
  console.log(item);
  if (typeof item.handler === "function") {
    item.handler();
    return;
  }

  switch (item.path) {
    case "/widget/classBehaviorRecord":
      const currentCourseId = await getElectronApi().send(
        "getCurrentChapterId",
      );
      if (!currentCourseId) {
        alert("请先设置当前上课章节");
        return;
      }
      break;
  }

  await getElectronApi().send("hoverBallMenuItemClick", item.path);
  isMenuOpen.value = false;
};
</script>

<style scoped lang="scss">
/* 主悬浮球样式 */
.floating-menu {
  pointer-events: auto;
  position: fixed;
  width: 70px;
  height: 70px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.main-ball {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    #4cafef,
    #6a5acd,
    #ff69b4,
    #ffa500
  ); /* 动态渐变色 */
  background-size: 300% 300%;
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.3),
    /* 外部阴影 */ 0 0 15px rgba(108, 90, 205, 0.7),
    /* 主体光晕 */ inset 0 0 10px rgba(255, 255, 255, 0.5); /* 内部高光 */
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  animation: gradient-shift 3s infinite ease-in-out; /* 动态渐变动画 */
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

/* 鼠标悬浮交互效果 */
.main-ball:hover {
  transform: scale(1.05); /* 放大效果 */
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.5),
    /* 加强阴影 */ 0 0 25px rgba(108, 90, 205, 0.9); /* 加强光晕 */
}

/* 菜单项通用样式 */
.menu-item {
  cursor: pointer;
  position: absolute;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffffff, #e0e0e0); /* 简洁渐变背景 */
  box-shadow:
    0 4px 10px rgba(0, 0, 0, 0.2),
    inset 0 0 5px rgba(255, 255, 255, 0.5); /* 内外阴影 */
  display: flex;
  justify-content: center;
  align-items: center;
  transform: scale(0); /* 默认收缩 */
  transition: all 0.4s ease;
  opacity: 0;

  &:hover {
    scale: 1.02; /* 放大效果 */
    transition: all linear 0.3s;
    opacity: 1;
    //box-shadow:
    //  0 10px 25px rgba(0, 0, 0, 0.5),
    //  /* 加强阴影 */ 0 0 25px rgba(108, 90, 205, 0.9); /* 加强光晕 */
  }
}

/* 菜单展开时的样式 */
.menu-item.show {
  transform: scale(1); /* 展开时正常大小 */
  opacity: 0.5; /* 完全可见 */
}

/* 动态渐变动画 */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
</style>

<style>
body,
html {
  pointer-events: none;
}
</style>
