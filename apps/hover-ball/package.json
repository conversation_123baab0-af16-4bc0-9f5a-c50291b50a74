{"name": "hover-ball", "private": true, "version": "2.1.52", "type": "module", "scripts": {"dev": "vite", "build:deploy": "vite build --mode=deploy", "preview": "vite preview"}, "dependencies": {"@repo/config": "workspace:*", "@repo/infrastructure": "workspace:*", "vue": "^3.5.13"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "typescript": "~5.6.2", "vite": "^6.0.7", "vue-tsc": "^2.2.0"}}