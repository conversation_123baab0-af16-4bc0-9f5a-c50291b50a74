<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { computed } from 'vue';
  import { useUserStore } from '@repo/infrastructure/store';
  import dayjs from 'dayjs';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    modelValue: {
      type: Object,
      required: true,
    },
  });

  const emit = defineEmits(['update:visible']);
  const userStore = useUserStore();

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  //   const assessmentLink = computed(() => {
  //     return `邀请您参加【${props.modelValue.name}】的评审，请您点击下面链接，进入评审页面：\n
  // ${userStore.getCompanyBindingUrl()}/module/pca.html?${props.modelValue.id}-${dayjs().format('YYYYMMDD')}-${
  //       Number(Math.random().toFixed(4)) * 10000
  //     }`;
  //   });
  const assessmentLink = computed(() => {
    return `邀请您参加【${props.modelValue.name}】的评审，请您点击下面链接，进入评审页面：\n
${userStore.getCompanyBindingUrl()}/module/pca.html?${props.modelValue.id}-${dayjs().format('YYYYMMDD')}`;
  });

  const handleCopy = () => {
    const input = document.createElement('input');
    input.setAttribute('value', assessmentLink.value);
    document.body.appendChild(input);
    input.select();
    document.execCommand('copy');
    document.body.removeChild(input);

    Message.success('已复制评审链接至剪切板');
  };
</script>

<template>
  <a-modal v-model:visible="modalVisible" title="获取评审链接">
    <a-textarea :model-value="assessmentLink" class="w-full" :auto-size="true" />
    <template #footer>
      <a-button type="primary" @click="handleCopy">复制链接</a-button>
      <a-button @click="modalVisible = false">关闭</a-button>
    </template>
  </a-modal>
</template>

<style scoped lang="scss"></style>
