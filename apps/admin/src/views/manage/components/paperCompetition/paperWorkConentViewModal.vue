<script setup lang="ts">
  import { computed } from 'vue';
  import { AttachmentsPreviewDisplay, RichTextDisplay } from '@repo/ui/components/data-display/components';
  import TeachingArchiveDetail from '@repo/components/course/teachingArchive/teachingArchiveDetail.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    competition: {
      type: Object,
      required: true,
    },
    raw: {
      type: Object,
      required: true,
    },
  });

  const emit = defineEmits(['update:visible']);

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });
</script>

<template>
  <a-modal v-model:visible="modalVisible" fullscreen title="参赛作品内容">
    <div v-if="modalVisible" class="flex-1 bg-slate-400 py-6 paper-wrapper shadow-inner">
      <div class="bg-white py-4 px-5 mx-auto page shadow-xl">
        <a-descriptions :column="3">
          <a-descriptions-item label="作品题目" :span="3">{{ raw.subject }}</a-descriptions-item>
          <a-descriptions-item label="类型">{{ raw.type }}</a-descriptions-item>
          <a-descriptions-item v-if="competition.type === '课例类'" label="上课时间">{{
            raw.classTime
          }}</a-descriptions-item>
          <a-descriptions-item label="附件" :span="2">
            <attachments-preview-display v-if="raw.attachments?.length" :raw="raw.attachments" />
            <span v-else>暂无附件</span>
          </a-descriptions-item>
          <a-descriptions-item label="介绍">{{ raw.description }}</a-descriptions-item>
        </a-descriptions>
        <teaching-archive-detail
          v-if="competition.type === '课例类'"
          :chapter="{
            content: {
              ...raw,
              teachingAssessCriteria: raw.targetContent,
            },
          }"
          :show-assessment="false"
        />
        <div v-else>
          <a-divider :margin="10" />
          <rich-text-display class="pr-4" :raw="raw.content" />
        </div>
      </div>
    </div>
  </a-modal>
  <!---->
</template>

<style scoped lang="scss">
  .paper-wrapper {
    height: calc(100vh - 160px);
    overflow-y: scroll;
    .page {
      width: 1000px;
    }
  }
</style>
