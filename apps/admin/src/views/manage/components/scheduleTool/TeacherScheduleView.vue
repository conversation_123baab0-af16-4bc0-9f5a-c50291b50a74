<template>
  <div class="teacher-schedule-view">
    <!-- 教师选择器 -->
    <div class="mb-4 flex justify-between items-center">
      <a-select
        v-model="selectedTeacherId"
        placeholder="请选择教师"
        :filter-option="true"
        :show-search="true"
        :allow-search="true"
        :allow-clear="true"
        class="w-56"
        size="mini"
        :filter-single-option="filterOption"
      >
        <a-option v-for="teacher in teachers" :key="teacher.id" :value="teacher.id" :label="teacher.name">
          {{ teacher.name }}
        </a-option>
      </a-select>
    </div>

    <!-- 课表内容区域 -->
    <div v-if="selectedTeacherId" class="schedule-container">
      <a-card :bordered="false" class="schedule-card">
        <template #title>
          <div class="flex items-center justify-between">
            <span>教师课表</span>
            <div class="flex items-center">
              <span class="text-sm text-gray-500">{{ selectedTeacherName }}</span>
              <div class="ml-2 flex gap-1">
                <a-tag v-for="group in selectedTeacherGroups" :key="group.id" type="primary" size="small">
                  {{ group.name }}
                </a-tag>
                <a-tag v-if="!selectedTeacherGroups.length" type="outline" size="small"> 未分配教研组 </a-tag>
              </div>
            </div>
          </div>
        </template>

        <!-- 课表表格 -->
        <div class="teacher-timetable">
          <table class="schedule-table">
            <thead>
              <tr>
                <th class="time-col">时间</th>
                <th v-for="day in weekdays" :key="day.value" class="day-col">{{ day.label }}</th>
              </tr>
            </thead>
            <tbody>
              <!-- 上午课程 -->
              <tr v-for="(period, index) in morningPeriods" :key="`morning-${index}`">
                <td class="period-cell">
                  <div class="period-label">上午</div>
                  <div class="period-number">第{{ period }}节</div>
                </td>
                <td v-for="(day, dayIndex) in weekdays" :key="`morning-${index}-${dayIndex}`" class="course-cell">
                  <div
                    v-if="getCourseInfo(dayIndex, 0, period)"
                    class="course-content"
                    :style="getCourseStyle(getCourseInfo(dayIndex, 0, period).courseId)"
                  >
                    <div class="course-name">{{ getCourseInfo(dayIndex, 0, period).courseName }}</div>
                    <div class="class-name">{{ getCourseInfo(dayIndex, 0, period).className }}</div>
                  </div>
                  <div v-else class="empty-cell">--</div>
                </td>
              </tr>

              <!-- 下午课程 -->
              <tr v-for="(period, index) in afternoonPeriods" :key="`afternoon-${index}`">
                <td class="period-cell">
                  <div class="period-label">下午</div>
                  <div class="period-number">第{{ period }}节</div>
                </td>
                <td v-for="(day, dayIndex) in weekdays" :key="`afternoon-${index}-${dayIndex}`" class="course-cell">
                  <div
                    v-if="getCourseInfo(dayIndex, 1, period)"
                    class="course-content"
                    :style="getCourseStyle(getCourseInfo(dayIndex, 1, period).courseId)"
                  >
                    <div class="course-name">{{ getCourseInfo(dayIndex, 1, period).courseName }}</div>
                    <div class="class-name">{{ getCourseInfo(dayIndex, 1, period).className }}</div>
                  </div>
                  <div v-else class="empty-cell">--</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 课程图例 -->
        <div class="course-legend mt-4">
          <div v-for="course in teacherCourses || []" :key="course.id" class="legend-item">
            <div class="legend-color" :style="getCourseStyle(course.id)">
              {{ course.name.substr(0, 1) }}
            </div>
            <div class="legend-name">{{ course.name }}</div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 未选择教师提示 -->
    <div v-else class="empty-state">
      <a-empty description="请选择教师查看课表" />
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, watch } from 'vue';
  import { getCourseColor } from './config/courseColors';

  // 属性定义
  const props = defineProps({
    teacherScheduleData: {
      type: Object,
      default: () => ({}),
    },
    record: {
      type: Object,
    },
  });

  // 响应式状态
  const selectedTeacherId = ref(null);
  const teachers = ref([]);
  const classes = ref([]);
  const courses = ref([]);
  const teachingGroups = ref([]);
  const teacherSchedule = ref(props.record.teacherSchedule || {});

  // 星期几配置
  const weekdays = [
    { label: '周一', value: 0 },
    { label: '周二', value: 1 },
    { label: '周三', value: 2 },
    { label: '周四', value: 3 },
    { label: '周五', value: 4 },
  ];

  // 节次设置
  const morningPeriods = ref([1, 2, 3]); // 默认上午3节课
  const afternoonPeriods = ref([4, 5, 6]); // 默认下午3节课

  // 计算属性：当前选中教师名称
  const selectedTeacherName = computed(() => {
    const teacher = teachers.value.find((t) => t.id === selectedTeacherId.value);
    return teacher ? teacher.name : '';
  });

  // 计算属性：当前教师的所有课程
  const teacherCourses = computed(() => {
    if (!selectedTeacherId.value || !teacherSchedule.value[selectedTeacherId.value]) {
      return [];
    }

    const courseIds = new Set();
    const teacherData = teacherSchedule.value[selectedTeacherId.value] || [];

    Object.values(teacherData).forEach(([courseId]) => {
      courseIds.add(courseId);
    });

    return Array.from(courseIds)
      .map((id) => courses.value.find((c) => c.id === id))
      .filter(Boolean);
  });

  // 获取课程样式
  const getCourseStyle = (courseId) => {
    const color = getCourseColor(courseId, courses.value);
    return {
      backgroundColor: color.bg,
      color: color.text,
      borderColor: `${color.text}33`, // 添加透明度
    };
  };

  // 获取标签样式
  // const getTagStyle = getCourseStyle;

  // --- 辅助函数 ---
  const getCourseName = (id) => {
    const course = courses.value.find((c) => c.id === id);
    return course ? course.name : '未知课程';
  };
  const getClassName = (id) => {
    const classItem = classes.value.find((c) => c.id === id);
    return classItem ? classItem.name : '未知班级';
  };
  // 获取指定时段的课程信息
  const getCourseInfo = (dayIndex, sessionIndex, periodNumber) => {
    if (!selectedTeacherId.value || !teacherSchedule.value[selectedTeacherId.value]) {
      return null;
    }

    const periodCode = `${dayIndex}-${sessionIndex}-${periodNumber}`;
    const teacherData = teacherSchedule.value[selectedTeacherId.value];

    if (!teacherData || !teacherData[periodCode]) {
      return null;
    }

    const [courseId, classId] = teacherData[periodCode];

    return {
      courseId,
      classId,
      courseName: getCourseName(courseId),
      className: getClassName(classId),
    };
  };

  // 在 script setup 部分添加计算属性
  const selectedTeacherGroups = computed(() => {
    const teacher = teachers.value.find((t) => t.id === selectedTeacherId.value);
    if (!teacher || !teacher.teachingGroups || !teacher.teachingGroups.length) {
      return [];
    }
    return teacher.teachingGroups.map((group) => {
      const fullGroup = teachingGroups.value.find((g) => g.id === group.id);
      return fullGroup || group;
    });
  });

  // 在 script setup 部分添加
  const filterOption = (inputValue, option) => {
    return option.label.toLowerCase().includes(inputValue.toLowerCase());
  };

  // --- 监听 prop 变化以更新本地状态 ---
  watch(
    () => props.teacherScheduleData,
    (newData) => {
      teacherSchedule.value = props.record.teacherSchedule || {};
    },
    { immediate: true, deep: true },
  ); // immediate: true 确保初始加载时也执行

  // 组件挂载时加载数据
  onMounted(() => {
    // 加载基础数据
    teachers.value = props.record.teachers || [];
    classes.value = props.record.classes || [];
    courses.value = props.record.courses || [];
    teachingGroups.value = props.record.teachingGroups || [];
    selectedTeacherId.value = teachers.value[0]?.id;
  });

  watch(
    () => props.record,
    (newVal) => {
      teacherSchedule.value = newVal.teacherSchedule || {};
    },
    { deep: true },
  );
</script>

<style scoped>
  .teacher-schedule-view {
    margin-bottom: 24px;
  }

  .schedule-container {
    margin-top: 16px;
  }

  .schedule-card {
    height: 100%;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .schedule-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #e5e7eb;
    table-layout: fixed;
  }

  .schedule-table th,
  .schedule-table td {
    border: 1px solid #e5e7eb;
    text-align: center;
    vertical-align: middle;
  }

  .schedule-table th {
    background-color: #f9fafb;
    padding: 10px;
    font-weight: 500;
    color: #374151;
  }

  .time-col {
    width: 80px;
  }

  .day-col {
    width: calc((100% - 80px) / 5);
  }

  .period-cell {
    background-color: #f3f4f6;
    padding: 8px;
  }

  .period-label {
    font-size: 12px;
    color: #6b7280;
  }

  .period-number {
    font-weight: 500;
    color: #374151;
  }

  .course-cell {
    height: 80px;
    padding: 4px;
    position: relative;
  }

  .course-content {
    position: absolute;
    inset: 4px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid;
    transition: all 0.2s;
  }

  .course-content:hover {
    transform: none;
    filter: brightness(0.95);
  }

  .course-name {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .class-name {
    font-size: 12px;
    opacity: 0.8;
  }

  .empty-cell {
    position: absolute;
    inset: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    font-size: 16px;
  }

  .empty-state {
    padding: 40px;
    background-color: #f9fafb;
    border-radius: 4px;
    margin-top: 16px;
  }

  .course-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e5e7eb;
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .legend-color {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 12px;
    border: 1px solid;
  }

  .legend-name {
    font-size: 13px;
    color: #374151;
  }

  @media (max-width: 768px) {
    .course-name {
      font-size: 12px;
    }

    .class-name {
      font-size: 11px;
    }

    .course-cell {
      height: 60px;
    }

    .period-cell {
      padding: 6px;
    }

    .legend-item {
      gap: 4px;
    }

    .legend-color {
      width: 20px;
      height: 20px;
      font-size: 10px;
    }

    .legend-name {
      font-size: 12px;
    }
  }
</style>
