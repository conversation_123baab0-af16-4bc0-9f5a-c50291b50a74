// // 数据存储服务
// const saveToStorage = (key, data) => {
//   localStorage.setItem(key, JSON.stringify(data));
// };
//
// const loadFromStorage = (key) => {
//   const data = localStorage.getItem(key);
//   return data ? JSON.parse(data) : [];
// };
//
// // eslint-disable-next-line import/prefer-default-export
// export const useDataService = () => {
//   // 教师数据操作
//   const saveTeachers = (teachers) => {
//     saveToStorage('teachers', teachers);
//   };
//
//   const loadTeachers = () => {
//     return loadFromStorage('teachers');
//   };
//
//   // 课程数据操作
//   const saveCourses = (courses) => {
//     saveToStorage('courses', courses);
//   };
//
//   const loadCourses = () => {
//     return loadFromStorage('courses');
//   };
//
//   // 班级数据操作
//   const saveClasses = (classes) => {
//     saveToStorage('classes', classes);
//   };
//
//   const loadClasses = () => {
//     return loadFromStorage('classes');
//   };
//
//   // 课时计划数据操作
//   const saveCoursePlan = (plan) => {
//     saveToStorage('coursePlan', plan);
//   };
//
//   const loadCoursePlan = () => {
//     return loadFromStorage('coursePlan');
//   };
//
//   // 约束条件数据操作
//   const saveConstraints = (constraints) => {
//     saveToStorage('constraints', constraints);
//   };
//
//   const loadConstraints = () => {
//     return loadFromStorage('constraints');
//   };
//
//   // 教室数据操作
//   const saveClassrooms = (classrooms) => {
//     localStorage.setItem('classrooms', JSON.stringify(classrooms));
//   };
//
//   const loadClassrooms = () => {
//     const classrooms = localStorage.getItem('classrooms');
//     return classrooms ? JSON.parse(classrooms) : [];
//   };
//
//   return {
//     saveTeachers,
//     loadTeachers,
//     saveCourses,
//     loadCourses,
//     saveClasses,
//     loadClasses,
//     saveCoursePlan,
//     loadCoursePlan,
//     saveConstraints,
//     loadConstraints,
//     saveClassrooms,
//     loadClassrooms,
//   };
// };
