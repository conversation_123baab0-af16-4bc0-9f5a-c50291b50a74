import { Message } from '@arco-design/web-vue';

interface ScheduleSetting {
  classrooms: any[];
  courses: any[];
  teachingGroups: any[];
  teachers: any[];
  classes: any[];
  weeklySchedule: any[];
  constraints: any;
  weeklyScheduleData: any;
  consecutiveTeachingSettings: any;
  constraintsStats: any;
}

interface ScheduleParam {
  courseDefinitions: any[]; // 课程id以及可用教室数量
  teacherProfiles: {
    teachers: any[];
    consecutiveLimit: number; // 最大连排数
    excludedTeacherIds: number[]; // 包含的教师
  };
  coursePlan: {
    classCourseAssignments: any;
  };
  scheduleConstraints: { classScheduleConstraints: any };

  // 排课服务的额外配置
  generate_debug_info: boolean;
  max_solve_time: number;
  log_search: boolean;
}

// 课时计划转换函数 注释在CoursePlan导出函数
const handleCoursePlanExport = (setting: ScheduleSetting): any => {
  try {
    const classes = setting?.classes || [];
    const courses = setting?.courses || [];
    const teachers = setting?.teachers || [];

    const exportPayload = {
      classCourseAssignments: [],
    };

    classes.forEach((cls) => {
      const classAssignment = {
        classId: cls.id,
        // gradeId: cls.gradeId,
        courses: [],
      };

      courses.forEach((course) => {
        const key = `${course.id}-${cls.id}`;
        const planItem = setting.weeklyScheduleData[key];
        if (planItem && planItem.weeklyRequiredHours > 0) {
          const courseAssignment = {
            courseId: course.id,
            weeklyRequiredHours: planItem.weeklyRequiredHours,
            sessionRequirement: planItem.sessionRequirement || 'any',
            dailyMaxHours: planItem.dailyMaxHours || 1, // 确保至少为1? 或按实际?
            teacherAssignments: [], // 存储该课程的教师分配
          };

          if (planItem.teacherWeeklyHours) {
            Object.entries(planItem.teacherWeeklyHours).forEach(([teacherIdStr, hours]: [string, number]) => {
              const teacherId = Number(teacherIdStr);
              if (hours > 0) {
                const teacher = teachers.find((t) => t.id === teacherId);
                if (teacher) {
                  courseAssignment.teacherAssignments.push({
                    teacherId,
                    assignedWeeklyHours: hours,
                  });
                } else {
                  // 如果找不到教师信息，可能数据不同步，可以选择记录日志或忽略
                }
              }
            });
          }
          classAssignment.courses.push(courseAssignment);
        }
      });
      if (classAssignment.courses.length > 0) {
        exportPayload.classCourseAssignments.push(classAssignment);
      }
    });
    return exportPayload;
  } catch (error) {
    Message.error('导出数据失败，请查看控制台了解详情');
  }
  return null;
};

// 导出所有年级和班级的约束设置
const exportConstraintSettings = (setting: ScheduleSetting) => {
  const extractUniqueGradeInfo = (data: any[]): any[] => {
    const gradeMap = new Map<number, any>();
    // eslint-disable-next-line no-restricted-syntax
    for (const item of data) {
      if (!gradeMap.has(item.gradeId)) {
        gradeMap.set(item.gradeId, { id: item.gradeId, name: item.gradeName });
      }
    }
    return Array.from(gradeMap.values());
  };

  const grades = extractUniqueGradeInfo(setting.classes);

  const exportData = {
    classScheduleConstraints: [], // 各班级的具体约束列表
  };

  // 遍历所有年级
  grades.forEach((grade) => {
    const gradeId = grade.id;
    const savedSettings = setting.constraints[`constraints_${gradeId}`];

    if (!savedSettings) return;

    const settings = savedSettings;

    const periodConfig = settings.periodSettings || {
      morning: 4,
      afternoon: 3,
    };

    const morning = settings.morningPeriods || [];
    const afternoon = settings.afternoonPeriods || [];

    // 找出该年级的所有班级
    const gradeClasses = setting.classes.filter((cls: any) => cls.gradeId === gradeId);

    if (gradeClasses.length === 0) return; // 跳过没有班级的年级
    // 为该年级的每个班级生成约束数据
    gradeClasses.forEach((classItem) => {
      const classConstraints = {
        classId: classItem.id,
        // gradeId: grade.id,
        periodConfiguration: {
          // 节次配置
          totalPeriodsPerDay: (periodConfig?.morning ?? 3) + (periodConfig?.afternoon ?? 4),
          morningPeriods: Array.from({ length: periodConfig.morning }, (_, i) => i + 1), // [1, 2, 3, 4]
          afternoonPeriods: Array.from({ length: periodConfig.afternoon }, (_, i) => i + periodConfig.morning + 1), // [5, 6, 7]
        },
        constraints: [], // 特殊约束列表 (非 normal 状态或有额外设置)
      };

      // 辅助函数处理上午或下午的约束数据
      const processConstraints = (periodRows, session, basePeriodNumber = 0) => {
        // 生成唯一的周期代码 (例如 '0-0-1' 表示周一上午第1节)
        const generatePeriodCode = (dayOfWeek, amOrPmKey, periodNumber) => {
          const dayMap = {
            monday: 0,
            tuesday: 1,
            wednesday: 2,
            thursday: 3,
            friday: 4,
          };
          const sessionMap = {
            morning: 0,
            afternoon: 1,
          };
          const dayCode = dayMap[dayOfWeek];
          const sessionCode = sessionMap[amOrPmKey];
          // 构建并返回periodCode
          return `${dayCode}-${sessionCode}-${periodNumber}`;
        };

        const weekDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
        periodRows.forEach((row, index) => {
          const periodNumber = basePeriodNumber + index + 1;
          weekDays.forEach((day) => {
            const constraint = row[day];
            if (!constraint) return; // 跳过无效数据

            // 只记录需要特殊处理的约束
            if (
              constraint.status !== 'normal' ||
              constraint.hasTeachingGroup ||
              (constraint.priorityCourses && constraint.priorityCourses.length > 0) ||
              (constraint.avoidCourses && constraint.avoidCourses.length > 0)
            ) {
              // 构建特殊约束对象
              const specialConstraint: any = {
                periodCode: generatePeriodCode(day, session, periodNumber), // 周期代码，例如 '0-0-1'
              };

              // 添加教研组信息
              if (constraint.hasTeachingGroup) {
                specialConstraint.teachingGroupId = constraint.teachingGroupId;
              }

              // 根据约束状态添加类型和具体信息
              if (constraint.status === 'empty') {
                specialConstraint.constraintType = 'empty';
              } else if (constraint.status === 'fixed') {
                specialConstraint.constraintType = 'fixed';
                specialConstraint.courseId = constraint.fixedCourseId;
              } else if (
                (constraint?.priorityCourses && constraint?.priorityCourses?.length > 0) ||
                (constraint?.avoidCourses && constraint.avoidCourses?.length > 0)
              ) {
                specialConstraint.constraintType = 'preference'; // 有课程偏好
                if (constraint.priorityCourses && constraint.priorityCourses.length > 0) {
                  specialConstraint.preferredCourses = constraint.priorityCourses.map((id) => id);
                }
                if (constraint.avoidCourses && constraint.avoidCourses.length > 0) {
                  specialConstraint.prohibitedCourses = constraint.avoidCourses.map((id) => ({
                    courseId: id,
                  }));
                }
              } else if (constraint.hasTeachingGroup) {
                specialConstraint.constraintType = 'teaching_group'; // 仅有教研组设置 (如果需要区分)
              } else {
                // 理论上不会进入此分支，因为 if 条件已包含所有情况
              }
              classConstraints.constraints.push(specialConstraint);
            }
          });
        });
      };
      // 处理上午和下午
      processConstraints(morning, 'morning');
      processConstraints(afternoon, 'afternoon', morning.length);
      exportData.classScheduleConstraints.push(classConstraints);
    });
  });

  // Message.success('约束数据导出成功');
  return exportData;
};

const convertSettingToRequestParams = (setting: ScheduleSetting): ScheduleParam => {
  // 课程信息 ✔️
  const courseDefinitions = setting.courses.map((item) => ({ id: item.id, capacity: item.capacity || null }));
  // 教师配置 ✔️
  const teacherProfiles = {
    teachers: setting.teachers.map((item) => ({
      id: item.id,
      teachingGroups: item.teachingGroups.map((group: any) => group.id),
      available_weekdays: item.availableWeekdays.length ? item.availableWeekdays : null,
    })),
    ...setting.consecutiveTeachingSettings,
    excludedTeacherIds: setting.consecutiveTeachingSettings.excludedTeacherIds || [],
  };
  // 约束条件 ✔️
  const coursePlan = handleCoursePlanExport(setting);
  // 课表总览 ✔️
  const scheduleConstraints = exportConstraintSettings(setting);
  return {
    courseDefinitions,
    teacherProfiles,
    coursePlan,
    scheduleConstraints,
    generate_debug_info: false,
    max_solve_time: 180,
    log_search: true,
  };
};

export default convertSettingToRequestParams;
