// 课程颜色配置
export const courseColors = {
  // 默认颜色配置
  default: { bg: '#f5f5f5', text: '#666666' },
  // 课程颜色映射 - 18种预设颜色
  colors: [
    { bg: '#e8f4ff', text: '#1677ff' }, // 蓝色
    { bg: '#f6ffed', text: '#52c41a' }, // 绿色
    { bg: '#fff7e6', text: '#fa8c16' }, // 橙色
    { bg: '#fff2f0', text: '#f5222d' }, // 红色
    { bg: '#e6fffb', text: '#13c2c2' }, // 青色
    { bg: '#fcffe6', text: '#a0d911' }, // 黄绿色
    { bg: '#fff0f6', text: '#eb2f96' }, // 粉色
    { bg: '#f0f5ff', text: '#2f54eb' }, // 深蓝色
    { bg: '#ffd6e7', text: '#c41d7f' }, // 玫红色
    { bg: '#f4ffb8', text: '#5b8c00' }, // 柠檬绿
    { bg: '#ffd8bf', text: '#d46b08' }, // 深橙色
    { bg: '#d9f7be', text: '#389e0d' }, // 翠绿色
    { bg: '#ffccc7', text: '#cf1322' }, // 深红色
    { bg: '#bae7ff', text: '#096dd9' }, // 天蓝色
    { bg: '#efdbff', text: '#531dab' }, // 紫色
    { bg: '#ffe7ba', text: '#d48806' }, // 金色
    { bg: '#ffd6e7', text: '#c41d7f' }, // 深粉色
    { bg: '#d9f8f6', text: '#08979c' }  // 湖蓝色
  ]
};

// 获取课程颜色的函数
export function getCourseColor(courseId, courses) {
  if (!courseId || !courses) return courseColors.default;
  
  // 找到课程在课程列表中的索引
  const courseIndex = courses.findIndex(course => course.id === courseId);
  if (courseIndex === -1) return courseColors.default;
  
  // 使用课程索引来循环使用颜色数组
  const colorIndex = courseIndex % courseColors.colors.length;
  return courseColors.colors[colorIndex];
}