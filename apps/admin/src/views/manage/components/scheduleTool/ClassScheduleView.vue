<template>
  <div class="class-schedule-view">
    <!-- 班级选择器 -->
    <div class="mb-4">
      <div class="class-buttons-container">
        <a-space wrap :size="8">
          <a-button
            v-for="classItem in state.classes"
            :key="classItem.id"
            :type="state.selectedClassId === classItem.id ? 'primary' : 'outline'"
            class="class-button"
            @click="handleClassChange(classItem.id)"
          >
            {{ classItem.name }}
          </a-button>
        </a-space>
      </div>
    </div>
    <!-- 课表内容区域 -->
    <div v-if="state.selectedClassId" class="schedule-container">
      <a-row :gutter="16">
        <!-- 左侧：班级课表 -->
        <a-col :span="16">
          <a-card title="班级课表" :bordered="false" class="schedule-card">
            <template #extra>
              <div class="flex items-center">
                <span class="text-sm text-gray-500">{{ selectedClassName }}</span>
                <a-tag class="ml-2" type="primary" size="small">
                  {{ getGradeName(selectedClassGradeId) }}
                </a-tag>
              </div>
            </template>

            <div class="class-timetable">
              <table class="schedule-table">
                <thead>
                  <tr>
                    <th class="time-col">时间</th>
                    <th v-for="day in weekdays" :key="day.value" class="day-col">{{ day.label }}</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- 上午课程 -->
                  <tr v-for="(period, index) in state.morningPeriods" :key="`morning-${index}`">
                    <td class="period-cell">
                      <div class="period-label">上午</div>
                      <div class="period-number">第{{ period }}节</div>
                    </td>
                    <td
                      v-for="(day, dayIndex) in weekdays"
                      :key="`morning-${index}-${dayIndex}`"
                      class="course-cell"
                      @dragover.prevent
                      @drop="handleDrop($event, dayIndex, 0, period)"
                    >
                      <div
                        v-if="getCourseInfo(dayIndex, 0, period)"
                        class="course-content"
                        :style="
                          (getCourseStyle(getCourseInfo(dayIndex, 0, period).courseId),
                          {
                            borderColor: getCourseStyle(getCourseInfo(dayIndex, 0, period).courseId).borderColor,
                            color: getCourseStyle(getCourseInfo(dayIndex, 0, period).courseId).color,
                            backgroundColor:
                              isConflict && [targetKey, startKey].includes(`${dayIndex}-${0}-${period}`)
                                ? '#ff0008'
                                : getCourseStyle(getCourseInfo(dayIndex, 0, period).courseId).backgroundColor,
                          })
                        "
                        :draggable="true"
                        @dragstart="handleDragStart($event, dayIndex, 0, period, getCourseInfo(dayIndex, 0, period))"
                        @dragover="handleDragOver($event, dayIndex, 0, period, getCourseInfo(dayIndex, 0, period))"
                      >
                        <div class="course-name">{{ getCourseInfo(dayIndex, 0, period).courseName }}</div>
                        <div class="teacher-name">{{ getCourseInfo(dayIndex, 0, period).teacherName }}</div>
                      </div>
                      <div v-else class="empty-cell">--</div>
                    </td>
                  </tr>

                  <!-- 下午课程 -->
                  <tr v-for="(period, index) in state.afternoonPeriods" :key="`afternoon-${index}`">
                    <td class="period-cell">
                      <div class="period-label">下午</div>
                      <div class="period-number">第{{ period }}节</div>
                    </td>
                    <td
                      v-for="(day, dayIndex) in weekdays"
                      :key="`afternoon-${index}-${dayIndex}`"
                      class="course-cell"
                      @dragover.prevent
                      @drop="handleDrop($event, dayIndex, 1, period)"
                    >
                      <div
                        v-if="getCourseInfo(dayIndex, 1, period)"
                        class="course-content"
                        :style="
                          (getCourseStyle(getCourseInfo(dayIndex, 1, period).courseId),
                          {
                            borderColor: getCourseStyle(getCourseInfo(dayIndex, 1, period).courseId).borderColor,
                            color: getCourseStyle(getCourseInfo(dayIndex, 1, period).courseId).color,
                            backgroundColor:
                              isConflict && [targetKey, startKey].includes(`${dayIndex}-${1}-${period}`)
                                ? '#ff0008'
                                : getCourseStyle(getCourseInfo(dayIndex, 1, period).courseId).backgroundColor,
                          })
                        "
                        :draggable="true"
                        @dragstart="handleDragStart($event, dayIndex, 1, period, getCourseInfo(dayIndex, 1, period))"
                        @dragover="handleDragOver($event, dayIndex, 1, period, getCourseInfo(dayIndex, 0, period))"
                      >
                        <div class="course-name">{{ getCourseInfo(dayIndex, 1, period).courseName }}</div>
                        <div class="teacher-name">{{ getCourseInfo(dayIndex, 1, period).teacherName }}</div>
                      </div>
                      <div v-else class="empty-cell">--</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </a-card>
        </a-col>

        <!-- 右侧：班级教师列表 -->
        <a-col :span="8">
          <a-card title="任课教师" :bordered="false" class="teachers-card">
            <template #extra>
              <div class="flex items-center gap-2">
                <span class="text-xs text-gray-500">共 {{ classTeachers.length }} 位教师</span>
                <a-button type="outline" size="mini" @click="toggleAllTeachers(!state.isAllExpanded)">
                  {{ state.isAllExpanded ? '收起' : '展开' }}
                </a-button>
              </div>
            </template>

            <a-empty v-if="!classTeachers.length" description="暂无教师信息" />

            <div v-else class="teachers-list">
              <div v-for="teacher in classTeachers" :key="teacher.id" class="teacher-item">
                <div class="teacher-info" @click="toggleTeacher(teacher.id)">
                  <div class="teacher-avatar">
                    <IconUser />
                  </div>
                  <div class="teacher-details">
                    <div class="teacher-name-title">
                      {{ teacher.name }}
                      <IconUp v-if="state.expandedTeachers.has(teacher.id)" />
                      <IconDown v-else />
                    </div>
                    <div class="teacher-subjects">
                      <a-tag v-for="course in teacher.courses" :key="course.id" :style="getTagStyle(course.id)">
                        {{ course.name }}
                      </a-tag>
                    </div>
                  </div>
                </div>

                <div v-show="state.expandedTeachers.has(teacher.id)" class="teacher-schedule">
                  <div class="teacher-weekly-schedule">
                    <table class="teacher-schedule-table">
                      <thead>
                        <tr>
                          <th></th>
                          <th v-for="day in weekdays" :key="day.value">{{ day.label }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <!-- 上午课程 -->
                        <template v-for="period in state.morningPeriods" :key="`morning-${period}`">
                          <tr>
                            <td v-if="period === 1" class="period-header" :rowspan="state.morningPeriods.length"
                              >上午</td
                            >
                            <td v-for="(day, dayIndex) in weekdays" :key="`morning-${period}-${dayIndex}`">
                              <template v-if="getTeacherCourseAtTime(teacher.id, dayIndex, 0, period)">
                                <a-tag
                                  :style="getTagStyle(getTeacherCourseAtTime(teacher.id, dayIndex, 0, period).courseId)"
                                >
                                  {{ getCourseName(getTeacherCourseAtTime(teacher.id, dayIndex, 0, period).courseId) }}
                                </a-tag>
                              </template>
                              <span v-else class="empty-slot">-</span>
                            </td>
                          </tr>
                        </template>

                        <!-- 下午课程 -->
                        <template v-for="period in state.afternoonPeriods" :key="`afternoon-${period}`">
                          <tr>
                            <td
                              v-if="period === state.afternoonPeriods[0]"
                              class="period-header"
                              :rowspan="state.afternoonPeriods.length"
                              >下午</td
                            >
                            <td v-for="(day, dayIndex) in weekdays" :key="`afternoon-${period}-${dayIndex}`">
                              <template v-if="getTeacherCourseAtTime(teacher.id, dayIndex, 1, period)">
                                <a-tag
                                  :style="getTagStyle(getTeacherCourseAtTime(teacher.id, dayIndex, 1, period).courseId)"
                                >
                                  {{ getCourseName(getTeacherCourseAtTime(teacher.id, dayIndex, 1, period).courseId) }}
                                </a-tag>
                              </template>
                              <span v-else class="empty-slot">-</span>
                            </td>
                          </tr>
                        </template>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 课程图例 -->
      <a-card title="课程图例" :bordered="false" class="legend-card mt-4">
        <div class="course-legend">
          <div v-for="course in state.courses" :key="course.id" class="legend-item">
            <div class="legend-color" :style="getTagStyle(course.id)">
              {{ course.name.substr(0, 1) }}
            </div>
            <div class="legend-name">{{ course.name }}</div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 未选择班级提示 -->
    <div v-else class="empty-state">
      <a-empty description="请选择班级查看课表" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref, watch } from 'vue';
  import { IconDown, IconUp, IconUser } from '@arco-design/web-vue/es/icon';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import { getCourseColor } from './config/courseColors.js';

  // 属性定义
  const props = defineProps({
    classScheduleData: {
      type: Object,
      default: () => ({}),
    },
    record: {
      type: Object,
    },
  });
  const emits = defineEmits(['updateRecord']);
  // 响应式状态
  const state = ref({
    selectedClassId: null,
    classes: [],
    grades: [],
    courses: [],
    teachers: [],
    classSchedule: {},
    teacherSchedule: {},
    isAllExpanded: false,
    expandedTeachers: new Set(),
    morningPeriods: [1, 2, 3],
    afternoonPeriods: [4, 5, 6],
  });
  const teacherSchedule = ref(props.record.teacherSchedule);

  // 星期几配置
  const weekdays = [
    { label: '周一', value: 0 },
    { label: '周二', value: 1 },
    { label: '周三', value: 2 },
    { label: '周四', value: 3 },
    { label: '周五', value: 4 },
  ];
  const getTeacherName = (id) => {
    const teacher = state.value.teachers.find((t) => t.id === id);
    return teacher ? teacher.name : '未知教师';
  };
  // 辅助函数
  const getCourseName = (id) => {
    const course = state.value.courses.find((c) => c.id === id);
    return course ? course.name : '未知课程';
  };
  // 计算属性
  const selectedClassGradeId = computed(() => {
    const selectedClass = state.value.classes.find((c) => c.id === state.value.selectedClassId);
    return selectedClass ? selectedClass.gradeId : null;
  });

  const selectedClassName = computed(() => {
    const selectedClass = state.value.classes.find((c) => c.id === state.value.selectedClassId);
    return selectedClass ? selectedClass.name : '';
  });

  const classTeachers = computed(() => {
    if (!state.value.selectedClassId || !state.value.classSchedule[state.value.selectedClassId]) {
      return [];
    }

    const classData = state.value.classSchedule[state.value.selectedClassId];
    const teacherMap = new Map();

    Object.entries(classData).forEach(([periodCode, [courseId, teacherId]]: [string, [number, number]]) => {
      if (!teacherMap.has(teacherId)) {
        teacherMap.set(teacherId, {
          id: teacherId,
          name: getTeacherName(teacherId),
          courses: new Map(),
        });
      }

      const teacher = teacherMap.get(teacherId);
      if (!teacher.courses.has(courseId)) {
        teacher.courses.set(courseId, {
          id: courseId,
          name: getCourseName(courseId),
        });
      }
    });

    return Array.from(teacherMap.values()).map((teacher) => ({
      ...teacher,
      courses: Array.from(teacher.courses.values()),
    }));
  });

  const getGradeName = (id) => {
    const grade = state.value.grades.find((g) => g.id === id);
    return grade ? grade.name : '未知年级';
  };

  const getCourseStyle = (courseId) => {
    const color = getCourseColor(courseId, state.value.courses);
    return {
      backgroundColor: color.bg,
      color: color.text,
      borderColor: `${color.text}33`,
    };
  };

  const getTagStyle = getCourseStyle;

  const getCourseInfo = (dayIndex, sessionIndex, periodNumber) => {
    if (!state.value.selectedClassId || !state.value.classSchedule[state.value.selectedClassId]) {
      return null;
    }

    const periodCode = `${dayIndex}-${sessionIndex}-${periodNumber}`;
    const classData = state.value.classSchedule[state.value.selectedClassId];

    if (!classData || !classData[periodCode]) {
      return null;
    }

    const [courseId, teacherId] = classData[periodCode];

    return {
      courseId,
      teacherId,
      courseName: getCourseName(courseId),
      teacherName: getTeacherName(teacherId),
    };
  };

  const getTeacherCourseAtTime = (teacherId, dayIndex, sessionIndex, periodNumber) => {
    if (!state.value.selectedClassId || !state.value.classSchedule[state.value.selectedClassId]) {
      return null;
    }

    const classData = state.value.classSchedule[state.value.selectedClassId];
    const periodCode = `${dayIndex}-${sessionIndex}-${periodNumber}`;

    if (!classData || !classData[periodCode]) {
      return null;
    }

    const [courseId, currentTeacherId] = classData[periodCode];
    return currentTeacherId === teacherId ? { courseId, teacherId: currentTeacherId } : null;
  };

  // 班级节次更新
  const updatePeriodsByClass = (classId) => {
    if (!classId) return;
    const classItem = state.value.classes.find((c) => c.id === classId);
    if (!classItem) return;

    const { gradeId } = classItem;
    const constraintsKey = `constraints_${gradeId}`;
    const savedSettings = localStorage.getItem(constraintsKey);

    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings);
        const periodSettings = settings.periodSettings || { morning: 3, afternoon: 3 };

        state.value.morningPeriods = Array.from({ length: periodSettings.morning }, (_, i) => i + 1);

        state.value.afternoonPeriods = Array.from(
          { length: periodSettings.afternoon },
          (_, i) => i + periodSettings.morning + 1,
        );
      } catch (error) {
        state.value.morningPeriods = [1, 2, 3];
        state.value.afternoonPeriods = [4, 5, 6];
      }
    }
  };

  // 事件处理函数
  const handleClassChange = (classId) => {
    state.value.selectedClassId = classId;
    updatePeriodsByClass(classId);
  };

  const toggleAllTeachers = (expand) => {
    state.value.isAllExpanded = expand;
    state.value.expandedTeachers = expand ? new Set(classTeachers.value.map((teacher) => teacher.id)) : new Set();
  };

  const toggleTeacher = (teacherId) => {
    if (state.value.expandedTeachers.has(teacherId)) {
      state.value.expandedTeachers.delete(teacherId);
    } else {
      state.value.expandedTeachers.add(teacherId);
    }
    state.value.isAllExpanded = classTeachers.value.every((teacher) => state.value.expandedTeachers.has(teacher.id));
  };

  // 数据加载与初始化
  const loadStorageData = () => {
    const extractUniqueGradeInfo = (data: any[]): any[] => {
      const gradeMap = new Map<number, any>();
      // eslint-disable-next-line no-restricted-syntax
      for (const item of data) {
        if (!gradeMap.has(item.gradeId)) {
          gradeMap.set(item.gradeId, { id: item.gradeId, name: item.gradeName });
        }
      }
      return Array.from(gradeMap.values());
    };
    state.value = {
      ...state.value,
      classes: props.record.classes || [],
      grades: extractUniqueGradeInfo(props.record.classes) || [],
      courses: props.record.courses || [],
      teachers: props.record.teachers || [],
      classSchedule: props.record.classSchedule || props.classScheduleData,
      teacherSchedule: props.record.teacherSchedule,
    };
  };

  // 查找第一个有课表数据的班级ID
  const findFirstClassWithSchedule = () => {
    if (!state.value.classSchedule || Object.keys(state.value.classSchedule).length === 0) {
      return null;
    }

    const classIdWithSchedule = Object.keys(state.value.classSchedule)[0];
    if (state.value.classes.some((c) => c.id === classIdWithSchedule)) {
      return classIdWithSchedule;
    }
    if (state.value.classes.length > 0) {
      return state.value.classes[0].id;
    }
    return null;
  };
  const savedData = ref();
  const handleSaveModify = async () => {
    const { data: res } = await request(`/teacher/scheduleTool/${props.record.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'put',
      data: Object.assign(props.record, {
        classSchedule: state.value.classSchedule,
        teacherSchedule: teacherSchedule.value,
      }),
    });
    savedData.value = res;
    emits('updateRecord', savedData.value);
    Message.success('保存成功');
  };
  // 课程交换校验 ---->
  const modifySchedule = ref(false);
  const startData = ref(); // [courseId,teacherId]
  const startKey = ref();
  const targetData = ref();
  const targetKey = ref();

  const isDragIng = ref(false);
  const isConflict = ref(false);

  const reset = () => {
    startData.value = [];
    startKey.value = null;
    targetData.value = [];
    targetKey.value = null;
    isDragIng.value = false;
    isConflict.value = false;
  };

  const handleDragStart = (e, dayIndex, sessionIndex, period, info) => {
    isDragIng.value = true;
    startKey.value = `${dayIndex}-${sessionIndex}-${period}`;
    startData.value = state.value.classSchedule[state.value.selectedClassId]?.[startKey.value];
    // console.log('起始坐标', startKey.value, startData.value);
  };

  const handleDrop = async (e, dayIndex, sessionIndex, period) => {
    targetKey.value = `${dayIndex}-${sessionIndex}-${period}`;
    if (isConflict.value) {
      reset();
      return false;
    }
    if (targetKey.value !== startKey.value) {
      targetData.value = state.value.classSchedule[state.value.selectedClassId]?.[targetKey.value];
      // console.log('目标位置', targetKey.value, targetData.value);
      // 交换班级课表信息 || 没问题
      state.value.classSchedule[state.value.selectedClassId][targetKey.value] = startData.value;
      state.value.classSchedule[state.value.selectedClassId][startKey.value] = targetData.value;

      const sourceOld = state.value.teacherSchedule[String(startData.value[1])][startKey.value];
      const targetOld = state.value.teacherSchedule[String(targetData.value[1])][targetKey.value];
      // 交换教师课表信息
      teacherSchedule.value[String(startData.value[1])][String(targetKey.value)] = sourceOld;
      delete teacherSchedule.value[String(startData.value[1])][String(startKey.value)];

      teacherSchedule.value[String(targetData.value[1])][String(startKey.value)] = targetOld;
      delete teacherSchedule.value[String(targetData.value[1])][String(targetKey.value)];
      modifySchedule.value = true;
      await handleSaveModify();
      reset();
    }
    return true;
  };

  // 检查除开当前位置，其它课表相同点是否有冲突
  const verifyConflict = (key, currentClassId, teacherId) => {
    let conflict = false;
    Object.entries(state.value.classSchedule).forEach(([k, v]: [string, any]) => {
      if (k !== String(currentClassId)) {
        if (v[key][1] === teacherId) {
          conflict = true;
        }
      }
    });
    return conflict;
  };

  const handleDragOver = (e, dayIndex, sessionIndex, period, info) => {
    targetKey.value = `${dayIndex}-${sessionIndex}-${period}`;
    if (startData.value[1] === state.value.classSchedule[state.value.selectedClassId][targetKey.value][1]) {
      // 相同课表检查交换是否属于同一个老师
      isConflict.value = false;
    } else if (targetKey.value !== startKey.value) {
      // 不在当前坐标悬浮
      const isSourceConflict = verifyConflict(targetKey.value, state.value.selectedClassId, startData.value[1]); // source to target is conflict
      const isTargetConflict = verifyConflict(
        startKey.value,
        state.value.selectedClassId,
        state.value.classSchedule[state.value.selectedClassId][targetKey.value][1],
      ); // target to source is conflict
      isConflict.value = isSourceConflict || isTargetConflict; // one lead to both
    }
  };

  // 课程交换校验 ---->

  // 生命周期钩子
  onMounted(() => {
    loadStorageData();

    const firstClassId = findFirstClassWithSchedule();
    if (firstClassId) {
      state.value.selectedClassId = firstClassId;
      updatePeriodsByClass(firstClassId);
    } else if (state.value.classes.length > 0) {
      state.value.selectedClassId = state.value.classes[0].id;
      updatePeriodsByClass(state.value.selectedClassId);
    }
  });

  // 监听课表数据变化
  watch(
    () => props.classScheduleData,
    (newData) => {
      if (newData && Object.keys(newData).length > 0) {
        state.value.classSchedule = newData;

        if (!state.value.selectedClassId) {
          const firstClassId = findFirstClassWithSchedule();
          if (firstClassId) {
            state.value.selectedClassId = firstClassId;
            updatePeriodsByClass(firstClassId);
          }
        }
      }
    },
    { deep: true },
  );

  watch(
    () => props.record,
    (newVal) => {
      loadStorageData();
    },
  );
</script>

<style scoped>
  .class-schedule-view {
    margin-bottom: 24px;
  }

  .schedule-container {
    margin-top: 16px;
  }

  /* 合并卡片通用样式 */
  .schedule-card,
  .teachers-card,
  .legend-card {
    height: 100%;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  /* 合并表格通用样式 */
  .schedule-table,
  .teacher-schedule-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #e5e7eb;
    table-layout: fixed;
  }

  .schedule-table th,
  .schedule-table td,
  .teacher-schedule-table th,
  .teacher-schedule-table td {
    border: 1px solid #e5e7eb;
    text-align: center;
    vertical-align: middle;
  }

  .schedule-table th,
  .teacher-schedule-table th {
    background-color: #f9fafb;
    padding: 10px;
    font-weight: 500;
    color: #374151;
  }

  .time-col {
    width: 80px;
  }

  .day-col {
    width: calc((100% - 80px) / 5);
  }

  .period-cell {
    background-color: #f3f4f6;
    padding: 8px;
  }

  .period-label {
    font-size: 12px;
    color: #6b7280;
  }

  .period-number {
    font-weight: 500;
    color: #374151;
  }

  .course-cell {
    height: 80px;
    padding: 4px;
    position: relative;
  }

  .course-content {
    position: absolute;
    inset: 4px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid;
    transition: all 0.2s;

    &:hover {
      filter: brightness(0.95);
    }
  }

  .course-name {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .teacher-name {
    font-size: 12px;
    opacity: 0.8;
  }

  .empty-cell {
    position: absolute;
    inset: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    font-size: 16px;
  }

  .empty-state {
    padding: 40px;
    background-color: #f9fafb;
    border-radius: 4px;
    margin-top: 16px;
  }

  .teachers-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .teacher-item {
    padding: 12px;
    background-color: #f9fafb;
    border-radius: 6px;
    transition: all 0.2s;

    &:hover {
      background-color: #f3f4f6;
    }
  }

  .teacher-info {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f3f4f6;
    }
  }

  .teacher-name-title {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .teacher-name-title :deep(.arco-icon) {
    font-size: 14px;
    color: #86909c;
  }

  .teacher-subjects {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }

  .teacher-schedule {
    margin-top: 8px;
    border-top: 1px dashed #e5e7eb;
    padding-top: 8px;
  }

  .teacher-schedule :deep(.arco-collapse) {
    border: none;
    background-color: transparent;
  }

  .teacher-schedule :deep(.arco-collapse-item-header) {
    background-color: transparent;
    border: none;
    padding: 4px 0;
  }

  .teacher-schedule :deep(.arco-collapse-item-content) {
    background-color: transparent;
    padding: 0 0 0 16px;
  }

  .teacher-weekly-schedule {
    width: 100%;
    overflow-x: auto;
  }

  .course-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .legend-color {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 12px;
    border: 1px solid;
  }

  .legend-name {
    font-size: 13px;
    color: #374151;
  }

  .class-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .class-button {
    min-width: 120px;
    text-align: center;
  }

  @media (max-width: 768px) {
    .course-name {
      font-size: 12px;
    }

    .teacher-name {
      font-size: 11px;
    }

    .course-cell {
      height: 60px;
    }

    .period-cell {
      padding: 6px;
    }

    .teacher-item {
      padding: 10px;
    }

    .legend-item {
      gap: 4px;
    }

    .legend-color {
      width: 20px;
      height: 20px;
      font-size: 10px;
    }

    .legend-name {
      font-size: 12px;
    }

    .teacher-schedule-table {
      font-size: 11px;
    }

    .teacher-schedule-table th,
    .teacher-schedule-table td {
      padding: 2px;
      height: 28px;
    }

    .class-button {
      min-width: 100px;
      font-size: 12px;
    }
  }
  ::-webkit-scrollbar {
    height: 1px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #fffbfb;
    border-radius: 10px;
  }

  ::-webkit-scrollbar-track {
    background-color: transparent; /* 可设置为其他背景色 */
  }

  /* 针对 Firefox 浏览器 */
  * {
    scrollbar-width: thin; /* 滚动条变细 */
    scrollbar-color: #fff9f9 transparent; /* 滚动条颜色和轨道背景色 */
  }
</style>
