<script setup lang="ts">
  import TeacherSelect from '@repo/components/org/teacherSelect.vue';
  import { computed, PropType } from 'vue';

  const props = defineProps({
    modelValue: {
      type: Array as PropType<any>,
      default: () => [] as number[],
    },
  });

  const emit = defineEmits(['update:modelValue']);

  const teachers = computed({
    get: () => props.modelValue || [],
    set: (value) => emit('update:modelValue', value),
  });

  const handleSelectTeacher = (teacherId, value) => {
    teachers.value.push({
      id: value.id,
      name: value.name,
    });
  };

  const handleRemove = (index) => {
    teachers.value.splice(index, 1);
  };
</script>

<template>
  <div>
    <teacher-select ref="teacherSelectRef" @change="handleSelectTeacher" />
    <a-table :data="teachers" size="mini" :pagination="false" class="mt-2">
      <template #columns>
        <a-table-column title="教师姓名" data-index="name" />
        <a-table-column title="操作">
          <template #cell="{ recordIndex }">
            <a-button @click="() => handleRemove(recordIndex)">删除</a-button>
          </template>
        </a-table-column>
      </template>
    </a-table>
    <small class="text-blue-500">* 教研组教师在其教研活动中不被安排上课</small>
  </div>
</template>

<style scoped lang="scss"></style>
