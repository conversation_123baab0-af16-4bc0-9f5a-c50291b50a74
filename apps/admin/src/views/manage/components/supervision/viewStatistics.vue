<script setup lang="ts">
  import { onMounted, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Column } from '@antv/g2plot';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    record: {
      type: Object,
    },
  });
  const emits = defineEmits(['update:modelValue']);
  const bodyStyle = {
    height: '100%',
    overflow: 'hidden',
  };
  const schoolOptions = ref([]);
  const school = ref();
  const record = ref({ ...props.record });

  const data = ref([]);
  const recordData = ref([]);

  let column: Column | null = null;

  const renderChart = () => {
    if (column) {
      column.destroy(); // 清理之前的图表实例
    }
    column = new Column('container', {
      data: data.value,
      xField: 'school',
      yField: 'value',
      seriesField: 'type',
      isGroup: true,
      columnStyle: {
        radius: [20, 20, 0, 0],
      },
    });

    column.render();
  };
  const selectData = (val: any) => {
    if (val != null && val !== '') data.value = recordData.value.filter((item) => item.school === val);
    else data.value = recordData.value;
    renderChart();
  };
  const getSchool = (val: any) => {
    const addedSchools = new Set<string>(); // 用于跟踪已添加的学校
    val.forEach((item: any) => {
      const result = item.school;
      if (!addedSchools.has(result)) {
        schoolOptions.value.push({
          label: result,
          value: result,
        });
        addedSchools.add(result);
      }
    });
  };
  const getData = async () => {
    try {
      const { data: res } = await request(
        `/resourceCenter/evaluationCriterionDetail/queryStatisticalResults/${record.value.id}`,
        {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'get',
        },
      );

      // 假设 res 直接是要使用的数据
      recordData.value = res;
      data.value = res;
      renderChart(); // 数据更新后渲染图表
      getSchool(res);
    } catch (error) {
      console.error('获取数据时出错:', error);
    }
  };

  onMounted(() => {
    getData(); // 组件挂载时请求数据
  });

  // 监听模态框可见性
  watch(
    () => props.visible,
    (newVal) => {
      if (newVal) {
        renderChart(); // 当模态框可见时渲染图表
      }
    },
  );
  watch(
    () => props.record,
    (newVal) => {
      record.value = { ...newVal };
    },
  );

  const resetData = () => {
    // 根据需要重置数据
  };

  const handlePreOk = () => {
    resetData();
    emits('update:modelValue', false);
  };

  const handleCancel = () => {
    resetData();
    emits('update:modelValue', false);
  };

  const handlePreOpen = () => {};
</script>

<template>
  <a-modal
    :on-before-ok="handlePreOk"
    :visible="visible"
    fullscreen
    :closable="false"
    :body-style="bodyStyle"
    @cancel="handleCancel"
    @before-open="handlePreOpen"
  >
    <div style="height: 5%">
      <span>学校</span>
      <a-select
        v-model="school"
        :options="schoolOptions"
        class="ml-4"
        allow-clear
        allow-search
        style="width: 200px"
        placeholder="请选择查看学校"
        @change="selectData"
      />
    </div>
    <div id="container" style="width: 100%; height: 95%; background-color: rgba(218, 218, 218, 0.17)"></div>
    <template #footer>
      <div class="mr-10">
        <a-button size="mini" @click="handleCancel">返回</a-button>
      </div>
    </template>
  </a-modal>
</template>

<style scoped lang="scss"></style>
