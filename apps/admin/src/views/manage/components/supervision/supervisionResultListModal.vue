<script setup lang="ts">
  import { onMounted, computed } from 'vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });
  const emits = defineEmits(['update:visible']);

  const visible = computed({
    get: () => {
      return props.visible;
    },
    set: (val) => {
      emits('update:visible', val);
    },
  });

  onMounted(() => {});
</script>

<template>
  <a-modal v-model:visible="visible" width="80%" title="数据列表" hide-cancel>
    <div class="">display some cant select dataList</div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
