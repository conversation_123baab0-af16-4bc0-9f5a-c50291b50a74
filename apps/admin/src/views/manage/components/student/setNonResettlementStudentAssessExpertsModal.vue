<script setup lang="ts">
  import { ref, PropType } from 'vue';
  import { useTeacherStore, useUserStore } from '@repo/infrastructure/store';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    ids: {
      type: Array,
      default: () => [],
    },
    row: {
      type: Object as PropType<any>,
    },
  });

  const emit = defineEmits(['ok']);
  const selectedTeacherIds = ref<any>([...(props.row?.additionalData?.assessmentExperts || [])]);

  const teachersList = ref([]);
  const teacherStore = useTeacherStore();
  const { userInfo } = useUserStore();
  const loading = ref(false);

  const handleOpen = async () => {
    const data = await teacherStore.getTeachersList();
    // selectedTeacherIds.value = [];
    teachersList.value = data?.map((item) => {
      return {
        ...item,
        label: item.name,
        value: item.id,
      };
    });
  };

  const handleSave = async () => {
    loading.value = true;
    try {
      await request({
        url: `/resourceRoom/student/setResettleAssessmentExperts`,
        method: 'PUT',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: {
          ids: props.ids,
          assessmentExperts: selectedTeacherIds.value,
        },
      });

      Message.success('设置评估专家成功');

      emit('ok');
    } finally {
      loading.value = false;
    }
  };
</script>

<template>
  <a-modal v-bind="$attrs" title="设置评估专家" :ok-loading="loading" :on-before-ok="handleSave" @open="handleOpen">
    <div v-if="ids?.length && $attrs.visible" class="flex items-center flex-col justify-center">
      <a-transfer v-model="selectedTeacherIds" show-search :data="teachersList" one-way class="flex-1">
        <template #source-title="{ countTotal }">
          <div> 专家列表 ({{ countTotal }}) </div>
        </template>
        <template #target-title="{ countTotal }">
          <div> 已选专家 ({{ countTotal }}) </div>
        </template>
      </a-transfer>
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
