<script setup lang="ts">
  import { useAllBoStore } from '@repo/infrastructure/store/common';
  import { onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { Modal } from '@arco-design/web-vue';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    student: {
      type: Object,
      required: true,
    },
  });

  const emit = defineEmits(['update:visible', 'ok']);

  const selectedBoId = ref();
  const boStore = useAllBoStore();
  const options = ref([]);
  const loading = ref(false);

  const handleResettle = async () => {
    if (!selectedBoId.value) {
      return;
    }
    const boName = options.value.find((item) => item.value === selectedBoId.value)?.label;
    Modal.confirm({
      title: '确认安置',
      content: `确认将学生【${props.student.name}】安置到【${boName}】吗？`,
      onOk: async () => {
        loading.value = true;
        try {
          await request(`/student/enrollResettlement/${props.student.id}/resettle`, {
            method: 'PUT',
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            data: {
              id: selectedBoId.value,
            },
          });
          emit('update:visible', false);
          emit('ok');
        } finally {
          loading.value = false;
        }
      },
    });
  };

  onMounted(async () => {
    options.value = await boStore.getOptions();
  });
</script>

<template>
  <a-modal v-bind="$attrs" title="修改学生信息" fullscreen :on-before-ok="handleResettle" :ok-loading="loading">
    <a-form-item label="安置学校">
      <a-select v-model="selectedBoId" allow-search :options="options" />
    </a-form-item>
  </a-modal>
</template>

<style scoped lang="scss"></style>
