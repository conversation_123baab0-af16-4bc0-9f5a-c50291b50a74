<script setup lang="ts">
  import { computed, PropType } from 'vue';

  const props = defineProps({
    modelValue: {
      type: Array as PropType<any[]>,
    },
    record: {},
  });

  const emit = defineEmits(['update:modelValue']);

  const defaultScores = [
    { score: 1, supportLevel: 5, targetLevel: '全面辅助', targetStandard: '个训' },
    { score: 2, supportLevel: 4, targetLevel: '动作辅助', targetStandard: '个训' },
    { score: 3, supportLevel: 3, targetLevel: '说明与示范', targetStandard: '个训' },
    { score: 4, supportLevel: 2, targetLevel: '监督提示', targetStandard: '个训' },
    { score: 5, supportLevel: 1, targetLevel: '通过', targetStandard: '通过' },
  ];
  const scores = computed({
    get: () => {
      if (!props.modelValue?.length) {
        emit('update:modelValue', defaultScores);
      }
      return props.modelValue;
    },
    set: (value) => {
      emit('update:modelValue', value);
    },
  });

  const handleAddRow = () => {
    const score = scores.value.length ? scores.value[scores.value.length - 1].score + 1 : 1;
    scores.value.push({
      score,
      display: `${score}分`,
    });
  };

  const handleDeleteRow = (index: number) => {
    scores.value.splice(index, 1);
  };
</script>

<template>
  <div class="flex-1">
    <a-button type="primary" size="mini" @click="handleAddRow">新增分值</a-button>
    <a-table :data="scores" row-key="score" size="mini" class="mt-2" :pagination="false">
      <template #columns>
        <a-table-column title="分值">
          <template #cell="{ record }">
            <a-input-number v-model="record.score" mode="button" :min="0" style="width: 120px" :max="100" size="mini" />
          </template>
        </a-table-column>
        <a-table-column title="支持程度">
          <template #cell="{ record }">
            <a-input-number
              v-model="record.supportLevel"
              size="mini"
              style="width: 120px"
              mode="button"
              :min="0"
              :max="100"
            />
          </template>
        </a-table-column>
        <a-table-column title="目标标准">
          <template #cell="{ record }">
            <a-input v-model="record.targetStandard" size="mini" />
          </template>
        </a-table-column>
        <a-table-column title="目标分级">
          <template #cell="{ record }">
            <a-input v-model="record.targetLevel" size="mini" />
          </template>
        </a-table-column>

        <a-table-column title="操作" :width="100">
          <template #cell="{ rowIndex }">
            <a-button type="outline" status="danger" size="mini" @click="() => handleDeleteRow(rowIndex)"
              >删除</a-button
            >
          </template>
        </a-table-column>
      </template>
    </a-table>
  </div>
</template>

<style scoped lang="scss"></style>
