<script setup lang="ts">
  import VueQrcode from '@chenfengyuan/vue-qrcode';
  import { computed } from 'vue';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    record: {
      type: Object,
      default: null,
    },
  });

  const url = computed(() => {
    return `${PROJECT_URLS.MAIN_PROJECT}/module/poster.html?uuid=${props.record.uuid}`;
  });
</script>

<template>
  <a-modal v-bind="$attrs" title="海报二维码" hide-cancel>
    <div v-if="record">
      <vue-qrcode :value="url" class="mx-auto" />

      <a-space direction="vertical" class="mx-auto w-full">
        <small>您也可以复制以下链接，使用其他二维码生成工具生成美化后的二维码：</small>
        <a-input v-model="url" size="mini" />
      </a-space>
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
