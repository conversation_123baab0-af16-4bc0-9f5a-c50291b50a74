<script setup lang="ts">
  import { computed, PropType } from 'vue';

  const props = defineProps({
    modelValue: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const emit = defineEmits(['update:modelValue']);
  const defaults = {
    marginX: 0,
    marginY: 0,
    padding: 0,
    rounded: 0,
    opacity: 100,
    backgroundColor: '#ffffff',
  };

  const commonStyle = computed({
    get: () => props.modelValue,
    set: (value) =>
      emit('update:modelValue', {
        ...defaults,
        ...value,
      }),
  });
</script>

<template>
  <a-form-item label="左右边距">
    <a-input-number v-model="commonStyle.marginX" :min="0" :step="1" :max="100" mode="button" />
  </a-form-item>
  <a-form-item label="上下边距">
    <a-input-number v-model="commonStyle.marginY" :min="0" :step="1" :max="100" mode="button" />
  </a-form-item>
  <a-form-item label="左右内边距">
    <a-input-number v-model="commonStyle.paddingX" :min="0" :step="1" :max="100" mode="button" />
  </a-form-item>
  <a-form-item label="上下内边距">
    <a-input-number v-model="commonStyle.paddingY" :min="0" :step="1" :max="100" mode="button" />
  </a-form-item>
  <a-form-item label="圆角">
    <a-space>
      <a-radio-group v-model="commonStyle.rounded" type="button" size="mini">
        <a-radio :value="6">小</a-radio>
        <a-radio :value="12">中</a-radio>
        <a-radio :value="24">大</a-radio>
      </a-radio-group>
      <a-tooltip content="背景透明度">
        <a-input-number v-model="commonStyle.opacity" :min="0" :max="100" :step="1" size="mini">
          <template #suffix>%</template>
        </a-input-number>
      </a-tooltip>
      <a-tooltip content="背景颜色">
        <a-color-picker v-model="commonStyle.backgroundColor" size="mini" />
      </a-tooltip>
    </a-space>
  </a-form-item>
</template>

<style scoped lang="scss"></style>
