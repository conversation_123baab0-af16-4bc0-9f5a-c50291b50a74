<script setup lang="ts">
  import { computed, PropType } from 'vue';
  import CommonWidgetStyle from '@/views/manage/components/system/posterDesigner/commonWidgetStyle.vue';
  import Uploader from '@repo/ui/components/upload/uploader.vue';
  import DriveFileSelectButton from '@repo/components/resource/drive/driveFileSelectButton.vue';

  const props = defineProps({
    modelValue: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const emit = defineEmits(['update:modelValue']);
  const defaults = {};

  const data = computed({
    get: () => props.modelValue,
    set: (value) => {
      emit('update:modelValue', value);
    },
  });

  const attachments = computed({
    get: () => {
      return data.value.content ? JSON.parse(data.value.content) : [];
    },
    set: (value) => {
      data.value.content = JSON.stringify(value);
    },
  });

  const handleUploaded = (items) => {
    attachments.value = [...attachments.value, ...items];
  };

  const handleSelected = (item) => {
    attachments.value = [...attachments.value, item];
  };
</script>

<template>
  <a-space direction="vertical w-64">
    <uploader v-model="attachments" sub-folder="poster/attachments" @uploaded="handleUploaded" />
    <drive-file-select-button @selected="handleSelected" />
  </a-space>
  <a-form size="mini" auto-label-width>
    <a-divider :margin="20">组件样式</a-divider>
    <common-widget-style v-model="data.style" />
    <a-space>
      <a-form-item label="文字大小">
        <a-space>
          <a-input-number v-model="data.style.fontSize" :min="12" :max="60" mode="button" />
          <a-color-picker v-model="data.style.color" size="mini" />
        </a-space>
      </a-form-item>
    </a-space>
  </a-form>
</template>

<style scoped lang="scss"></style>
