<script setup lang="ts">
  import { computed, nextTick, PropType, ref, watch } from 'vue';
  import { merge } from 'lodash';
  import UploaderButton from '@repo/ui/components/upload/uploaderButton.vue';
  import CommonWidgetStyle from '@/views/manage/components/system/posterDesigner/commonWidgetStyle.vue';

  const props = defineProps({
    modelValue: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const emit = defineEmits(['update:modelValue']);
  const imgRef = ref(null);

  const data = computed({
    get: () => props.modelValue,
    set: (value) => {
      emit(
        'update:modelValue',
        merge(
          {
            marginX: 0,
            marginY: 0,
            textAlign: 'flex-start',
          },
          value,
        ),
      );
    },
  });

  const handleUploaded = (items) => {
    data.value.content = items[0]?.url;
  };

  const handleSizeChange = () => {
    if (data.value.style.lockRatio) {
      const img = imgRef.value as HTMLImageElement;
      const { width, height } = img;
      const ratio = width / height;
      data.value.style.height = data.value.style.width / ratio;
    }
  };

  const handleImageLoad = () => {
    if (data.value.style.width && data.value.style.height) {
      return;
    }
    const img = imgRef.value as HTMLImageElement;
    const { width, height } = img;

    data.value.style.width = width;
    data.value.style.height = height;
  };
</script>

<template>
  <a-space>
    <uploader-button :multiple="false" accept="image/*" sub-folder="poster/image" @uploaded="handleUploaded" />
    <a-button size="mini" type="outline">
      <template #icon>
        <IconPlus />
      </template>
      从云盘共享中选择
    </a-button>
  </a-space>
  <a-form size="mini" auto-label-width>
    <a-divider :margin="20">组件样式</a-divider>
    <common-widget-style v-model="data.style" />
    <a-form-item label="尺寸">
      <a-space>
        <a-input-number v-model="data.style.width" :min="0" :step="1" :max="750" @change="handleSizeChange" />
        <a-input-number v-model="data.style.height" :disabled="data.style.lockRatio" :min="0" :step="1" :max="750" />
        <a-tooltip content="比例锁定">
          <a-button size="mini" @click="data.style.lockRatio = !data.style.lockRatio">
            <template #icon>
              <IconLock v-if="data.style.lockRatio" />
              <IconUnlock v-else />
            </template>
          </a-button>
        </a-tooltip>
      </a-space>
    </a-form-item>
    <a-form-item label="对齐">
      <a-radio-group v-model="data.style.textAlign" type="button">
        <a-radio value="flex-start"><IconAlignLeft /> 左对齐 </a-radio>
        <a-radio value="center"><IconAlignCenter /> 居中</a-radio>
        <a-radio value="flex-end"><IconAlignRight /> 右对齐</a-radio>
      </a-radio-group>
    </a-form-item>
    <img v-if="data.content" ref="imgRef" :src="data.content" style="display: none" @load="handleImageLoad" />
  </a-form>
</template>

<style scoped lang="scss"></style>
