<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import { merge } from 'lodash';
  import Uploader from '@repo/ui/components/upload/uploader.vue';
  import CommonWidgetStyle from '@/views/manage/components/system/posterDesigner/commonWidgetStyle.vue';
  import DriveFileSelectButton from '@repo/components/resource/drive/driveFileSelectButton.vue';

  const props = defineProps({
    modelValue: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const emit = defineEmits(['update:modelValue']);
  const imgRef = ref(null);

  const data = computed({
    get: () => props.modelValue,
    set: (value) => {
      emit(
        'update:modelValue',
        merge(
          {
            marginX: 0,
            marginY: 0,
            textAlign: 'flex-start',
          },
          value,
        ),
      );
    },
  });

  const handleUploaded = (items) => {
    data.value.content = items[0]?.url;
  };

  const handleSizeChange = () => {
    if (data.value.style.lockRatio) {
      const img = imgRef.value as HTMLImageElement;
      const { width, height } = img;
      const ratio = width / height;
      data.value.style.height = data.value.style.width / ratio;
    }
  };

  const handleImageLoad = () => {
    if (data.value.style.width && data.value.style.height) {
      return;
    }
    const img = imgRef.value as HTMLImageElement;
    const { width, height } = img;

    data.value.style.width = width;
    data.value.style.height = height;
  };

  const handleSelected = (item) => {
    data.value.content = item.url;
  };
</script>

<template>
  <a-space direction="vertical" class="w-64">
    <uploader :multiple="false" accept="video/*" sub-folder="poster/video" @uploaded="handleUploaded" />
    <drive-file-select-button @selected="handleSelected" />
  </a-space>
  <a-form size="mini" auto-label-width>
    <a-divider :margin="20">组件样式</a-divider>
    <common-widget-style v-model="data.style" />
    <a-form-item label="尺寸">
      <a-space>
        <a-input-number v-model="data.style.width" :min="0" :step="1" :max="750" @change="handleSizeChange" />
        <a-input-number v-model="data.style.height" :disabled="data.style.lockRatio" :min="0" :step="1" :max="750" />
        <a-tooltip content="比例锁定">
          <a-button size="mini" @click="data.style.lockRatio = !data.style.lockRatio">
            <template #icon>
              <IconLock v-if="data.style.lockRatio" />
              <IconUnlock v-else />
            </template>
          </a-button>
        </a-tooltip>
      </a-space>
    </a-form-item>
    <a-form-item label="对齐">
      <a-radio-group v-model="data.style.textAlign" type="button">
        <a-radio value="flex-start"><IconAlignLeft /> 左对齐 </a-radio>
        <a-radio value="center"><IconAlignCenter /> 居中</a-radio>
        <a-radio value="flex-end"><IconAlignRight /> 右对齐</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-divider :margin="10">视频标题</a-divider>

    <a-form-item label="视频标题">
      <a-textarea v-model="data.style.subject" />
    </a-form-item>
    <a-space>
      <a-form-item label="文字大小">
        <a-space>
          <a-input-number v-model="data.style.fontSize" :min="12" :max="60" mode="button" />
          <a-color-picker v-model="data.style.color" size="mini" />
        </a-space>
      </a-form-item>
    </a-space>
    <a-form-item label="对齐">
      <a-radio-group v-model="data.style.textAlign" type="button">
        <a-radio value="left"><IconAlignLeft /> 左对齐 </a-radio>
        <a-radio value="center"><IconAlignCenter /> 居中</a-radio>
        <a-radio value="right"><IconAlignRight /> 右对齐</a-radio>
      </a-radio-group>
    </a-form-item>
    <img v-if="data.content" ref="imgRef" :src="data.content" style="display: none" @load="handleImageLoad" />
  </a-form>
</template>

<style scoped lang="scss"></style>
