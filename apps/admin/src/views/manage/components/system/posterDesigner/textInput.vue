<script setup lang="ts">
  import { computed, PropType } from 'vue';
  import CommonWidgetStyle from '@/views/manage/components/system/posterDesigner/commonWidgetStyle.vue';
  import { merge } from 'lodash';

  const props = defineProps({
    modelValue: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const emit = defineEmits(['update:modelValue']);
  const defaults = {
    fontSize: 14,
    color: '#000000',
  };

  const data = computed({
    get: () => props.modelValue,
    set: (value) => {
      emit(
        'update:modelValue',
        merge(
          {
            style: {
              ...defaults,
            },
          },
          value,
        ),
      );
    },
  });
</script>

<template>
  <a-textarea v-model="data.content" placeholder="请输入文字内容" />
  <a-form size="mini" auto-label-width>
    <a-divider :margin="20">组件样式</a-divider>
    <common-widget-style v-model="data.style" />
    <a-space>
      <a-form-item label="文字大小">
        <a-space>
          <a-input-number v-model="data.style.fontSize" :min="12" :max="60" mode="button" />
          <a-color-picker v-model="data.style.color" size="mini" />
        </a-space>
      </a-form-item>
    </a-space>
    <a-form-item label="对齐">
      <a-radio-group v-model="data.style.textAlign" type="button">
        <a-radio value="left"><IconAlignLeft /> 左对齐 </a-radio>
        <a-radio value="center"><IconAlignCenter /> 居中</a-radio>
        <a-radio value="right"><IconAlignRight /> 右对齐</a-radio>
      </a-radio-group>
    </a-form-item>
  </a-form>
</template>

<style scoped lang="scss"></style>
