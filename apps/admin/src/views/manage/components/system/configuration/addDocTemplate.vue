<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import Uploader from '@repo/ui/components/upload/uploader.vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  // import UploaderModal from '@repo/ui/components/upload/uploaderModal.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: '',
    },
    typeOptions: { type: Array },
    record: { type: Object },
  });
  const emits = defineEmits(['update:modelValue']);
  const uploadedList = ref<any>([]);
  const formData = ref<any>({
    type: props.type,
    onlineDoc: true,
    attachment: {},
  });

  const resetData = () => {
    formData.value = {
      type: '',
      onlineDoc: true,
      attachment: {},
    };
  };
  const handleChange = (val) => {
    if (!val) {
      uploadedList.value = [];
      formData.value.attachment = {};
    }
  };
  const handleUpload = (val) => {
    // eslint-disable-next-line prefer-destructuring
    formData.value.attachment = {
      name: uploadedList.value[0].name,
      udf1: uploadedList.value[0].url,
      id: uploadedList.value[0].id,
    };
  };
  const handleCancel = () => {
    resetData();
    emits('update:modelValue', false);
  };
  const handlePreOk = async () => {
    if (uploadedList.value.length > 1) {
      Message.warning('只能上传一个模板');
      return;
    }
    handleUpload();

    if (!formData.value.type || !formData.value.name || !formData.value.attachment?.udf1) {
      Message.warning('请完善信息后提交');
      return;
    }
    await request('/document/docTemplate/save', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'post',
      data: formData.value,
    }).then(() => {
      resetData();
      emits('update:modelValue', false);
    });
  };
  const handleUploads = (options: any) => {
    if (!options?.fileItem?.file) return;
    const formDataSet = new FormData();
    formDataSet.set('type', 'Wps');
    formDataSet.set('file', options.fileItem.file);
    formDataSet.set('fileName', options.fileItem.name);
    try {
      request('/common/uploadedResource/uploadAndGetDirectUrl', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'POST',
        data: formDataSet,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }).then((res) => {
        uploadedList.value.push({
          name: res.data.name,
          url: res.data.directVisitUrl,
          id: res.data.id,
        });
      });
    } finally {
      /**/
    }
  };
  onMounted(() => {
    if (props.record) {
      formData.value = props.record;
      if (props.record.onlineDoc) {
        uploadedList.value.push({
          name: props.record.attachment.name,
          url: props.record.attachment.udf1,
          id: props.record.attachment?.id,
        });
      }
    }
  });
</script>

<template>
  <a-modal
    :visible="visible"
    :on-before-ok="handlePreOk"
    :closable="false"
    title="新增送教模版"
    width="500px"
    class="overflow-hidden"
    @cancel="handleCancel"
  >
    <a-form auto-label-width>
      <a-row class="flex justify-between">
        <a-col :span="10">
          <a-form-item label="模版类型" required>
            <a-select v-model="formData.type" :disabled="record === null" :options="typeOptions" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="模版名称" required>
            <a-input v-model="formData.name" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row class="flex justify-between">
        <a-col :span="10">
          <a-form-item label="排序">
            <a-input-number v-model="formData.sort" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注">
            <a-input v-model="formData.remark" placeholder="填写备注" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item v-if="false" label="在线编辑" required>
        <a-switch v-model="formData.onlineDoc" :disabled="true" @change="handleChange" />
      </a-form-item>
    </a-form>
    <uploader v-if="false" v-model="uploadedList" sub-folder="" @update:model-value="handleUpload" />
    <a-upload v-if="true" :multiple="false" draggable :custom-request="handleUploads as any" />
  </a-modal>
</template>

<style scoped lang="scss"></style>
