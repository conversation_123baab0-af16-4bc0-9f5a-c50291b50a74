<script setup lang="ts">
  /**
   * Register("SMS_269210501"),
   *     Login("SMS_269210501"),
   *     UserVerify("SMS_269210501"),
   *     Exception("SMS_269210501"),
   *     ChangePassword("SMS_269210501"),
   *     InfoChanged("SMS_269210501"),
   *     AccountVerified("SMS_463598513"),
   *     PaperCompetitionAward("SMS_474180229")
   */
  import { computed, ref, watch } from 'vue';

  const props = defineProps({
    modelValue: {
      type: String,
      required: true,
    },
  });

  const emit = defineEmits(['update:modelValue']);

  const templatesType = {
    Register: '注册验证码',
    Login: '登录验证码',
    UserVerify: '用户验证',
    Exception: '异常通知',
    ChangePassword: '修改密码验证码',
    InfoChanged: '信息变更验证码',
    AccountVerified: '账号验证验证码',
    PaperCompetitionAward: '论文竞赛奖励',
    StudentResettlementAssess: '学生安置测评通知',
  };

  const templatesData = computed(() => {
    return Object.keys(templatesType).map((key) => ({
      key,
    }));
  });

  const formData = ref({});

  watch(
    () => props.modelValue,
    (val) => {
      formData.value = JSON.parse(val || '{}');
    },
  );

  const handleTemplateChange = (k, v) => {
    formData.value = {
      ...formData.value,
      [k]: v,
    };
    emit('update:modelValue', JSON.stringify(formData.value));
  };
</script>

<template>
  <a-table size="mini" :data="templatesData" :pagination="false">
    <template #columns>
      <a-table-column title="#">
        <template #cell="{ rowIndex }">
          {{ rowIndex + 1 }}
        </template>
      </a-table-column>
      <a-table-column title="模板类型">
        <template #cell="{ record }">
          {{ templatesType[record.key] }}
        </template>
      </a-table-column>
      <a-table-column title="模板ID" :width="300">
        <template #cell="{ record }">
          <a-input
            :model-value="formData[record.key]"
            placeholder="请输入短信服务商提供的短信模板ID"
            size="mini"
            @update:model-value="(val) => handleTemplateChange(record.key, val)"
          />
        </template>
      </a-table-column>
    </template>
  </a-table>
</template>

<style scoped lang="scss"></style>
