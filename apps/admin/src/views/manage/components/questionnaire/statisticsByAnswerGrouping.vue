<script setup lang="ts">
  import { presetTypesMap } from '@repo/components/questionLibrary/presets.ts';
  import { statisticsByAnswerGrouping } from '@repo/infrastructure/openapi/questionnaireRecordController';
  import { onMounted, ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import QuestionnaireFormAttachmentAnswerModal from '@/views/manage/components/questionnaire/questionnaireFormAttachmentAnswerModal.vue';
  import QuestionnaireFormShortAnswerModal from '@/views/manage/components/questionnaire/questionnaireFormShortAnswerModal.vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';

  const props = defineProps({
    form: {
      type: Object,
      required: true,
    },
  });

  const statistics = ref<any>({});
  const currentQuestionId = ref<number>(0);

  const loadStatisticsByAnswerGrouping = async () => {
    const { data } = await statisticsByAnswerGrouping({
      formId: props.form.id,
    });

    statistics.value = data;
  };

  const getOptionRowClass = (record) => {
    if (record.isCorrect) {
      return 'correct-option';
    }
    return '';
  };

  const getFillBlankOptions = (statisticsData: any, q, opt) => {
    // {content: count}
    const raw = statisticsData?.blankAnswers || {};
    let total = 0;
    const result = Object.keys(raw[opt.sequence] || {}).map((key) => {
      total += raw[opt.sequence][key];
      return { content: key, count: raw[opt.sequence][key] };
    });

    return result.map((r: any) => {
      r.percent = Number((r.count / total).toFixed(2));
      return r;
    });
  };

  const shortAnswerListVisible = ref(false);
  const handleShowShortAnswerList = (question: any) => {
    shortAnswerListVisible.value = true;
    currentQuestionId.value = question.id;
  };

  const attachmentAnswerVisible = ref(false);
  const handleShowAttachmentAnswerList = async (question: any) => {
    if (!statistics.value[question.id]?.answers?.attachment) {
      return;
    }
    if (question.visible) {
      question.visible = false;
      return;
    }
    attachmentAnswerVisible.value = true;
    question.visible = true;
    question.loading = true;
    currentQuestionId.value = question.id;

    try {
      const { data } = await request('/questionnaire/record', {
        baseURL: PROJECT_URLS.GO_PROJECT_API,
        params: {
          pageSize: 999,
          submitted: true,
          formId: props.form.id,
        },
      });

      question.details = data.list
        .map((item: any) => {
          return {
            id: item.id,
            user: item.createdBy,
            attachments: JSON.parse(item.answers[question.id] || '[]'),
          };
        })
        ?.filter((item) => item.attachments?.length);
    } finally {
      question.loading = false;
    }
  };

  onMounted(async () => {
    await loadStatisticsByAnswerGrouping();
  });
</script>

<template>
  <div v-if="form?.questions?.length" class="mx-auto question-wrapper">
    <div v-for="(q, i) in form.questions" :key="i" class="mb-2">
      <div class="flex justify-between items-start mb-2">
        <div>
          <strong>第 {{ i + 1 }} 题：</strong>
          <span v-html="q.question" />
        </div>
        <small class="text-gray-400 w-32 text-right">{{
          q.questionType?.name || presetTypesMap[q.presetType]?.name || q.presetType
        }}</small>
      </div>
      <div v-if="q.presetType === 'fill'">
        <div v-for="(opt, optIdx) in q.options" :key="optIdx" class="pl-4 mt-2">
          <div>第{{ opt.sequence }}空 正确答案： {{ opt.content }}</div>
          <a-table
            :data="getFillBlankOptions(statistics[q.id], q, opt)"
            :pagination="false"
            size="mini"
            :bordered="{ cell: true }"
          >
            <template #columns>
              <a-table-column title="答案" data-index="content" />
              <a-table-column title="小计" data-index="count" :width="70" />
              <a-table-column title="占比" :width="140">
                <template #cell="{ record }">
                  <a-progress :percent="record.percent" />
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>
      </div>
      <div v-else-if="q.presetType === 'textInput'" class="ml-4">
        <div class="p-4 bg-slate-50 border border-slate-200" v-html="q.correctAnswer"> </div>
        <a-button size="mini" class="mt-2" @click="() => handleShowShortAnswerList(q)"
          >查看全部 {{ statistics[q.id]?.totalAnswerCount }} 个回答</a-button
        >
      </div>
      <div v-else-if="q.presetType === 'attachmentInput'" class="ml-4">
        <a-button size="mini" class="mt-2" @click="() => handleShowAttachmentAnswerList(q)"
          >{{ q.visible ? '收起' : '查看' }}全部 {{ statistics[q.id]?.answers?.attachment || 0 }} 个回答</a-button
        >
        <div v-if="q.visible" class="w-full mt-2">
          <a-table :loading="q.loading" size="mini" :data="q.details" :pagination="false">
            <template #columns>
              <a-table-column title="姓名" data-index="user">
                <template #cell="{ record }">
                  {{ record.user?.name }}
                </template>
              </a-table-column>
              <a-table-column title="附件">
                <template #cell="{ record }">
                  <attachments-preview-display :raw="record.attachments" />
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>
      </div>
      <div v-else>
        <a-table
          :summary="true"
          size="mini"
          :data="q.options"
          :pagination="false"
          :bordered="{ cell: true }"
          :row-class="getOptionRowClass"
        >
          <template #columns>
            <a-table-column title="序号" data-index="sequence" :width="70" />
            <a-table-column title="选项" data-index="content">
              <template #cell="{ record }">
                <div v-html="record.content"></div>
              </template>
            </a-table-column>
            <a-table-column title="小计" :width="70" data-index="count">
              <template #cell="{ record }">
                {{ statistics[q.id]?.answers[record.content] || 0 }}
              </template>
            </a-table-column>
            <a-table-column title="占比" :width="140">
              <template #cell="{ record }">
                <div v-if="statistics[q.id]?.totalAnswerCount > 0">
                  <a-progress
                    :percent="
                      Number(
                        (statistics[q.id]?.answers[record.content] / statistics[q.id].totalAnswerCount || 0).toFixed(2),
                      )
                    "
                  />
                </div>
              </template>
            </a-table-column>
          </template>

          <template #summary-cell="{ column }">
            <div v-if="column.dataIndex === 'sequence'">合计</div>
            <div v-else-if="column.dataIndex === 'count'">{{ statistics[q.id]?.totalAnswerCount }}</div>
          </template>
        </a-table>
      </div>
      <a-divider v-if="i !== form.questions.length - 1" :margin="10" />
    </div>

    <!--    <questionnaire-form-attachment-answer-modal-->
    <!--      v-model:visible="attachmentAnswerVisible"-->
    <!--      :form-id="form.id"-->
    <!--      :question-id="currentQuestionId"-->
    <!--    />-->
    <questionnaire-form-short-answer-modal
      v-model:visible="shortAnswerListVisible"
      :form-id="form.id"
      :question-id="currentQuestionId"
    />
  </div>
</template>

<style scoped lang="scss">
  .question-wrapper {
    width: 700px;
  }

  :deep .correct-option td {
    background-color: #f0f9eb;
  }
</style>
