<script setup lang="ts">
  import { getQuestionnaireRecordTopList } from '@repo/infrastructure/openapi/questionnaireRecordController';
  import { onMounted, ref } from 'vue';
  import { secondsFormatter } from '@repo/ui/components/utils/utils';

  const props = defineProps({
    form: {
      type: Object,
      required: true,
    },
  });

  const topList = ref<any[]>([]);

  const loadTopList = async () => {
    const { data } = await getQuestionnaireRecordTopList({
      formId: props.form.id,
      top: 20,
    });

    topList.value = data;
  };

  onMounted(async () => {
    await loadTopList();
  });
</script>

<template>
  <div class="wrapper mx-auto">
    <a-table :data="topList" size="small" :bordered="{ cell: true }" :pagination="false">
      <template #columns>
        <a-table-column title="排名">
          <template #cell="{ rowIndex }"> 第{{ rowIndex + 1 }}名 </template>
        </a-table-column>
        <a-table-column title="姓名" data-index="user"> </a-table-column>
        <a-table-column title="用时">
          <template #cell="{ record }">
            {{ secondsFormatter(record.timeSpend) }}
          </template>
        </a-table-column>
        <a-table-column title="得分" data-index="totalScore"> </a-table-column>
        <a-table-column title="来源" data-index="source"> </a-table-column>
      </template>
    </a-table>
  </div>
</template>

<style scoped lang="scss">
  .wrapper {
    width: 800px;
  }
</style>
