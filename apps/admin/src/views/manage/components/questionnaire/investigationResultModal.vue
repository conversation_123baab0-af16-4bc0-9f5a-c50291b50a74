<script setup lang="ts">
  import StatisticsByAnswerGrouping from '@/views/manage/components/questionnaire/statisticsByAnswerGrouping.vue';

  const props = defineProps({
    form: {
      type: Object,
      required: true,
    },
  });

  const handleOpen = async () => {};
</script>

<template>
  <a-modal v-bind="$attrs" fullscreen :title="form?.name" @open="handleOpen">
    <statistics-by-answer-grouping v-if="$attrs.visible" :form="form" />
  </a-modal>
</template>
