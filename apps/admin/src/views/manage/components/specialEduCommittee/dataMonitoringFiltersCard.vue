<script setup lang="ts">
  import { randomColor, randomDarkColor } from '@repo/ui/components/utils/randomColor';
  import { PropType, ref } from 'vue';
  import { getPeriodsList } from '@repo/infrastructure/utils/scaffolds';

  const props = defineProps({
    modules: {
      type: Object,
      required: true,
    },
    data: {
      type: Object as PropType<any>,
    },
    params: {
      type: Object as PropType<any>,
    },
  });
  const emits = defineEmits(['handler', 'handleSearch']);
  const filters = ref({ ...props.params });
  const handleMiniCardClick = (item: any, child: any) => {
    emits('handler', item, child);
  };

  const handleSearch = () => {
    emits('handleSearch', filters.value);
  };
  const periodsList = getPeriodsList({
    from: 2023,
    wholeIsNeeded: false,
  });
</script>

<template>
  <div class="w-full" :style="{ backgroundColor: randomColor(-1) }">
    <div class="w-full h-[50px] flex mb-2 justify-start items-center px-10 bg-white rounded space-x-2">
      <a-select
        v-model="filters.period"
        placeholder="请选择学期"
        :options="periodsList || []"
        class="w-[200px]"
        size="mini"
      />
      <a-button size="mini" type="outline" @click="handleSearch">
        <icon-search />
        查询
      </a-button>
    </div>
    <div class="flex justify-around">
      <div
        v-for="(item, index) in modules"
        :key="index"
        class="w-1/4 rounded-lg bg-white p-2 shadow h-[120px] m-2 flex flex-col justify-between"
      >
        <div class="font-bold text-gray-500">{{ item.label }}</div>
        <div class="flex justify-start items-center mb-4">
          <div v-for="(child, childIndex) in item.children" :key="child.key" class="flex items-center cursor-pointer">
            <div
              class="flex flex-col items-center hover:bg-gray-50 p-2 rounded"
              @click="handleMiniCardClick(item, child)"
            >
              <div
                :style="{ color: child.color ? randomColor(child.color) : randomDarkColor() }"
                class="font-bold text-2xl"
              >
                {{ data?.[item.key]?.[child.key] || 0 }}
              </div>
              <div class="text-gray-400 text-xs">{{ child.label }}</div>
            </div>
            <div v-if="childIndex < item.children.length - 1" class="mx-3 h-4 border-l-2 border-gray-300"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
