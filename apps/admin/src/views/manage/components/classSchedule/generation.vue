<script setup lang="ts">
  import { computed, nextTick, onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import PresetLessonSchedule from '@/views/manage/components/classSchedule/presetLessonSchedule.vue';
  import { Message, Modal } from '@arco-design/web-vue';
  import ArrangeResult from '@/views/manage/components/classSchedule/arrangeResult.vue';
  import TeacherLessonSchedule from '@repo/components/teacher/teacherLessonSchedule.vue';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { useRoute } from 'vue-router';
  import AdministrationClass from '@/views/manage/components/classSchedule/administrationClass.vue';

  const props = defineProps({
    task: {
      type: Object,
      required: true,
    },
    teachersMap: {
      type: Object,
      required: true,
    },
    lessonsMap: {
      type: Object,
      required: true,
    },
    teachingConfigs: {
      type: Array,
      required: true,
    },
  });

  const emit = defineEmits(['update:task', 'save']);
  const route = useRoute();

  const computedTask = computed({
    get: () => props.task,
    set: (value) => emit('update:task', value),
  });

  const gradeClassesTree = ref<any[]>([]);
  const gradeClassesMap = ref<any>({});
  const ready = ref(false);
  const currentGradeClass = ref<any>(null);
  const splitSize = ref(280);
  const arrangementResult = ref<any>(null);
  const personSchedules = ref<any>({});
  const weekdays = ['一', '二', '三', '四', '五'];
  const teacherScheduleVisible = ref(false);
  const currentTeacherId = ref(null);
  const courseAssignments = ref<any>({});
  const menuStore = useUserMenuStore();
  const orgNature = menuStore.getCurrentMenuInfo(route)?.app?.label;

  const currentGradeCondition = computed(() => {
    if (!currentGradeClass.value?.id) {
      return null;
    }
    return computedTask.value.gradeConditions?.find((item) => {
      return item.gradeId === currentGradeClass.value?.grade?.id;
    });
  });

  const totalDayPeriods = computed(() => {
    if (!currentGradeCondition.value) {
      return 0;
    }
    return currentGradeCondition.value.morningCount + currentGradeCondition.value.afternoonCount;
  });

  const handleGradeClassSelected = (selectedKeys: string[], info: any) => {
    if (info.node.type === 'gradeClass') {
      currentGradeClass.value = info.node.id;
    }
  };

  const handleResultFormatted = (data) => {
    personSchedules.value = {};
    const result: any = {};
    data.forEach((item: any) => {
      result[item.gradeClassId] = result[item.gradeClassId] || {};
      const dayPeriodIndex =
        item.dayPeriod === 0 ? item.classIndex : currentGradeCondition.value.morningCount + item.classIndex;
      result[item.gradeClassId][dayPeriodIndex] = result[item.gradeClassId][dayPeriodIndex] || {};
      result[item.gradeClassId][dayPeriodIndex][item.weekdayIndex] = item;

      personSchedules.value[item.gradeClassId] = personSchedules.value[item.gradeClassId] || [];
      personSchedules.value[item.gradeClassId].push(item);
    });

    arrangementResult.value = result;
  };

  const handlePreview = async () => {
    const modal = Modal.info({
      title: '提示',
      content: '系统正在自动排课... 请稍后',
      closable: false,
      maskClosable: false,
      okLoading: true,
      escToClose: false,
    });

    try {
      const { data } = await request(`/teacher/lessonSchedule/generateSchedule/${computedTask.value.id}`, {
        method: 'POST',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });

      courseAssignments.value = data;
      handleResultFormatted(data);
      Message.success('自动排课成功，请点击班级查看排课详情');
    } finally {
      modal.close();
    }
  };

  /**
   * 获取教师的课表
   * {teacherId: [[1,2,3,4,5]]}
   */
  const getTeacherSchedule = computed(() => (gradeClassId) => {
    const result = {};
    personSchedules.value[gradeClassId]?.forEach((lesson: any) => {
      const dayPeriodIndex =
        lesson.dayPeriod === 0 ? lesson.classIndex : currentGradeCondition.value.morningCount + lesson.classIndex;
      result[lesson.teacherId] = result[lesson.teacherId] || [];
      if (!result[lesson.teacherId].length) {
        for (let i = 0; i < totalDayPeriods.value; i += 1) {
          result[lesson.teacherId].push([]);
        }
      }
      result[lesson.teacherId][dayPeriodIndex] = result[lesson.teacherId][dayPeriodIndex] || [];
      result[lesson.teacherId][dayPeriodIndex][lesson.weekdayIndex] =
        result[lesson.teacherId][dayPeriodIndex][lesson.weekdayIndex] || lesson;
    });

    return result;
  });

  const loadAssignments = async () => {
    const { data } = await request('/teacher/courseAssignment', {
      params: {
        pageSize: 9999,
        taskId: computedTask.value.id,
      },
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    courseAssignments.value = data.items || [];
    if (data.items?.length) {
      handleResultFormatted(data.items);
    }
  };

  const handleSave = async () => {
    const modal = Modal.info({
      title: '提示',
      content: '系统正在保存排课结果... 请稍后',
      closable: false,
      maskClosable: false,
      okLoading: true,
    });

    const result = Object.values(arrangementResult.value).reduce((acc: any, item) => {
      return acc.concat(Object.values(item).reduce((acc2, item2) => acc2.concat(Object.values(item2)), []));
    }, []);

    try {
      await request('/teacher/courseAssignment/save', {
        method: 'POST',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: {
          taskId: computedTask.value.id,
          data: result,
        },
      });
      Message.success('保存成功');
    } catch (error) {
      Message.error('保存失败');
    } finally {
      modal.close();
    }
  };

  onMounted(async () => {
    const { data: gradesRaw } = await request('/teacher/schoolGrade', {
      params: {
        pageSize: 999,
        graduate: false,
        orgNature,
      },
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    const { data: classesRaw } = await request('/resourceRoom/gradeClass', {
      params: {
        pageSize: 999,
        orgNature,
      },
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    gradeClassesMap.value = classesRaw.items.reduce((acc: any, item: any) => {
      acc[item.id] = item;
      return acc;
    }, {});

    const classesMap = classesRaw.items.reduce((acc: any, item: any) => {
      if (!acc[item.grade?.id]) {
        acc[item.grade?.id] = [];
      }
      acc[item.grade?.id].push({
        id: item,
        key: `grade-class-${item.id}`,
        name: item.name,
        title: item.className,
        type: 'gradeClass',
        gradeId: item.grade?.id,
      });
      return acc;
    }, {});

    gradeClassesTree.value = gradesRaw.items.map((grade: any) => {
      return {
        id: grade.id,
        key: `grade-${grade.id}`,
        title: grade.name,
        children: classesMap[grade.id] || [],
        type: 'grade',
      };
    });

    ready.value = true;

    currentGradeClass.value = gradeClassesTree.value[0]?.children[0]?.id;

    if (currentGradeClass.value) {
      await loadAssignments();
    }
  });

  const handleManualAdjust = async () => {
    await loadAssignments();
  };

  const handlePushToTable = () => {
    Modal.confirm({
      title: `${props.task.period} 课表推送确认`,
      content: '确定要将当前排课结果推送至课表吗？（本学期所有教师课表将被覆盖）',
      onOk: async () => {
        await handleSave();
        Message.info('正在推送课表，请稍后');
        await request(`/teacher/timetable/convertFromSchedule`, {
          method: 'POST',
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          params: {
            scheduleId: computedTask.value.id,
          },
        });

        Message.clear();
        Modal.info({
          title: '提示',
          content: '课表已推送，如需使用请前往课表管理启用',
        });
      },
    });
  };
</script>

<template>
  <div v-if="ready" class="m-2">
    <div class="flex justify-between items-center">
      <div class="flex gap-2 items-center">
        <a-button type="outline" size="mini" :disabled="true" @click="handlePreview">
          <template #icon>
            <IconEye />
          </template>
          自动排课预览
        </a-button>
        <a-popconfirm
          class="w-64"
          content="确定要保存当前排课结果吗？（原排课结果将被覆盖，但不影响课表）"
          @ok="handleSave"
        >
          <a-button type="outline" size="mini">
            <template #icon>
              <IconSave />
            </template>
            保存当前排课结果
          </a-button>
        </a-popconfirm>
      </div>

      <a-button type="primary" size="mini" @click="handlePushToTable">
        <template #icon>
          <IconShareExternal />
        </template>
        推送至课表
      </a-button>
    </div>
    <a-split v-model:size="splitSize" class="mt-2 border-t border-slate-200 pt-2 split-container">
      <template #first>
        <a-tree
          :data="gradeClassesTree"
          show-line
          :selectable="true"
          :default-selected-keys="[gradeClassesTree[0]?.children[0]?.id]"
          @select="handleGradeClassSelected"
        >
          <template #title="node">
            {{ node.name || node.title }}
          </template>
        </a-tree>
      </template>
      <template #second>
        <a-empty v-if="!currentGradeClass || !currentGradeCondition" content="在左侧选择班级以继续"></a-empty>
        <div v-else class="flex gap-2">
          <div v-if="teachingConfigs?.length && arrangementResult" class="flex-1 border-r border-slate-200 px-2">
            <arrange-result
              :teachers-map="teachersMap"
              :lessons-map="lessonsMap"
              :task="computedTask"
              :grade-class="currentGradeClass"
              :arrange-result="arrangementResult"
              :grade-condition="currentGradeCondition"
              :teaching-configs="teachingConfigs"
              mode="generation"
              @manual-adjustment="handleManualAdjust"
            />
          </div>
          <a-card v-if="currentGradeClass?.id" class="w-96" :bordered="false" :body-style="{ padding: '10px' }">
            <template #title> 行政班视角 </template>
            <a-empty v-if="!getTeacherSchedule(currentGradeClass.id)" content="暂无排课结果"></a-empty>
            <div class="grid grid-cols-2 gap-2 mt-2">
              <administration-class
                v-for="(teacherSchedule, idx) in getTeacherSchedule(currentGradeClass.id)"
                :key="idx"
                :teacher-schedule="teacherSchedule"
                :teachers-map="teachersMap"
                :weekdays="weekdays"
                :lessons-map="lessonsMap"
                :course-assignments="courseAssignments"
                :grade-classes-map="gradeClassesMap"
                :task="computedTask"
                :idx="idx"
                :total-day-periods="totalDayPeriods"
              />
            </div>
          </a-card>
        </div>
      </template>
    </a-split>
  </div>
  <a-skeleton v-else animation>
    <a-skeleton-line :rows="5" />
  </a-skeleton>
  <a-modal
    v-model:visible="teacherScheduleVisible"
    title="教师课程安排"
    :width="800"
    hide-cancel
    @close="handleHideTeacherSchedule"
  >
    <teacher-lesson-schedule
      v-if="currentTeacherId"
      :course-assignments="courseAssignments"
      :teacher="teachersMap[currentTeacherId]"
      :task="computedTask"
      :grade-classes-map="gradeClassesMap"
    />
  </a-modal>
</template>

<style scoped lang="scss">
  .split-container {
    height: calc(100vh - 240px);
  }
</style>
