<script setup lang="ts">
  import { computed, nextTick, PropType, ref, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    gradeClass: {
      type: Object as PropType<any>,
      required: true,
    },
    arrangeResult: {
      type: Object,
      required: true,
    },
    teachersMap: {
      type: Object,
      required: true,
    },
    lessonsMap: {
      type: Object,
      required: true,
    },
    teachingConfigs: {
      type: Array as PropType<any[]>,
    },
    gradeCondition: {
      type: Object,
      required: true,
    },
    mode: {
      type: String as PropType<'generation' | 'timetable'>,
      default: 'generation',
    },
  });
  const emits = defineEmits(['manualAdjustment']);
  const weekdays = ['周一', '周二', '周三', '周四', '周五'];
  const dayPeriodSequence = computed(() => props.gradeCondition?.dayPeriodSequence);
  const result = ref(props.arrangeResult[props.gradeClass.id.toString()]);
  const manualAdjustmentLoading = ref(false);
  const manualAdjustmentVisible = ref(false);
  const currentAdjustSchedule = ref({
    period: null,
    idx: null,
    type: null,
    row: null,
  });

  const dragItem = ref<any>({});

  const handleDragStart = (periodIndex: number, typeIndex: number) => {
    dragItem.value = {
      periodIndex,
      typeIndex,
      value: result.value[periodIndex][typeIndex],
    };
  };

  const handleDrop = (e: any, periodIndex: number, typeIndex: number) => {
    // exchange item.teacherId and item.lessonId
    // result[periodIndex][typeIndex] = dragItem.value;

    const raw = {
      ...result.value,
    };

    const fromItem = dragItem.value.value;
    const toItem = raw[periodIndex][typeIndex];

    if (fromItem.teacherId === toItem.teacherId && fromItem.lessonId === toItem.lessonId) {
      return;
    }

    // exchange item.teacherId and item.lessonId
    const temp = fromItem.teacherId;
    fromItem.teacherId = toItem.teacherId;
    toItem.teacherId = temp;

    const temp2 = fromItem.lessonId;
    fromItem.lessonId = toItem.lessonId;
    toItem.lessonId = temp2;

    // update result
    raw[periodIndex][typeIndex] = toItem;
    raw[dragItem.value.periodIndex][dragItem.value.typeIndex] = fromItem;

    // update result
    result.value = raw;
    dragItem.value = {};
  };

  const handleDragOver = (e: any) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleShowManualAdjustment = (period, idx, type) => {
    const raw = result.value[period][idx];
    Message.clear();
    if (!raw) {
      Message.error('留空课或排课失败，无法手动调整');
      return;
    }
    if (props.mode === 'generation' && !raw.id) {
      Message.error('请先保存当前排课结果后再手动调整');
      return;
    }
    currentAdjustSchedule.value = {
      period,
      idx,
      type,
      row: {
        ...raw,
      },
    };
    manualAdjustmentVisible.value = true;
  };

  const handleHideManualAdjustment = () => {
    currentAdjustSchedule.value = {
      period: null,
      idx: null,
      type: null,
      row: null,
    };
    manualAdjustmentLoading.value = false;
    manualAdjustmentVisible.value = false;
  };

  const handleManualAdjustment = async () => {
    result.value[currentAdjustSchedule.value.period][currentAdjustSchedule.value.idx] = {
      ...currentAdjustSchedule.value.row,
    };

    if (props.mode === 'timetable') {
      emits('manualAdjustment', result.value, props.gradeClass, currentAdjustSchedule.value.row);
      handleHideManualAdjustment();
      return;
    }

    manualAdjustmentLoading.value = true;
    Message.success('正在保存手动调整结果...');
    try {
      await request(`/teacher/courseAssignment/${currentAdjustSchedule.value.row.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'PUT',
        data: currentAdjustSchedule.value.row,
      });
      Message.success('保存成功');

      emits('manualAdjustment', result.value, props.gradeClass, currentAdjustSchedule.value.row);
      handleHideManualAdjustment();
    } catch (e) {
      Message.error('保存失败');
    } finally {
      manualAdjustmentLoading.value = false;
    }
  };

  const updateConflicts = () => {
    const raw = props.arrangeResult;
  };

  const getTdBgClass = (periodIndex, idx) => {
    // {
    //   'bg-red-50':
    //   result[periodIndex] && !result[periodIndex][idx]?.teacherId && !result[periodIndex][idx]?.lessonId,
    //     'bg-red-300':
    //   (result[periodIndex] && !result[periodIndex][idx]?.teacherId) || !result[periodIndex][idx]?.lessonId,
    // }

    if (!result.value[periodIndex] || !result.value[periodIndex][idx]) {
      return 'bg-red-100';
    }

    const teacherId = result.value[periodIndex][idx]?.teacherId;
    const lessonId = result.value[periodIndex][idx]?.lessonId;

    if (!teacherId && !lessonId) {
      return 'bg-red-100';
    }

    if (!teacherId || !lessonId) {
      return 'bg-red-50';
    }

    return '';
  };

  watch(
    () => {
      return {
        arrangeResult: props.arrangeResult,
        gradeClass: props.gradeClass,
      };
    },
    () => {
      result.value = props.arrangeResult[props.gradeClass.id.toString()];
      updateConflicts();
    },
    { immediate: true, deep: true },
  );
</script>

<template>
  <a-card :bordered="false">
    <template #title>
      <div class="flex gap-2 items-center justify-between">
        <div class="flex gap-2 items-center">
          <div class="text-lg font-medium"> {{ gradeClass.name }} </div>
          <small class="text-gray-500">* 拖动单元格交换排课</small>
        </div>
        <slot name="extra-actions"></slot>
      </div>
    </template>
    <table v-if="result && gradeCondition" class="border-collapse border border-slate-400 w-full text-center mt-2">
      <thead>
        <tr>
          <th class="border border-slate-300 bg-slate-100 p-4"> 节次 / 周 </th>
          <th v-for="d in weekdays" :key="d" class="border border-slate-300 bg-slate-100 p-4">{{ d }}</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(period, periodIndex) in dayPeriodSequence" :key="periodIndex">
          <th class="border border-slate-300 p-4">
            {{ period.name }}
          </th>
          <td
            v-for="(type, idx) in period.types"
            :key="idx"
            style="width: 17%"
            class="border border-slate-300 p-4"
            :class="getTdBgClass(periodIndex, idx)"
            @drop="(e) => handleDrop(e, periodIndex, idx)"
            @dragover="(e) => handleDragOver(e)"
          >
            <div
              v-if="result[periodIndex]"
              class="cursor-move"
              :draggable="true"
              @drag="() => handleDragStart(periodIndex, idx)"
            >
              <div
                class="text-gray-500 cursor-pointer"
                @click="() => handleShowManualAdjustment(periodIndex, idx, 'teacher')"
              >
                {{ teachersMap[result[periodIndex][idx]?.teacherId]?.name || '未定义' }}
              </div>
              <div class="mt-2 cursor-pointer" @click="() => handleShowManualAdjustment(periodIndex, idx, 'course')">
                {{ lessonsMap[result[periodIndex][idx]?.lessonId]?.name || '无课程' }}
              </div>
            </div>
            <div v-else>本节课排课失败</div>
          </td>
        </tr>
      </tbody>
    </table>
    <div v-else>
      <slot name="not-configured"></slot>
    </div>

    <a-modal
      v-if="currentAdjustSchedule?.row && gradeCondition"
      v-model:visible="manualAdjustmentVisible"
      :ok-loading="manualAdjustmentLoading"
      :title="`手动调整 ${gradeClass.name}`"
      @close="handleHideManualAdjustment"
      @ok="handleManualAdjustment"
    >
      <a-form size="small">
        <a-form-item label="节次">
          {{ weekdays[currentAdjustSchedule.idx] }}
          {{ dayPeriodSequence[currentAdjustSchedule.period].name }}
        </a-form-item>
        <a-form-item label="课程">
          <a-select v-model="currentAdjustSchedule.row.lessonId" allow-search allow-clear placeholder="选择课程">
            <a-option
              v-for="lesson in Object.values(lessonsMap)"
              :key="lesson.id"
              :value="lesson.id"
              :label="lesson.name"
            ></a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="教师">
          <a-select
            v-if="teachingConfigs?.length"
            v-model="currentAdjustSchedule.row.teacherId"
            allow-search
            placeholder="选择或搜索教师"
            allow-clear
          >
            <a-option
              v-for="teacher in teachingConfigs"
              :key="teacher.teacher.id"
              :value="teacher.teacher.id"
              :label="teacher.teacher.name"
            >
              <a-tooltip
                v-if="
                  teacher.teachingSubject?.map((item) => item.id)?.indexOf(currentAdjustSchedule.row.lessonId) === -1
                "
                :content="`该教师未配置 ${lessonsMap[currentAdjustSchedule.row.lessonId].name} 科目`"
              >
                {{ teacher.teacher.name }}
                <small>
                  <IconInfoCircle class="text-red-500" />
                </small>
              </a-tooltip>
              <div v-else>
                {{ teacher.teacher.name }}
              </div>
            </a-option>
          </a-select>

          <a-select
            v-else
            v-model="currentAdjustSchedule.row.teacherId"
            allow-search
            placeholder="选择或搜索教师"
            allow-clear
          >
            <a-option v-for="teacher in teachersMap" :key="teacher.id" :value="teacher.id" :label="teacher.name">
              {{ teacher.name }}
            </a-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </a-card>
</template>

<style scoped lang="scss"></style>
