<script setup lang="ts">
  import { PropType, ref } from 'vue';
  import { useConfigureStore } from '@repo/infrastructure/store';

  const props = defineProps({
    task: {
      type: Object,
      required: true,
    },
    grades: {
      type: Array as PropType<any[]>,
      required: true,
    },
  });

  const emit = defineEmits(['save', 'update:task']);

  const gradeLessons = ref<Record<number, string[]>>(props.task.gradeLessons || {});
  const configureStore = useConfigureStore();
  const allLessons = configureStore.getConfigureItemList('TextBookSubject');

  const handleAddLessonToGrade = (lesson: string, gradeId: number) => {
    gradeLessons.value[gradeId] = gradeLessons.value[gradeId] || [];
    gradeLessons.value[gradeId].push(lesson);
  };

  const handleRemoveLessonFromGrade = (lesson: string, gradeId: number) => {
    gradeLessons.value[gradeId] = gradeLessons.value[gradeId].filter((l) => l !== lesson);
  };

  const handleSave = () => {
    emit('update:task', {
      ...props.task,
      gradeLessons: gradeLessons.value,
    });
    emit('save');
  };
</script>

<template>
  <div class="m-2">
    <a-button type="primary" size="mini" @click="handleSave">
      <template #icon>
        <IconSave />
      </template>
      保存课程分组
    </a-button>
  </div>
  <div class="grid grid-cols-4">
    <div v-for="(grade, idx) in grades" :key="idx" class="m-2 p-4 border-gray-200 border rounded-xl">
      <div class="flex justify-between items-center">
        <div class="text-lg font-medium">{{ grade.name }}</div>
        <div>{{ gradeLessons[grade.id]?.length || 0 }}门课程</div>
      </div>
      <div class="mt-4 flex gap-2 flex-wrap">
        <a-tag
          v-for="l in gradeLessons[grade.id]"
          :key="l"
          size="large"
          bordered
          color="arcoblue"
          class="rounded"
          closable
          @close="() => handleRemoveLessonFromGrade(l, grade.id)"
        >
          {{ l }}
        </a-tag>
        <a-popover trigger="click" class="w-80">
          <template #content>
            <div class="flex flex-wrap gap-2">
              <a-tag
                v-for="al in allLessons.filter((al) => !gradeLessons[grade.id]?.includes(al))"
                :key="al"
                class="cursor-pointer"
                @click="() => handleAddLessonToGrade(al, grade.id)"
              >
                {{ al }}
              </a-tag>
              <div v-if="!allLessons.filter((al) => !gradeLessons[grade.id]?.includes(al))?.length"> 暂无可选课程 </div>
            </div>
          </template>
          <a-tag class="cursor-pointer rounded" size="large" color="green" bordered>
            <template #icon>
              <IconPlus />
            </template>
            添加课程
          </a-tag>
        </a-popover>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
