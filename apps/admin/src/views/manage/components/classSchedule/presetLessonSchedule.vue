<script setup lang="ts">
  import { computed } from 'vue';

  const props = defineProps({
    task: {
      type: Object,
      required: true,
    },
    gradeClass: {
      type: Object,
      required: true,
    },
    gradeCondition: {
      type: Object,
      required: true,
    },
  });

  const weekdays = ['周一', '周二', '周三', '周四', '周五'];
  const dayPeriodSequence = computed(() => props.gradeCondition.dayPeriodSequence);
</script>

<template>
  <div class="flex gap-2 items-center">
    <div class="text-lg font-medium"> {{ gradeClass.name }} 课表预设 </div>
    <small class="text-gray-400"> * </small>
  </div>
  <table class="border-collapse border border-slate-400 w-full text-center mt-2">
    <thead>
      <tr>
        <th class="border border-slate-300 bg-slate-100 p-4"> 节次 / 周 </th>
        <th v-for="d in weekdays" :key="d" class="border border-slate-300 bg-slate-100 p-4">{{ d }}</th>
      </tr>
    </thead>
    <tbody>
      <tr v-for="(period, periodIndex) in dayPeriodSequence" :key="periodIndex">
        <th class="border border-slate-300 p-4">
          {{ period.name }}
        </th>
        <td v-for="(type, idx) in period.types" :key="idx" style="width: 17%" class="border border-slate-300 p-4"> </td>
      </tr>
    </tbody>
  </table>
</template>

<style scoped lang="scss"></style>
