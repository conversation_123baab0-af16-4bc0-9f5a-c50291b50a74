<script setup lang="ts">
  import { computed, inject, onMounted, PropType, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { CrudTable } from '@repo/ui/components/table';
  import TeacherSelect from '@repo/components/org/teacherSelect.vue';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import useSchoolCourseStore from '@repo/components/store/schoolCourseStore';
  import useTeachingResearchGroupStore from '@repo/components/store/teachingResearchGroupStore';
  import { selectInput } from '@repo/ui/components/form/inputComponents';
  import RecycleBinButton from '@repo/ui/components/table/recycleBinButton.vue';

  const props = defineProps({
    raw: {
      type: Array as PropType<any[]>,
      required: true,
    },
    task: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const emit = defineEmits(['update:raw']);

  const schema = ref(null);
  const visibleColumns = [
    'branchOffice',
    'teacher',
    'teachingSubject',
    'teachingResearchGroup',
    'classHoursRequired',
    'allClassHours',
  ];

  const currentRow = ref(null);

  const teacherSelectVisible = ref(false);
  const classHourSetVisible = ref(false);
  const teachingSubjectVisible = ref(false);
  const teachingGroupVisible = ref(false);

  const allClassHours = ref([]);
  const teachingSubjects = ref([]);
  const teachingGroups = ref([]);

  const allTeachingSubjects = ref([]);
  const allTeachingGroups = ref([]);

  const schoolCourseStore = useSchoolCourseStore();
  const teachingResearchGroupStore = useTeachingResearchGroupStore();
  const teachingGroupSchemaField = ref<any>({});

  const getTeachers = inject('getTeachers') as any;

  const dataList = computed({
    get: () => props.raw,
    set: (value) => emit('update:raw', value),
  });

  const handleModalClose = () => {
    teacherSelectVisible.value = false;
    classHourSetVisible.value = false;
    teachingSubjectVisible.value = false;
    teachingGroupVisible.value = false;

    teachingSubjects.value = [];
    teachingGroups.value = [];
    currentRow.value = null;
  };

  const handleShowTeachingSubjectSet = (record) => {
    currentRow.value = record;
    teachingSubjectVisible.value = true;
    teachingSubjects.value = currentRow.value.teachingSubject?.map((item) => item.id) || [];
  };

  const handleShowTeachingGroup = (record) => {
    currentRow.value = record;
    teachingGroupVisible.value = true;
    teachingGroups.value = currentRow.value.teachingResearchGroup?.map((item) => item.id) || [];
  };

  const handleShowTeacherSelect = () => {
    teacherSelectVisible.value = true;
  };

  const handleShowClassHourSet = (row) => {
    classHourSetVisible.value = true;
    currentRow.value = row;
    allClassHours.value = [...(currentRow.value.allClassHours || [])];
  };

  const handleSetClassHours = async () => {
    classHourSetVisible.value = false;
    Message.info('正在设置课时... 请稍后');
    const postData = {
      ...currentRow.value,
      allClassHours: (allClassHours.value || [])
        .filter((item) => item.name?.trim() && item.id)
        .sort((a, b) => b.name.localeCompare(a.name)),
    };
    await request(`/teacher/teacherTeachingConfig/${currentRow.value.id}`, {
      method: 'PUT',
      data: postData,
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    Message.clear();
    Message.success('设置成功');

    dataList.value = dataList.value.map((item) => {
      if (item.id === currentRow.value.id) {
        return postData;
      }
      return item;
    });

    handleModalClose();
  };

  const handleSetTeachingSubjects = async () => {
    teachingSubjectVisible.value = false;
    Message.info('正在设置教学科目... 请稍后');
    const postData = {
      ...currentRow.value,
      teachingSubject: teachingSubjects.value?.map((item) => ({ id: item })),
    };
    await request(`/teacher/teacherTeachingConfig/${currentRow.value.id}`, {
      method: 'PUT',
      data: postData,
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    Message.clear();
    Message.success('设置成功');

    dataList.value = dataList.value.map((item) => {
      if (item.id === currentRow.value.id) {
        return postData;
      }
      return item;
    });

    handleModalClose();
    getTeachers();
  };

  const handleSetTeachingGroups = async () => {
    teachingGroupVisible.value = false;
    Message.info('正在设置教研组... 请稍后');
    const postData = {
      ...currentRow.value,
      teachingResearchGroup: teachingGroups.value?.map((item) => ({ id: item })),
    };
    await request(`/teacher/teacherTeachingConfig/${currentRow.value.id}`, {
      method: 'PUT',
      data: postData,
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    Message.clear();
    Message.success('设置成功');

    dataList.value = dataList.value.map((item) => {
      if (item.id === currentRow.value.id) {
        return postData;
      }
      return item;
    });

    handleModalClose();
    getTeachers();
  };

  const teachers = ref([]);
  const tableRef = ref();
  const tableRe1f = ref();
  const handleTeacherSelectChange = (val: any, teacher: any, values: any, teacherList: any) => {
    teachers.value = teacherList.filter((item) => {
      return values.includes(item.id);
    });
  };
  const handleSaveSetting = async () => {
    const result = teachers.value.map((teacher) => {
      return {
        teacher,
        lessonSchedule: {
          id: props.task.id,
        },
        teachingSubject:
          teacher.teachingSubjects.map((subject) => {
            return { name: subject };
          }) || [],
        allClassHours: [],
      };
    });
    const { data } = await request('/teacher/teacherTeachingConfig/batchSave', {
      method: 'POST',
      data: result,
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    await tableRef.value?.loadData();
    tableRe1f.value?.cleanValue();
  };

  const handleTeacherSelected = async (teacherId, teacher: any) => {
    teacherSelectVisible.value = false;
    Message.info(`正在创建 ${teacher.name} 的教学配置... 请稍后`);
    const { data } = await request('/teacher/teacherTeachingConfig', {
      method: 'POST',
      data: {
        teacher,
        lessonSchedule: {
          id: props.task.id,
        },
        teachingSubject:
          teacher.teachingSubjects.map((subject) => {
            return { name: subject };
          }) || [],
        allClassHours: [],
      },
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    Message.success('创建成功，请配置教师的教学配置');

    dataList.value = [...dataList.value, data];
  };

  const handleRemoveGroups = (option: any) => {
    console.log(option);
    const newOptions = allTeachingGroups.value.filter((item) => item.id !== option.value);
    teachingGroupSchemaField.value.inputWidgetProps.options = newOptions;
    allTeachingGroups.value = newOptions;
  };
  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/teacher/teacherTeachingConfig');
    allTeachingSubjects.value = await schoolCourseStore.getSchoolCourses();
    allTeachingGroups.value = await teachingResearchGroupStore.getTeachingResearchGroups();
    let teachingGroupSchema;

    [schema.value, allTeachingSubjects.value, allTeachingGroups.value, teachingGroupSchema] = await Promise.all([
      SchemaHelper.getInstanceByDs('/teacher/teacherTeachingConfig'),
      schoolCourseStore.getSchoolCourses(),
      teachingResearchGroupStore.getTeachingResearchGroups(),
      SchemaHelper.getInstanceByDs('/teacher/teachingResearchGroup'),
    ]);

    teachingGroupSchemaField.value = {
      key: 'teachingResearchGroup',
      label: '教研组',
      inputWidgetProps: {
        labelField: 'name',
        valueField: 'id',
        multiple: true,
        size: 'mini',
        options: allTeachingGroups.value,
        allowEdit: true,
        allowDelete: true,
        deleteProps: {
          api: '/teacher/teachingResearchGroup',
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          destroy: true,
        },
        createRemote: {
          schema: teachingGroupSchema,
          onCreated: (data) => {
            allTeachingGroups.value = [...allTeachingGroups.value, data];
            teachingResearchGroupStore.onAdd(data);
            teachingGroupSchemaField.value.inputWidgetProps.options = allTeachingGroups.value;
          },
        },
        fieldNames: {
          label: 'name',
          value: 'id',
        },
      },
    };
  });
</script>

<template>
  <div v-if="schema">
    <a-space class="w-full justify-between">
      <a-button size="mini" type="primary" @click="handleShowTeacherSelect">
        <template #icon>
          <IconPlus />
        </template>
        添加教师
      </a-button>
      <a-space>
        <a-button size="mini" @click="getTeachers">
          <template #icon>
            <IconRefresh />
          </template>
          刷新
        </a-button>
        <recycle-bin-button
          :visible-columns="visibleColumns"
          :default-query-params="{
            lessonSchedule: task.id,
          }"
          :schema="schema"
          @refresh="getTeachers"
        />
      </a-space>
    </a-space>
    <crud-table
      ref="tableRef"
      class="mt-2"
      :schema="schema"
      :visible-columns="visibleColumns"
      :data="dataList"
      :auto-load="false"
      :pagination="false"
      @refresh="getTeachers"
    >
      <template #custom-column-teachingSubject="{ record }">
        <a-tag
          class="cursor-pointer"
          :color="record.teachingSubject?.length ? 'blue' : 'red'"
          @click="() => handleShowTeachingSubjectSet(record)"
        >
          <span v-if="record.teachingSubject?.length > 2"> {{ record.teachingSubject.length }} 个科目 </span>
          <span v-else-if="record.teachingSubject?.length > 0">
            {{ record.teachingSubject.map((item) => item.name).join('、') }}
          </span>
          <span v-else>未设置</span>
        </a-tag>
      </template>
      <template #custom-column-teachingResearchGroup="{ record }">
        <a-tag
          class="cursor-pointer"
          :color="record.teachingResearchGroup?.length ? 'green' : 'red'"
          @click="() => handleShowTeachingGroup(record)"
        >
          <span v-if="record.teachingResearchGroup?.length > 2">
            {{ record.teachingResearchGroup.length }} 个科目
          </span>
          <span v-else-if="record.teachingResearchGroup?.length > 0">
            {{ record.teachingResearchGroup.map((item) => item.name).join('、') }}
          </span>
          <span v-else>未设置</span>
        </a-tag>
      </template>
      <template #custom-column-classHoursRequired="{ record }">
        <a-tag class="cursor-pointer" color="blue" @click="() => handleShowClassHourSet(record)">
          {{ record.classHoursRequired || 0 }} 课时
        </a-tag>
      </template>
    </crud-table>

    <a-modal v-model:visible="teacherSelectVisible" title="选择教师" :on-before-ok="handleSaveSetting">
      <teacher-select ref="tableRe1f" :multiple="true" @change="handleTeacherSelectChange" />
    </a-modal>

    <a-modal
      v-if="currentRow?.id"
      v-model:visible="classHourSetVisible"
      :title="`${currentRow?.teacher?.name} - 课时任务设置`"
    >
      <a-input-number v-model="currentRow.classHoursRequired" mode="button" size="mini" placeholder="请输入课时任务">
        <template #suffix> 课时 </template>
      </a-input-number>
      <template #footer>
        <a-button type="primary" @click="() => handleSetClassHours()"> 确定 </a-button>
      </template>
    </a-modal>

    <a-modal
      v-if="currentRow?.id"
      v-model:visible="teachingSubjectVisible"
      :title="`${currentRow?.teacher?.name} - 教学科目设置`"
    >
      <a-select
        v-model="teachingSubjects"
        multiple
        size="mini"
        :options="allTeachingSubjects"
        :field-names="{
          label: 'name',
          value: 'id',
        }"
      />
      <template #footer>
        <a-button type="primary" @click="handleSetTeachingSubjects"> 确定 </a-button>
      </template>
    </a-modal>

    <a-modal
      v-if="currentRow?.id"
      v-model:visible="teachingGroupVisible"
      :title="`${currentRow?.teacher?.name} - 教研组设置`"
    >
      <select-input
        v-if="teachingGroupSchemaField.key && teachingGroupVisible"
        v-model="teachingGroups"
        :schema-field="teachingGroupSchemaField"
        @delete="handleRemoveGroups"
      />
      <template #footer>
        <a-button type="primary" @click="handleSetTeachingGroups"> 确定 </a-button>
      </template>
    </a-modal>
  </div>
</template>
