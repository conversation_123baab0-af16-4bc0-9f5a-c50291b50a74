<script setup lang="ts">
  import { onMounted, ref, PropType, computed, nextTick, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    task: {
      type: Object,
      required: true,
    },
    grades: {
      type: Array as PropType<any[]>,
      required: true,
    },
    gradeClasses: {
      type: Array as PropType<any[]>,
      required: true,
    },
    gradeClassesMap: {
      type: Object as PropType<any>,
      required: true,
    },
    lessons: {
      type: Array as PropType<any[]>,
      required: true,
    },
    teacherConfigs: {
      type: Array as PropType<any[]>,
      required: true,
    },
  });

  const emit = defineEmits(['save', 'update:task']);
  const editVisible = ref(false);
  const gradeClassesGroup = computed(() => {
    const group = {};
    const ids = props.task?.noNeedScheduleClassIds || [];
    props.gradeClasses.forEach((c) => {
      // 过滤掉在task 中设置了不需要排课的班级
      if (!ids.includes(c.id)) {
        if (!group[c.grade?.id]) {
          group[c.grade?.id] = [];
        }
        group[c.grade?.id].push(c);
      }
    });
    return group;
  });

  const hours = ref<Record<number, Record<number, any>>>({});
  const currentGrade = ref<any>();
  const currentLesson = ref<any>();
  const currentGradeClass = ref<any>();
  const currentLessonTeachers = ref<any[]>([]);
  const teacherConfigsData = ref<any[]>(props.teacherConfigs || []);
  const tableHeight = document.documentElement.clientHeight - 240;
  const selectedOtherVisibleTeacher = ref(null);
  const teacherConfigsMap = computed(() => {
    return teacherConfigsData.value.reduce((acc, tc) => {
      acc[tc.teacher.id] = tc;
      return acc;
    }, {});
  });

  // 当前课程可用教师
  const currentLessonAvailableTeachers = computed(() => {
    if (!currentLesson.value?.id) {
      return [];
    }
    return teacherConfigsData.value
      .filter((tc) => {
        return tc.teachingSubject?.map((item) => item.id)?.indexOf(currentLesson.value.id) >= 0;
      })
      .map((tc) => {
        return {
          value: tc.teacher.id,
          label: tc.teacher.name,
          fixed: false,
          hours: 0,
        };
      });
  });

  // 当前课程教师总课时
  const currentLessonTeachersTotalHours = computed(() => {
    return currentLessonTeachers.value.reduce((acc, t) => {
      return acc + t.hours;
    }, 0);
  });

  const handleShowEdit = (grade, gradeClass, lesson) => {
    currentGrade.value = grade;
    currentGradeClass.value = gradeClass;
    currentLesson.value = lesson;
    editVisible.value = true;

    if (hours.value[gradeClass.id][lesson.id]?.teachers?.length) {
      currentLessonTeachers.value = hours.value[gradeClass.id][lesson.id].teachers;
    } else {
      currentLessonTeachers.value = gradeClass.teacherList
        ?.filter((teacher) => {
          return teacherConfigsMap.value[teacher.id]?.teachingSubject?.map((item) => item.id)?.indexOf(lesson.id) >= 0;
        })
        .map((teacher) => {
          return {
            id: teacher.id,
            name: teacher.name,
            fixed: teacher.fixed,
            hours: 0,
          };
        });
    }
  };

  // 计算某个教师所有已的计划课时
  const calculateTeacherPlannedHours = (teacherId: number) => {
    return Object.values(hours.value).reduce((acc, grade) => {
      return (
        acc +
        Object.values(grade).reduce((acc1, lesson) => {
          return (
            acc1 +
            lesson.teachers.reduce((acc2, t) => {
              return acc2 + (t.id === teacherId ? t.hours : 0);
            }, 0)
          );
        }, 0)
      );
    }, 0);
  };

  const updateTeacherConfigData = () => {
    teacherConfigsData.value = props.teacherConfigs.map((tc) => {
      const { teacher } = tc;
      const planned = calculateTeacherPlannedHours(teacher.id);
      const requiredHours = tc.allClassHours?.find((item) => item.name === props.task.period);
      return {
        ...tc,
        requiredHours: requiredHours?.id || 0,
        planned,
      };
    });
  };

  const handleHideEdit = () => {
    const hoursData = hours.value;
    hoursData[currentGradeClass.value.id][currentLesson.value.id].teachers = currentLessonTeachers.value;
    hours.value = hoursData;

    editVisible.value = false;
    currentLesson.value = null;
    currentGrade.value = null;
    currentGradeClass.value = null;
    currentLessonTeachers.value = [];

    selectedOtherVisibleTeacher.value = null;
    updateTeacherConfigData();
  };

  const getGradeClassHoursSum = computed(() => (dataIndex: string) => {
    const [id, type] = dataIndex.split('-');
    let count = 0;
    if (type === 'total') {
      // hours[currentGradeClass.id][currentLesson.id].hours
      Object.values(hours.value[Number(id)]).forEach((lesson) => {
        count += lesson.hours;
      });
    } else if (type === 'teacher') {
      // hours[currentGradeClass.id][currentLesson.id].teachers
      Object.values(hours.value[Number(id)]).forEach((lesson) => {
        lesson.teachers.forEach((t) => {
          count += t.hours;
        });
      });
    }

    return count;
  });

  const handleOtherTeacherSelected = (value) => {
    const teacher = currentLessonAvailableTeachers.value.find((t) => t.value === value);
    if (teacher) {
      const exists = currentLessonTeachers.value.find((t) => t.id === teacher.value);
      if (exists) {
        Message.error('已选择该教师');
        return;
      }
      currentLessonTeachers.value.push({
        ...teacher,
        id: teacher.value,
        fixed: false,
        name: teacher.label,
      });
    }
  };

  const handleSave = () => {
    // 过滤课时为0的教师
    Object.keys(hours.value).forEach((gradeClassId) => {
      Object.keys(hours.value[gradeClassId]).forEach((lessonId) => {
        const gradeHourConfig = hours.value[gradeClassId][lessonId];
        gradeHourConfig.teachers = gradeHourConfig.teachers?.filter((item) => item.hours > 0);
        hours.value[gradeClassId][lessonId] = gradeHourConfig;
      });
    });

    emit('update:task', {
      ...props.task,
      gradeHours: hours.value,
    });
    emit('save');
  };

  /**
   * 当前课程课时 平均分配给 currentLessonTeachers 取整
   * @param val
   */
  const handleCurrentLessonHoursChange = () => {
    nextTick(() => {
      const total = hours.value[currentGradeClass.value.id][currentLesson.value.id].hours;
      const len = currentLessonTeachers.value?.filter((item) => item.fixed).length;
      if (len <= 0) {
        return;
      }
      const avg = Math.floor(total / len);
      currentLessonTeachers.value.forEach((t) => {
        t.hours = avg;
      });
    });
  };

  watch(
    () => props.task,
    (val) => {
      const hoursData = {};
      props.gradeClasses.forEach((gradeClass) => {
        const gradeClassId = Number(gradeClass.id);
        hoursData[gradeClassId] = {};
        props.lessons?.forEach((lesson) => {
          const lessonId = Number(lesson.id);
          const raw = val.gradeHours[gradeClassId];
          hoursData[gradeClassId][lessonId] = (raw && raw[lessonId]) || {
            hours: 0,
            teachers: [],
          };
        });
      });

      hours.value = hoursData;

      updateTeacherConfigData();
    },
    {
      immediate: true,
      deep: true,
    },
  );

  onMounted(() => {});
</script>

<template>
  <div>
    <div class="m-2 flex gap-2 items-center">
      <a-button type="primary" size="mini" @click="handleSave">
        <template #icon>
          <IconSave />
        </template>
        保存课时计划
      </a-button>
      <small>* 单击单元格修改课时计划</small>
    </div>
    <div class="m-2">
      <div class="float-left main-table">
        <a-table
          :data="lessons"
          :bordered="{ cell: true }"
          :pagination="false"
          :summary="true"
          :scrollbar="true"
          :style="{ height: `${tableHeight}px` }"
          :scroll="{ x: gradeClasses.length * 180, y: tableHeight }"
        >
          <template #columns>
            <a-table-column title="课程/年级" data-index="lesson" fixed="left">
              <template #cell="{ record }">
                {{ record.name }}
              </template>
            </a-table-column>
            <a-table-column
              v-for="(grade, idx) in grades"
              :key="idx"
              :title="grade.name"
              :data-index="grade.id.toString()"
            >
              <a-table-column
                v-for="(gradeClass, gcIdx) in gradeClassesGroup[grade.id]"
                :key="gcIdx"
                :title="gradeClass.name"
              >
                <a-table-column title="课时" :width="70" :data-index="`${gradeClass.id}-total`">
                  <template #cell="{ record }">
                    <div class="py-2 cursor-pointer" @click="() => handleShowEdit(grade, gradeClass, record)">
                      <div v-if="hours[gradeClass.id] && hours[gradeClass.id][record.id]?.hours" class="font-medium">
                        <div class="text-green-500"> {{ hours[gradeClass.id][record.id].hours }}</div>
                      </div>
                      <div v-else class="text-red-400"> 未设</div>
                    </div>
                  </template>
                </a-table-column>
                <a-table-column :data-index="`${gradeClass.id}-teacher`" title="教师安排">
                  <template #cell="{ record }">
                    <div
                      v-for="(t, ti) in hours[gradeClass.id][record.id]?.teachers"
                      :key="ti"
                      class="cursor-pointer"
                      :class="{ hidden: t.hours <= 0 }"
                      @click="() => handleShowEdit(grade, gradeClass, record)"
                    >
                      <small> {{ t.name }} </small>
                      <small> * </small>
                      <small>{{ t.hours || 0 }} </small>
                    </div>
                  </template>
                </a-table-column>
              </a-table-column>
            </a-table-column>
          </template>

          <template #summary-cell="{ column }">
            <div class="font-medium py-2">
              <div v-if="column.dataIndex === 'lesson'"> 课时合计</div>
              <div v-else> {{ getGradeClassHoursSum(column.dataIndex) }}</div>
            </div>
          </template>
        </a-table>
      </div>

      <a-table
        :data="teacherConfigsData"
        :pagination="false"
        class="w-72 float-right"
        :style="{ height: `${tableHeight}px` }"
        :scroll="{ y: tableHeight }"
        :scrollbar="true"
        :summary="true"
        summary-text="合计"
      >
        <template #columns>
          <a-table-column data-index="teacher.name" title="教师" />
          <a-table-column title="课时" data-index="requiredHours">
            <template #cell="{ record }">
              <div
                :class="{
                  'text-green-400': !record.planned,
                  'text-blue-600 font-bold': record.requiredHours > (record.planned || 0) && record.planned > 0,
                  'text-red-600 font-bold': record.requiredHours < (record.planned || 0) && record.planned > 0,
                }"
              >
                {{ record.requiredHours }}
              </div>
            </template>
          </a-table-column>
          <a-table-column title="已计划" data-index="planned">
            <template #cell="{ record }">
              <div
                :class="{
                  'text-green-400': !record.planned,
                  'text-blue-600 font-bold': record.requiredHours > (record.planned || 0) && record.planned > 0,
                  'text-red-600 font-bold': record.requiredHours < (record.planned || 0) && record.planned > 0,
                }"
              >
                {{ record.planned || 0 }}
              </div>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>

    <a-modal
      v-model:visible="editVisible"
      title="课时设置"
      ok-text="完成"
      hide-cancel
      :closable="false"
      :esc-to-close="false"
      @close="handleHideEdit"
    >
      <div v-if="editVisible">
        <a-form :model="hours[currentGradeClass.id][currentLesson.id]" :auto-label-width="true">
          <a-form-item label="总课时">
            <a-input-number
              v-model="hours[currentGradeClass.id][currentLesson.id].hours"
              autofocus
              class="w-40"
              mode="button"
              :step="1"
              :min="0"
              :max="50"
              @change="handleCurrentLessonHoursChange"
              @keyup="handleCurrentLessonHoursChange"
            >
              <template #suffix>课时</template>
            </a-input-number>
            <div class="ml-2">
              <small
                v-if="currentLessonTeachersTotalHours > hours[currentGradeClass.id][currentLesson.id].hours"
                class="text-red-500"
                >* 所选老师课时超出总课时</small
              >
              <small
                v-else-if="currentLessonTeachersTotalHours === hours[currentGradeClass.id][currentLesson.id].hours"
                class="text-green-500"
              >
                <IconCheck />
                课时分配完成
              </small>
            </div>
          </a-form-item>
          <a-divider :margin="10" />
          <div class="grid grid-cols-2 gap-2">
            <a-form-item v-for="(t, i) in currentLessonTeachers" :key="i" :label="t.name">
              <a-input-number v-model="t.hours" class="w-40" :step="1" :min="0" :max="50" mode="button">
                <template #suffix>课时</template>
              </a-input-number>
            </a-form-item>

            <a-form-item v-if="currentLessonAvailableTeachers?.length" label="选择">
              <a-select
                v-model="selectedOtherVisibleTeacher"
                placeholder="选择其他教师"
                :options="currentLessonAvailableTeachers"
                allow-search
                @change="handleOtherTeacherSelected"
              />
            </a-form-item>
          </div>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="scss">
  .main-table {
    width: calc(100% - 19rem);
  }
</style>
