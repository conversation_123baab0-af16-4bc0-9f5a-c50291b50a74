<script setup lang="ts">
  import { onMounted, PropType, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import useSchoolCourseStore from '@repo/components/store/schoolCourseStore';
  import useTeachingResearchGroupStore from '@repo/components/store/teachingResearchGroupStore';

  const props = defineProps({
    task: {
      type: Object,
      required: true,
    },
    grades: {
      type: Array as PropType<any[]>,
      required: true,
    },
  });

  const emit = defineEmits(['save', 'update:task']);

  const splitPanelSize = ref(200);
  const activeGrade = ref<any>({});
  const gradeCondition = ref<any>({});

  const weekdays = ['周一', '周二', '周三', '周四', '周五'];
  const allCourses = ref<any>([]);
  const courseTypesMap = ref<any>({
    Normal: '正常',
    Empty: '留空',
    Fixed: '固定',
  });
  const schoolCourseStore = useSchoolCourseStore();
  const teachingResearchGroupStore = useTeachingResearchGroupStore();
  const teachingGroupsMap = ref({});

  // 更改节次类型
  const handlePeriodTypeChange = (type: any, periodIndex: number, typeIndex: number) => {
    gradeCondition.value.dayPeriodSequence[periodIndex].types[typeIndex] = type;
  };

  // 更改教研课
  const handleTeachingGroupChange = (teachingGroupId, periodIndex: number, typeIndex: number) => {
    gradeCondition.value.dayPeriodSequence[periodIndex].researchGroups[typeIndex] = teachingGroupId;
  };

  const handleSave = () => {
    const allConditions = props.task.gradeConditions || [];
    const index = allConditions.findIndex((item) => item.gradeId === activeGrade.value.id);
    if (index > -1) {
      allConditions[index] = {
        ...gradeCondition.value,
        gradeId: activeGrade.value.id,
      };
    } else {
      allConditions.push({
        ...gradeCondition.value,
        gradeId: activeGrade.value.id,
      });
    }
    emit('update:task', {
      ...props.task,
      gradeConditions: allConditions,
    });
    emit('save');
  };

  const handleGradeConditionChange = () => {
    const periods = [];

    for (let i = 0; i < gradeCondition.value.morningCount; i += 1) {
      periods.push({
        name: `上午第${i + 1}节`,
        types: gradeCondition.value.dayPeriodSequence[i]?.types || weekdays.map(() => 'Normal'),
        preferCourse: gradeCondition.value.dayPeriodSequence[i]?.preferCourse || weekdays.map(() => []),
        ignoreCourse: gradeCondition.value.dayPeriodSequence[i]?.ignoreCourse || weekdays.map(() => []),
        fixedCourse: gradeCondition.value.dayPeriodSequence[i]?.fixedCourse || weekdays.map(() => null),
        researchGroups: gradeCondition.value.dayPeriodSequence[i]?.researchGroups || weekdays.map(() => null),
      });
    }
    for (let i = 0; i < gradeCondition.value.afternoonCount; i += 1) {
      periods.push({
        name: `下午第${i + 1}节`,
        types:
          gradeCondition.value.dayPeriodSequence[gradeCondition.value.morningCount + i]?.types ||
          weekdays.map(() => 'Normal'),
        preferCourse:
          gradeCondition.value.dayPeriodSequence[gradeCondition.value.morningCount + i]?.preferCourse ||
          weekdays.map(() => []),
        ignoreCourse:
          gradeCondition.value.dayPeriodSequence[gradeCondition.value.morningCount + i]?.ignoreCourse ||
          weekdays.map(() => []),
        fixedCourse:
          gradeCondition.value.dayPeriodSequence[gradeCondition.value.morningCount + i]?.fixedCourse ||
          weekdays.map(() => null),
        researchGroups:
          gradeCondition.value.dayPeriodSequence[gradeCondition.value.morningCount + i]?.researchGroups ||
          weekdays.map(() => null),
      });
    }

    gradeCondition.value.dayPeriodSequence = periods;
  };

  const handleActiveGradeChange = (grade) => {
    activeGrade.value = grade;

    gradeCondition.value = props.task.gradeConditions?.find((item) => item.gradeId === grade.id) || {
      morningCount: 3,
      afternoonCount: 3,
      dayPeriodSequence: [],
    };

    handleGradeConditionChange();
  };

  watch(
    () => {
      return [gradeCondition.value.morningCount, gradeCondition.value.afternoonCount];
    },
    () => {
      handleGradeConditionChange();
    },
  );

  onMounted(async () => {
    const courses = await schoolCourseStore.getSchoolCourses();
    allCourses.value = courses.map((item) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
    const teachingGroups = await teachingResearchGroupStore.getTeachingResearchGroups();
    teachingGroups.forEach((item) => {
      teachingGroupsMap.value[item.id] = item;
      teachingGroupsMap.value[-1] = {
        id: -1,
        name: '无教研',
      };
    });
    handleActiveGradeChange(props.grades[0]);
  });
</script>

<template>
  <div>
    <a-split v-model:size="splitPanelSize" class="split-container" min="80px">
      <template #first>
        <div class="px-2">
          <div
            v-for="(grade, idx) in grades"
            :key="idx"
            class="py-2 cursor-pointer"
            :class="{ 'text-blue-600 font-medium': activeGrade.id === grade.id }"
            @click="() => handleActiveGradeChange(grade)"
          >
            {{ grade.name }}
          </div>
        </div>
      </template>
      <template #second>
        <div v-if="activeGrade?.id" class="px-2">
          <div class="flex gap-2 mx-2 items-center justify-between">
            <div class="flex gap-2 items-center">
              <a-form-item label="上午" class="mb-0 w-40">
                <a-input-number v-model="gradeCondition.morningCount" :min="0" :max="6">
                  <template #suffix> 节 </template>
                </a-input-number>
              </a-form-item>
              <a-form-item label="下午" class="mb-0 w-40">
                <a-input-number v-model="gradeCondition.afternoonCount" :min="0" :max="6">
                  <template #suffix> 节 </template>
                </a-input-number>
              </a-form-item>
            </div>

            <a-button type="primary" size="mini" @click="handleSave">
              <template #icon>
                <IconSave />
              </template>
              保存 {{ activeGrade.name }} 条件设定
            </a-button>
          </div>

          <div class="m-2 mt-4">
            <table class="border-collapse border border-slate-400 w-full text-center">
              <thead>
                <tr>
                  <th class="border border-slate-300 bg-slate-100 p-4"> 节次 / 周 </th>
                  <th v-for="d in weekdays" :key="d" class="border border-slate-300 bg-slate-100 p-4">{{ d }}</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(period, periodIndex) in gradeCondition.dayPeriodSequence" :key="periodIndex">
                  <th class="border border-slate-300 p-4">
                    {{ period.name }}
                  </th>
                  <td
                    v-for="(type, idx) in period.types"
                    :key="idx"
                    style="width: 18%"
                    class="border border-slate-300"
                    :class="{ 'bg-red-50': type === 'Empty', 'bg-green-100': type === 'Fixed' }"
                  >
                    <a-space class="px-2 mt-1">
                      <a-dropdown size="mini" @select="(val) => handlePeriodTypeChange(val, periodIndex, idx)">
                        <div class="cursor-pointer">{{ courseTypesMap[type] || '-' }}</div>
                        <template #content>
                          <a-doption v-for="(label, value) in courseTypesMap" :key="value" :value="value">
                            {{ label }}
                          </a-doption>
                        </template>
                      </a-dropdown>
                      <a-divider direction="vertical" :margin="2" />
                      <a-dropdown size="mini" @select="(val) => handleTeachingGroupChange(val, periodIndex, idx)">
                        <div class="cursor-pointer" :class="{ 'text-blue-600': period.researchGroups[idx] > 0 }">
                          {{
                            period.researchGroups[idx] ? teachingGroupsMap[period.researchGroups[idx]]?.name : '无教研'
                          }}</div
                        >
                        <template #content>
                          <a-doption
                            v-for="(group, groupIdx) in Object.values(teachingGroupsMap)"
                            :key="groupIdx"
                            :value="group.id"
                          >
                            {{ group.name }}
                          </a-doption>
                        </template>
                      </a-dropdown>
                    </a-space>
                    <a-divider v-if="type !== 'Empty'" :margin="4" />
                    <div class="px-2 my-1">
                      <a-form size="mini" auto-label-width>
                        <div v-if="type === 'Normal'">
                          <a-form-item label="优先" class="mb-0">
                            <a-select
                              v-if="type !== 'Empty'"
                              v-model="gradeCondition.dayPeriodSequence[periodIndex].preferCourse[idx]"
                              size="mini"
                              multiple
                              :options="allCourses"
                              allow-clear
                              placeholder="选择优先排课课程"
                            />
                          </a-form-item>
                          <a-form-item label="不排" class="mb-0">
                            <a-select
                              v-if="type !== 'Empty'"
                              v-model="gradeCondition.dayPeriodSequence[periodIndex].ignoreCourse[idx]"
                              size="mini"
                              multiple
                              :options="allCourses"
                              allow-clear
                              placeholder="选择不排的课程"
                            />
                          </a-form-item>
                        </div>
                        <a-form-item v-else-if="type === 'Fixed'" label="固定" class="mb-0">
                          <a-select
                            v-if="type !== 'Empty'"
                            v-model="gradeCondition.dayPeriodSequence[periodIndex].fixedCourse[idx]"
                            size="mini"
                            :options="allCourses"
                            allow-clear
                            placeholder="选择固定课程"
                          />
                        </a-form-item>
                      </a-form>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <a-skeleton v-else animation>
          <div class="p-4">
            <a-skeleton-line :rows="6" />
          </div>
        </a-skeleton>
      </template>
    </a-split>
  </div>
</template>

<style scoped lang="scss">
  .split-container {
    height: calc(100vh - 198px);
  }
</style>
