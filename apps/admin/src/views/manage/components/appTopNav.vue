<script lang="ts" setup>
  import { computed, PropType, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { menuService } from '@repo/infrastructure/data';

  const props = defineProps({
    width: {
      type: Number,
      default: 0,
    },
    itemWidth: {
      type: Number,
      default: 110,
    },
    tabs: {
      type: Array as PropType<menuService.UserMenu[]>,
      default: () => [],
    },
    active: {
      type: [String, Number],
      default: '',
    },
  });

  const emit = defineEmits(['update:active']);

  const activeTabs = computed({
    get: () => [props.active],
    set: (value) => emit('update:active', value[0]),
  });
  const router = useRouter();

  const handleMenuClick = (tab: menuService.UserMenu) => {
    router.push(tab.link as string);
  };
</script>

<template>
  <a-menu v-if="tabs?.length > 0" v-model:selected-keys="activeTabs" mode="horizontal">
    <a-menu-item v-for="tab in tabs" :key="tab.key" :title="tab.label" @click="() => handleMenuClick(tab)">
      <a-badge :count="tab.count || 0" :dot="tab.dot !== false"> {{ tab.label }} </a-badge>
    </a-menu-item>
  </a-menu>
</template>

<style lang="less" scoped>
  .wrapper {
    margin-left: 8px;
    overflow: hidden;
    height: 28px;
  }
  .arco-menu-horizontal :deep {
    .arco-menu-inner {
      overflow: hidden;
      padding: 9px 0;
      .arco-menu-selected-label {
        bottom: -3px;
      }
    }
    .arco-menu-item {
      padding: 0 8px;
      &:nth-child(2) {
        margin-left: 0;
      }
    }
  }
</style>
