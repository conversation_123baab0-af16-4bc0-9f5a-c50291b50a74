<script setup lang="ts">
  import { onMounted, ref, PropType } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import useSchoolCourseStore from '@repo/components/store/schoolCourseStore';
  import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';
  import TeachingArchiveDetail from '@repo/components/course/teachingArchive/teachingArchiveDetail.vue';
  import { getChapter } from '@repo/infrastructure/openapi/course';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    record: {
      type: Object as PropType<any>,
      required: true,
    },
    period: {
      type: Object as PropType<any>,
    },
    lessonCount: {
      type: Object as PropType<any>,
    },
    currentTask: {
      type: Object as PropType<any>,
    },
  });

  // 当前页显示的日期范围
  const paginatedDateRange = ref<string[]>([]);

  // 学期内的所有日期
  const semesterDateRange = ref<string[]>([]);

  // 当前页码
  const currentPage = ref(0);

  // 每页显示的日期数量
  const pageSize = 7;

  // 星期几的映射
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  const switchClass =
    'font-bold text-lg  size-10 text-gray-400 hover:text-blue-500 cursor-pointer transition-transform transform hover:scale-110';

  // 章节信息
  const chapterMap = ref<Record<string, any>>({});
  const currentChapter = ref();
  const currentChapterInfo = ref();

  const teacherLesson = ref<Record<string, any>>({}); // 初始化为空对象

  // 加载教学课程  || 这里需要注意，加载的章节没有按照学期来查找，可能导致相同的位置存在之前的老教案 导致，本学期没有提交教案但是依然能看到之前的教案（数据串了..）
  const loadTeachingLesson = async (range?: string[]) => {
    const { data: res } = await request('/course/chapter/getChapterListBySchedule', {
      method: 'put',
      params: {
        cbIds: [props.record?.teacher.id],
        classDate: range,
        period: props.period,
      },
    });
    res.list?.forEach((item) => {
      chapterMap.value[item.classDate] = item;
    });
  };

  // 格式化日期
  const formatDate = (date?: Date, term?: number, isSpring?: boolean): string | string[] => {
    if (term && isSpring !== undefined) {
      if (isSpring) {
        return [`${term}-03-01`, `${term}-08-31`];
      }
      const nextYear = term + 1;
      const febDay = (nextYear % 4 === 0 && nextYear % 100 !== 0) || nextYear % 400 === 0 ? '29' : '28';
      return [`${term}-09-01`, `${nextYear}-02-${febDay}`];
    }
    if (!date) throw new Error('日期参数不能为空');
    date = new Date(date);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 生成日期范围内的所有日期
  const generateDateRange = (startDate: string, endDate: string): string[] => {
    const dates = [];
    const currentDate = new Date(startDate);
    const lastDate = new Date(endDate);

    while (currentDate <= lastDate) {
      dates.push(formatDate(currentDate)); // 将日期格式化为 YYYY-MM-DD
      currentDate.setDate(currentDate.getDate() + 1); // 增加一天
    }

    return dates;
  };

  // 获取背景样式
  const getBgStyle = (index: number, dayIndex: number, info: any) => {
    const styles = [];
    if (
      index === props.lessonCount.morning + props.lessonCount.afternoon - 1 &&
      dayIndex === paginatedDateRange.value.length - 1
    ) {
      styles.push('rounded-br-lg');
    }
    const lessonInfo = teacherLesson.value[`${dayIndex - 1}_${index}`];
    // 背景颜色样式
    if (info.length > 0) {
      styles.push('bg-green-50');
    } else if (lessonInfo) {
      styles.push('bg-blue-50');
    } else {
      styles.push('bg-red-50');
    }
    return styles.join(' ');
  };

  // 补全 paginatedDateRange 到 7 天
  const fillPaginatedDateRange = (dates: string[]): string[] => {
    const filledDates = [...dates]; // 复制原始数据
    while (filledDates.length < pageSize) {
      filledDates.push(''); // 补充空字符串
    }
    return filledDates;
  };

  // 上一页
  const prevPage = () => {
    if (currentPage.value > 0) {
      currentPage.value -= 1;
      const start = currentPage.value * pageSize;
      const end = (currentPage.value + 1) * pageSize;
      paginatedDateRange.value = fillPaginatedDateRange(semesterDateRange.value.slice(start, end));
    }
  };

  // 下一页
  const nextPage = () => {
    if ((currentPage.value + 1) * pageSize < semesterDateRange.value.length) {
      currentPage.value += 1;
      const start = currentPage.value * pageSize;
      const end = (currentPage.value + 1) * pageSize;
      paginatedDateRange.value = fillPaginatedDateRange(semesterDateRange.value.slice(start, end));
    }
  };

  const schoolCourseStore = useSchoolCourseStore();

  const lessonsMap = ref();
  const getLessons = async () => {
    lessonsMap.value = await schoolCourseStore.getSchoolCoursesMap();
  };

  const filterTeacherLesson = () => {
    const result = props.currentTask?.assignments.filter((item) => item?.teacherId === props.record.id);
    teacherLesson.value = {};
    result.forEach((item) => {
      const key = `${item?.coordinate.x}_${item?.coordinate.y}`;
      teacherLesson.value[key] = item;
    });
  };

  const findLessonInfo = (date: Date, index: number): any => {
    const isMorning = index + 1 <= props.lessonCount.morning;
    const time = new Date(date);

    if (!Object.keys(teacherLesson.value).includes(`${time.getDay() - 1}_${index}`)) return [];
    const chapters = chapterMap.value[formatDate(date)]?.chapters || [];
    return (
      chapters.filter((item) => {
        if (isMorning) {
          // 还要排除在课表以外的课程
          return item?.lesson?.[0].includes(index);
        }
        return item?.lesson?.[1].includes(index);
      }) || []
    );
  };

  const lessonPlanVisible = ref(false);
  const currentDate = ref();
  const currentClassInfo = ref();
  const loading = ref(false);
  const course = ref();

  const handleViewTeachingPlan = (date: Date, info: any, chapter: any) => {
    currentDate.value = new Date(date);
    currentClassInfo.value = info;
    currentChapterInfo.value = chapter;
    lessonPlanVisible.value = true;
  };

  const loadChapterDetails = async () => {
    loading.value = true;
    try {
      const { data: courseData } = await request(`/course/course/${currentChapterInfo.value.courseId}`);
      course.value = courseData;
      const { data } = await getChapter({
        id: currentChapterInfo.value.id,
      });
      currentChapterInfo.value = data;
    } finally {
      loading.value = false;
    }
  };

  const ready = ref(false);

  // 时间范围选择
  const dateRange = ref<[string, string]>(); // 用户选择的时间范围

  const handleDateRangeChange = (dates: [string, string]) => {
    if (dates && dates[0] && dates[1]) {
      // 更新 semesterDateRange
      semesterDateRange.value = generateDateRange(dates[0], dates[1]);
      // 重置当前页码
      currentPage.value = 0;
      // 更新 paginatedDateRange
      paginatedDateRange.value = fillPaginatedDateRange(semesterDateRange.value.slice(0, pageSize));
      // 重新加载数据
      loadTeachingLesson([dates[0], dates[1]]);
      filterTeacherLesson();
    }
  };
  const semesterStartDate = ref<Date>();
  const semesterEndDate = ref<Date>();
  const initTable = async (block?: boolean) => {
    const year = Number(props.period.split('年')[0]);
    const isSpring = props.period.includes('春');
    const range = formatDate(null, year, isSpring) as string[];
    if (block) {
      semesterStartDate.value = new Date(range[0]);
      semesterEndDate.value = new Date(range[1]);
    }
    semesterDateRange.value = generateDateRange(range[0], range[1]);
    currentPage.value = 0;
    paginatedDateRange.value = fillPaginatedDateRange(semesterDateRange.value.slice(0, pageSize));
    await loadTeachingLesson(range);
  };

  const disabledDate = (current: Date): boolean => {
    if (!semesterStartDate.value || !semesterEndDate.value) return false;
    if (current < semesterStartDate.value) return true;
    if (current > semesterEndDate.value) return true;

    return false;
  };
  onMounted(async () => {
    if (props.currentTask) {
      await initTable(true);
      await getLessons();
      filterTeacherLesson();
      ready.value = true;
    }
  });
</script>

<template>
  <!--  {{ chapterMap }}-->
  <a-range-picker
    v-model:value="dateRange"
    class="ml-14"
    format="YYYY-MM-DD"
    size="mini"
    :disabled-date="disabledDate"
    @change="handleDateRangeChange"
    @clear="initTable"
  />
  <div v-if="ready && currentTask" class="flex justify-between items-center select-none">
    <icon-left
      class="mr-4"
      :class="[{ 'opacity-50 cursor-not-allowed': currentPage === 0 }, switchClass]"
      @click="prevPage"
    />
    <div class="overflow-hidden w-full">
      <div>
        <table class="table-fixed w-full border-collapse border text-center rounded-lg overflow-hidden">
          <thead>
            <tr class="bg-gray-200">
              <th class="border border-gray-300 w-1/6 py-2 rounded-tl-lg">
                <div class="px-4 items-center justify-center h-10 flex">
                  <span>时间</span>
                </div>
              </th>
              <th
                v-for="(date, index) in paginatedDateRange"
                :key="index"
                class="border border-gray-300 w-1/6"
                :class="index === paginatedDateRange.length - 1 ? 'rounded-tr-lg' : ''"
              >
                <div class="flex px-4 items-center h-10" :class="date ? 'justify-between' : 'justify-center'">
                  <span v-if="date" class="text-xs text-gray-400">{{ date }}</span>
                  <span v-else class="text-xs text-gray-400">无数据</span>
                  <span v-if="date" class="text-black">
                    {{ weekdays[new Date(date).getDay()] }}
                  </span>
                </div>
              </th>
            </tr>
          </thead>
          <tbody class="bg-gray-50">
            <tr v-for="(timeSlot, index) in lessonCount.morning + lessonCount.afternoon" :key="index">
              <td
                class="border border-gray-300 px-2 py-2 font-semibold h-20"
                :class="index === lessonCount.morning + lessonCount.afternoon - 1 ? 'rounded-bl-lg' : ''"
              >
                {{
                  index < lessonCount.morning ? `上午第${index + 1}节` : `下午第${index - lessonCount.morning + 1}节`
                }}
              </td>
              <td
                v-for="(date, dayIndex) in paginatedDateRange"
                :key="dayIndex"
                class="border border-gray-300 px-4 py-2 h-20 cursor-pointer hover:bg-sky-100"
                :class="getBgStyle(index, new Date(date).getDay(), findLessonInfo(date, index))"
                @click="() => {}"
              >
                <div v-if="date" class="w-full h-full flex justify-center items-center">
                  <div>
                    <div class="font-bold">
                      {{ lessonsMap[teacherLesson[`${new Date(date).getDay() - 1}_${index}`]?.lessonId]?.name }}
                    </div>

                    <div class="flex justify-center space-x-2 mt-2">
                      <span
                        v-for="(chapter, lessonIndex) in findLessonInfo(date, index)"
                        :key="lessonIndex"
                        class="hover:text-blue-600 text-xs text-gray-500"
                        @click="handleViewTeachingPlan(date, findLessonInfo(date, index), chapter)"
                      >
                        {{ chapter?.name }}
                      </span>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <icon-right
      class="ml-4"
      :class="[
        { 'opacity-50 cursor-not-allowed': (currentPage + 1) * pageSize >= semesterDateRange.length },
        switchClass,
      ]"
      @click="nextPage"
    />
  </div>
  <a-empty v-else description="暂时没有课程可查看" />

  <a-modal v-model:visible="lessonPlanVisible" fullscreen cancel-text="返回" @before-open="loadChapterDetails">
    <template #title>
      <div class="flex space-x-2 items-center">
        <span>
          {{ record.teacher?.name }}
        </span>
        <span v-if="currentDate" class="text-sm text-gray-500">
          {{ formatDate(currentDate) }}
        </span>
        <a-tag size="mini" color="green">{{ weekdays[currentDate?.getDay()] }}</a-tag>
      </div>
    </template>
    <a-spin v-if="lessonsMap" class="w-full" :loading="loading">
      <a-empty v-if="!currentChapterInfo?.id" />
      <div v-else class="flex-1 bg-slate-400 py-6 paper-wrapper shadow-inner p-4">
        <div :id="printAreaId" class="bg-white py-4 px-5 mx-auto page shadow-xl">
          <a-descriptions v-if="course?.id" title="课程信息" :column="4">
            <a-descriptions-item label="教师">{{ course.createdBy.name }}</a-descriptions-item>
            <a-descriptions-item label="科目">{{ lessonsMap[course.category]?.name }}</a-descriptions-item>
            <a-descriptions-item label="年级">{{ course.grade }} {{ course.period }}</a-descriptions-item>
            <a-descriptions-item label="教材版本">{{ course.description }}</a-descriptions-item>
            <a-descriptions-item label="上课时间">{{ currentChapter?.classTime || '-' }}</a-descriptions-item>
            <a-descriptions-item v-if="currentChapter?.content?.lessonPrepareAttachments?.length" label="课件">
              <attachments-preview-display :raw="currentChapter?.content?.lessonPrepareAttachments" />
            </a-descriptions-item>
            <a-descriptions-item label="引用教材说明">{{ currentChapter?.description || '-' }}</a-descriptions-item>
          </a-descriptions>
          <a-divider :margin="10" />
          <a-spin class="w-full p-2">
            <teaching-archive-detail
              v-if="currentChapterInfo && currentChapterInfo.content"
              :chapter="currentChapterInfo"
              :show-assessment="false"
              annotation-module=""
              current-directory=""
            />
          </a-spin>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<style scoped>
  body {
  }
</style>
