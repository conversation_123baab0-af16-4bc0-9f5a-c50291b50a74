<script setup lang="ts">
  import { PROJECT_URLS } from '@repo/env-config';
  import { useRoute } from 'vue-router';
  import { useUserMenuStore } from '@repo/infrastructure/store';

  const route = useRoute();
  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo(route);

  let orgNature = `?orgNature=${menuInfo?.app?.label}`;
  if (!menuInfo?.app || menuInfo.app?.key === 'system') {
    orgNature = '';
  }
  const frameSrc = `${PROJECT_URLS.MAIN_PROJECT}/centerManage.html#/organization/user${orgNature}`;
</script>

<template>
  <iframe :src="frameSrc" class="frame" allow="geolocation" />
</template>

<style scoped lang="less">
  .frame {
    width: 100%;
    height: calc(100vh - 50px);
    border: none;
  }
</style>
