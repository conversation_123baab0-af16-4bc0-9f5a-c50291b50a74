<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { onMounted, ref, computed, watch } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useRoute, useRouter } from 'vue-router';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const schema = ref(null);

  const queryParams = {
    sort: '-id',
  };

  const columns = ref(['student', 'followUpDate', 'ended']);

  const route = useRoute();
  const router = useRouter();
  const tourGuide = ref<any>({});
  const ready = ref(false);

  const showTourGuide = computed(() => {
    return !route.query.tourGuideId;
  });

  const tableActionsProps = computed(() => {
    if (!route.query.tourGuideId) {
      return {
        visibleComponents: ['refresh', 'layout'],
      };
    }
    return {
      visibleComponents: undefined,
    };
  });

  const defaultEditValue = ref({});

  const loadTourGuide = async () => {
    if (!route.query.tourGuideId) return;
    const { data } = await request(`/resourceRoom/tourGuide/${route.query.tourGuideId}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    tourGuide.value = data;
  };

  const init = async () => {
    if (showTourGuide.value) {
      columns.value = ['tourGuide', ...columns.value];
    }
    if (showTourGuide.value) {
      columns.value = ['tourGuide', ...columns.value];
    }

    await loadTourGuide();

    if (route.query.tourGuideId && tourGuide.value) {
      defaultEditValue.value = {
        tourGuide: tourGuide.value,
      };
    }

    ready.value = true;
  };

  watch(route.query, async () => {
    ready.value = false;
    await init();
    setTimeout(() => {
      ready.value = true;
    }, 0);
  });

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/tourGuideFollowUp');
    await init();
  });
</script>

<template>
  <table-with-modal-form
    v-if="schema && ready"
    module-name="回访记录"
    :schema="schema"
    :table-actions-property="tableActionsProps"
    :default-query-params="queryParams"
    :default-edit-value="defaultEditValue"
    :visible-columns="columns"
  >
    <template #title>
      <div v-if="route.query.tourGuideId" class="flex-1 flex items-center gap-2">
        <a-button size="mini" @click="router.back()">
          <template #icon>
            <IconArrowLeft />
          </template>
          返回
        </a-button>
        <div v-if="route.query.tourGuideId">
          <span>回访记录 - {{ tourGuide?.subject }}</span>
        </div>
        <div v-else>回访记录</div>
      </div>
    </template>
  </table-with-modal-form>
</template>

<style scoped lang="scss"></style>
