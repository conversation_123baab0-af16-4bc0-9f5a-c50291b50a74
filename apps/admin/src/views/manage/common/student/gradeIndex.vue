<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { getPeriodsList } from '@repo/infrastructure/utils/scaffolds';
  import { getAdminClientNature } from '@repo/components/utils/utils';

  const schema = ref(null);
  const { loading, setLoading } = useLoading();
  const queryParams = {
    sort: '-enrollmentYear',
    orgNature: getAdminClientNature(),
  };
  const periodMaintainVisible = ref(false);
  const currentRow = ref(null);
  const periods = ref([]);
  const periodsNames = getPeriodsList();
  const tableRef = ref(null);

  const handleRowAction = (action: Record<string, any>, record: any) => {
    if (action.key === 'periods') {
      currentRow.value = record;
      periods.value = [...(record.gradePeriods || [])];
      periodMaintainVisible.value = true;
    }
  };

  const checkPeriods = () => {
    for (let i = 0; i < periods.value.length; i += 1) {
      const period = periods.value[i];
      if (!period.name || !period.startTime || !period.endTime) {
        Message.error(`第 ${i + 1} 个学期信息不完整`);
        return false;
      }

      if (period.startTime >= period.endTime) {
        Message.error(`第 ${i + 1} 个学期的开始时间不能大于结束时间`);
        return false;
      }

      if (periods.value.slice(i + 1).some((item) => item.startTime < period.endTime)) {
        Message.error(`第 ${i + 1} 个学期的结束时间不能大于下一个学期的开始时间`);
        return false;
      }

      if (periods.value.slice(0, i).some((item) => item.endTime > period.startTime)) {
        Message.error(`第 ${i + 1} 个学期的开始时间不能小于上一个学期的结束时间`);
        return false;
      }
    }
    return true;
  };

  const handleSubmitPeriods = async () => {
    try {
      setLoading(true);
      if (!checkPeriods()) {
        setLoading(false);
        return false;
      }
      await request(`/teacher/schoolGrade/${currentRow.value.id}`, {
        method: 'PUT',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: {
          ...currentRow.value,
          gradePeriods: periods.value,
        },
      });

      Message.success('保存学期成功');
      tableRef.value.handleLoadData();
      return true;
    } catch (e) {
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handleAddPeriod = () => {
    periods.value.push({
      name: '',
      startTime: null,
      endTime: null,
    });
  };

  const handleRemovePeriod = (index: number) => {
    periods.value.splice(index, 1);
  };

  const handleCloseModal = () => {
    periodMaintainVisible.value = false;
    currentRow.value = null;
    periods.value = [];
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/teacher/schoolGrade');
  });
</script>

<template>
  <div>
    <table-with-modal-form
      v-if="schema"
      ref="tableRef"
      module-name="学校年级管理"
      :schema="schema"
      :default-query-params="queryParams"
      :visible-columns="[
        'school',
        'name',
        'currentPeriod',
        'enrollmentYear',
        'graduateYear',
        'gradeName',
        'graduate',
        'sort',
        'remark',
      ]"
      @row-action="handleRowAction"
    />
    <a-modal
      v-model:visible="periodMaintainVisible"
      :title="`${currentRow?.name} - 学期管理`"
      :destroy-on-close="true"
      width="60%"
      :ok-loading="loading"
      :on-before-ok="handleSubmitPeriods"
      @close="handleCloseModal"
    >
      <a-button size="mini" type="outline" @click="handleAddPeriod">
        <template #icon>
          <IconPlus />
        </template>
        添加学期
      </a-button>
      <a-table class="mt-2" size="mini" :data="periods" :pagination="false">
        <template #columns>
          <a-table-column title="学期名称" :width="180">
            <template #cell="{ record }">
              <a-select v-model="record.name" :options="periodsNames" />
            </template>
          </a-table-column>
          <a-table-column title="开始时间">
            <template #cell="{ record }">
              <a-date-picker v-model="record.startTime" size="mini" />
            </template>
          </a-table-column>
          <a-table-column title="结束时间">
            <template #cell="{ record }">
              <a-date-picker v-model="record.endTime" size="mini" />
            </template>
          </a-table-column>
          <a-table-column title="是否假期">
            <template #cell="{ record }">
              <a-switch v-model="record.holiday" size="mini" />
            </template>
          </a-table-column>
          <a-table-column title="操作">
            <template #cell="{ rowIndex }">
              <a-button size="mini" type="outline" @click="() => handleRemovePeriod(rowIndex)">
                <template #icon>
                  <IconDelete />
                </template>
                删除
              </a-button>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<style scoped lang="scss"></style>
