<script setup lang="ts">
  import { QuestionEdit } from '@repo/components/question-library';
  import { useRoute, useRouter } from 'vue-router';
  import { useUserMenuStore } from '@repo/infrastructure/store';

  const router = useRouter();
  const route = useRoute();
  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo(route);

  const { id } = route.query;

  const handleBack = () => {
    router.back();
  };
</script>

<template>
  <question-edit :default-edit-data="{ orgNature: menuInfo.app.label }" :edit-id="id" @go-back="handleBack" />
</template>

<style scoped lang="scss"></style>
