<script setup lang="ts">
  import DefaultLayout from '@/common/layout/default-layout.vue';
  import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router';
  import { useUserMenuStore, useUserStore } from '@repo/infrastructure/store';
  import { computed, onMounted, ref } from 'vue';
  import AppTopNav from '@/views/manage/components/appTopNav.vue';

  const route = useRoute();
  const router = useRouter();
  const menuStore = useUserMenuStore();
  const menuInfo = ref<any>(menuStore.getCurrentMenuInfo(route));

  const activeSubMenu = ref((menuInfo.value.module?.key as string) || '');

  const checkModuleIndex = (r) => {
    menuInfo.value = menuStore.getCurrentMenuInfo(r);
    if (!menuInfo.value.module && menuInfo.value.app?.children?.length) {
      router.push(menuInfo.value.app.children[0]?.link as string);
    } else if (!menuInfo.value.subModules?.length && menuInfo.value.module?.children?.length > 0) {
      router.push(menuInfo.value.module.children[0].link as string);
    }
  };

  onBeforeRouteUpdate((to) => {
    checkModuleIndex(to);
    activeSubMenu.value = (menuInfo.value.module?.key as string) || '';
  });
  const userStore = useUserStore();

  const siteTitle = computed(() => {
    return userStore?.userInfo?.branchOffice?.name;
  });
  onMounted(() => {
    checkModuleIndex(route);
  });
</script>

<template>
  <default-layout>
    <template #page-title>{{ menuInfo?.app?.label }}</template>
    <template #navbar-center>
      <app-top-nav v-model:active="activeSubMenu" :tabs="menuInfo?.app?.children" />
    </template>
    <div class="main-wrapper">
      <router-view />
    </div>
  </default-layout>
</template>

<style scoped lang="less">
  .main-wrapper {
    min-height: calc(100vh - 60px);
  }
  :deep {
    .arco-card-size-small .arco-card-header {
      height: auto;
      min-height: 40px;
    }
  }
</style>
