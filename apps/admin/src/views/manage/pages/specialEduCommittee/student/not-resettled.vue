<script setup lang="ts">
  import { CrudTable, TableAction } from '@repo/ui/components/table';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { UNIT_NATURES_MAP } from '@repo/infrastructure/constants';
  import { usePrompt } from '@repo/ui/components';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import SetNonResettlementStudentAssessExpertsModal from '@/views/manage/components/student/setNonResettlementStudentAssessExpertsModal.vue';
  import { useTeacherStore } from '@repo/infrastructure/store';
  import NonResettlementDetailModal from '@/views/manage/components/student/nonResettlementDetailModal.vue';
  import studentSchema from '@repo/components/schemas/student.ts';
  import StudentResettleModal from '@/views/manage/components/student/studentResettleModal.vue';
  import { isArray } from 'lodash';
  import dayjs from 'dayjs';
  import { Modal, Message } from '@arco-design/web-vue';
  import BatchIntroductionModal from '@/views/manage/components/specialEduCommittee/batchIntroductionModal.vue';
  import ImportStudentModal from '@/views/manage/components/specialEduCommittee/importStudentModal.vue';
  import StudentEditPage from '@repo/components/student/studentEditPage.vue';
  import { getOrgNature } from '@repo/components/utils/utils';

  const tableRef = ref<any>(null);
  const schema = ref<any>();
  const assessmentExpertSelectVisible = ref(false);
  const detailVisible = ref(false);
  const resettleVisible = ref(false);
  const batchIntroduction = ref(false);
  const importStudentModalVisible = ref(false);
  const currentRow = ref(null);
  const { prompt } = usePrompt();
  const teacherStore = useTeacherStore();
  const currentIds = ref([]);

  const allTeachers = ref([]);

  const queryParams = {
    status: 'WaitingResettlement',
    orgNature: UNIT_NATURES_MAP.SpecialEduCommittee,
  };

  const visibleColumns = [
    'symbol',
    'name',
    'gender',
    'age',
    'birthday',
    'entranceType',
    'fusionSchool',
    'assessmentDate',
    'assessmentExperts',
    'resettleAssessmentFinished',
    'guardianConfirmedResettlement',
    'status',
  ];

  const handleSetAssessmentDate = async (record: any) => {
    const ids = isArray(record) ? record : [record.id];
    if (!ids.length) {
      return;
    }

    const date = await prompt({
      title: '设置评估时间',
      placeholder: '请选择评估时间',
      inputWidget: 'date',
    });

    try {
      tableRef.value.setLoading(true);
      await request({
        url: `/resourceRoom/student/setResettleAssessmentDate`,
        method: 'PUT',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: {
          ids,
          assessmentDate: date,
        },
      });

      await tableRef.value.loadData();
    } finally {
      tableRef.value.setLoading(false);
    }
  };

  const handleSetAssessmentExperts = async (record: any) => {
    const ids = isArray(record) ? record : [record.id];
    if (!ids.length) {
      return;
    }
    currentRow.value = record;
    assessmentExpertSelectVisible.value = true;
    currentIds.value = ids;
  };

  const handleShowResettle = (record: any) => {
    currentRow.value = record;
    resettleVisible.value = true;
  };

  const handleShowDetail = (record: any) => {
    currentRow.value = record;
    detailVisible.value = true;
  };

  const handleRefresh = async () => {
    await tableRef.value.loadData();
  };

  const handleExport = async (record: any, action: any) => {
    let studentIds = [];
    if (Array.isArray(record)) {
      studentIds = record;
    } else {
      studentIds.push(record?.id);
    }
    try {
      Message.info('正在导出，请稍后在右上角导出结果中查看');
      await request({
        url: `/resourceRoom/student/exportPlacementStudentInfo`,
        method: 'PUT',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          orgNature: getOrgNature(),
        },
        data: studentIds,
      });
    } catch (e) {
      console.error(e);
    }
  };
  const handleRowActions = async (action: any, record: any) => {
    switch (action.key) {
      case 'exportInfo':
        await handleExport(record, action);
        break;
      default:
        break;
    }
  };

  const handleConfirm = (record: any) => {
    Modal.confirm({
      title: '家长确认',
      content: '是否确认',
      onOk: async () => {
        record.additionalData.guardianConfirmedResettlement = true;
        await request(`/student/enrollResettlement/${record.id}/guardianConfirm`, {
          method: 'put',
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        });
      },
    });
  };

  onMounted(async () => {
    [allTeachers.value, schema.value] = await Promise.all([
      teacherStore.getTeachersList(),
      SchemaHelper.getInstanceByDs('/resourceRoom/student', {
        fieldsOverride: {
          hasSendPlan: {
            visibleInForm: false,
            visibleInTable: false,
            visibleInDetail: false,
          },
          entranceType: {
            visibleInTable: true,
            visibleInDetail: true,
          },
          fusionSchool: {
            label: '请求安置单位',
          },
          assessmentDate: {
            key: 'assessmentDate',
            label: '评估时间',
            listProps: {
              columnWidth: 120,
            },
            displayProps: {
              toDisplay: (value: string, record: any) => {
                return record.additionalData?.assessmentDate
                  ? dayjs(record.additionalData?.assessmentDate).format('YYYY-MM-DD')
                  : '';
              },
            },
          },
          assessmentExperts: {
            key: 'assessmentExperts',
            label: '评估专家',
            listProps: {
              columnWidth: 150,
            },
            displayProps: {
              toDisplay: (value: string, record: any) => {
                const lists = allTeachers.value.filter((item) => {
                  return record.additionalData?.assessmentExperts?.includes(item.id);
                });

                return lists.map((item) => item.name).join('、');
              },
            },
          },
          resettleAssessmentFinished: {
            key: 'resettleAssessmentFinished',
            label: '评估完成',
            listProps: {
              columnWidth: 90,
            },
            displayProps: {
              toDisplay: (value: any, record: any) => {
                return record.additionalData?.resettleAssessmentFinished ? '是' : '未评估';
              },
            },
          },
          guardianConfirmedResettlement: {
            key: 'guardianConfirmedResettlement',
            label: '家长确认',
            listProps: {
              columnWidth: 90,
            },
            displayProps: {
              toDisplay: (value: any, record: any) => {
                return record.additionalData?.guardianConfirmedResettlement ? '是' : '未确认';
              },
            },
          },
        },
        schemaOverride: {
          listViewProps: {
            rowActionWidth: 140,
          },
          importable: {
            enabled: true,
            template: 'https://tejiao-prod-static1.oss-cn-chengdu.aliyuncs.com/templates/v2学生导入模板.xlsx',
          },
          rowActions: [
            // { key: 'view', visible: false },
            ...(studentSchema.studentSchema.rowActions?.map((item) => {
              return {
                key: item.key,
                visible: false,
              };
            }) || []),
            { key: 'view', visible: false },
            { key: 'edit', visible: false },
            // { key: 'editInfo', label: '修改', icon: 'icon-edit', expose: false, handler: handleShowDetail },
            { key: 'viewDetail', label: '查看', expose: true, handler: handleShowDetail },
            {
              key: 'setAssessTime',
              multiple: true,
              label: '评估时间',
              expose: false,
              disabled(record) {
                return record.additionalData?.guardianConfirmedResettlement;
              },
              handler: handleSetAssessmentDate,
            },
            {
              key: 'setAssessExperts',
              multiple: false,
              label: '评估专家',
              expose: false,
              disabled(record) {
                return record.additionalData?.guardianConfirmedResettlement;
              },
              handler: handleSetAssessmentExperts,
            },
            {
              key: 'sms',
              label: '短信通知',
              multiple: true,
              expose: false,
              permNode: 'specialEduCommittee:student:resettle',
              disabled(record: any) {
                return true;
              },
              handler: handleShowResettle,
            },
            {
              key: 'resettle',
              label: '安置',
              expose: false,
              permNode: 'specialEduCommittee:student:resettle',
              // disabled(record: any) {
              //   return !record.additionalData?.guardianConfirmedResettlement;
              // },
              handler: handleShowResettle,
            },
            {
              key: 'exportInfo',
              label: '导出',
              icon: 'icon-export',
              multiple: true,
              handler: handleExport,
              visible: true,
            },
          ],
        } as any,
      }),
    ]);
  });
</script>

<template>
  <a-card v-if="schema">
    <template #title>
      <table-action
        v-if="tableRef"
        class="flex-1"
        :schema="schema"
        :visible-components="['refresh', 'search', 'recycleBin', 'quickSearch']"
        :table="tableRef"
        component-size="mini"
        :show-layout-manage="false"
      >
        <template #title>
          <slot name="title">待安置学生信息库</slot>
        </template>
        <template #extra-actions>
          <a-tooltip content="批量引入系统中的学生">
            <a-button size="mini" @click="batchIntroduction = true"> 批量引入学生</a-button>
          </a-tooltip>
          <a-tooltip content="导入待安置学生">
            <a-button v-if="false" size="mini" @click="importStudentModalVisible = true">导入学生</a-button>
          </a-tooltip>
        </template>
      </table-action>
      <slot name="table-action-append"></slot>
    </template>
    <crud-table
      ref="tableRef"
      :schema="schema"
      size="mini"
      :default-query-params="queryParams"
      :visible-columns="visibleColumns"
      module-path="/manage/specialEduCommittee/student"
      @row-action="handleRowActions"
    >
      <template #custom-column-guardianConfirmedResettlement="{ record }">
        <div class="cursor-pointer hover:text-gray-500" @click="handleConfirm(record)">
          <a-popover content="点击家长确认">
            <span :style="{ color: record.additionalData?.guardianConfirmedResettlement ? 'green' : 'red' }">
              {{ record.additionalData?.guardianConfirmedResettlement ? '是' : '未确认' }}
            </span>
          </a-popover>
        </div>
      </template>
    </crud-table>

    <set-non-resettlement-student-assess-experts-modal
      v-if="assessmentExpertSelectVisible"
      v-model:visible="assessmentExpertSelectVisible"
      :ids="currentIds"
      :row="currentRow"
      @ok="() => tableRef.loadData()"
    />

    <non-resettlement-detail-modal v-model:visible="detailVisible" :schema="schema" :row="currentRow" />
    <student-resettle-modal v-model:visible="resettleVisible" :student="currentRow" @ok="handleRefresh" />
    <batchIntroductionModal v-if="batchIntroduction" v-model:visible="batchIntroduction" />
  </a-card>
</template>

<style scoped lang="scss"></style>
