<script setup lang="ts">
  import { randomColor } from '@repo/ui/components/utils/randomColor';

  const baseModules = [
    {
      key: 'total',
      label: '总数',
    },
    {
      key: 'finished',
      label: '已完成',
    },
    {
      key: 'progress',
      label: '待完成',
    },
  ];

  const modules = [
    {
      label: '教康评估',
      key: 'evaluateAssessment',
      description: '教康评估',
      children: [...baseModules],
    },
    {
      label: '安置评估',
      key: 'placementAssessment',
      description: '安置评估',
      children: [...baseModules],
    },
    {
      label: '安置报告',
      key: 'placementReport',
      description: '安置报告',
      children: [...baseModules],
    },
  ];

  const filterColumns = [];
</script>

<template>
  <div v-if="false" class="w-screen flex justify-start space-x-4 items-center p-2">
    <div v-for="(item, index) in modules" :key="index" class="w-[350px] h-[150px] bg-white rounded-lg px-2 py-1 shadow">
      <div class="font-bold text-gray-500 flex justify-between">
        {{ item.label }}
        <a-tooltip :content="item?.description">
          <icon-question-circle class="mt-1 mr-2 cursor-pointer" />
        </a-tooltip>
      </div>
      <hr class="mb-2 mt-2" />
      <div class="flex mt-4 justify-start space-x-6 cursor-pointer pl-2">
        <div
          v-for="(child, cIndex) in item.children"
          :key="cIndex"
          class="flex items-center justify-center flex-col hover:border-violet-500 border-b-4 border-white"
        >
          <div class="font-bold text-2xl" :style="{ color: randomColor() }">
            {{ Math.floor(Math.random() * 200) }}
          </div>
          <div class="text-xs mt-1 text-gray-500 pb-1">
            {{ child.label }}
          </div>
        </div>
      </div>
      <a-progress
        class="mt-4"
        :percent="0.8"
        :style="{ width: '80%' }"
        :color="{
          '0%': randomColor(11),
          '100%': randomColor(19),
        }"
      />
    </div>
  </div>
  <a-divider v-if="false" />
  <div v-if="false" class="w-screen flex justify-start space-x-4 items-center m-2 rounded-lg overflow-hidden">
    <a-table class="w-full" :columns="filterColumns"> </a-table>
  </div>
  <a-empty description="coding..." />
</template>

<style scoped lang="scss"></style>
