<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import RehabilitationPlanDetail from '@repo/components/institution/rehabilitationPlanDetail.vue';

  const schema = ref(null);

  const queryParams = {
    sort: '-id',
  };

  const currentRow = ref(null);
  const detailVisible = ref(false);

  const handleRowAction = (action, row) => {
    if (action.key === 'detail') {
      currentRow.value = row;
      detailVisible.value = true;
    }
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/rehabilitation/rehabilitationPlan');
  });
</script>

<template>
  <table-with-modal-form
    v-if="schema"
    module-name="康复计划"
    :schema="schema"
    :default-query-params="queryParams"
    :table-actions-property="{ visibleComponents: ['refresh', 'quickSearch'] }"
    :visible-columns="['institution', 'employee', 'student', 'dateRange']"
    @row-action="handleRowAction"
  />

  <rehabilitation-plan-detail v-if="detailVisible" v-model:visible="detailVisible" :plan="currentRow" />
</template>

<style scoped lang="scss"></style>
