<script setup lang="ts">
  import { useUserStore } from '@repo/infrastructure/store';
  import { onMounted, ref } from 'vue';
  import { Modal } from '@arco-design/web-vue';

  const userStore = useUserStore();
  const { userInfo } = userStore;
  const ready = ref(false);

  onMounted(() => {
    const nature = userInfo?.branchOffice?.school?.nature;
    if (nature !== '机构') {
      Modal.error({
        title: '提示',
        content: '您没有权限访问该页面，只有机构管理员可对机构信息进行维护',
        hideCancel: true,
        maskClosable: false,
        closable: false,
        escToClose: false,
        onOk: () => {
          // @ts-ignore
          window.history.back();
        },
      });
    } else {
      ready.value = true;
    }
  });
</script>

<template>
  <div v-if="ready">profile</div>
</template>

<style scoped lang="scss"></style>
