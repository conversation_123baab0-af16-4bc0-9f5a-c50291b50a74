<!--<script setup lang="ts">-->
<!--  import { useRoute } from 'vue-router';-->
<!--  import { PROJECT_URLS } from '@repo/env-config';-->

<!--  const route = useRoute();-->
<!--  const { id } = route.query;-->

<!--  const frameSrc = `${PROJECT_URLS.MAIN_PROJECT}/centerManage.html#/evaluation/customCriterion/detail?id=${id}`;-->
<!--</script>-->

<!--<template>-->
<!--  <iframe :src="frameSrc" class="frame" allow="geolocation" />-->
<!--</template>-->

<!--<style scoped lang="less">-->
<!--  .frame {-->
<!--    width: 100%;-->
<!--    height: calc(100vh - 50px);-->
<!--    border: none;-->
<!--  }-->
<!--</style>-->
<script setup lang="ts">
  import { useRoute, useRouter } from 'vue-router';
  import { PROJECT_URLS } from '@repo/env-config';
  import { onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';

  const route = useRoute();
  const { id } = route.query;

  const criterionList = ref([]);
  const selectedValue = ref([]);
  const handleGradeClassChange = (val: number) => {};

  const loadCriterion = async () => {
    try {
      const { data: res } = await request(`/evaluation/customCriterionDetail/getTree/${id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      criterionList.value = res;
    } finally {
      /**/
    }
  };

  const currentNode = ref(null);

  const handleEditNode = (node: any) => {
    currentNode.value = node;
  };
  const handleSubmit = async () => {
    try {
      await request(`/evaluation/customCriterionDetail/${currentNode.value.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'put',
        data: { ...currentNode.value, customCriterion: { id } },
      });
      Message.success('保存成功');
    } finally {
      /**/
    }
  };
  const router = useRouter();
  const handleBack = () => {
    router.go(-1);
  };
  onMounted(async () => {
    await loadCriterion();
  });
</script>

<template>
  <a-card class="w-full h-full">
    <template #extra>
      <a-button size="mini" @click="handleBack">返回</a-button>
    </template>
    <a-split class="w-full h-full" :min="0.1" :max="0.9">
      <template #first>
        <div class="h-dvh overflow-y-auto border-r border-gray-200">
          <a-tree
            v-if="criterionList.length > 0"
            v-model:selected-keys="selectedValue"
            :data="criterionList"
            show-line
            :field-names="{ key: 'id', title: 'name', children: 'children' }"
            :selectable="true"
            class="select-none"
            @select="handleGradeClassChange"
          >
            <template #title="node">
              <div class="rounded hover:bg-gray-100 cursor-pointer transition-colors" @click="handleEditNode(node)">
                {{ node.name || node.title }}
              </div>
            </template>
          </a-tree>
        </div>
      </template>

      <template #second>
        <div v-if="currentNode" class="w-full px-6 py-2 bg-white mr-2">
          <div class="text-lg font-semibold mb-4">名称</div>
          <a-textarea v-model="currentNode.name" class="w-full mb-6 rounded border border-gray-200 p-2" />
          <div class="text-lg font-semibold mb-4">注释</div>
          <a-textarea
            v-model="currentNode.notes"
            class="w-full mb-6 min-h-[120px] rounded border border-gray-200 p-2"
          />
          <a-button
            size="mini"
            type="primary"
            class="rounded px-4 py-2 bg-blue-500 text-white hover:bg-blue-600 transition-colors"
            @click="handleSubmit"
          >
            保存
          </a-button>
        </div>
        <a-empty v-else description="请先选择编辑项" />
      </template>
    </a-split>
  </a-card>
</template>

<style scoped lang="less">
  .frame {
    width: 100%;
    height: calc(100vh - 50px);
    border: none;
  }
</style>
