<script setup lang="ts">
  import sendStatistics from '@repo/components/d2dEduShare/sendStatistics.vue';
  import sendStatisticsSimple from '@repo/components/d2dEduShare/sendStatisticsSimple.vue';
  import { onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const configure = ref<any>({});
  const loadData = async () => {
    const { data } = await request('/resourceRoom/centralConfiguration/findByCompany', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'GET',
    });

    configure.value = data;
  };
  onMounted(async () => {
    await loadData();
  });
</script>

<template>
  <!--文档模式-->
  <sendStatistics v-if="configure?.sendVersion === 2" />
  <!--简单模式 -> 自定义+默认-->
  <send-statistics-simple v-else />
  <!--  <a-empty description="模块升级 ing..." class="items-center justify-center" />-->
</template>

<style scoped lang="scss"></style>
