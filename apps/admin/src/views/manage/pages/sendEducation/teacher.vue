<script setup lang="ts">
  import UserManage from '@repo/components/org/userManage.vue';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { ref, computed } from 'vue';
  import { UNIT_NATURES_OPTIONS } from '@repo/infrastructure/constants';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const menuStore = useUserMenuStore();

  const userManageRef = ref(null);
  const queryParams = ref({
    // nature: menuStore.getCurrentOrgNature(),
    sendTeacher: true,
  });
  const importVisible = ref(false);
  const exceptUnit = ['Center', 'SendEducation', 'SpecialEduCommittee', 'DisabledPersonsFederation'];
  const importQueryParams = ref({});

  const boCatch = ref<any>({});
  const teacherCatch = ref<any>({});
  const selectedTeachers = ref<any[]>([]);
  const teacherIds = ref<number[]>([]);
  const teacherSearching = ref(false);
  const handleNatureChange = async (e) => {
    if (!boCatch.value?.[e] && e)
      try {
        const { data: res } = await request('/org/branchOffice/simpleList', {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          params: {
            nature: e,
          },
        });
        boCatch.value[e] = res;
      } finally {
        /**/
      }
  };

  const handleBoChange = async (e) => {
    if (!teacherCatch.value?.[e] && e)
      try {
        teacherSearching.value = true;
        const { data: res } = await request(`/org/companyUser/byBranchOffice/${e}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        });
        teacherCatch.value[e] = res;
      } finally {
        /**/
        teacherSearching.value = false;
      }
  };
  const handleSelectTeacher = (node: any) => {
    if (selectedTeachers.value.map((s) => s.id).includes(node.id)) {
      selectedTeachers.value = selectedTeachers.value.filter((s) => s.id !== node.id);
    } else selectedTeachers.value.push(node);
  };

  const handleClose = (t: any) => {
    selectedTeachers.value = selectedTeachers.value.filter((s) => s.id !== t.id);
    teacherIds.value = teacherIds.value.filter((id) => id !== t.id);
  };

  const reset = () => {
    boCatch.value = {};
    teacherCatch.value = {};
    importQueryParams.value = {};
  };
  const handlePreOk = async () => {
    try {
      await request(`/org/companyUser/batchSetSendTeacher`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'put',
        data: teacherIds.value.map((s) => Number(s)),
      });
      reset();
      userManageRef.value?.refresh();
    } finally {
      /**/
    }
  };
  defineExpose({
    reset,
  });
</script>

<template>
  <user-manage ref="userManageRef" :visible-component="[]" module-name="特校教师管理" :query-params="queryParams">
    <template #supplementary-button>
      <a-button size="mini" status="success" type="primary" @click="importVisible = true">
        <template #icon>
          <icon-import />
        </template>
        导入
      </a-button>
    </template>
  </user-manage>
  <a-modal v-model:visible="importVisible" title="导入送教教师" :on-before-ok="handlePreOk">
    <a-form auto-label-width>
      <a-form-item label="请选择单位类型">
        <a-select
          v-model="importQueryParams.nature"
          size="mini"
          :field-names="{ label: 'label', value: 'label' }"
          :options="UNIT_NATURES_OPTIONS.filter((u) => !exceptUnit.includes(u.value))"
          @change="handleNatureChange"
        />
      </a-form-item>
      <a-form-item label="请选择学校">
        <a-select
          v-model="importQueryParams.branchOffice"
          size="mini"
          allow-search
          placeholder=""
          :options="boCatch?.[importQueryParams?.nature] || []"
          :disabled="!importQueryParams.nature"
          :field-names="{ label: 'name', value: 'id' }"
          @change="handleBoChange"
        />
      </a-form-item>
      <a-form-item label="请选择教师">
        <a-select
          v-model="teacherIds"
          size="mini"
          :field-names="{ label: 'name', value: 'id' }"
          placeholder=""
          :disabled="!importQueryParams.branchOffice"
          :loading="teacherSearching"
          :max-tag-count="2"
          multiple
        >
          <a-option
            v-for="t in teacherCatch?.[importQueryParams?.branchOffice] || []"
            :key="t?.id"
            :value="t.id"
            @click="handleSelectTeacher({ id: t.id, name: t.name })"
          >
            {{ t.name }}
          </a-option>
        </a-select>
      </a-form-item>
    </a-form>
    <div class="rounded w-full">
      <div class="flex flex-wrap gap-2 items-center text-center">
        <a-tag v-for="i in selectedTeachers" :key="i" size="mini" closable color="blue" @close="handleClose(i)">
          {{ i?.name }}
        </a-tag>
      </div>
    </div>
  </a-modal>
</template>
