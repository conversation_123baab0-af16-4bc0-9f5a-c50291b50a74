<script setup lang="ts">
  import { useRoute, useRouter } from 'vue-router';
  import { PROJECT_URLS } from '@repo/env-config';
  import { onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { Message, Modal } from '@arco-design/web-vue';

  const route = useRoute();
  const { id } = route.query;

  const criterionList = ref([]);
  const selectedValue = ref([]);
  const handleGradeClassChange = (val: number) => {};

  const loadCriterion = async () => {
    try {
      const { data: res } = await request(`/evaluation/customCriterionDetail/getTree/${id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      criterionList.value = res;
    } finally {
      /**/
    }
  };

  const currentNode = ref(null);

  const handleEditNode = (node: any) => {
    currentNode.value = node;
  };
  const handleSubmit = async () => {
    try {
      await request(`/evaluation/customCriterionDetail/${currentNode.value.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'put',
        data: { ...currentNode.value, customCriterion: { id } },
      });
      Message.success('保存成功');
    } finally {
      /**/
    }
  };
  const router = useRouter();
  const handleBack = () => {
    router.go(-1);
  };
  const formData = ref({});
  const currentNodeData = ref(null);
  const addNodeVisible = ref(false);

  const handleAdd = (nodeData: any) => {
    console.log(nodeData);
    currentNodeData.value = nodeData;
    addNodeVisible.value = true;
  };

  const handlePreOk = async () => {
    if (!currentNodeData.value) {
      Message.warning('新增失败，请选择节点');
      return false;
    }
    try {
      const parentId = currentNodeData.value.id;
      const data = {
        parentId,
        name: formData.value.name,
        notes: formData.value.notes,
        customCriterion: {
          id,
        },
      };
      currentNodeData.value.children.push(data);
      await request('/evaluation/customCriterionDetail', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'post',
        data,
      });
      Message.success('新增成功');

      // currentNodeData.value = null;
      // formData.value = {};
    } finally {
      /**/
    }
    return true;
  };

  const removeNodeFromTree = (tree: any[], nodeId: string) => {
    for (let i = 0; i < tree.length; i += 1) {
      const node = tree[i];
      if (node.id === nodeId) {
        tree.splice(i, 1);
        return;
      }
      if (node.children && node.children.length > 0) {
        removeNodeFromTree(node.children, nodeId);
      }
    }
  };
  const handleDel = async (nodeData: any) => {
    Modal.confirm({
      title: '确认删除当前节点？',
      content: `内容：${nodeData.name}`,
      onOk: async () => {
        try {
          const idsToDelete: number[] = [];
          const collectAllIds = (node: any) => {
            idsToDelete.push(node.id);
            if (node.children?.length) {
              node.children.forEach((child: any) => collectAllIds(child));
            }
          };
          collectAllIds(nodeData);

          await request(`/evaluation/customCriterionDetail/deleteNote/${nodeData.id}`, {
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            method: 'delete',
            data: idsToDelete,
          });

          removeNodeFromTree(criterionList.value, nodeData.id);

          Message.success('删除成功');
        } catch (error) {
          Message.error('删除失败');
        }
      },
    });
  };

  onMounted(async () => {
    await loadCriterion();
  });
</script>

<template>
  <a-card class="w-full h-full">
    <template #extra>
      <a-button size="mini" @click="handleBack">返回</a-button>
    </template>
    <a-split class="w-full h-full" :min="0.1" :max="0.9">
      <template #first>
        <div class="h-dvh overflow-y-auto border-r border-gray-200">
          <a-tree
            v-if="criterionList.length > 0"
            v-model:selected-keys="selectedValue"
            :data="criterionList"
            show-line
            :field-names="{ key: 'id', title: 'name', children: 'children' }"
            :selectable="true"
            class="select-none"
            @select="handleGradeClassChange"
          >
            <template #title="node">
              <div class="rounded hover:bg-gray-100 cursor-pointer transition-colors" @click="handleEditNode(node)">
                {{ node.name || node.title }}
              </div>
            </template>
            <template #extra="nodeData">
              <div v-if="nodeData?.id" class="opacity-0 hover:opacity-100 w-auto">
                <a-button size="mini" type="text" status="normal" @click="handleAdd(nodeData)">新增</a-button>
                <a-button
                  v-if="nodeData?.parentId"
                  size="mini"
                  type="text"
                  status="danger"
                  @click="handleDel(nodeData)"
                >
                  删除
                </a-button>
              </div>
            </template>
          </a-tree>
        </div>
      </template>

      <template #second>
        <div v-if="currentNode" class="w-full px-6 py-2 bg-white mr-2">
          <div class="text-lg font-semibold mb-4">名称</div>
          <a-textarea v-model="currentNode.name" class="w-full mb-6 rounded border border-gray-200 p-2" />
          <div class="text-lg font-semibold mb-4">注释</div>
          <a-textarea
            v-model="currentNode.notes"
            class="w-full mb-6 min-h-[120px] rounded border border-gray-200 p-2"
          />
          <a-button
            size="mini"
            type="primary"
            class="rounded px-4 py-2 bg-blue-500 text-white hover:bg-blue-600 transition-colors"
            @click="handleSubmit"
          >
            保存
          </a-button>
        </div>
        <a-empty v-else description="请先选择编辑项" />
      </template>
    </a-split>
  </a-card>
  <a-modal v-model:visible="addNodeVisible" title="新增条目" :on-before-ok="handlePreOk">
    <a-form auto-label-width>
      <a-form-item label="内容" required>
        <a-input v-model="formData.name" placeholder="请输入评估项内容" />
      </a-form-item>
      <a-form-item label="注释">
        <a-input v-model="formData.notes" placeholder="请输入评估项注释" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style scoped lang="less">
  .frame {
    width: 100%;
    height: calc(100vh - 50px);
    border: none;
  }
</style>
