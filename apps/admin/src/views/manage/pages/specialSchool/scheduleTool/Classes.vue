<template>
  <div class="classes-container">
    <a-card class="mb-4">
      <template #title>班级管理</template>
      <a-space direction="vertical" size="large" fill>
        <a-row justify="space-between">
          <a-col>
            <a-space>
              <a-button size="mini" type="primary" @click="showAddModal = true">
                <template #icon><icon-plus /></template>
                添加班级
              </a-button>

              <a-input-search v-model="searchKeyword" placeholder="请输入班级名称搜索" search-button size="mini" />
            </a-space>
          </a-col>
        </a-row>

        <a-table :columns="columns" :data="filteredClasses" :pagination="false" row-key="id">
          <template #operations="{ record }">
            <a-space>
              <a-popconfirm content="确定要删除该班级吗？" type="warning" @ok="handleDelete(record)">
                <a-button type="text" status="danger" size="mini"> 删除 </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </a-table>
      </a-space>
    </a-card>

    <!--selected only-->
    <a-modal
      v-model:visible="showAddModal"
      :title="editingClass ? '编辑班级' : '添加班级'"
      @ok="handleSubmit"
      @cancel="resetForm"
    >
      <a-form :model="formData">
        <a-form-item field="gradeId" label="所属年级" :rules="{ required: true, message: '请选择年级' }">
          <a-select v-model="formData.gradeId" placeholder="请选择年级" allow-search>
            <a-option v-for="grade in grades" :key="grade.id" :value="grade.id" @click="handleGradeChange(grade)">
              {{ grade.label }}
            </a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="id" label="班级名称">
          <a-select v-model="formData.id" multiple placeholder="请选择参与排课班级">
            <a-option
              v-for="grade in filterGradClass"
              :key="grade.id"
              :value="grade.id"
              :label="grade.label"
              @click="handleGradeClassChange(grade)"
            />
          </a-select>
        </a-form-item>
      </a-form>
      <div class="w-full mt-2 flex-wrap gap-2 flex">
        <a-tag v-for="item in importClass" :key="item.value">{{ item.label }}</a-tag>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { IconPlus } from '@arco-design/web-vue/es/icon';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    setting: {
      type: Object,
    },
    record: {
      type: Object,
    },
  });
  const emits = defineEmits(['update:setting', 'autoSave']);
  const generalSettings = computed({
    get: () => {
      return props.setting || {};
    },
    set: (val) => {
      emits('update:setting', val);
    },
  });
  const grades = ref([]);

  const columns = [
    { title: '班级名称', dataIndex: 'name' },
    {
      title: '所属年级',
      dataIndex: 'gradeId',
      render: ({ record }) => {
        const grade = grades.value.find((g) => g.id === record.gradeId);
        return grade ? grade?.label : '-';
      },
    },
    { title: '操作', slotName: 'operations' },
  ];

  // 初始化数据时从 localStorage 加载
  const classes = ref(props.setting?.classes);
  const searchKeyword = ref('');
  const showAddModal = ref(false);
  const editingClass = ref(null);
  const formData = ref<any>({
    name: '',
    gradeId: null,
  });

  const filteredClasses = computed(() => {
    return classes.value.filter((cls) => cls.name.includes(searchKeyword.value));
  });

  const handleDelete = async (cls) => {
    classes.value = classes.value.filter((c) => c.id !== cls.id);
    generalSettings.value.classes = classes.value;

    // 触发班级更新事件
    window.dispatchEvent(new CustomEvent('classesUpdated'));

    Message.success('删除成功');
  };

  const gradeClass = ref([]);
  const filterGradClass = ref([]);
  const importClass = ref([]);

  const resetForm = () => {
    formData.value = {
      name: '',
      gradeId: null,
    };
    editingClass.value = null;
    showAddModal.value = false;
    importClass.value = [];
  };

  const handleSubmit = async () => {
    // 校验班级名称是否为空 (根据需要添加)
    if (!importClass.value.length) {
      Message.warning('请选择班级');
      return false;
    }

    // 添加班级，并过滤掉相同的班级
    const exitsClassIds = classes.value.map((cls) => cls.id) || [];
    const newClass = importClass.value
      .filter((i) => !exitsClassIds.includes(i.id))
      .map((i) => ({ id: i.id, name: i.label, gradeId: i.row.grade.id, gradeName: i.row.grade.name }));

    classes.value = [...classes.value, ...newClass];

    generalSettings.value.classes = classes.value;

    Message.success('添加成功');

    resetForm();
    return true;
  };

  const handleGradeChange = (val: any) => {
    filterGradClass.value = gradeClass.value
      .filter((item: any) => item.row.grade.id === val.id)
      .map((item) => ({ label: item.label, value: item.value, row: item.row, id: item.id }));
    formData.value.gradeName = val.label;
    const importIds = importClass.value.map((item) => item.value);
    // 切换年级回显选项
    formData.value.id = filterGradClass.value.filter((cls) => importIds.includes(cls.value)).map((cls) => cls.value);
  };

  const handleGradeClassChange = (grade: any) => {
    formData.value.name = grade.label;
    const exits = importClass.value.filter((item: any) => item.value === grade.value);
    if (!exits.length) {
      importClass.value.push(grade);
    } else {
      importClass.value = importClass.value.filter((item: any) => item.value !== grade.value);
    }
  };

  const loadGrades = async () => {
    const { data: res } = await request('/teacher/schoolGrade/findByBoId', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
      params: {
        branchOfficeId: props.record.boId,
      },
    });
    grades.value = res.items.map((item) => ({ label: item.name, value: item.id, row: item, id: item.id }));
  };
  const loadGradeClass = async () => {
    const { data: res } = await request('/resourceRoom/gradeClass', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
      params: {
        boId: props.record.boId,
      },
    });
    gradeClass.value = res.items.map((item) => ({ label: item.name, value: item.id, row: item, id: item.id }));
  };

  onMounted(async () => {
    await loadGrades();
    await loadGradeClass();
  });
  watch(
    () => props.setting,
    (newVal) => {
      emits('autoSave');
    },
    { deep: true },
  );
</script>
