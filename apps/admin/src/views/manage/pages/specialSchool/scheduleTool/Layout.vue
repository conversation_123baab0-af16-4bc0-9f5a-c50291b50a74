<template>
  <a-layout class="h-[90vh]">
    <a-layout-sider collapsible>
      <div class="logo p-4 text-white text-center text-lg">课表系统</div>
      <a-menu :default-selected-keys="['courses']" :style="{ width: '100%' }">
        <a-menu-item key="courses" @click="switchComponent('courses')">
          <template #icon><icon-book /></template>
          课程设置
        </a-menu-item>
        <a-menu-item key="teachers" @click="switchComponent('teachers')">
          <template #icon><icon-user /></template>
          教师设置
        </a-menu-item>
        <a-menu-item key="classes" @click="switchComponent('classes')">
          <template #icon><icon-apps /></template>
          班级设置
        </a-menu-item>
        <a-menu-item key="course-plan" @click="switchComponent('course-plan')">
          <template #icon><icon-calendar /></template>
          课时计划
        </a-menu-item>
        <a-menu-item key="constraints" @click="switchComponent('constraints')">
          <template #icon><icon-filter /></template>
          约束条件
        </a-menu-item>
        <a-menu-item key="overview" @click="switchComponent('overview')">
          <template #icon><icon-eye /></template>
          课表总览
        </a-menu-item>
      </a-menu>
    </a-layout-sider>
    <a-layout>
      <a-layout-header class="bg-white px-4 flex items-center">
        <template #default>
          <div class="flex justify-between w-full items-center p-2">
            <!-- 左侧 -->
            <div class="flex justify-start items-center space-x-2 flex-1">
              <span>课表前置条件填报系统</span>
              <a-tag class="ml-2" :color="randomColor(30)">{{ recordParams?.boName }}</a-tag>
            </div>

            <!-- 中间提示 -->
            <div class="text-center text-gray-300 flex-1"> 所有改动将在页面关闭后自动保存 </div>

            <!-- 右侧 -->
            <div class="flex justify-end space-x-2 flex-1">
              <a-button v-if="false" size="mini" type="primary" status="success" @click="handleSave">
                <template #icon>
                  <icon-save />
                </template>
                保存设置
              </a-button>
              <a-button size="mini" type="outline" @click="handleBack">
                <template #icon>
                  <icon-undo />
                </template>
                返回列表
              </a-button>
            </div>
          </div>
        </template>
      </a-layout-header>
      <a-layout-content class="p-2 bg-white" style="overflow: auto">
        <!--<router-view></router-view>-->
        <component
          :is="currentComponent"
          v-if="isLoad"
          v-model:setting="generalSettings"
          :record="currentRecord"
          @auto-save="handleSave"
          @flush-record="loadData(recordParams.recordId)"
        />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, nextTick, onBeforeUnmount } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { IconUser, IconBook, IconApps, IconCalendar, IconFilter, IconEye } from '@arco-design/web-vue/es/icon';
  import Courses from '@/views/manage/pages/specialSchool/scheduleTool/Courses.vue';
  import Teachers from '@/views/manage/pages/specialSchool/scheduleTool/Teachers.vue';
  import Classes from '@/views/manage/pages/specialSchool/scheduleTool/Classes.vue';
  import CoursePlan from '@/views/manage/pages/specialSchool/scheduleTool/CoursePlan.vue';
  import Constraints from '@/views/manage/pages/specialSchool/scheduleTool/Constraints.vue';
  import ScheduleOverview from '@/views/manage/pages/specialSchool/scheduleTool/ScheduleOverview.vue';
  import { randomColor } from '@repo/ui/components/utils/randomColor';
  import { getClientRole, PROJECT_URLS } from '@repo/env-config';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';
  import { getToken } from '@repo/infrastructure/auth';
  import { storage } from '@repo/infrastructure/adapter';

  const router = useRouter();
  const route = useRoute();
  const currentComponent = ref<any>(Courses);
  const isLoad = ref(false);
  const switchComponent = (key: string) => {
    isLoad.value = false;
    switch (key) {
      case 'courses':
        currentComponent.value = Courses;
        break;
      case 'teachers':
        currentComponent.value = Teachers;
        break;
      case 'classes':
        currentComponent.value = Classes;
        break;
      case 'course-plan':
        currentComponent.value = CoursePlan;
        break;
      case 'constraints':
        currentComponent.value = Constraints;
        break;
      case 'overview':
        currentComponent.value = ScheduleOverview;
        break;
      default:
        currentComponent.value = Courses;
        break;
    }
    nextTick(() => {
      isLoad.value = true;
    });
  };

  const handleBack = () => {
    router.push('/manage/specialSchool/scheduleTool');
  };

  const recordParams = ref();
  const generalSettings = ref<any>({});
  const currentRecord = ref(null);

  // 根据recordId 加载排课条件设置
  const loadData = async (recordId: number) => {
    if (!recordId) return;
    const { data: res } = await request(`/teacher/scheduleTool/${recordId}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
    });
    currentRecord.value = res;

    if (res) {
      generalSettings.value = {
        classrooms: res?.classrooms || [],
        courses: res?.courses || [],
        teachingGroups: res?.teachingGroups || [],
        teachers: res?.teachers || [],
        classes: res?.classes || [],
        weeklySchedule: res?.weeklySchedule || [],
        constraints: res?.constraints || {},
        weeklyScheduleData: res?.weeklyScheduleData || {},
        teacherWeeklyWorkloads: res?.teacherWeeklyWorkloads || {},
        consecutiveTeachingSettings: res?.consecutiveTeachingSettings || {},
        constraintsStats: res?.constraintsStats || { totalAvailablePeriods: 0 },
      };
    } else {
      generalSettings.value = {
        classrooms: [],
        courses: [],
        teachingGroups: [],
        teachers: [],
        classes: [],
        weeklySchedule: [],
        constraints: {},
        weeklyScheduleData: {},
        teacherWeeklyWorkloads: {},
        consecutiveTeachingSettings: {},
        constraintsStats: { totalAvailablePeriods: 0 },
      };
    }
  };
  const handleSave = async () => {
    Message.clear('top');
    await request(`/teacher/scheduleTool/${currentRecord.value.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'put',
      data: Object.assign(currentRecord.value, generalSettings.value),
    });
    Message.success('保存成功');
  };

  onMounted(async () => {
    recordParams.value = route.query;
    await loadData(recordParams.value.recordId);
    isLoad.value = true;
  });

  onBeforeUnmount(async () => {
    await handleSave();
  });
</script>
