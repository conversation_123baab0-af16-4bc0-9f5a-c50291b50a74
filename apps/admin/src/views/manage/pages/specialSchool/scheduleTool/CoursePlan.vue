<template>
  <a-spin :loading="isLoading" tip="加载数据中..." class="w-full">
    <div class="course-plan-container">
      <!-- 新增说明卡片 -->
      <a-card class="mb-4 guide-card">
        <template #title>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <icon-info-circle class="mr-2 text-blue-500" />
              <span class="text-base">使用说明</span>
            </div>
          </div>
        </template>
        <div class="guide-content">
          <div class="text-gray-500 text-sm mb-2"
            >请先在"课程安排"功能页设置每个班级的课程课时，然后再为每门课程分配授课教师。
          </div>
          <div class="icon-guide-list">
            <div class="icon-guide-item">
              <icon-calendar class="option-icon icon-weekly" />
              <span class="guide-text">每周课时：该课程每周需要上课的总课时数</span>
            </div>
            <div class="icon-guide-item">
              <icon-sun class="option-icon icon-daily" />
              <span class="guide-text">单日上限：该课程每天最多可以安排的课时数</span>
            </div>
            <div class="icon-guide-item">
              <icon-clock-circle class="option-icon icon-time" />
              <span class="guide-text">时段安排：该课程只能在上午或下午安排，或不限制时段</span>
            </div>
          </div>
        </div>
      </a-card>

      <!-- 标签页容器 -->
      <a-card class="course-tabs-card">
        <a-tabs v-model:active-key="activeTab" lazy-load @change="handleTabChange">
          <!-- 课时计划标签页 -->
          <a-tab-pane key="1">
            <template #title>
              <span class="flex items-center gap-2">
                <icon-calendar />
                教师分配
              </span>
            </template>
            <div class="batch-header">
              <div class="batch-info">
                <icon-info-circle class="info-icon" />
                <span class="info-text">在此设置每个教师的课程课时数，设置完成后点击应用设置按钮</span>
              </div>
              <a-button size="mini" type="primary" @click="handleSaveData">
                <template #icon>
                  <icon-check />
                </template>
                应用设置
              </a-button>
            </div>
            <!-- 表格区域优化 -->
            <div class="table-container">
              <table class="course-table">
                <thead class="sticky top-[0px] z-10 bg-white pb-2 border">
                  <tr>
                    <th class="subject-header" rowspan="2">课程</th>
                    <template v-for="grade in grades" :key="grade.id">
                      <th :colspan="getGradeClassCount(grade.id)" class="grade-header">
                        {{ grade.name }}
                      </th>
                    </template>
                  </tr>
                  <tr>
                    <template v-for="grade in grades" :key="grade.id">
                      <th v-for="cls in getGradeClasses(grade.id)" :key="cls.id" class="class-header">
                        {{ cls.name }}
                      </th>
                    </template>
                  </tr>
                  <tr>
                    <td class="bg-gray-100 font-medium">
                      <div class="stats-title w-full text-center">课时统计</div>
                      <div
                        class="total-stats w-full text-center"
                        :class="{
                          'text-red-500': calculateTotalAssignedWeeklyHours() !== calculateTotalWeeklyHours(),
                        }"
                      >
                        {{ calculateTotalAssignedWeeklyHours() }}/{{ calculateTotalWeeklyHours() }}
                      </div>
                    </td>
                    <template v-for="grade in grades" :key="grade.id">
                      <td
                        v-for="cls in getGradeClasses(grade.id)"
                        :key="cls.id"
                        class="text-center font-medium bg-gray-100"
                      >
                        <div class="stats-cell">
                          <div class="stats-label">已分配/总课时</div>
                          <div
                            class="stats-numbers"
                            :class="{
                              'text-red-500':
                                calculateClassAssignedWeeklyTotal(cls.id) !== calculateClassWeeklyTotal(cls.id),
                            }"
                          >
                            {{ calculateClassAssignedWeeklyTotal(cls.id) }}/{{ calculateClassWeeklyTotal(cls.id) }}
                          </div>
                        </div>
                      </td>
                    </template>
                  </tr>
                </thead>

                <!-- 优化单元格内容布局 -->
                <tbody>
                  <tr v-for="course in courses" :key="course.id">
                    <td class="subject-cell">
                      {{ course.name }}
                    </td>
                    <template v-for="grade in grades" :key="grade.id">
                      <td
                        v-for="cls in getGradeClasses(grade.id)"
                        :key="cls.id"
                        class="course-cell"
                        :class="
                          weeklyScheduleData[`${course.id}-${cls.id}`]?.weeklyRequiredHours >
                            computedCellActualHours(course.id, cls.id) || computedExitsNewTeacher(course.id, cls.id)
                            ? ' bg-red-50' /*incomplete time settings or new teacher*/
                            : ''
                        "
                      >
                        <div class="cell-content">
                          <!-- 课时设置区域 -->
                          <div class="option-row-display">
                            <div class="icon-value-group weekly-group">
                              <icon-calendar class="option-icon icon-weekly" />
                              <span>{{ weeklyScheduleData[`${course.id}-${cls.id}`]?.weeklyRequiredHours || 0 }}</span>
                            </div>
                            <div class="icon-value-group daily-group">
                              <icon-sun class="option-icon icon-daily" />
                              <span>{{ weeklyScheduleData[`${course.id}-${cls.id}`]?.dailyMaxHours || 2 }}</span>
                            </div>
                            <div class="icon-value-group time-group">
                              <icon-clock-circle class="option-icon icon-time" />
                              <span
                                :class="[
                                  'session-text',
                                  {
                                    'session-morning':
                                      weeklyScheduleData[`${course.id}-${cls.id}`]?.sessionRequirement === 'morning',
                                    'session-afternoon':
                                      weeklyScheduleData[`${course.id}-${cls.id}`]?.sessionRequirement === 'afternoon',
                                    'session-any':
                                      !weeklyScheduleData[`${course.id}-${cls.id}`]?.sessionRequirement ||
                                      weeklyScheduleData[`${course.id}-${cls.id}`]?.sessionRequirement === 'any',
                                  },
                                ]"
                              >
                                {{ getSessionText(weeklyScheduleData[`${course.id}-${cls.id}`]?.sessionRequirement) }}
                              </span>
                            </div>
                          </div>

                          <!-- 教师分配区域 -->
                          <div class="teachers-container">
                            <div
                              v-for="teacher in getAvailableTeachers(course.id, cls.id)"
                              :key="teacher.id"
                              :class="[
                                'teacher-item',
                                {
                                  'teacher-assigned':
                                    weeklyScheduleData[course.id + '-' + cls.id]?.teacherWeeklyHours[teacher.id] > 0,
                                  'bg-blue-100 border-blue-400 border':
                                    weeklyScheduleData[course.id + '-' + cls.id]?.teacherWeeklyHours[teacher.id] > 0,
                                },
                              ]"
                            >
                              <div class="teacher-content">
                                <div class="teacher-info">
                                  <a-tooltip
                                    :content="`当前分配:${getDistribute(course, cls, teacher)} `"
                                    position="top"
                                  >
                                    <!--新增徽章-->
                                    <icon-check-circle-fill v-if="teacher?.newTeacher" class="text-green-600" />
                                    <span class="teacher-name">{{ teacher.name }}</span>
                                  </a-tooltip>
                                </div>

                                <div
                                  class="teacher-controls"
                                  :class="{
                                    'controls-hidden-by-default': isFullyAssigned(course.id, cls.id),
                                  }"
                                >
                                  <a-button
                                    size="mini"
                                    class="control-button"
                                    :disabled="
                                      !weeklyScheduleData[course.id + '-' + cls.id]?.teacherWeeklyHours[teacher.id] ||
                                      weeklyScheduleData[course.id + '-' + cls.id].teacherWeeklyHours[teacher.id] <= 0
                                    "
                                    @click.stop="adjustTeacherHours(course.id, cls.id, teacher.id, -1)"
                                  >
                                    <icon-minus />
                                  </a-button>

                                  <span class="hours-number">
                                    {{
                                      weeklyScheduleData[course.id + '-' + cls.id]?.teacherWeeklyHours[teacher.id] || 0
                                    }}
                                  </span>

                                  <a-button
                                    size="mini"
                                    class="control-button"
                                    :disabled="!canAddHours(course.id, cls.id /*, teacher.id*/)"
                                    @click.stop="adjustTeacherHours(course.id, cls.id, teacher.id, 1)"
                                  >
                                    <icon-plus />
                                  </a-button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>
                    </template>
                  </tr>
                </tbody>

                <!-- 修改表格底部的统计行 -->
                <tfoot v-if="false">
                  <tr>
                    <td class="bg-gray-50 font-medium">
                      <div class="stats-title">课时统计</div>
                      <div
                        class="total-stats"
                        :class="{
                          'text-red-500': calculateTotalAssignedWeeklyHours() !== calculateTotalWeeklyHours(),
                        }"
                      >
                        {{ calculateTotalAssignedWeeklyHours() }}/{{ calculateTotalWeeklyHours() }}
                      </div>
                    </td>
                    <template v-for="grade in grades" :key="grade.id">
                      <td
                        v-for="cls in getGradeClasses(grade.id)"
                        :key="cls.id"
                        class="text-center font-medium bg-gray-50"
                      >
                        <div class="stats-cell">
                          <div class="stats-label">已分配/总课时</div>
                          <div
                            class="stats-numbers"
                            :class="{
                              'text-red-500':
                                calculateClassAssignedWeeklyTotal(cls.id) !== calculateClassWeeklyTotal(cls.id),
                            }"
                          >
                            {{ calculateClassAssignedWeeklyTotal(cls.id) }}/{{ calculateClassWeeklyTotal(cls.id) }}
                          </div>
                        </div>
                      </td>
                    </template>
                  </tr>
                </tfoot>
              </table>
            </div>
          </a-tab-pane>
          <!-- 批量设置标签页 -->
          <a-tab-pane key="2">
            <template #title>
              <span class="flex items-center gap-2">
                <icon-settings />
                课程安排
              </span>
            </template>

            <div class="batch-hours-form">
              <!-- 修改批量设置的操作区域布局 -->
              <div class="batch-header">
                <div class="batch-info">
                  <icon-info-circle class="info-icon" />
                  <span class="info-text">在此设置每个班级的课程课时数，设置完成后点击应用设置按钮</span>
                </div>
                <a-button size="mini" type="primary" @click="applyBatchHours">
                  <template #icon>
                    <icon-check />
                  </template>
                  应用设置
                </a-button>
              </div>

              <div class="overflow-auto" style="height: 85vh">
                <table v-if="Object.keys(batchHoursData).length > 0" class="w-full batch-table">
                  <thead>
                    <tr>
                      <th class="w-32 bg-gray-50 sticky top-[-1px] z-10" :rowspan="2">
                        <div class="course-header">
                          <div class="course-title">课程</div>
                        </div>
                      </th>
                      <template v-for="grade in grades" :key="grade.id">
                        <th
                          :colspan="getGradeClassCount(grade.id)"
                          class="text-center bg-blue-50 h-10 grade-header-cell sticky top-0 z-30"
                        >
                          <div class="grade-title">{{ grade.name }}</div>
                        </th>
                      </template>
                    </tr>
                    <tr>
                      <template v-for="grade in grades" :key="grade.id">
                        <th
                          v-for="cls in getGradeClasses(grade.id)"
                          :key="cls.id"
                          class="text-center w-20 h-10 bg-blue-50 sticky top-[60px] z-20"
                        >
                          {{ cls.name }}
                        </th>
                      </template>
                    </tr>
                    <tr class="text-center w-20 h-[30px] bg-blue-50 sticky top-[105px] z-20">
                      <td class="font-medium text-right pr-4 bg-gray-50">班级课时合计</td>
                      <template v-for="grade in grades" :key="grade.id">
                        <td
                          v-for="cls in getGradeClasses(grade.id)"
                          :key="cls.id"
                          class="text-center font-medium bg-gray-50"
                        >
                          {{ calculateBatchClassTotal(cls.id) }}
                        </td>
                      </template>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(course, index) in courses" :key="course.id" :class="{ striped: index % 2 === 0 }">
                      <td class="font-medium whitespace-nowrap course-cell">
                        <div class="course-name">{{ course.name }}</div>
                        <div class="batch-settings-row">
                          <div class="batch-input-item">
                            <icon-calendar class="option-icon icon-weekly" />
                            <a-input-number
                              size="mini"
                              placeholder="周课时"
                              :min="0"
                              @change="(value) => batchSetWeeklyHours(course.id, value)"
                            />
                          </div>
                          <div class="batch-input-item">
                            <icon-sun class="option-icon icon-daily" />
                            <a-input-number
                              size="mini"
                              placeholder="日上限"
                              :min="1"
                              @change="(value) => batchSetDailyMax(course.id, value)"
                            />
                          </div>
                          <div class="batch-input-item">
                            <icon-clock-circle class="option-icon icon-time" />
                            <a-select
                              size="mini"
                              placeholder="时段"
                              @change="(value) => batchSetTimeSlot(course.id, value)"
                            >
                              <a-option value="any">
                                <span class="session-text session-any">不限制</span>
                              </a-option>
                              <a-option value="morning">
                                <span class="session-text session-morning">仅上午</span>
                              </a-option>
                              <a-option value="afternoon">
                                <span class="session-text session-afternoon">仅下午</span>
                              </a-option>
                            </a-select>
                          </div>
                        </div>
                      </td>
                      <template v-for="grade in grades" :key="grade.id">
                        <td v-for="cls in getGradeClasses(grade.id)" :key="cls.id" class="text-center batch-cell">
                          <div class="batch-cell-content">
                            <div class="input-group">
                              <div class="input-item">
                                <icon-calendar class="option-icon icon-weekly" />
                                <a-input-number
                                  v-model="batchHoursData[`${course.id}-${cls.id}`].weeklyRequiredHours"
                                  :min="0"
                                  size="small"
                                  class="batch-input"
                                  placeholder="周课时"
                                />
                              </div>
                              <div class="input-item">
                                <icon-sun class="option-icon icon-daily" />
                                <a-input-number
                                  v-model="batchHoursData[`${course.id}-${cls.id}`].dailyMaxHours"
                                  :min="1"
                                  :max="batchHoursData[`${course.id}-${cls.id}`].weeklyRequiredHours || 1"
                                  size="small"
                                  class="batch-input"
                                  placeholder="日上限"
                                />
                              </div>
                              <div class="input-item">
                                <icon-clock-circle class="option-icon icon-time" />
                                <a-select
                                  v-model="batchHoursData[`${course.id}-${cls.id}`].sessionRequirement"
                                  size="small"
                                  class="batch-select"
                                  @change="(value) => updateBatchTimeSlot(course.id, cls.id, value)"
                                >
                                  <a-option value="any">
                                    <span class="session-text session-any">不限制</span>
                                  </a-option>
                                  <a-option value="morning">
                                    <span class="session-text session-morning">仅上午</span>
                                  </a-option>
                                  <a-option value="afternoon">
                                    <span class="session-text session-afternoon">仅下午</span>
                                  </a-option>
                                </a-select>
                              </div>
                            </div>
                          </div>
                        </td>
                      </template>
                    </tr>
                  </tbody>
                  <!-- 添加底部统计行 -->
                  <tfoot v-if="false">
                    <td class="font-medium text-right pr-4 bg-gray-50">班级课时合计</td>
                    <template v-for="grade in grades" :key="grade.id">
                      <td
                        v-for="cls in getGradeClasses(grade.id)"
                        :key="cls.id"
                        class="text-center font-medium bg-gray-50"
                      >
                        {{ calculateBatchClassTotal(cls.id) }}
                      </td>
                    </template>
                  </tfoot>
                </table>
                <a-empty v-else description="正在加载数据..."></a-empty>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>
  </a-spin>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
  import { Message, Tooltip as ATooltip, Spin as ASpin } from '@arco-design/web-vue';
  import {
    IconMinus,
    IconPlus,
    IconSettings,
    IconClockCircle,
    IconCalendar,
    IconSun,
    IconInfoCircle,
    IconCheck,
  } from '@arco-design/web-vue/es/icon';

  // --- 响应式状态定义 ---
  const props = defineProps({
    setting: {
      type: Object,
    },
  });
  const emits = defineEmits(['update:setting', 'autoSave']);
  const generalSettings = computed({
    get: () => {
      return props.setting;
    },
    set: (val) => {
      emits('update:setting', val);
    },
  });

  const grades = ref([]); // 年级数据
  const classes = ref(props.setting?.classes || []); // 班级数据
  const courses = ref(props.setting?.courses || []); // 课程数据
  const teachers = ref(props.setting?.teachers || []); // 教师数据
  const isLoading = ref(false);
  const teachersByCourse = ref({}); // 按课程ID缓存的可授课教师列表

  /**
   * 周课时计划的核心数据结构。
   * 存储在 localStorage，键名为 'weeklySchedule'。
   * 结构: {
   *   '${course.id}-${class.id}': {
   *     weeklyRequiredHours: number,      // 每周需求课时
   *     sessionRequirement: 'any'|'morning'|'afternoon', // 上课时段要求
   *     dailyMaxHours: number,             // 每日最大课时
   *     teacherWeeklyHours: { [teacherId]: number }, // { 教师ID: 分配的周课时 }
   *     // teachers: number[]              // (已移除) 分配了课时的教师ID列表 (冗余)
   *   }
   * }
   */
  const weeklyScheduleData = ref({});

  /**
   * 根据 weeklyScheduleData 计算出的每位教师的周总工作量。
   * 存储在 localStorage，键名为 'teacherWeeklyWorkloads'。
   * 这是衍生数据，主要为了方便其他组件（如 Teachers.vue）直接读取教师已分配总课时。
   * 结构: { [teacherId]: { assignedWeeklyHours: number, teacherName: string } }
   */
  const teacherWeeklyWorkloads = ref(generalSettings.value?.teacherWeeklyWorkloads || {});

  // 批量设置标签页中使用的数据模型。
  const batchHoursData = ref({});

  const activeTab = ref('1');

  const getDistribute = (course, cls, teacher) => {
    return `${weeklyScheduleData[`${course.id}-${cls.id}`]?.teacherWeeklyHours[teacher.id] || 0} / 总计: ${teacher.assignedWeeklyHours}`;
  };
  // --- 数据持久化与同步 ---

  const saveWeeklyScheduleData = () => {
    generalSettings.value.weeklyScheduleData = weeklyScheduleData.value;
  };
  const saveTeacherWeeklyWorkloads = () => {
    try {
      generalSettings.value.teacherWeeklyWorkloads = teacherWeeklyWorkloads.value;
    } catch (error) {
      // console.error('保存教师工作量数据失败:', error);
    }
  };

  /**
   * 基于当前的 weeklyScheduleData 重新计算所有教师的周工作量并保存。
   */
  const updateTeacherWeeklyWorkloads = () => {
    const newWorkloads = {};

    // 初始化所有教师的工作量为 0
    teachers.value.forEach((teacher: any) => {
      newWorkloads[teacher.id] = {
        assignedWeeklyHours: teacher?.actualAssignedHours || 0,
        teacherName: teacher.name,
      };
    });

    // 遍历 weeklyScheduleData，累加每个教师在所有课程-班级单元格的分配课时
    Object.values(weeklyScheduleData.value).forEach((data: any) => {
      if (data.teacherWeeklyHours) {
        Object.entries(data.teacherWeeklyHours).forEach(([teacherId, hours]) => {
          if (newWorkloads[teacherId]) {
            newWorkloads[teacherId].assignedWeeklyHours += hours || 0;
          }
          // 如果 newWorkloads[teacherId] 不存在，说明可能教师数据不同步，暂时忽略
        });
      }
    });

    teacherWeeklyWorkloads.value = newWorkloads;
    saveTeacherWeeklyWorkloads(); // 保存更新后的工作量
  };

  // --- 初始化与数据加载 ---
  const loadData = async () => {
    try {
      const extractUniqueGradeInfo = (data: any[]): any[] => {
        const gradeMap = new Map<number, any>();
        // eslint-disable-next-line no-restricted-syntax
        for (const item of data) {
          if (!gradeMap.has(item.gradeId)) {
            gradeMap.set(item.gradeId, { id: item.gradeId, name: item.gradeName });
          }
        }
        return Array.from(gradeMap.values());
      };

      grades.value = extractUniqueGradeInfo(props.setting?.classes) || [];
      classes.value = props.setting?.classes || [];
      courses.value = props.setting?.courses || [];
      teachers.value = props.setting?.teachers || [];

      const newWeeklyScheduleData = {};
      const newBatchData = {}; // 用于 batchHoursData
      const newWorkloads = {}; // 用于 teacherWeeklyWorkloads

      // 初始化所有教师的工作量
      teachers.value.forEach((teacher: any) => {
        newWorkloads[teacher.id] = {
          assignedWeeklyHours: 0,
          teacherName: teacher.name,
        };
      });

      const existingSchedule = props.setting?.weeklyScheduleData || {};

      if (courses.value && classes.value && teachers.value) {
        courses.value.forEach((course: any) => {
          if (!course || !course.id) return; // 跳过无效课程

          classes.value.forEach((cls: any) => {
            if (!cls || !cls.id) return; // 跳过无效班级

            const key = `${course.id}-${cls.id}`;
            const existingData = existingSchedule[key];
            let currentTeacherHours = {}; // 当前单元格的教师课时

            // 构建 weeklyScheduleData 条目 (移除 teachers 字段)
            if (existingData) {
              currentTeacherHours = existingData.teacherWeeklyHours || {};
              newWeeklyScheduleData[key] = {
                weeklyRequiredHours: existingData.weeklyRequiredHours || 0,
                sessionRequirement: existingData.sessionRequirement || 'any',
                dailyMaxHours: existingData.dailyMaxHours || 2,
                teacherWeeklyHours: currentTeacherHours,
                // 不再包含 teachers 字段
              };
            } else {
              newWeeklyScheduleData[key] = {
                weeklyRequiredHours: 0,
                sessionRequirement: 'any',
                dailyMaxHours: 2,
                teacherWeeklyHours: {},
                // 不再包含 teachers 字段
              };
            }

            // 同时构建 batchHoursData 条目 (基于刚确定的 weeklyScheduleData)
            newBatchData[key] = {
              weeklyRequiredHours: newWeeklyScheduleData[key].weeklyRequiredHours,
              sessionRequirement: newWeeklyScheduleData[key].sessionRequirement,
              dailyMaxHours: newWeeklyScheduleData[key].dailyMaxHours,
            };

            // 同时累加教师工作量 (基于 currentTeacherHours)
            Object.entries(currentTeacherHours).forEach(([teacherId, hours]) => {
              if (newWorkloads[teacherId]) {
                newWorkloads[teacherId].assignedWeeklyHours += hours || 0;
              }
            });
          });
        });
      }

      weeklyScheduleData.value = newWeeklyScheduleData;
      batchHoursData.value = newBatchData; // 直接赋值初始化后的批量数据
      teacherWeeklyWorkloads.value = newWorkloads; // 直接赋值计算后的工作量

      // 保存可能更新后的 weeklyScheduleData 结构和计算出的工作量
      saveWeeklyScheduleData();
      // saveTeacherWeeklyWorkloads();

      // 2. 构建 teachersByCourse 缓存
      const newTeachersByCourse = {};
      if (courses.value && teachers.value) {
        courses.value.forEach((course: any) => {
          if (course && course.id && course.name) {
            newTeachersByCourse[course.id] = teachers.value.filter(
              (teacher: any) =>
                teacher.subjects && teacher.subjects.some((subject: any) => subject.name === course.name),
            );
          }
        });
      }
      teachersByCourse.value = newTeachersByCourse;
    } catch (error) {
      Message.error('加载数据出错，请检查localStorage或刷新页面');
    }
  };

  const setupWatchers = (): any => {
    const events = ['classesUpdated', 'coursesUpdated', 'gradesUpdated', 'teachersUpdated'];
    const handleDataUpdate = () => {
      loadData(); // 重新加载基础数据
    };

    events.forEach((event) => {
      window.addEventListener(event, handleDataUpdate);
    });

    // 返回清理函数
    return () => {
      events.forEach((event) => {
        window.removeEventListener(event, handleDataUpdate);
      });
    };
  };

  // --- 计算属性与辅助函数 ---

  const getSessionText = (value: string) => {
    switch (value) {
      case 'morning':
        return '仅上午';
      case 'afternoon':
        return '仅下午';
      default:
        return '不限制';
    }
  };

  const getGradeClasses = (gradeId) => {
    return classes.value.filter((cls: any) => cls.gradeId === gradeId);
  };

  const getGradeClassCount = (gradeId) => {
    return getGradeClasses(gradeId).length;
  };

  const getAvailableTeachers = (courseId, classId): any => {
    const key = `${courseId}-${classId}`;
    const cellData = weeklyScheduleData.value[key];

    if (!cellData) return [];

    // 1. 从缓存获取能教这门课的老师
    let potentialTeachers = teachersByCourse.value[courseId] || [];
    potentialTeachers = potentialTeachers.filter((p) => {
      if (!p.gradeClassId || p.gradeClassId.includes(classId)) {
        return true;
      }
      return false;
    });
    if (!potentialTeachers || potentialTeachers.length === 0) return []; // 如果没有老师能教这门课

    const requiredHours = cellData.weeklyRequiredHours || 0;
    const assignedHoursMap = cellData.teacherWeeklyHours || {};

    // 计算当前单元格已分配的总课时
    const totalAssignedInCell = Object.values(assignedHoursMap).reduce(
      (sum: number, hours: number) => sum + (hours || 0),
      0,
    );

    const isFullyAllocated = totalAssignedInCell >= requiredHours && requiredHours > 0;

    // 2. 如果已完全分配，进一步筛选
    let displayTeachers = potentialTeachers;
    if (isFullyAllocated) {
      displayTeachers = potentialTeachers.filter((teacher: any) => assignedHoursMap[teacher.id] > 0);
    }

    // 3. 映射最终列表，添加显示所需的信息
    return displayTeachers.map((teacher: any) => {
      const workload = teacherWeeklyWorkloads.value[teacher.id];
      const totalAssignedWeeklyHours = workload ? workload.assignedWeeklyHours : 0;
      const cellAssignedHours = assignedHoursMap[teacher.id] || 0;

      return {
        ...teacher,
        assignedWeeklyHours: totalAssignedWeeklyHours, // 教师总的已分配周课时 (用于 Tooltip 显示)
        cellAssignedHours, // 教师在此单元格分配的课时 (可能未来有用)
        // disabled: false,
      };
    });
  };

  const calculateHours = (filter = null, type = 'total') => {
    const entries = Object.entries(weeklyScheduleData.value);
    const filteredEntries = filter ? entries.filter(filter) : entries;

    return filteredEntries.reduce((sum, [, data]: [any, any]) => {
      if (type === 'total') {
        return sum + (data?.weeklyRequiredHours || 0);
      }
      if (type === 'assigned') {
        // 累加该条目下所有教师的分配课时
        const teacherTotal: any = data.teacherWeeklyHours
          ? Object.values(data.teacherWeeklyHours).reduce((tSum: number, hours: number) => tSum + (hours || 0), 0) || 0
          : 0;
        return sum + teacherTotal;
      }
      return sum;
    }, 0);
  };

  // 计算单元格内所有老师的实际分配课时
  const computedCellActualHours = (courseId, clsId) => {
    let sum = 0;
    const teacherIds: number[] = getAvailableTeachers(courseId, clsId)?.map((item) => item.id);
    teacherIds.forEach((teacherId) => {
      const hours = weeklyScheduleData.value[`${courseId}-${clsId}`]?.teacherWeeklyHours[String(teacherId)] || 0;
      sum += hours;
      if (hours > 0) {
        // 移除教师的 新增标志位
        // 分配了课时的教师id
        let exitsNewTeacherNotSet = false;
        const tIds = Object.keys(weeklyScheduleData.value[`${courseId}-${clsId}`]?.teacherWeeklyHours);
        teachers.value
          .filter((t) => teacherIds.includes(t.id) && t?.newTeacher)
          .forEach((t) => {
            if (!tIds.includes(t.id)) {
              exitsNewTeacherNotSet = true;
            }
          });

        teachers.value.forEach((teacher: any) => {
          if (teacher.id === teacherId && teacher?.newTeacher && !exitsNewTeacherNotSet) {
            delete teacher?.newTeacher; // 这个地方需要新增的教师有课时才删除
          }
        });
      }
    });

    return sum;
  };
  // is exits new teacher in cell group
  const computedExitsNewTeacher = (courseId: number, clsId) => {
    return getAvailableTeachers(courseId, clsId)?.some((item) => item.newTeacher);
  };

  // --- 表格底部统计计算 ---
  // 计算指定班级的总周需求课时。
  const calculateClassWeeklyTotal = (classId: number) => {
    return calculateHours(([key]) => key.endsWith(`-${classId}`), 'total');
  };

  // 计算指定班级的总已分配周课时。
  const calculateClassAssignedWeeklyTotal = (classId: number) => {
    return calculateHours(([key]) => key.endsWith(`-${classId}`), 'assigned');
  };

  // 计算所有班级、所有课程的总周需求课时。
  const calculateTotalWeeklyHours = () => {
    return calculateHours(null, 'total');
  };

  // 计算所有班级、所有课程的总已分配周课时。
  const calculateTotalAssignedWeeklyHours = () => {
    return calculateHours(null, 'assigned');
  };

  // --- 用户操作 ---

  const canAddHours = (courseId: number, classId: number) => {
    const key = `${courseId}-${classId}`;
    const data = weeklyScheduleData.value[key];

    if (!data) return false; // 防御性编程

    const totalAssignedWeeklyHours = data.teacherWeeklyHours
      ? Object.values(data.teacherWeeklyHours).reduce((sum: number, hours: number) => sum + (hours || 0), 0)
      : 0;

    // 只要已分配 < 总需求，就可以添加
    return totalAssignedWeeklyHours < data.weeklyRequiredHours;
  };

  const adjustTeacherHours = (courseId: number, classId: number, teacherId: number, delta: number) => {
    const key = `${courseId}-${classId}`;
    const data = weeklyScheduleData.value[key];

    if (!data) return; // 防御性编程

    // 确保内部结构存在 (不再需要检查 data.teachers)
    if (!data.teacherWeeklyHours) data.teacherWeeklyHours = {};

    const currentHours = data.teacherWeeklyHours[teacherId] || 0;
    const newHours = currentHours + delta;

    // 1. 验证下限
    if (newHours < 0) return;

    // 2. 验证上限
    const totalAssignedForThisCellBefore = data?.teacherWeeklyHours
      ? Object.values(data.teacherWeeklyHours).reduce((sum: number, h: number) => sum + (h || 0), 0)
      : 0;
    const totalAssignedForThisCellAfter = Number(totalAssignedForThisCellBefore) - currentHours + newHours;

    if (totalAssignedForThisCellAfter > data.weeklyRequiredHours) {
      Message.warning(`分配课时 (${totalAssignedForThisCellAfter}) 不能超过课程要求 (${data.weeklyRequiredHours})`);
      return;
    }

    // 3. 更新课时
    if (newHours === 0) {
      // 如果课时归零，从 teacherWeeklyHours 中彻底删除该教师条目
      delete data.teacherWeeklyHours[teacherId];
    } else {
      // 否则更新或添加课时
      data.teacherWeeklyHours[teacherId] = newHours;
    }
    teachers.value.forEach((t) => {
      if (t.id === teacherId) {
        t.newTeacher = false;
      }
    });
    // 5. 更新全局教师工作量并保存
    updateTeacherWeeklyWorkloads();
    saveWeeklyScheduleData();
  };

  const isFullyAssigned = (courseId: number, classId: number) => {
    const key = `${courseId}-${classId}`;
    const data = weeklyScheduleData.value[key];

    // 如果没有数据或需求课时为0，则不算完全分配
    if (!data || !data.weeklyRequiredHours || data.weeklyRequiredHours <= 0) return false;

    const totalAssignedWeeklyHours = data.teacherWeeklyHours
      ? Object.values(data.teacherWeeklyHours).reduce((sum: number, hours: number) => sum + (hours || 0), 0)
      : 0;

    return totalAssignedWeeklyHours === data.weeklyRequiredHours;
  };

  // --- 批量设置相关 ---
  const handleTabChange = () => {};

  const batchSetWeeklyHours = (courseId: number, value: any) => {
    try {
      if (value === null || value === undefined || value === '') return;

      const weeklyHours = parseInt(value, 10);
      if (Number.isNaN(weeklyHours) || weeklyHours < 0) {
        Message.error('请输入有效的非负整数课时');
        return;
      }

      classes.value.forEach((cls: any) => {
        const key = `${courseId}-${cls.id}`;
        if (batchHoursData.value[key]) {
          batchHoursData.value[key].weeklyRequiredHours = weeklyHours;
          // 自动调整单日上限：不能超过周课时，且至少为1 (除非周课时为0)
          const currentDailyMax = batchHoursData.value[key].dailyMaxHours || 1;
          batchHoursData.value[key].dailyMaxHours =
            weeklyHours === 0 ? 1 : Math.max(1, Math.min(currentDailyMax, weeklyHours));
        }
      });
    } catch (error) {
      Message.error('批量设置周课时失败');
    }
  };

  const batchSetDailyMax = (courseId: number, value: any) => {
    try {
      if (value === null || value === undefined || value === '') return;

      const dailyMax = parseInt(value, 10);
      if (Number.isNaN(dailyMax) || dailyMax < 1) {
        return;
      }

      classes.value.forEach((cls: any) => {
        const key = `${courseId}-${cls.id}`;
        if (batchHoursData.value[key]) {
          const weeklyHours = batchHoursData.value[key].weeklyRequiredHours || 0;
          batchHoursData.value[key].dailyMaxHours =
            weeklyHours === 0 ? dailyMax : Math.max(1, Math.min(dailyMax, weeklyHours));
        }
      });
    } catch (error) {
      Message.error('批量设置单日上限失败');
    }
  };

  const batchSetTimeSlot = (courseId, value) => {
    try {
      if (!value || !['any', 'morning', 'afternoon'].includes(value)) {
        return;
      }

      // 按年级分组班级
      const gradeMap = new Map();
      classes.value.forEach((cls) => {
        if (!gradeMap.has(cls.gradeId)) {
          gradeMap.set(cls.gradeId, []);
        }
        gradeMap.get(cls.gradeId).push(cls);
      });

      // 为每个年级的所有班级设置相同的时段
      gradeMap.forEach((gradeClasses) => {
        gradeClasses.forEach((cls) => {
          const key = `${courseId}-${cls.id}`;
          if (batchHoursData.value[key]) {
            batchHoursData.value[key].sessionRequirement = value;
          }
        });
      });

      // Message.success(`已临时将该课程所有年级的时段设置为 ${getSessionText(value)}`)
    } catch (error) {
      // console.error('批量设置时段(临时)失败:', error);
      Message.error('批量设置时段失败');
    }
  };

  const handleSaveData = async () => {
    emits('autoSave');
  };

  // 提前设置单独教师的课时
  const preLoadSingleTeacherHours = () => {
    Object.entries(weeklyScheduleData.value).forEach(([key, value]: [string, any]) => {
      const ids = key.split('-');
      const courseId = Number(ids[0]);
      const classId = Number(ids[1]);
      let potentialTeachers: any = teachersByCourse.value[courseId] || [];
      potentialTeachers = potentialTeachers.filter((p) => {
        // 设置为当前班级 or 没设置固定班级
        if (!p.gradeClassId || p.gradeClassId.includes(classId)) {
          return true;
        }
        return false;
      });
      // 单个教师
      if (potentialTeachers.length === 1) {
        if (Object.keys(value.teacherWeeklyHours).length <= 1) {
          const teacherId = potentialTeachers[0]?.id;
          value.teacherWeeklyHours = {
            [teacherId]: value.weeklyRequiredHours,
          };
          teachers.value.forEach((teacher: any) => {
            // 分配课时后去掉新增标签
            if (teacherId === teacher.id) {
              teacher.newTeacher = false;
              delete teacher.newTeacher;
            }
          });
        }
      } /* else if (potentialTeachers?.length > 1) {
        // 多个教师
        const hasNewTeachers = potentialTeachers?.some((teacher: any) => teacher?.newTeacher) || false;
        const setHoursTeacherIds = Object.keys(value.teacherWeeklyHours);
        const newTeacherIds = potentialTeachers.filter((t) => t.newTeacher);
        let exitsNewNotSetHour = false;
        // eslint-disable-next-line no-restricted-syntax
        for (const id in newTeacherIds) {
          if (!setHoursTeacherIds.includes(id)) {
            exitsNewNotSetHour = true; // 存在未分配的新增教师
            break;
          }
        }
        if (hasNewTeachers && exitsNewNotSetHour) {
          // 存在新增教师，并且，这个新增教师不存在于分配的课时里面
          value.teacherWeeklyHours = {}; // 全部重置
        }
      } */
    });
    updateTeacherWeeklyWorkloads();

    generalSettings.value.weeklyScheduleData = weeklyScheduleData.value;
    generalSettings.value.teachers = teachers.value;
  };

  const applyBatchHours = () => {
    try {
      if (!batchHoursData.value || Object.keys(batchHoursData.value).length === 0) {
        Message.warning('没有可应用的批量设置数据');
        return;
      }

      let validationErrors = false; // 标记是否有校验错误
      const courseGradeTimeSlots = {}; // 存储每个 {课程ID-年级ID} 的最终时段设置

      // 1. 预处理：收集每个课程在各年级的最终时段设置，并进行初步校验
      Object.entries(batchHoursData.value).forEach(([key, data]: [string, any]) => {
        if (!data) {
          validationErrors = true;
          return;
        }
        if (
          typeof data.weeklyRequiredHours !== 'number' ||
          data.weeklyRequiredHours < 0 ||
          typeof data.dailyMaxHours !== 'number' ||
          data.dailyMaxHours < 1
        ) {
          validationErrors = true;
        }

        const [courseIdStr, classIdStr] = key.split('-');
        if (!courseIdStr || !classIdStr) return; // 无效 key
        const courseId = Number(courseIdStr);
        const classId = Number(classIdStr);

        const currentClass = classes.value.find((cls) => cls.id === classId);
        if (currentClass) {
          const { gradeId } = currentClass;
          const timeSlotKey = `${courseId}-${gradeId}`;
          courseGradeTimeSlots[timeSlotKey] = data.sessionRequirement || 'any';
        } else {
          validationErrors = true;
        }
      });

      if (validationErrors) {
        Message.warning('批量设置数据中存在无效条目，请检查输入。部分设置可能未应用。');
      }

      // 2. 应用设置到 weeklyScheduleData
      Object.entries(batchHoursData.value).forEach(([key, data]: [string, any]) => {
        if (
          !data ||
          typeof data.weeklyRequiredHours !== 'number' ||
          data.weeklyRequiredHours < 0 ||
          typeof data.dailyMaxHours !== 'number' ||
          data.dailyMaxHours < 1
        ) {
          return;
        }

        const [courseIdStr, classIdStr] = key.split('-');
        if (!courseIdStr || !classIdStr) return;
        const courseId = Number(courseIdStr);
        const classId = Number(classIdStr);

        const currentClass = classes.value.find((cls) => cls.id === classId);
        if (currentClass) {
          const { gradeId } = currentClass;
          // 获取该课程在该年级的最终时段设置
          const finalTimeSlot = courseGradeTimeSlots[`${courseId}-${gradeId}`] || 'any';

          // 确保 weeklyScheduleData 中存在该条目
          if (!weeklyScheduleData.value[key]) {
            // 如果不存在，创建一个新的默认条目再更新，虽然理论上 initBatchHoursData 后应该都存在
            weeklyScheduleData.value[key] = {
              weeklyRequiredHours: 0,
              sessionRequirement: 'any',
              dailyMaxHours: 2,
              teacherWeeklyHours: {},
              teachers: [],
            };
          }

          // 更新 weeklyScheduleData
          const targetData = weeklyScheduleData.value[key];
          const newWeeklyHours = data.weeklyRequiredHours;
          const newDailyMax = data.dailyMaxHours;

          targetData.weeklyRequiredHours = newWeeklyHours;
          targetData.sessionRequirement = finalTimeSlot;

          // 校验并更新 dailyMaxHours: 不能超过周课时 (除非周课时为0)，且至少为1
          targetData.dailyMaxHours =
            newWeeklyHours === 0 ? newDailyMax : Math.max(1, Math.min(newDailyMax, newWeeklyHours));
        }
      });

      // 3. 保存并切换 Tab
      saveWeeklyScheduleData();
      updateTeacherWeeklyWorkloads(); // 应用设置后可能影响工作量 (虽然这里只改要求，但保持同步)
      Message.success('批量课时安排已应用');
      activeTab.value = '1';
      preLoadSingleTeacherHours();
    } catch (error) {
      Message.error('应用批量设置失败，请查看控制台');
    }
  };

  const calculateBatchClassTotal = (classId) => {
    return courses.value.reduce((sum, course) => {
      const key = `${course.id}-${classId}`;
      // 安全访问 batchHoursData
      return sum + (batchHoursData.value[key]?.weeklyRequiredHours || 0);
    }, 0);
  };

  const updateBatchTimeSlot = (courseId, classId, timeSlot) => {
    try {
      if (!timeSlot || !['any', 'morning', 'afternoon'].includes(timeSlot)) {
        return;
      }

      const currentClass = classes.value.find((cls) => cls.id === classId);
      if (!currentClass) return;
      const { gradeId } = currentClass;

      const sameGradeClasses = classes.value.filter((cls) => cls.gradeId === gradeId);

      sameGradeClasses.forEach((cls) => {
        const key = `${courseId}-${cls.id}`;
        if (batchHoursData.value[key]) {
          batchHoursData.value[key].sessionRequirement = timeSlot;
        }
      });
    } catch (error) {
      // console.error('更新批量设置时段(单元格)失败:', error);
    }
  };

  // --- 生命周期钩子 ---
  let cleanupWatchers = null;

  onMounted(async () => {
    isLoading.value = true;
    try {
      await loadData();
      preLoadSingleTeacherHours();
      cleanupWatchers = setupWatchers();
    } catch (error) {
      Message.error('页面加载失败');
    } finally {
      isLoading.value = false;
    }
  });

  onUnmounted(() => {
    if (cleanupWatchers) {
      cleanupWatchers();
    }
  });
  watch(
    () => props.setting,
    (newVal) => {
      emits('autoSave');
    },
  );
</script>

<style scoped>
  /* 表格基础布局 */
  .table-container {
    overflow-x: auto;
    overflow-y: auto;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    background: white;
    max-height: calc(100vh - 200px);
  }

  .course-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    position: relative;
  }

  /* 表头样式 */
  .subject-header {
    width: 120px;
    min-width: 120px;
    background: #f3f4f6;
    position: sticky;
    left: 0;
    top: 0;
    z-index: 3;
    padding: 12px;
    font-weight: 600;
    border-bottom: 1px solid #e5e7eb;
    border-right: 1px solid #e5e7eb;
    height: 88px;
  }

  .grade-header {
    background: #e5e7eb;
    padding: 12px;
    text-align: center;
    font-weight: 600;
    color: #374151;
    position: sticky;
    top: 0;
    z-index: 2;
    height: 44px;
    border: none;
  }

  .class-header {
    background: #f3f4f6;
    padding: 12px;
    text-align: center;
    font-weight: 500;
    color: #4b5563;
    min-width: 240px;
    width: 240px;
    position: sticky;
    top: 44px;
    z-index: 2;
    height: 44px;
    border: none;
  }

  /* 表格单元格样式 */
  .course-table td,
  .course-table th {
    border: 1px solid #d1d5db;
  }

  .subject-cell {
    width: 120px;
    min-width: 120px;
    background: #f9fafb !important;
    position: sticky;
    left: 0;
    z-index: 1;
    padding: 12px;
    font-weight: 600;
    color: #3b82f6;
    border-top: 1px solid #d1d5db;
  }

  /* 统计行样式 */
  tfoot tr td {
    position: sticky;
    bottom: 0;
    z-index: 2;
    padding: 8px 12px;
    background: #f3f4f6 !important;
    box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.08);
    border-top: 1px solid #d1d5db;
  }

  .stats-cell {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 4px 0;
  }

  .stats-label {
    font-size: 12px;
    color: #4b5563;
    font-weight: 500;
  }

  .stats-numbers {
    font-weight: 600;
    font-size: 14px;
    color: #1f2937;
  }

  tbody tr:last-child td {
    padding-bottom: 24px;
  }

  /* 课程单元格样式 */
  .course-cell {
    padding: 12px;
    min-width: 100px;
    transition: background-color 0.2s;
    vertical-align: top;
  }

  .course-cell:hover {
    background-color: #f0f9ff !important;
  }

  .cell-content {
    min-height: 120px;
  }

  /* 课时设置区域 */
  .option-row-display {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    width: 100%;
    justify-content: center;
  }

  /* 图标样式 */
  .option-icon {
    font-size: 18px;
  }

  .icon-weekly {
    color: #22c55e !important;
  }

  .icon-daily {
    color: #f97316 !important;
  }

  .icon-time {
    color: #3b82f6 !important;
  }

  /* 新增：图标值组合样式 */
  .icon-value-group {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 14px;
    color: #4b5563; /* 默认文字颜色 */
  }

  .weekly-group {
    background-color: #e8f9ee; /* 浅绿色背景 */
  }

  .daily-group {
    background-color: #fff7e6; /* 浅橙色背景 */
  }

  .time-group {
    background-color: #e6f4ff; /* 浅蓝色背景 */
  }

  /* 教师列表样式 */
  .teachers-container {
    margin-top: 16px;
  }

  .teacher-item {
    border-radius: 6px;
    margin: 0 auto 8px;
    transition: all 0.2s;
    max-width: 180px;
  }

  .teacher-item:hover {
    border-color: #d4e6ff;
    background-color: #f5f8ff;
  }

  .teacher-content {
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 6px;
  }

  .teacher-info {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 0;
    flex: 1;
  }

  .teacher-name {
    font-size: 13px;
    color: #374151;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 8px;
  }

  .teacher-controls {
    display: flex;
    align-items: center;
    gap: 2px;
  }

  /* 控制按钮样式 */
  .control-button {
    width: 24px;
    height: 24px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-bg-2);
    color: var(--color-text-2);
    border: 1px solid var(--color-border);
    border-radius: 2px;
    transition: all 0.1s cubic-bezier(0, 0, 1, 1);
  }

  .control-button:hover:not(:disabled) {
    background-color: var(--color-fill-3);
    border-color: var(--color-border);
  }

  .control-button:disabled {
    background-color: var(--color-fill-2);
    color: var(--color-text-4);
    border-color: var(--color-border);
    cursor: not-allowed;
  }

  .hours-number {
    min-width: 24px;
    text-align: center;
    font-size: 13px;
    color: #4b5563;
    font-weight: 500;
  }

  /* Arco Design 组件样式覆盖 */
  :deep(.arco-btn) {
    border-radius: 2px;
  }

  /* 批量设置表单样式 */
  .batch-hours-modal :deep(.arco-modal-body) {
    padding: 16px 20px;
  }

  /* 批量设置头部样式 */
  .batch-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    margin-bottom: 16px;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e5e7eb;
  }

  .batch-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .info-icon {
    color: #3b82f6;
    font-size: 16px;
  }

  .info-text {
    color: #4b5563;
    font-size: 14px;
  }

  .course-header {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .course-title {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
  }

  .batch-settings-row {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
    padding: 12px 16px;
  }

  /* 合并相同的图标样式 */
  .batch-input-item,
  .input-item {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .batch-input-item .option-icon,
  .input-item .option-icon {
    font-size: 14px;
    color: #4b5563;
    flex-shrink: 0;
  }

  :deep(.arco-input-number),
  :deep(.arco-select) {
    flex: 1;
    min-width: 60px;
  }

  .input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  /* 批量设置表格样式 */
  .batch-table {
    border-collapse: collapse;
    width: 100%;
  }

  .batch-table th,
  .batch-table td {
    border: 1px solid #e5e7eb;
    padding: 12px;
  }

  .batch-table tbody tr.striped {
    background-color: #f9fafb;
  }

  .batch-table tbody tr:hover {
    background-color: #f3f4f6;
  }

  .course-name {
    font-weight: 600;
    color: #3b82f6;
    margin-bottom: 8px;
  }

  .grade-header-cell {
    background-color: #f3f4f6;
    font-weight: 500;
  }

  .grade-title {
    padding: 8px;
    font-size: 14px;
    color: #374151;
  }

  .batch-table tfoot td {
    background-color: #f3f4f6;
    font-weight: 500;
    color: #374151;
    padding: 12px;
  }

  /* 说明卡片样式 */
  .guide-card {
    width: fit-content;
    min-width: 800px;
    margin-left: 0;
    margin-bottom: 12px;
  }

  .guide-card :deep(.arco-card-header) {
    padding: 8px 16px;
    min-height: auto;
    border-bottom: 1px solid #e5e7eb;
  }

  .guide-card :deep(.arco-card-body) {
    padding: 12px 16px;
  }

  .guide-content {
    padding: 0;
  }

  .icon-guide-list {
    display: flex;
    flex-direction: row;
    gap: 12px;
    width: fit-content;
  }

  .icon-guide-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: #f9fafb;
    border-radius: 4px;
    border: 1px solid #e5e7eb;
    white-space: nowrap;
    flex: 1;
  }

  .icon-guide-item .option-icon {
    font-size: 16px;
    flex-shrink: 0;
  }

  .icon-guide-item .guide-text {
    color: #374151;
    font-size: 13px;
  }

  /* 标签页样式 */
  :deep(.arco-tabs-nav) {
    margin-bottom: 16px;
  }

  :deep(.arco-tabs-nav-tab) {
    border-bottom-color: #e5e7eb;
  }

  :deep(.arco-tabs-nav-item) {
    padding: 12px 24px;
    font-size: 14px;
  }

  :deep(.arco-tabs-nav-item-active) {
    color: #1d4ed8;
    font-weight: 500;
  }

  .course-tabs-card :deep(.arco-card-body) {
    padding: 12px 16px;
  }

  .batch-hours-form .arco-alert {
    margin: 12px 0;
  }

  /* 时段设置样式 */
  .session-text {
    font-size: 13px;
  }

  .session-morning {
    color: #1677ff;
  }

  .session-afternoon {
    color: #fa8c16;
  }

  .session-any {
    color: #666;
  }

  ::-webkit-scrollbar {
    height: 1px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #93cfff;
    border-radius: 10px;
  }

  ::-webkit-scrollbar-track {
    background-color: transparent; /* 可设置为其他背景色 */
  }

  /* 针对 Firefox 浏览器 */
  * {
    scrollbar-width: thin; /* 滚动条变细 */
    scrollbar-color: #93cfff transparent; /* 滚动条颜色和轨道背景色 */
  }
</style>
