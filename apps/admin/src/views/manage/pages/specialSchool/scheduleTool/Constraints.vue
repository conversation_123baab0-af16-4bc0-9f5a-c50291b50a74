<template>
  <div class="constraints-container">
    <a-card class="mb-4">
      <template #title>排课约束设置</template>
      <template #extra>
        <a-space>
          <a-button size="mini" type="primary" status="danger" @click="showResetConfirm">清空课表</a-button>
        </a-space>
      </template>
      <a-space direction="vertical" size="large" fill>
        <!-- 将年级选择和节次设置合并到同一行，进一步缩窄各元素宽度 -->
        <a-row :gutter="16" class="mb-4">
          <a-col :span="6">
            <a-form-item label="选择年级">
              <a-select
                v-model="selectedGradeId"
                placeholder="请选择年级"
                :style="{ width: '100%' }"
                size="mini"
                @change="handleGradeChange"
              >
                <a-option v-for="grade in grades" :key="grade.id" :value="grade.id">
                  {{ grade.name }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="上午节数">
              <a-input-number
                v-model="periodSettings.morning"
                :min="1"
                size="mini"
                :max="6"
                :style="{ width: '100%' }"
                @change="updatePeriods"
              />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="下午节数">
              <a-input-number
                v-model="periodSettings.afternoon"
                :min="1"
                :max="6"
                size="mini"
                :style="{ width: '100%' }"
                @change="updatePeriods"
              />
            </a-form-item>
          </a-col>
          <a-col :span="2">
            <a-form-item label=" " class="button-form-item">
              <a-button size="mini" type="primary" @click="savePeriodSettings">保存<!--节次-->设置</a-button>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 添加可排课节次统计信息区域 -->
        <a-row :gutter="16" class="mb-4">
          <a-col :span="24">
            <a-card title="可排课节次统计" :bordered="false" class="statistics-card">
              <div class="custom-statistic">
                <div class="statistic-title">可排课节次数量</div>
                <div class="statistic-content">
                  <span class="statistic-value">{{ currentGradeAvailablePeriods }}/{{ totalAvailablePeriods }}</span>
                  <span class="statistic-label">（当前年级/全校）</span>
                  <a-tooltip content="状态不为'留空'的节次总和">
                    <icon-info-circle class="info-icon" />
                  </a-tooltip>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>

        <!-- 课表约束设置 -->
        <a-card title="课表约束设置" class="mb-4">
          <template #extra>
            <a-button type="primary" size="mini" @click="showCopyModal">从其他年级复制</a-button>
          </template>
          <a-space direction="vertical" size="large" fill>
            <!-- 上午课表 -->
            <div>
              <h3 class="section-title">上午课程</h3>
              <a-table :columns="weekdayColumns" :data="morningPeriods" :pagination="false" bordered>
                <template #period="{ record }">
                  {{ record.period }}
                </template>
                <template #monday="{ record }">
                  <div class="constraint-cell" @click="editConstraint(record, 'monday', true)">
                    <div class="status-tag" :class="record?.monday?.status">
                      {{ getStatusText(record?.monday?.status) }}
                    </div>
                    <div
                      v-if="record?.monday?.status !== 'empty' && record?.monday?.hasTeachingGroup"
                      class="teaching-group"
                    >
                      教研组: {{ getTeachingGroupName(record?.monday?.teachingGroupId) }}
                    </div>
                    <div v-if="record?.monday?.status === 'fixed'" class="fixed-course">
                      课程: {{ getCourseName(record?.monday?.fixedCourseId) }}
                    </div>
                    <div
                      v-if="
                        record?.monday?.status === 'normal' &&
                        record?.monday?.priorityCourses &&
                        record?.monday?.priorityCourses.length > 0
                      "
                      class="priority-course high"
                    >
                      优先: {{ getPriorityCourseNames(record?.monday?.priorityCourses) }}
                    </div>
                    <div
                      v-if="
                        record?.monday?.status === 'normal' &&
                        record?.monday?.avoidCourses &&
                        record?.monday?.avoidCourses.length > 0
                      "
                      class="priority-course low"
                    >
                      避免: {{ getPriorityCourseNames(record?.monday?.avoidCourses) }}
                    </div>
                  </div>
                </template>
                <template #tuesday="{ record }">
                  <div class="constraint-cell" @click="editConstraint(record, 'tuesday', true)">
                    <div class="status-tag" :class="record?.tuesday?.status">
                      {{ getStatusText(record?.tuesday?.status) }}
                    </div>
                    <div v-if="record?.tuesday?.hasTeachingGroup" class="teaching-group">
                      教研组: {{ getTeachingGroupName(record?.tuesday?.teachingGroupId) }}
                    </div>
                    <div v-if="record?.tuesday?.status === 'fixed'" class="fixed-course">
                      课程: {{ getCourseName(record?.tuesday?.fixedCourseId) }}
                    </div>
                    <div
                      v-if="record?.tuesday?.priorityCourses && record?.tuesday?.priorityCourses.length > 0"
                      class="priority-course high"
                    >
                      优先: {{ getPriorityCourseNames(record?.tuesday?.priorityCourses) }}
                    </div>
                    <div
                      v-if="record?.tuesday?.avoidCourses && record?.tuesday?.avoidCourses.length > 0"
                      class="priority-course low"
                    >
                      避免: {{ getPriorityCourseNames(record?.tuesday?.avoidCourses) }}
                    </div>
                  </div>
                </template>
                <template #wednesday="{ record }">
                  <div class="constraint-cell" @click="editConstraint(record, 'wednesday', true)">
                    <div class="status-tag" :class="record?.wednesday?.status">
                      {{ getStatusText(record?.wednesday?.status) }}
                    </div>
                    <div v-if="record?.wednesday?.hasTeachingGroup" class="teaching-group">
                      教研组: {{ getTeachingGroupName(record?.wednesday?.teachingGroupId) }}
                    </div>
                    <div v-if="record?.wednesday?.status === 'fixed'" class="fixed-course">
                      课程: {{ getCourseName(record?.wednesday?.fixedCourseId) }}
                    </div>
                    <div
                      v-if="record?.wednesday?.priorityCourses && record?.wednesday?.priorityCourses.length > 0"
                      class="priority-course high"
                    >
                      优先: {{ getPriorityCourseNames(record?.wednesday?.priorityCourses) }}
                    </div>
                    <div
                      v-if="record?.wednesday?.avoidCourses && record?.wednesday?.avoidCourses.length > 0"
                      class="priority-course low"
                    >
                      避免: {{ getPriorityCourseNames(record?.wednesday?.avoidCourses) }}
                    </div>
                  </div>
                </template>
                <template #thursday="{ record }">
                  <div class="constraint-cell" @click="editConstraint(record, 'thursday', true)">
                    <div class="status-tag" :class="record?.thursday?.status">
                      {{ getStatusText(record?.thursday?.status) }}
                    </div>
                    <div v-if="record?.thursday?.hasTeachingGroup" class="teaching-group">
                      教研组: {{ getTeachingGroupName(record?.thursday?.teachingGroupId) }}
                    </div>
                    <div v-if="record?.thursday?.status === 'fixed'" class="fixed-course">
                      课程: {{ getCourseName(record?.thursday?.fixedCourseId) }}
                    </div>
                    <div
                      v-if="record?.thursday?.priorityCourses && record?.thursday?.priorityCourses.length > 0"
                      class="priority-course high"
                    >
                      优先: {{ getPriorityCourseNames(record?.thursday?.priorityCourses) }}
                    </div>
                    <div
                      v-if="record?.thursday?.avoidCourses && record?.thursday?.avoidCourses.length > 0"
                      class="priority-course low"
                    >
                      避免: {{ getPriorityCourseNames(record?.thursday?.avoidCourses) }}
                    </div>
                  </div>
                </template>
                <template #friday="{ record }">
                  <div class="constraint-cell" @click="editConstraint(record, 'friday', true)">
                    <div class="status-tag" :class="record?.friday?.status">
                      {{ getStatusText(record?.friday?.status) }}
                    </div>
                    <div v-if="record?.friday?.hasTeachingGroup" class="teaching-group">
                      教研组: {{ getTeachingGroupName(record?.friday?.teachingGroupId) }}
                    </div>
                    <div v-if="record?.friday?.status === 'fixed'" class="fixed-course">
                      课程: {{ getCourseName(record?.friday?.fixedCourseId) }}
                    </div>
                    <div
                      v-if="record?.friday?.priorityCourses && record?.friday?.priorityCourses.length > 0"
                      class="priority-course high"
                    >
                      优先: {{ getPriorityCourseNames(record?.friday?.priorityCourses) }}
                    </div>
                    <div
                      v-if="record?.friday?.avoidCourses && record?.friday?.avoidCourses.length > 0"
                      class="priority-course low"
                    >
                      避免: {{ getPriorityCourseNames(record?.friday?.avoidCourses) }}
                    </div>
                  </div>
                </template>
              </a-table>
            </div>

            <!-- 下午课表 -->
            <div>
              <h3 class="section-title">下午课程</h3>
              <a-table :columns="weekdayColumns" :data="afternoonPeriods" :pagination="false" bordered>
                <template #period="{ record }">
                  {{ record.period }}
                </template>
                <template #monday="{ record }">
                  <div class="constraint-cell" @click="editConstraint(record, 'monday')">
                    <div class="status-tag" :class="record?.monday?.status">
                      {{ getStatusText(record?.monday?.status) }}
                    </div>
                    <div v-if="record?.monday?.hasTeachingGroup" class="teaching-group">
                      教研组: {{ getTeachingGroupName(record?.monday?.teachingGroupId) }}
                    </div>
                    <div v-if="record?.monday?.status === 'fixed'" class="fixed-course">
                      课程: {{ getCourseName(record?.monday?.fixedCourseId) }}
                    </div>
                    <div
                      v-if="record?.monday?.priorityCourses && record?.monday?.priorityCourses.length > 0"
                      class="priority-course high"
                    >
                      优先: {{ getPriorityCourseNames(record?.monday?.priorityCourses) }}
                    </div>
                    <div
                      v-if="record?.monday?.avoidCourses && record?.monday?.avoidCourses.length > 0"
                      class="priority-course low"
                    >
                      避免: {{ getPriorityCourseNames(record?.monday?.avoidCourses) }}
                    </div>
                  </div>
                </template>
                <template #tuesday="{ record }">
                  <div class="constraint-cell" @click="editConstraint(record, 'tuesday')">
                    <div class="status-tag" :class="record?.tuesday?.status">
                      {{ getStatusText(record?.tuesday?.status) }}
                    </div>
                    <div v-if="record?.tuesday?.hasTeachingGroup" class="teaching-group">
                      教研组: {{ getTeachingGroupName(record?.tuesday?.teachingGroupId) }}
                    </div>
                    <div v-if="record?.tuesday?.status === 'fixed'" class="fixed-course">
                      课程: {{ getCourseName(record?.tuesday?.fixedCourseId) }}
                    </div>
                    <div
                      v-if="record?.tuesday?.priorityCourses && record?.tuesday?.priorityCourses.length > 0"
                      class="priority-course high"
                    >
                      优先: {{ getPriorityCourseNames(record?.tuesday?.priorityCourses) }}
                    </div>
                    <div
                      v-if="record?.tuesday?.avoidCourses && record?.tuesday?.avoidCourses.length > 0"
                      class="priority-course low"
                    >
                      避免: {{ getPriorityCourseNames(record?.tuesday?.avoidCourses) }}
                    </div>
                  </div>
                </template>
                <template #wednesday="{ record }">
                  <div class="constraint-cell" @click="editConstraint(record, 'wednesday')">
                    <div class="status-tag" :class="record?.wednesday?.status">
                      {{ getStatusText(record?.wednesday?.status) }}
                    </div>
                    <div v-if="record?.wednesday?.hasTeachingGroup" class="teaching-group">
                      教研组: {{ getTeachingGroupName(record?.wednesday?.teachingGroupId) }}
                    </div>
                    <div v-if="record?.wednesday?.status === 'fixed'" class="fixed-course">
                      课程: {{ getCourseName(record?.wednesday?.fixedCourseId) }}
                    </div>
                    <div
                      v-if="record?.wednesday?.priorityCourses && record?.wednesday?.priorityCourses.length > 0"
                      class="priority-course high"
                    >
                      优先: {{ getPriorityCourseNames(record?.wednesday?.priorityCourses) }}
                    </div>
                    <div
                      v-if="record?.wednesday?.avoidCourses && record?.wednesday?.avoidCourses.length > 0"
                      class="priority-course low"
                    >
                      避免: {{ getPriorityCourseNames(record?.wednesday?.avoidCourses) }}
                    </div>
                  </div>
                </template>
                <template #thursday="{ record }">
                  <div class="constraint-cell" @click="editConstraint(record, 'thursday')">
                    <div class="status-tag" :class="record?.thursday?.status">
                      {{ getStatusText(record?.thursday?.status) }}
                    </div>
                    <div v-if="record?.thursday?.hasTeachingGroup" class="teaching-group">
                      教研组: {{ getTeachingGroupName(record?.thursday?.teachingGroupId) }}
                    </div>
                    <div v-if="record?.thursday?.status === 'fixed'" class="fixed-course">
                      课程: {{ getCourseName(record?.thursday?.fixedCourseId) }}
                    </div>
                    <div
                      v-if="record?.thursday?.priorityCourses && record?.thursday?.priorityCourses.length > 0"
                      class="priority-course high"
                    >
                      优先: {{ getPriorityCourseNames(record?.thursday?.priorityCourses) }}
                    </div>
                    <div
                      v-if="record?.thursday?.avoidCourses && record?.thursday?.avoidCourses.length > 0"
                      class="priority-course low"
                    >
                      避免: {{ getPriorityCourseNames(record?.thursday?.avoidCourses) }}
                    </div>
                  </div>
                </template>
                <template #friday="{ record }">
                  <div class="constraint-cell" @click="editConstraint(record, 'friday')">
                    <div class="status-tag" :class="record?.friday?.status">
                      {{ getStatusText(record?.friday?.status) }}
                    </div>
                    <div v-if="record?.friday?.hasTeachingGroup" class="teaching-group">
                      教研组: {{ getTeachingGroupName(record?.friday?.teachingGroupId) }}
                    </div>
                    <div v-if="record?.friday?.status === 'fixed'" class="fixed-course">
                      课程: {{ getCourseName(record?.friday?.fixedCourseId) }}
                    </div>
                    <div
                      v-if="record?.friday?.priorityCourses && record?.friday?.priorityCourses.length > 0"
                      class="priority-course high"
                    >
                      优先: {{ getPriorityCourseNames(record?.friday?.priorityCourses) }}
                    </div>
                    <div
                      v-if="record?.friday?.avoidCourses && record?.friday?.avoidCourses.length > 0"
                      class="priority-course low"
                    >
                      避免: {{ getPriorityCourseNames(record?.friday?.avoidCourses) }}
                    </div>
                  </div>
                </template>
              </a-table>
            </div>
          </a-space>
        </a-card>
      </a-space>
    </a-card>

    <!-- 约束编辑弹窗 -->
    <a-modal
      v-model:visible="showConstraintModal"
      title="编辑约束设置"
      width="600px"
      @ok="saveEditConstraint"
      @cancel="cancelEditConstraint"
    >
      <a-form :model="editingConstraint" layout="vertical">
        <!-- 第一行：状态选择 -->
        <a-form-item label="状态">
          <a-select v-model="editingConstraint.status">
            <a-option value="normal">正常排课</a-option>
            <a-option value="empty">留空</a-option>
            <a-option value="fixed">固定课程</a-option>
          </a-select>
        </a-form-item>

        <!-- 第二行：教研活动相关设置 -->
        <div v-show="editingConstraint.status !== 'empty' && editingConstraint.status !== 'fixed'">
          <a-form-item label="教研活动">
            <div class="teaching-group-container">
              <a-switch v-model="editingConstraint.hasTeachingGroup" class="teaching-group-switch" />
              <div v-if="editingConstraint.hasTeachingGroup" class="teaching-group-select-container">
                <a-select
                  v-model="editingConstraint.teachingGroupId"
                  placeholder="选择教研组"
                  class="teaching-group-select"
                >
                  <a-option v-for="group in teachingGroups" :key="group.id" :value="group.id">
                    {{ group.name }}
                  </a-option>
                </a-select>
              </div>
            </div>
          </a-form-item>
        </div>

        <!-- 第三行：固定课程选择 -->
        <a-form-item v-show="editingConstraint.status === 'fixed'" label="固定课程">
          <a-select v-model="editingConstraint.fixedCourseId" placeholder="选择固定课程">
            <a-option v-for="course in courses" :key="course.id" :value="course.id">
              {{ course.name }}
            </a-option>
          </a-select>
        </a-form-item>

        <!-- 课程优先级设置部分 -->
        <template v-if="editingConstraint.status === 'normal'">
          <a-divider style="margin: 16px 0 12px 0">课程优先级设置</a-divider>

          <a-form-item label="优先安排">
            <a-select
              v-model="editingConstraint.priorityCourses"
              placeholder="选择优先排课的课程"
              multiple
              @change="handlePriorityChange"
            >
              <a-option
                v-for="course in availablePriorityCourses"
                :key="course.id"
                :value="course.id"
                :disabled="course.disabled || currentCantAvailableCourse.includes(course.id)"
              >
                {{ course.label || course.name }}
              </a-option>
            </a-select>
          </a-form-item>

          <a-form-item label="禁排课程">
            <a-select
              v-model="editingConstraint.avoidCourses"
              placeholder="选择避免排课的课程"
              multiple
              @change="handleAvoidChange"
            >
              <a-option
                v-for="course in availableAvoidCourses"
                :key="course.id"
                :value="course.id"
                :disabled="course.disabled"
              >
                {{ course.label || course.name }}
              </a-option>
            </a-select>
          </a-form-item>
        </template>

        <!-- 当状态为empty时的提示 -->
        <a-empty
          v-if="editingConstraint.status === 'empty'"
          description="此时段将留空，不排课"
          :image-size="100"
          style="margin: 20px 0"
        />
      </a-form>
    </a-modal>

    <!-- 清空课表确认弹窗 -->
    <a-modal
      v-model:visible="showResetModal"
      title="确认清空"
      width="400px"
      @ok="resetConstraintSettings"
      @cancel="cancelReset"
    >
      <p>确定要清空当前年级的所有课表设置吗？此操作不可恢复。</p>
    </a-modal>

    <!-- 添加复制设置弹窗 -->
    <a-modal
      v-model:visible="showCopySettingsModal"
      title="从其他年级复制约束设置"
      width="400px"
      @ok="copyConstraintSettings"
      @cancel="cancelCopy"
    >
      <a-form layout="vertical" :model="copyFormModel">
        <a-form-item label="选择来源年级">
          <a-select v-model="copySourceGradeId" placeholder="请选择要复制的来源年级">
            <a-option v-for="grade in availableCopyGrades" :key="grade.id" :value="grade.id">
              {{ grade.name }}
            </a-option>
          </a-select>
        </a-form-item>

        <!-- 使用自定义警告样式 -->
        <div class="custom-warning">
          <span class="warning-icon">!</span>
          <span class="warning-text">此操作将会覆盖当前年级 ({{ getCurrentGradeName() }}) 的所有课表约束设置。</span>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { IconInfoCircle } from '@arco-design/web-vue/es/icon';

  const props = defineProps({
    setting: {
      type: Object,
    },
  });
  const emits = defineEmits(['update:setting', 'autoSave']);
  const generalSettings = computed({
    get: () => {
      return props.setting;
    },
    set: (val) => {
      emits('update:setting', val);
    },
  });
  // 周一到周五的课表列定义
  const weekdayColumns = [
    { title: '节次', dataIndex: 'period', slotName: 'period' },
    { title: '周一', dataIndex: 'monday', slotName: 'monday' },
    { title: '周二', dataIndex: 'tuesday', slotName: 'tuesday' },
    { title: '周三', dataIndex: 'wednesday', slotName: 'wednesday' },
    { title: '周四', dataIndex: 'thursday', slotName: 'thursday' },
    { title: '周五', dataIndex: 'friday', slotName: 'friday' },
  ];

  // --- 响应式状态定义 ---

  const showConstraintModal = ref(false);
  const editingConstraint = ref<any>({});
  const editingRecord = ref(null);
  const editingDay = ref('');

  const grades = ref([]);
  const selectedGradeId = ref(null);

  const teachingGroups = ref([]);

  const courses = ref([]);

  // 用于检查课程的时段限制 (上午/下午)
  const coursePlanData = ref({});

  const classes = ref([]);

  const periodSettings = ref({
    morning: 4,
    afternoon: 3,
  });

  const defaultPeriodSettings = {
    morning: 4,
    afternoon: 3,
  };

  const morningPeriods = ref([]);
  const afternoonPeriods = ref([]);

  const showResetModal = ref(false);

  const showCopySettingsModal = ref(false);
  const copySourceGradeId = ref(null);
  const copyFormModel = ref({ sourceGradeId: null });

  // 当前年级可排课的总节次数 (单个班级)
  const currentGradeAvailablePeriods = ref(0);
  // 全校所有班级可排课的总节次数
  const totalAvailablePeriods = ref(0);

  const currentCantAvailableCourse = ref<number[]>([]);

  const preferMap = computed(() => {
    const gradeToClassesMap = generalSettings.value.classes.reduce((acc, curr) => {
      if (!acc[curr.gradeId]) {
        acc[curr.gradeId] = [];
      }
      acc[curr.gradeId].push(curr.id);
      return acc;
    }, {});
    const data = generalSettings.value?.weeklyScheduleData;
    const result = {}; // gradeId:{afternoon:[courseId],morning:[courseId]}
    Object.entries(data).forEach(([k, val]: [string, any]) => {
      const key = k.split('-')[1]; // 班级id 但是需要年级id
      const courseId = Number(k.split('-')[0]);
      let gradeId = null;
      Object.entries(gradeToClassesMap).forEach(([gradeIdKey, gradeClassIds]: [string, any]) => {
        if (gradeClassIds?.includes(Number(key))) {
          gradeId = gradeIdKey;
        }
      });
      if (gradeId !== null) {
        if (!result[gradeId]) {
          result[gradeId] = {
            afternoon: new Set(),
            morning: new Set(),
          };
        }
        if (val?.sessionRequirement === 'morning') {
          result[gradeId].afternoon.add(courseId);
        } else if (val?.sessionRequirement === 'afternoon') {
          result[gradeId].morning.add(courseId);
        }
      }
    });
    return result;
  });

  // 打开编辑约束弹窗，加载选中单元格的数据
  const editConstraint = (record, day, isMorning?: boolean) => {
    const preferCourse = preferMap.value?.[String(selectedGradeId.value)];
    currentCantAvailableCourse.value = [];
    // 模拟上午课程
    if (isMorning) {
      currentCantAvailableCourse.value = Array.from(preferCourse?.morning || []);
    } else {
      currentCantAvailableCourse.value = Array.from(preferCourse?.afternoon || []);
    }

    editingRecord.value = record;
    editingDay.value = day;
    // 深拷贝当前约束数据到编辑模型
    editingConstraint.value = { ...record[day] };
    // 确保优先级数组存在
    if (!editingConstraint.value.priorityCourses) {
      editingConstraint.value.priorityCourses = [];
    }
    if (!editingConstraint.value.avoidCourses) {
      editingConstraint.value.avoidCourses = [];
    }
    showConstraintModal.value = true;
  };

  // 计算当前年级和全校的可排课总节次数
  const calculateAvailablePeriods = () => {
    try {
      let currentGradeTotal = 0;
      let schoolTotal = 0;

      // 遍历所有年级来计算全校总数，并顺便计算当前选中年级的总数
      grades.value.forEach((grade) => {
        const gradeId = grade.id;
        const savedSettings = generalSettings.value.constraints[`constraints_${gradeId}`];
        if (!savedSettings) return; // 跳过没有设置的年级

        try {
          const settings = savedSettings;
          const morning = settings.morningPeriods || [];
          const afternoon = settings.afternoonPeriods || [];

          // 找出该年级的所有班级数量
          const gradeClassesCount = classes.value.filter((cls) => cls.gradeId === gradeId).length || 1;

          let nonEmptyPeriodsInGrade = 0; // 该年级单个班级的非空节次数

          // 辅助函数计算一个时段表格的非空节次数
          const countNonEmpty = (periods) => {
            let count = 0;
            periods.forEach((period) => {
              ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'].forEach((day) => {
                if (period[day] && period[day].status !== 'empty') {
                  count += 1;
                }
              });
            });
            return count;
          };

          nonEmptyPeriodsInGrade = countNonEmpty(morning) + countNonEmpty(afternoon);

          // 累加到全校总数
          schoolTotal += nonEmptyPeriodsInGrade * gradeClassesCount;

          // 如果是当前选中的年级，记录其总数
          if (gradeId === selectedGradeId.value) {
            currentGradeTotal = nonEmptyPeriodsInGrade * gradeClassesCount;
          }
        } catch (e) {
          // 保留错误日志
        }
      });

      currentGradeAvailablePeriods.value = currentGradeTotal;
      totalAvailablePeriods.value = schoolTotal;

      generalSettings.value.constraintsStats = {
        totalAvailablePeriods: schoolTotal,
        currentGradeAvailablePeriods: currentGradeTotal,
      };

      // 触发约束更新事件
      window.dispatchEvent(
        new CustomEvent('constraintsUpdated', {
          detail: {
            totalAvailablePeriods: schoolTotal,
            currentGradeAvailablePeriods: currentGradeTotal,
          },
        }),
      );
    } catch (e) {
      currentGradeAvailablePeriods.value = 0;
      totalAvailablePeriods.value = 0;
    }
  };

  // 保存当前选中年级的约束设置到 localStorage
  const saveConstraintSettings = () => {
    if (!selectedGradeId.value) return;

    // 直接保存到map吧
    generalSettings.value.constraints[`constraints_${selectedGradeId.value}`] = {
      periodSettings: periodSettings.value,
      morningPeriods: morningPeriods.value,
      afternoonPeriods: afternoonPeriods.value,
    };
    calculateAvailablePeriods(); // 保存后重新计算统计数据
  };

  // 保存编辑后的约束信息
  const saveEditConstraint = () => {
    if (editingRecord.value && editingDay.value) {
      // 当状态设为"留空"时，清除其他无关设置
      if (editingConstraint.value.status === 'empty') {
        editingConstraint.value.priorityCourses = [];
        editingConstraint.value.avoidCourses = [];
        editingConstraint.value.hasTeachingGroup = false;
        editingConstraint.value.teachingGroupId = null;
        editingConstraint.value.fixedCourseId = null;
      }
      // 更新表格数据
      editingRecord.value[editingDay.value] = { ...editingConstraint.value };
      saveConstraintSettings();
      calculateAvailablePeriods(); // 重新计算统计数据
      showConstraintModal.value = false;
    }
  };

  // 取消编辑约束
  const cancelEditConstraint = () => {
    showConstraintModal.value = false;
  };

  // --- 辅助函数与计算属性 ---

  // 根据状态值获取对应的中文文本
  const getStatusText = (status) => {
    const statusMap = {
      normal: '正常排课',
      empty: '留空',
      fixed: '固定课程',
    };
    return statusMap[status] || '未设置';
  };

  // 根据教研组 ID 获取教研组名称
  const getTeachingGroupName = (groupId) => {
    const group = teachingGroups.value.find((g) => g.id === groupId);
    return group ? group.name : '未设置';
  };

  // 根据课程 ID 获取课程名称
  const getCourseName = (courseId) => {
    const course = courses.value.find((c) => c.id === courseId);
    return course ? course.name : '未设置';
  };

  // 将课程 ID 数组转换为逗号分隔的课程名称字符串
  const getPriorityCourseNames = (courseIds) => {
    if (!courseIds || courseIds.length === 0) return '';

    return courseIds
      .map((id) => {
        const course = courses.value.find((c) => c.id === id);
        return course ? course.name : '';
      })
      .filter((name) => name)
      .join(', ');
  };

  // 生成唯一的周期代码 (例如 '0-0-1' 表示周一上午第1节)
  const generatePeriodCode = (dayOfWeek, session, periodNumber) => {
    const dayMap = {
      monday: 0,
      tuesday: 1,
      wednesday: 2,
      thursday: 3,
      friday: 4,
    };

    const sessionMap = {
      morning: 0,
      afternoon: 1,
    };

    // 获取星期的数字代码
    const dayCode = dayMap[dayOfWeek];

    // 获取时段的数字代码
    const sessionCode = sessionMap[session];

    // 构建并返回periodCode
    return `${dayCode}-${sessionCode}-${periodNumber}`;
  };

  // 判断当前编辑的约束是否属于下午时段
  const isEditingAfternoonPeriod = computed(() => {
    if (!editingRecord.value || !editingRecord.value.period) {
      return false;
    }
    // 从节次文本（如 "第5节"）解析数字
    const periodNumber = parseInt(editingRecord.value.period.replace(/[^0-9]/g, ''), 10) || 0;
    // 如果节次号大于上午的总节数，则为下午
    return periodNumber > periodSettings.value.morning;
  });

  // 根据课时计划中的时段限制 (上午/下午) 过滤课程列表
  // 用于在编辑约束时，禁用或提示不符合时段要求的课程
  const filterCoursesByTimeSlot = (coursesList) => {
    if (!selectedGradeId.value || !editingRecord.value) {
      return coursesList; // 如果没有选择年级或不在编辑状态，则不过滤
    }

    // 找出当前年级的所有班级
    const gradeClasses = classes.value.filter((cls) => cls.gradeId === selectedGradeId.value);

    return coursesList.map((course) => {
      // 检查该课程在当前年级所有班级的时段设置是否有冲突
      let hasTimeSlotConflict = false;
      let restrictedTimeSlot = ''; // 记录冲突的时段名称

      hasTimeSlotConflict = gradeClasses.some((cls) => {
        const key = `${course.id}-${cls.id}`;
        const planItem = coursePlanData.value[key];

        if (planItem && planItem.sessionRequirement) {
          // 情况1: 课程要求仅上午，但正在编辑下午的约束
          if (planItem.sessionRequirement === 'morning' && isEditingAfternoonPeriod.value) {
            restrictedTimeSlot = '上午';
            return true; // 返回 true 停止 some() 遍历，表示找到了冲突
          }

          // 情况2: 课程要求仅下午，但正在编辑上午的约束
          if (planItem.sessionRequirement === 'afternoon' && !isEditingAfternoonPeriod.value) {
            restrictedTimeSlot = '下午';
            return true; // 返回 true 停止 some() 遍历，表示找到了冲突
          }
        }
        return false; // 继续 some() 遍历
      });

      // 返回带有禁用状态和提示信息的课程对象
      return {
        ...course,
        disabled: hasTimeSlotConflict,
        // 如果有冲突，在课程名称后添加提示
        label: hasTimeSlotConflict ? `${course.name} (该课程已在课时计划中限制在${restrictedTimeSlot})` : course.name,
      };
    });
  };

  // 计算编辑约束时"优先排课"下拉框的可选课程列表
  // 排除已在"避免排课"中选择的课程，并应用时段过滤
  const availablePriorityCourses = computed(() => {
    if (!editingConstraint.value || !editingConstraint.value.avoidCourses) {
      return filterCoursesByTimeSlot(courses.value); // 如果没有避免课程，直接过滤所有课程
    }

    // 先从所有课程中排除已选为"避免"的课程，再进行时段过滤
    return filterCoursesByTimeSlot(
      courses.value.filter((course) => !editingConstraint.value.avoidCourses.includes(course.id)),
    );
  });

  // 计算编辑约束时"避免排课"下拉框的可选课程列表
  // 排除已在"优先排课"中选择的课程，并应用时段过滤
  const availableAvoidCourses = computed(() => {
    if (!editingConstraint.value || !editingConstraint.value.priorityCourses) {
      return filterCoursesByTimeSlot(courses.value); // 如果没有优先课程，直接过滤所有课程
    }

    // 先从所有课程中排除已选为"优先"的课程，再进行时段过滤
    return filterCoursesByTimeSlot(
      courses.value.filter((course) => !editingConstraint.value.priorityCourses.includes(course.id)),
    );
  });

  // 获取当前年级的名称
  const getCurrentGradeName = () => {
    const grade = grades.value.find((g) => g.id === selectedGradeId.value);
    return grade ? grade.name : '未选择';
  };

  // 计算可用于复制设置的年级列表 (排除当前选中的年级)
  const availableCopyGrades = computed(() => {
    return grades.value.filter((grade) => grade.id !== selectedGradeId.value);
  });

  // --- 数据加载与初始化 ---

  // 加载年级数据
  const loadGrades = () => {
    const extractUniqueGradeInfo = (data: any[]): any[] => {
      const gradeMap = new Map<number, any>();
      // eslint-disable-next-line no-restricted-syntax
      for (const item of data) {
        if (!gradeMap.has(item.gradeId)) {
          gradeMap.set(item.gradeId, { id: item.gradeId, name: item.gradeName });
        }
      }
      return Array.from(gradeMap.values());
    };

    grades.value = extractUniqueGradeInfo(generalSettings.value?.classes) || [];
    // 如果未选中年级且存在年级数据，则默认选中第一个
    if (grades.value.length > 0 && !selectedGradeId.value) {
      selectedGradeId.value = grades.value[0].id;
    }
  };

  // 加载教研组数据
  const loadTeachingGroups = () => {
    teachingGroups.value = generalSettings.value?.teachingGroups;
  };

  // 加载课程数据
  const loadCourses = () => {
    courses.value = generalSettings.value?.courses;
  };

  // 加载课时计划数据 (用于时段检查)
  const loadCoursePlanData = () => {
    coursePlanData.value = generalSettings.value?.weeklySchedule;
  };

  // 加载班级数据 (用于计算统计和导出)
  const loadClasses = () => {
    classes.value = generalSettings.value?.classes;
  };

  // 创建一个默认的约束对象 (状态为 normal，无特殊设置)
  const createDefaultConstraint = () => {
    return {
      status: 'normal',
      hasTeachingGroup: false,
      teachingGroupId: null,
      fixedCourseId: null,
      priorityCourses: [],
      avoidCourses: [],
    };
  };

  // 根据节次设置，初始化上午或下午的课表数据结构（仅在无数据时调用）
  // 返回初始化后的节次数组，或直接修改 ref (根据调用方式)
  const initializePeriods = (timeOfDay) => {
    const count = timeOfDay === 'morning' ? periodSettings.value.morning : periodSettings.value.afternoon;
    const periods = [];

    for (let i = 1; i <= count; i += 1) {
      // 计算正确的节次编号 (下午节次基于上午节数累加)
      const periodNum = timeOfDay === 'morning' ? i : i + periodSettings.value.morning;
      const period = {
        period: `第${periodNum}节`,
        monday: createDefaultConstraint(), // 创建默认约束对象
        tuesday: createDefaultConstraint(),
        wednesday: createDefaultConstraint(),
        thursday: createDefaultConstraint(),
        friday: createDefaultConstraint(),
      };
      periods.push(period);
    }

    // 根据 timeOfDay 更新对应的 ref
    if (timeOfDay === 'morning') {
      morningPeriods.value = periods;
    } else {
      afternoonPeriods.value = periods;
    }
    return periods; // 也可返回数组供直接赋值
  };

  // 更新下午节次的文本编号 (当上午节次数变化时需要调用)
  const updatePeriodNumbers = () => {
    afternoonPeriods.value.forEach((period, index) => {
      period.period = `第${index + 1 + periodSettings.value.morning}节`;
    });
  };

  // 加载当前选中年级的约束设置
  const loadConstraintSettings = () => {
    if (!selectedGradeId.value) return; // 未选择年级则不加载

    const savedSettings = props.setting?.constraints?.[`constraints_${selectedGradeId.value}`];
    if (savedSettings) {
      const settings = savedSettings;

      // 加载节次设置，若无则使用默认值
      periodSettings.value = settings.periodSettings || { ...defaultPeriodSettings };

      // 加载上午课表，若无则初始化
      morningPeriods.value = settings.morningPeriods || initializePeriods('morning');

      // 加载下午课表，若无则初始化
      afternoonPeriods.value = settings.afternoonPeriods || initializePeriods('afternoon');
    } else {
      // 如果该年级从未保存过设置，则使用默认值并初始化
      periodSettings.value = { ...defaultPeriodSettings };
      initializePeriods('morning');
      initializePeriods('afternoon');
    }
    // 确保节次编号正确
    updatePeriodNumbers();
  };

  // --- 数据持久化 ---

  // --- 用户操作 ---

  // 处理年级下拉框选择变化
  const handleGradeChange = () => {
    loadConstraintSettings(); // 加载新选中年级的设置
    calculateAvailablePeriods(); // 重新计算统计数据
  };

  // 处理节次数输入框变化 (仅更新内存中的数据结构，不立即保存)
  const updatePeriods = () => {
    const oldMorningCount = morningPeriods.value.length;
    const newMorningCount = periodSettings.value.morning;

    // 调整上午节次数组
    if (newMorningCount > oldMorningCount) {
      // 增加节次：在末尾添加新的默认节次行
      for (let i = oldMorningCount + 1; i <= newMorningCount; i += 1) {
        morningPeriods.value.push({
          period: `第${i}节`,
          monday: createDefaultConstraint(),
          // ... 其他星期
          friday: createDefaultConstraint(),
        });
      }
    } else if (newMorningCount < oldMorningCount) {
      // 减少节次：截断数组
      morningPeriods.value = morningPeriods.value.slice(0, newMorningCount);
    }

    // 调整下午节次数组 (逻辑类似)
    const oldAfternoonCount = afternoonPeriods.value.length;
    const newAfternoonCount = periodSettings.value.afternoon;

    if (newAfternoonCount > oldAfternoonCount) {
      for (let i = oldAfternoonCount + 1; i <= newAfternoonCount; i += 1) {
        const periodNum = i + periodSettings.value.morning; // 注意下午节次的起始编号
        afternoonPeriods.value.push({
          period: `第${periodNum}节`,
          monday: createDefaultConstraint(),
          // ... 其他星期
          friday: createDefaultConstraint(),
        });
      }
    } else if (newAfternoonCount < oldAfternoonCount) {
      afternoonPeriods.value = afternoonPeriods.value.slice(0, newAfternoonCount);
    }

    // 更新所有节次的文本编号 (特别是下午)
    updatePeriodNumbers();

    // 注意：这里不自动保存，用户需点击"保存节次设置"按钮
  };

  // 保存节次设置按钮的点击处理函数
  const savePeriodSettings = () => {
    saveConstraintSettings(); // 调用通用的保存函数
    emits('autoSave');
    // Message.success('节次设置已保存');
  };

  // 监听编辑约束弹窗中"优先课程"选择的变化
  const handlePriorityChange = (selectedCourses) => {
    // 自动从"避免课程"列表中移除已选为"优先"的课程，防止冲突
    if (editingConstraint.value.avoidCourses) {
      editingConstraint.value.avoidCourses = editingConstraint.value.avoidCourses.filter(
        (id) => !selectedCourses.includes(id),
      );
    }
  };

  // 监听编辑约束弹窗中"避免课程"选择的变化
  const handleAvoidChange = (selectedCourses) => {
    // 自动从"优先课程"列表中移除已选为"避免"的课程，防止冲突
    if (editingConstraint.value.priorityCourses) {
      editingConstraint.value.priorityCourses = editingConstraint.value.priorityCourses.filter(
        (id) => !selectedCourses.includes(id),
      );
    }
  };

  // --- 清空与复制 ---
  // 显示清空当前年级设置的确认弹窗
  const showResetConfirm = () => {
    if (!selectedGradeId.value) {
      Message.warning('请先选择年级');
      return;
    }
    showResetModal.value = true;
  };

  // 取消清空操作
  const cancelReset = () => {
    showResetModal.value = false;
  };

  // 执行清空当前年级课表设置的操作
  const resetConstraintSettings = () => {
    if (!selectedGradeId.value) return; // 防御性检查

    // 恢复默认节次设置
    periodSettings.value = { ...defaultPeriodSettings };
    // 重新初始化课表数据
    initializePeriods('morning');
    initializePeriods('afternoon');
    // 保存清空后的状态
    saveConstraintSettings();
    calculateAvailablePeriods(); // 重新计算统计

    showResetModal.value = false;
    Message.success('当前年级课表已清空');
  };

  // 显示从其他年级复制设置的弹窗
  const showCopyModal = () => {
    if (!selectedGradeId.value) {
      Message.warning('请先选择年级');
      return;
    }
    // 重置来源年级选择
    copySourceGradeId.value = null;
    showCopySettingsModal.value = true;
  };

  // 取消复制操作
  const cancelCopy = () => {
    showCopySettingsModal.value = false;
  };

  // 执行从源年级复制约束设置到当前年级的操作
  const copyConstraintSettings = () => {
    if (!selectedGradeId.value || !copySourceGradeId.value) {
      Message.warning('请选择来源年级和目标年级');
      return;
    }

    const sourceSettings = props.setting?.constraints?.[`constraints_${copySourceGradeId.value}`];
    if (!sourceSettings) {
      Message.warning('来源年级没有保存的约束设置');
      showCopySettingsModal.value = false;
      return;
    }

    const settings = sourceSettings;

    // 使用源设置更新当前视图的数据 (深拷贝以避免引用问题)
    periodSettings.value = settings.periodSettings ? { ...settings.periodSettings } : { ...defaultPeriodSettings };

    morningPeriods.value = settings.morningPeriods ? settings.morningPeriods : initializePeriods('morning');
    afternoonPeriods.value = settings.afternoonPeriods ? settings.afternoonPeriods : initializePeriods('afternoon');

    // 保存到当前年级
    saveConstraintSettings();
    calculateAvailablePeriods(); // 重新计算统计

    // 关闭弹窗并提示成功
    showCopySettingsModal.value = false;
    const sourceGradeName = grades.value.find((g) => g.id === copySourceGradeId.value)?.name || '未知年级';
    const targetGradeName = getCurrentGradeName();
    Message.success(`已成功从 ${sourceGradeName} 复制约束设置到 ${targetGradeName}`);
  };

  // --- 生命周期钩子 ---
  // 组件挂载时加载初始数据并设置监听器
  onMounted(() => {
    loadGrades(); // 加载年级
    loadTeachingGroups(); // 加载教研组
    loadCourses(); // 加载课程
    loadCoursePlanData(); // 加载课时计划 (用于时段检查)
    loadClasses(); // 加载班级 (用于统计和导出)
    loadConstraintSettings(); // 加载当前选中年级的约束设置
    calculateAvailablePeriods(); // 计算初始统计数据

    // 监听 CoursePlan.vue 发出的课时计划更新事件
    window.addEventListener('weeklyScheduleUpdated', loadCoursePlanData);
  });

  // 组件卸载时移除事件监听器
  onUnmounted(() => {
    window.removeEventListener('weeklyScheduleUpdated', loadCoursePlanData);
  });

  // --- 监听器 ---

  // 监听选中年级 ID 的变化，自动加载新选中年级的约束设置
  watch(
    () => selectedGradeId.value,
    () => {
      loadConstraintSettings();
      calculateAvailablePeriods(); // 重新计算统计
    },
  );

  // 监听编辑约束弹窗中状态的变化
  watch(
    () => editingConstraint.value.status,
    (newStatus) => {
      // 如果状态切换为"固定课程"，清除可能存在的教研组设置
      if (newStatus === 'fixed') {
        editingConstraint.value.hasTeachingGroup = false;
        editingConstraint.value.teachingGroupId = null;
      }
      // 如果状态切换为"留空"，清除固定课程和教研组
      if (newStatus === 'empty') {
        editingConstraint.value.hasTeachingGroup = false;
        editingConstraint.value.teachingGroupId = null;
        editingConstraint.value.fixedCourseId = null;
        // 优先级课程会自动清除 (在 saveEditConstraint 中处理)
      }
    },
  );
  watch(
    () => props.setting,
    (newVal) => {
      emits('autoSave');
    },
  );
</script>

<style scoped>
  /* 约束单元格基本样式 */
  .constraint-cell {
    min-height: 60px;
    padding: 8px;
    cursor: pointer;
    border: 1px dashed #e5e6eb;
    border-radius: 4px;
    position: relative;
    overflow: visible;
  }

  /* 统计卡片样式 */
  .statistics-card {
    background-color: #f7f8fa;
    border-radius: 4px;
  }

  /* 自定义统计组件样式 */
  .custom-statistic {
    padding: 8px 0;
  }

  .statistic-title {
    font-size: 14px;
    color: #4e5969;
    margin-bottom: 8px;
  }

  .statistic-content {
    display: flex;
    align-items: center;
  }

  .statistic-value {
    font-size: 24px;
    font-weight: 600;
    color: #0084ff;
  }

  .statistic-label {
    font-size: 14px;
    color: #4e5969;
    margin-left: 4px;
  }

  .info-icon {
    margin-left: 8px;
    cursor: pointer;
    color: #86909c;
  }

  .statistics-card :deep(.arco-statistic-title) {
    font-size: 14px;
    color: #4e5969;
    margin-bottom: 8px;
  }

  .statistics-card :deep(.arco-statistic-value) {
    font-size: 20px;
    font-weight: 600;
  }

  /* 状态标签共同样式 */
  .status-tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    margin-bottom: 4px;
    font-size: 12px;
    position: relative;
    z-index: 1;
  }

  /* 状态标签变体 */
  .status-tag.normal {
    background-color: #e8f7ff;
    color: #0084ff;
  }

  .status-tag.empty {
    background-color: #f2f3f5;
    color: #86909c;
  }

  .status-tag.fixed {
    background-color: #e8ffea;
    color: #00b42a;
  }

  /* 文本元素共同样式 */
  .teaching-group,
  .fixed-course,
  .priority-course {
    font-size: 12px;
    color: #4e5969;
    position: relative;
    z-index: 1;
    margin-bottom: 2px;
  }

  /* 优先级课程样式覆盖 */
  .priority-course {
    margin-top: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 优先级标记颜色 */
  .priority-course.high {
    color: #00b42a;
  }

  .priority-course.low {
    color: #f53f3f;
  }

  /* 节次标题样式 */
  .section-title {
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 500;
    color: #1d2129;
  }

  /* 按钮表单项对齐 */
  .button-form-item {
    display: flex;
    align-items: flex-end;
    height: 100%;
  }

  /* 教研活动相关设置样式 */
  .teaching-group-container {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .teaching-group-switch {
    flex-shrink: 0;
  }

  .teaching-group-select-container {
    flex-grow: 1;
  }

  .teaching-group-select {
    width: 100%;
  }

  /* 自定义警告样式 */
  .custom-warning {
    display: flex;
    align-items: flex-start;
    padding: 12px 16px;
    margin: 8px 0;
    background-color: #fff3e8;
    border-radius: 4px;
    border-left: 3px solid #ff7d00;
  }

  .warning-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 20px;
    margin-right: 8px;
    background-color: #ff7d00;
    color: white;
    border-radius: 50%;
    font-weight: bold;
  }

  .warning-text {
    color: #4e5969;
    font-size: 14px;
    line-height: 20px;
  }
</style>
