<template>
  <div v-if="isReady" class="teachers-container p-6">
    <!-- 页面标题和概述 -->
    <section class="dashboard-header mb-6">
      <h1 class="text-2xl font-bold mb-2">教师管理</h1>
      <p class="text-gray-600">管理教师信息、任教科目及工作量分配</p>
    </section>

    <!-- 统计卡片 -->
    <section class="evaluation-metrics mb-8">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <a-card class="metric-card" :bordered="false">
          <div class="flex items-center">
            <div class="metric-icon bg-blue-100 text-blue-600 p-3 rounded-lg mr-4">
              <icon-calendar />
            </div>
            <div>
              <div class="text-sm text-gray-500">计划周课时</div>
              <div class="text-2xl font-bold">{{ totalPlannedWeeklyHours }}</div>
            </div>
          </div>
        </a-card>

        <a-card class="metric-card" :bordered="false">
          <div class="flex items-center">
            <div class="metric-icon bg-green-100 text-green-600 p-3 rounded-lg mr-4">
              <icon-clock-circle />
            </div>
            <div>
              <div class="text-sm text-gray-500">实际周课时</div>
              <div class="text-2xl font-bold">{{ totalActualAssignedHours }}</div>
            </div>
          </div>
        </a-card>
      </div>
    </section>

    <!-- 主要操作区域 -->
    <div class="sticky top-[-21px] z-10 bg-white pb-2 border-b shadow-sm">
      <a-row justify="space-between">
        <a-col>
          <a-space>
            <a-button size="mini" type="primary" @click="showAddModal = true">
              <template #icon>
                <icon-plus />
              </template>
              添加教师
            </a-button>
            <a-button size="mini" type="outline" @click="showGroupModal = true">
              <template #icon>
                <icon-settings />
              </template>
              教研组管理
            </a-button>
            <a-button size="mini" type="outline" @click="showConsecutiveSettingsModal = true">
              <template #icon>
                <icon-settings />
              </template>
              连排上限
            </a-button>
            <a-button v-if="false" size="mini" type="outline" @click="handleBatchSetting">
              <template #icon>
                <icon-settings />
              </template>
              批量设置
            </a-button>
            <!--筛选区域-->
            <span class="font-bold mr-4 ml-4">/</span>
            <a-input v-model="searchKeyword" placeholder="请输入教师姓名搜索" search-button size="mini" />
            <a-select
              v-model="filterParams.course"
              size="mini"
              placeholder="按课程筛选"
              allow-search
              multiple
              :max-tag-count="2"
              :options="courses.map((item) => ({ label: item.name, value: item.id })) || []"
            />
            <a-select
              v-model="filterParams.gradeClass"
              size="mini"
              placeholder="按年级筛选"
              :options="gradeClass"
              allow-search
              :max-tag-count="2"
              multiple
            />
            <a-select
              v-model="filterParams.group"
              size="mini"
              placeholder="按教研组筛选"
              :max-tag-count="2"
              allow-search
              multiple
              :options="teachingGroups.map((g) => ({ label: g.name, value: g.id })) || []"
            />
            <span class="text-gray-300">|</span>
            <div v-if="false" class="flex justify-center mr-2 ml-2 items-center w-[250px]">
              <a-input-number size="mini" mode="button" class="max-w-[100px]" />
              <span class="text-sm">到</span>
              <a-input-number size="mini" mode="button" class="max-w-[100px]" />
            </div>
            <a-button size="mini" type="outline" @click="computedTeachers">
              筛选
              <template #icon>
                <icon-search />
              </template>
            </a-button>
            <!--多少到多少之间的-->
          </a-space>
        </a-col>
      </a-row>
    </div>

    <!--列表信息-->
    <a-card class="mb-4">
      <a-space direction="vertical" size="large" fill>
        <a-table
          :columns="columns"
          :data="filteredTeachers"
          :pagination="false"
          row-key="id"
          :row-selection="rowSelection"
          :bordered="{ cell: true }"
          @selection-change="onSelectionChange"
        >
          <!-- 添加序号列的插槽 -->
          <template #index="{ rowIndex }">
            {{ rowIndex + 1 }}
          </template>

          <template #subjects="{ record }">
            <a-tooltip content="课程设置">
              <a-space
                v-if="record.subjects?.length"
                class="w-full h-full cursor-pointer"
                wrap
                @click="handleSetting(record, 'course')"
              >
                <a-tag v-for="subject in record.subjects" :key="subject.id" color="blue" size="small">
                  {{ subject.name }}
                </a-tag>
              </a-space>
              <div v-else class="text-gray-400 cursor-pointer w-full h-full" @click="handleSetting(record, 'course')">
                未设置
              </div>
            </a-tooltip>
          </template>

          <template #teachingGroups="{ record }">
            <a-tooltip content="教研组设置" @click="handleSetting(record, 'group')">
              <a-space v-if="record.teachingGroups?.length" wrap class="flex flex-wrap w-full h-full cursor-pointer">
                <a-tag v-for="group in record.teachingGroups" :key="group.id" color="green" size="small">
                  {{ group.name }}
                </a-tag>
              </a-space>
              <span v-else class="text-gray-400 cursor-pointer w-full h-full cursor-pointer">未设置</span>
            </a-tooltip>
          </template>
          <template #gradeClassId="{ record }">
            <a-tooltip content="班级设置" @click="handleSetting(record, 'gradeClass')">
              <div
                v-if="record.gradeClassId && record.gradeClassId.length > 0"
                class="flex justify-start space-x-1 grid-2 w-full h-full cursor-pointer"
              >
                <div
                  v-for="(item, index) in classesIdToDisplay(record?.gradeClassId) || []"
                  :key="index"
                  class="rounded text-cyan-400 px-1 bg-cyan-50 opacity-70 text-sm"
                >
                  {{ item }}
                </div>
              </div>
              <div v-else class="w-full h-full cursor-pointer">所有班级</div>
            </a-tooltip>
          </template>

          <!-- 新增：显示工作日的插槽 -->
          <template #availableWeekdays="{ record }">
            <a-tooltip content="工作日设置" @click="handleSetting(record, 'day')">
              <a-space
                v-if="record.availableWeekdays && record.availableWeekdays.length > 0"
                class="w-full h-full cursor-pointer"
                wrap
              >
                <a-tag
                  v-for="day in record.availableWeekdays.sort((a, b) => a - b)"
                  :key="day"
                  color="purple"
                  size="small"
                >
                  {{ formatWeekday(day) }}
                </a-tag>
              </a-space>
              <div v-else class="w-full h-full cursor-pointer">所有工作日</div>
            </a-tooltip>
          </template>
          <!--考虑加一个批量功能-->
          <template #actualAssignedHours="{ record }">
            <a-input-number
              v-model="record.actualAssignedHours"
              :disabled="true"
              mode="button"
              @change="handleHoursChange($event, record, 'actualAssignedHours')"
            />
          </template>
          <template #plannedWeeklyHours="{ record }">
            <a-input-number
              v-model="record.plannedWeeklyHours"
              mode="button"
              @change="handleHoursChange($event, record, 'plannedWeeklyHours')"
            />
          </template>

          <template #operations="{ record }">
            <a-space>
              <a-popconfirm content="确定要删除该教师吗？" type="warning" @ok="handleDelete(record)">
                <a-button type="text" status="danger" size="small"> 删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </a-table>
      </a-space>
    </a-card>

    <!-- 添加/编辑教师弹窗 -->
    <a-modal v-model:visible="showAddModal" :title="'批量添加教师'" @ok="handleSubmit" @cancel="resetForm">
      <a-form ref="formRef" :model="formData" style="width: 90%; margin: 0 auto">
        <a-form-item label="教师" required>
          <a-select
            v-model="selectedTeachers"
            allow-search
            multiple
            placeholder="请选择老师"
            size="mini"
            :loading="optionLoading"
            :options="teacherOptions || []"
            @change="handleTeacherChange"
          >
            <template #footer>
              <div class="flex justify-end">
                <a-button size="mini" type="text" @click="handleSelectAll">全选</a-button>
              </div>
            </template>
          </a-select>
        </a-form-item>
        <a-form-item v-if="false" field="availableWeekdays ">
          <template #label>
            <a-space :size="4">
              工作日
              <a-tooltip content="可限定教师在周几排课。">
                <icon-info-circle class="text-blue-600 cursor-pointer" />
              </a-tooltip>
            </a-space>
          </template>
          <a-select
            v-model="formData.availableWeekdays"
            placeholder="默认周一至周五均可排课"
            multiple
            :options="weekdayOptions"
            allow-clear
          >
          </a-select>
        </a-form-item>
        <a-form-item v-if="false" field="plannedWeeklyHours" label="计划课时">
          <a-input-number
            v-model="formData.plannedWeeklyHours"
            :min="0"
            :precision="0"
            :step="1"
            mode="button"
            size="large"
            style="width: 120px"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 教研组管理弹窗 -->
    <a-modal v-model:visible="showGroupModal" :title="'教研组管理'" :footer="false" @cancel="resetGroupForm(true)">
      <div class="mb-4">
        <a-input-group>
          <a-input v-model="groupFormData.name" placeholder="请输入教研组名称" allow-clear />
          <a-button size="mini" type="primary" @click="handleGroupSubmit"> 添加</a-button>
        </a-input-group>
      </div>

      <a-table :data="teachingGroups" :pagination="false">
        <template #columns>
          <a-table-column title="教研组名称" data-index="name" />
          <a-table-column title="操作" align="center">
            <template #cell="{ record }">
              <a-space>
                <a-button type="text" size="small" @click="handleEditGroup(record)"> 编辑</a-button>
                <a-popconfirm content="确定要删除该教研组吗？" type="warning" @ok="handleDeleteGroup(record)">
                  <a-button type="text" status="danger" size="small"> 删除</a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>

      <div class="modal-footer mt-4 flex justify-end">
        <a-space>
          <a-button size="mini" @click="resetGroupForm">关闭</a-button>
        </a-space>
      </div>
    </a-modal>

    <!-- 连续授课设置弹窗 -->
    <a-modal
      v-model:visible="showConsecutiveSettingsModal"
      title="连续授课设置"
      @ok="handleConsecutiveSettingsSubmit"
      @cancel="resetConsecutiveSettingsForm"
    >
      <a-form ref="consecutiveSettingsFormRef" :model="consecutiveSettingsForm">
        <a-form-item field="maxPeriods" label="连排上限">
          <a-space direction="vertical" :size="4" style="width: 100%">
            <a-input-number
              v-model="consecutiveSettingsForm.maxPeriods"
              :min="1"
              :max="10"
              :precision="0"
              mode="button"
              size="large"
              style="width: 140px"
            >
              <template #suffix>节</template>
            </a-input-number>
            <div class="text-gray-500 text-sm">
              <icon-info-circle style="margin-right: 4px; vertical-align: -2px" />
              设置教师最多可以连续上课的节数，默认为3节。
            </div>
          </a-space>
        </a-form-item>

        <a-form-item field="excludedTeachers" label="例外教师">
          <a-space direction="vertical" :size="4" style="width: 100%">
            <a-select
              v-model="consecutiveSettingsForm.excludedTeachers"
              placeholder="请选择教师"
              multiple
              allow-search
              allow-clear
              style="width: 100%"
            >
              <a-option v-for="teacher in teachers" :key="teacher.id" :value="teacher.id" :label="teacher.name">
                {{ teacher.name }}
              </a-option>
            </a-select>
            <div class="text-gray-500 text-sm">
              <icon-info-circle style="margin-right: 4px; vertical-align: -2px" />
              被选中的教师将不受连续授课节数的限制。
            </div>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!--批量设置&暂时隐藏-->
    <a-modal
      v-model:visible="batchSettingVisible"
      width="50%"
      :closable="false"
      cancel-text="关闭"
      :on-before-ok="handlePreOk"
    >
      <div class="w-full mb-2 p-2 bg-gray-100 rounded">
        <span class="ml-4 text-gray-500">
          <span class="font-bold">说明：</span>
          仅当每项设置
          <span class="text-red-500">被勾选后</span>
          对应设置才生效，
          <span class="text-red-500">共有追加、覆盖模式</span>
          ，追加模式会保留原数据并追加新数据、覆盖模式会直接替换掉原数据。
        </span>
      </div>
      <div class="rounded-lg p-2 mb-2 flex justify-start space-x-2">
        <div class="mr-4">课时设置</div>
        <a-input-number v-model="batchCheckVal.plannedWeeklyHoursVal" mode="button" class="w-[30%]" size="mini" />
        <a-checkbox v-model="batchCheckVal.plannedWeeklyHours" />
      </div>

      <div class="rounded-lg p-2 mb-2 flex justify-start space-x-2">
        <div>班级设置&nbsp;&nbsp;&nbsp;</div>
        <a-select
          v-model="batchCheckVal.gradeClassVal"
          size="mini"
          class="w-[50%]"
          :options="gradeClass"
          placeholder="请选择班级"
          multiple
        />
        <a-checkbox v-model="batchCheckVal.gradeClass" />
      </div>

      <div class="rounded-lg p-2 mb-2 flex justify-start space-x-2">
        <div>课程设置&nbsp;&nbsp;&nbsp;</div>
        <a-select
          v-model="batchCheckVal.courseVal"
          size="mini"
          class="w-[50%]"
          placeholder="请选择课程"
          :options="courses.map((item) => ({ label: item.name, value: item.id }))"
          multiple
        />
        <a-checkbox v-model="batchCheckVal.course" />
      </div>

      <div class="rounded-lg p-2 mb-2 flex justify-start space-x-2">
        <div>教研组设置</div>
        <a-select
          v-model="batchCheckVal.groupsVal"
          size="mini"
          class="w-[50%]"
          placeholder="请选择教研组"
          multiple
          :options="teachingGroups.map((item) => ({ label: item.name, value: item.id }))"
        />
        <a-checkbox v-model="batchCheckVal.group" />
      </div>

      <a-divider>
        <span v-if="batchSelectedTeachers.length" class="text-gray-300"> 本次设置将批量适用于以下教师 </span>
        <span v-else class="text-red-500 font-bold">请先在列表勾选教师</span>
      </a-divider>
      <div class="flex flex-wrap gap-2">
        <a-tag
          v-for="item in batchSelectedTeachers"
          :key="item.id"
          class="cursor-pointer"
          :color="randomColor(44)"
          closable
          @close="handleClose(item)"
        >
          {{ item.name }}
        </a-tag>
      </div>
      <template #footer>
        <div class="flex justify-end space-x-2">
          <a-switch type="round" size="large" checked-value="cover" unchecked-value="Append">
            <template #checked> 覆盖模式 </template>
            <template #unchecked> 追加模式 </template>
          </a-switch>

          <a-button size="mini" type="primary" @click="handlePreOk">确定</a-button>
          <a-button size="mini" @click="batchSettingVisible = false">关闭</a-button>
        </div>
      </template>
    </a-modal>

    <!--设置任教科目-->
    <a-modal
      v-model:visible="setCourseVisible"
      :body-style="{ width: 'auto' }"
      :closable="false"
      :on-before-ok="handleSingleSetting"
    >
      <div class="w-full h-full">
        <div class="text-center">{{ '课程设置' }}</div>
        <hr class="m-2" />
        <div class="flex justify-start space-x-2 w-full h-full mb-2">
          <div>
            <span>课程&nbsp;&nbsp;&nbsp;&nbsp;</span>
          </div>
          <a-select
            v-model="recordVal.course"
            size="mini"
            placeholder="请选择课程"
            class="w-[60%]"
            multiple
            :max-tag-count="1"
            :options="courses.map((item) => ({ label: item.name, value: item.id }))"
          />
        </div>
        <div class="w-full p-2 flex flex-wrap gap-2 space-x-2 mb-2">
          <a-tag v-for="c in currentCourseRecord?.subjects" :key="c?.id" :color="randomColor(76)">
            {{ c.name }}
          </a-tag>
        </div>

        <div class="flex justify-start space-x-2 w-full h-full mb-2">
          <div>
            <span>班级&nbsp;&nbsp;&nbsp;&nbsp;</span>
          </div>
          <a-select
            v-model="recordVal.gradeClass"
            size="mini"
            placeholder="请选择年级"
            class="w-[60%]"
            :options="gradeClass"
            multiple
            :max-tag-count="1"
          />
        </div>
        <div class="w-full p-2 flex flex-wrap gap-2 space-x-2 mb-2"> </div>

        <div class="flex justify-start space-x-2 w-full h-full mb-2">
          <div>
            <span>教研组</span>
          </div>
          <a-select
            v-model="recordVal.group"
            size="mini"
            placeholder="请选择教研组"
            class="w-[60%]"
            multiple
            :max-tag-count="1"
            :options="teachingGroups.map((item) => ({ label: item.name, value: item.id }))"
          />
        </div>
        <div class="w-full p-2 flex flex-wrap gap-2 space-x-2 mb-2"> </div>
        <div class="flex justify-start space-x-2 w-full h-full mb-2">
          <div>
            <span>工作日</span>
          </div>
          <a-select
            v-model="recordVal.day"
            size="mini"
            placeholder="请选择工作日"
            class="w-[60%]"
            multiple
            :options="weekdayOptions"
          />
        </div>
        <div class="w-full p-2 flex flex-wrap gap-2 space-x-2 mb-2"> </div>
      </div>
    </a-modal>

    <!--教研组设置-->
    <a-modal
      v-for="mode in settingModel"
      :key="mode.type"
      v-model:visible="mode.visible"
      :title="mode.title"
      @ok="handleSetPreOk(mode.type)"
    >
      <!--多选，直接覆盖-->
      <div v-if="isMultiPleSelect && mode.visible">
        <a-select
          v-model="settingData[mode.type]"
          :options="mode.option"
          multiple
          allow-search
          placeholder="请选着教研组"
        >
          <template #footer>
            <div class="w-full flex pr-2 justify-end">
              <a-button size="mini" type="text" @click="handleSetSelectAll(mode)">
                {{ isSelectAll ? '取消全选' : '全选' }}
              </a-button>
            </div>
          </template>
        </a-select>
      </div>
      <div v-else-if="mode.visible">
        <a-select
          v-model="recordVal[mode.type]"
          :options="mode.option"
          multiple
          allow-search
          placeholder="请选着教研组"
        >
          <template #footer>
            <div class="w-full flex pr-2 justify-end">
              <a-button size="mini" type="text" @click="handleSetSelectAll(mode)">
                {{ isSelectAll ? '取消全选' : '全选' }}
              </a-button>
            </div>
          </template>
        </a-select>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { IconCalendar, IconClockCircle, IconInfoCircle, IconPlus, IconSettings } from '@arco-design/web-vue/es/icon';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { randomColor } from '@repo/ui/components/utils/randomColor';

  const props = defineProps({
    setting: {
      type: Object,
    },
    record: {
      type: Object,
    },
  });
  const emits = defineEmits(['update:setting', 'autoSave']);
  const generalSettings = computed({
    get: () => {
      return props.setting;
    },
    set: (val) => {
      emits('update:setting', val);
    },
  });
  const selectedKeys = ref<number[]>([]); // 选中的id

  const onSelectionChange = (val) => {
    selectedKeys.value = val;
  };
  const rowSelection = ref({
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
  });
  const columns = [
    {
      title: '序号',
      slotName: 'index',
      width: 80,
      align: 'center',
    },
    {
      title: '教师姓名',
      dataIndex: 'name',
    },
    {
      title: '任教科目',
      slotName: 'subjects',
    },
    {
      title: '教研组',
      slotName: 'teachingGroups',
    },
    {
      title: '负责班级',
      slotName: 'gradeClassId',
    },
    {
      title: '工作日',
      slotName: 'availableWeekdays',
      align: 'center',
    },
    {
      title: '计划课时',
      dataIndex: 'plannedWeeklyHours',
      align: 'center',
      slotName: 'plannedWeeklyHours',
      width: 150,
    },
    {
      title: '实际分配课时',
      dataIndex: 'actualAssignedHours',
      align: 'center',
      slotName: 'actualAssignedHours',
      width: 150,
    },
    {
      title: '操作',
      slotName: 'operations',
      width: 100,
    },
  ];

  const teachers = ref(generalSettings.value?.teachers || []);
  const courses = ref(generalSettings.value?.courses || []);
  const teachingGroups = ref(generalSettings.value?.teachingGroups || []);
  const gradeClass = ref([]);

  // 搜索关键词
  const searchKeyword = ref('');
  // 控制添加/编辑教师弹窗的显示
  const showAddModal = ref(false);
  // 当前正在编辑的教师对象，null表示添加模式
  const editingTeacher = ref(null);
  // 添加/编辑教师表单的数据模型
  const formData = ref({
    name: '',
    subjects: [], // 存储科目 ID
    teachingGroups: [], // 存储教研组 ID
    plannedWeeklyHours: 0,
    availableWeekdays: [], // 新增字段
  });

  const selectedTeachers = ref(teachers.value.map((item) => item?.id));
  const batchSelectedTeachers = computed(() => {
    return teachers.value.filter((item) => selectedKeys.value.includes(item?.id));
  });

  // 控制教研组管理弹窗的显示
  const showGroupModal = ref(false);
  // 当前正在编辑的教研组对象，null表示添加模式
  const editingGroup = ref(null);
  // 添加/编辑教研组表单的数据模型
  const groupFormData = ref({
    name: '',
  });

  const teacherWorkloads = ref(generalSettings.value.teacherWeeklyWorkloads || {});

  // 合并教师基础数据和实际工作量数据，用于表格显示
  const enrichedTeachers = computed(() => {
    return teachers.value.map((teacher) => ({
      ...teacher,
      actualAssignedHours: teacherWorkloads.value[teacher.id]?.assignedWeeklyHours || 0,
      plannedWeeklyHours: teacher.plannedWeeklyHours || 0,
      availableWeekdays: teacher.availableWeekdays || [],
    }));
  });
  const filterParams = ref({
    course: [],
    gradeClass: [],
    group: [],
  });
  const filteredTeachers = ref([]);

  const computedTeachers = () => {
    filteredTeachers.value = enrichedTeachers.value.filter((teacher) => {
      const hasSearchKeyword = searchKeyword.value.trim() !== '';
      const matchesKeyword = hasSearchKeyword ? teacher.name.includes(searchKeyword.value) : true;

      const matchesGradeClass =
        filterParams.value.gradeClass.length === 0 ||
        filterParams.value.gradeClass.some((item) => teacher.gradeClassId.includes(item)) ||
        teacher.gradeClassId?.length === 0;

      const matchesCourse =
        filterParams.value.course.length === 0 ||
        filterParams.value.course.some((item) => teacher.subjects?.map((c) => c.id).includes(item));

      const matchesGroup =
        filterParams.value.group.length === 0 ||
        filterParams.value.group.some((item) => teacher.teachingGroups.map((g) => g.id).includes(item));

      if (hasSearchKeyword) {
        return matchesKeyword && matchesGradeClass && matchesCourse && matchesGroup;
      }

      return matchesGradeClass && matchesCourse && matchesGroup;
    });
  };

  const currentCourseRecord = ref();

  const recordValCopy = ref({
    course: [],
    gradeClass: undefined,
    group: [],
    day: [],
  });

  const recordVal = computed({
    get: () => recordValCopy.value,
    set: (newValue) => {
      recordValCopy.value = newValue;
    },
  });

  watch(
    () => currentCourseRecord.value,
    (newVal) => {
      if (newVal) {
        recordValCopy.value = {
          course: newVal.subjects?.map((c) => c.id) || [],
          gradeClass: newVal.gradeClassId || [],
          group: newVal.teachingGroups?.map((g) => g.id) || [],
          day: newVal.availableWeekdays || [],
        };
      } else {
        recordValCopy.value = { course: [], gradeClass: undefined, group: [], day: [] };
      }
    },
    { immediate: true },
  );

  // 定义工作日选项，用于下拉选择框
  const weekdayOptions = [
    { label: '周一', value: 0 },
    { label: '周二', value: 1 },
    { label: '周三', value: 2 },
    { label: '周四', value: 3 },
    { label: '周五', value: 4 },
    // 根据需要可以添加周六周日 { label: '周六', value: 5 }, { label: '周日', value: 6 }
  ];

  const settingModel = ref([
    {
      type: 'course',
      visible: false,
      title: '课程设置',
      option: computed(() => {
        return courses.value.map((item) => ({ label: item.name, value: item.id })) || [];
      }),
    },
    {
      type: 'group',
      visible: false,
      title: '教研组设置',
      option: computed(() => {
        return teachingGroups.value.map((g) => ({ label: g.name, value: g.id })) || [];
      }),
    },
    {
      type: 'gradeClass',
      visible: false,
      title: '年级设置',
      option: computed(() => {
        return gradeClass.value;
      }),
    },
    {
      type: 'day',
      visible: false,
      title: '工作日设置',
      option: weekdayOptions || [],
    },
  ]);

  const setCourseVisible = ref(false);

  const isMultiPleSelect = computed(() => {
    return selectedKeys.value?.length > 1;
  });

  const settingData = ref({
    course: [],
    group: [],
    gradeClass: [],
    day: 0,
  });

  const handleSingleSetting = async () => {
    teachers.value.forEach((item: any) => {
      if (item.id === currentCourseRecord.value.id) {
        item.subjects = courses.value
          .filter((c: any) => recordValCopy.value.course.includes(c.id))
          .map((c) => ({ id: c.id, name: c.name }));
        item.teachingGroups = teachingGroups.value.filter((g) => recordValCopy.value.group.includes(g.id)); // 需要id name
        item.availableWeekdays = recordValCopy.value.day;
        item.gradeClassId = recordValCopy.value.gradeClass;
      }
    });
    generalSettings.value.teachers = teachers.value;

    currentCourseRecord.value = null;
    setCourseVisible.value = false;
  };

  const handleTeacherUpdate = (options: {
    field: 'subjects' | 'teachingGroups' | 'gradeClassId' | 'availableWeekdays';
    multiSelectFilter: (item: any) => any[] | any;
    singleSelectFilter: (item: any) => any[] | any;
  }) => {
    teachers.value.forEach((item: any) => {
      if (isMultiPleSelect.value && selectedKeys.value.includes(item.id)) {
        item[options.field] = options.multiSelectFilter(item);
      } else if (item.id === currentCourseRecord.value?.id) {
        item[options.field] = options.singleSelectFilter(item);
      }
    });
  };

  const handleSetCourse = () => {
    handleTeacherUpdate({
      field: 'subjects',
      multiSelectFilter: () =>
        courses.value.filter((g) => settingData.value.course.includes(g.id)).map((g) => ({ id: g.id, name: g.name })) ||
        [],
      singleSelectFilter: () =>
        courses.value.filter((g) => recordVal.value.course.includes(g.id)).map((g) => ({ id: g.id, name: g.name })) ||
        [],
    });
  };

  const handleSetGroup = () => {
    handleTeacherUpdate({
      field: 'teachingGroups',
      multiSelectFilter: () =>
        teachingGroups.value
          .filter((g) => settingData.value.group.includes(g.id))
          .map((g) => ({ id: g.id, name: g.name })) || [],
      singleSelectFilter: () =>
        teachingGroups.value
          .filter((g) => recordVal.value.group.includes(g.id))
          .map((g) => ({ id: g.id, name: g.name })) || [],
    });
  };

  const handleSetGradeClass = () => {
    handleTeacherUpdate({
      field: 'gradeClassId',
      multiSelectFilter: () => settingData.value.gradeClass,
      singleSelectFilter: () => recordVal.value.gradeClass,
    });
  };

  const handleSetDay = () => {
    handleTeacherUpdate({
      field: 'availableWeekdays',
      multiSelectFilter: () => settingData.value.day,
      singleSelectFilter: () => recordVal.value.day,
    });
  };

  const isSelectAll = ref(false);
  const handleSetSelectAll = (mode: any) => {
    if (isSelectAll.value) {
      settingData.value[mode.type] = [];
      recordValCopy.value[mode.type] = [];
    } else if (isMultiPleSelect.value) {
      settingData.value[mode.type] = mode.option.map((o) => o.value);
    } else {
      recordValCopy.value[mode.type] = mode.option.map((o) => o.value);
    }
    isSelectAll.value = !isSelectAll.value;
  };

  const handleSetPreOk = async (type: string) => {
    switch (type) {
      case 'course':
        handleSetCourse();
        break;
      case 'group':
        handleSetGroup();
        break;
      case 'gradeClass':
        handleSetGradeClass();
        break;
      case 'day':
        handleSetDay();
        break;
      default:
        break;
    }
    computedTeachers();
    generalSettings.value.teachers = teachers.value;
  };
  // 点击设置入口
  const handleSetting = (item: any, type: string) => {
    currentCourseRecord.value = item;
    isSelectAll.value = false;
    settingModel.value.forEach((m) => {
      if (m.type === type) {
        m.visible = true;
      } else {
        m.visible = false;
      }
    });
  };

  const handleDelete = (teacher) => {
    // 新增：检查教师是否有实际分配的课时
    const workload = teacherWorkloads.value[teacher.id];
    // 安全地获取已分配课时，如果 workload 或 assignedWeeklyHours 不存在，则默认为 0
    const assignedHours = workload ? workload.assignedWeeklyHours || 0 : 0;

    if (assignedHours > 0) {
      Message.error(
        `教师 ${teacher.name} 当前已分配 ${assignedHours} 课时，请先在"课时计划"页面取消其所有课时分配后再删除。`,
      );
      return;
    }

    // 如果检查通过（assignedHours 为 0），则继续执行删除逻辑
    teachers.value = teachers.value.filter((t) => t.id !== teacher.id);
    generalSettings.value.teachers = teachers.value;
    computedTeachers();
    Message.success('操作成功');
  };

  const resetForm = () => {
    formData.value = {
      name: '',
      subjects: [],
      teachingGroups: [],
      plannedWeeklyHours: 0,
      availableWeekdays: [], // 重置工作日
    };
    editingTeacher.value = null;
    showAddModal.value = false;
  };

  const batchSettingVisible = ref(false);
  const batchCheckVal = ref({
    group: false,
    groupsVal: [],
    course: false,
    courseVal: [],
    gradeClass: false,
    gradeClassVal: [],
    plannedWeeklyHours: false,
    plannedWeeklyHoursVal: 0,
    mode: 'Append',
  });

  // 重置
  const resetSettingArgs = () => {
    batchCheckVal.value.group = false;
    batchCheckVal.value.course = false;
    batchCheckVal.value.gradeClass = false;
    batchCheckVal.value.plannedWeeklyHours = false;
    batchCheckVal.value.groupsVal = [];
    batchCheckVal.value.courseVal = [];
    batchCheckVal.value.gradeClassVal = [];
    batchCheckVal.value.plannedWeeklyHoursVal = 0;
    batchCheckVal.value.mode = 'Append';
  };
  // 如果选择了老师，那么将批量修改
  const handleHoursChange = (e, item: any, type: string) => {
    if (!isMultiPleSelect.value) {
      teachers.value.forEach((teacher) => {
        if (teacher.id === item.id) {
          if (type === 'plannedWeeklyHours') {
            teacher.plannedWeeklyHours = e;
          } else {
            teacherWorkloads.value[`${item.id}`] = { assignedWeeklyHours: e || 0, teacherName: item.id };
            teacher.actualAssignedHours = e;
          }
        }
      });
    } else {
      // 批量修改
      teachers.value.forEach((teacher) => {
        if (selectedKeys.value.includes(teacher.id)) {
          if (type === 'plannedWeeklyHours') {
            teacher.plannedWeeklyHours = e;
          } else {
            teacherWorkloads.value[`${item.id}`] = { assignedWeeklyHours: e || 0, teacherName: item.id };
            teacher.actualAssignedHours = e;
          }
        }
      });
    }
    computedTeachers();
    generalSettings.value.teachers = teachers;
    generalSettings.value.teacherWeeklyWorkloads = teacherWorkloads;
  };
  const handlePreOk = async () => {
    if (!batchSelectedTeachers.value.length) {
      Message.warning('请先在列表勾选教师');
      return false;
    }
    /* 批量保存 */
    teachers.value.forEach((t: any) => {
      if (selectedKeys.value.includes(t.id)) {
        // 勾选了班级
        if (batchCheckVal.value.gradeClass) {
          t.gradeClassId = batchCheckVal.value.gradeClassVal;
        }
        // 勾选了教研组
        if (batchCheckVal.value.group) {
          t.teachingGroups = teachingGroups.value
            .filter((g) => batchCheckVal.value.groupsVal.includes(g.id))
            ?.map((g) => ({ id: g.id, name: g.name }));
        }
        // 勾选了课程
        if (batchCheckVal.value.course) {
          t.subjects = courses.value
            .filter((c) => batchCheckVal.value.courseVal.includes(c.id))
            ?.map((c) => ({ id: c.id, name: c.name }));
        }
        // 勾选了计划课时
        if (batchCheckVal.value.plannedWeeklyHours) {
          t.plannedWeeklyHours = batchCheckVal.value.plannedWeeklyHoursVal;
        }
      }
    });
    generalSettings.value.teachers = teachers.value;

    resetSettingArgs();

    return true;
  };

  const handleBatchSetting = () => {
    batchSettingVisible.value = true;
  };
  const handleClose = (item: any) => {
    selectedKeys.value.splice(selectedKeys.value.indexOf(item.id), 1);
  };

  const handleSubmit = () => {
    const processedFormData = {
      availableWeekdays: formData.value.availableWeekdays || [],
      plannedWeeklyHours: formData.value.plannedWeeklyHours || 0,
    };

    teachers.value = teachers.value.map((t) => ({ ...t, ...processedFormData }));

    generalSettings.value.teachers = teachers.value;

    computedTeachers();
    Message.success('添加成功');

    resetForm();
  };

  // 计算所有教师实际分配的周课时总数，用于统计卡片
  const totalActualAssignedHours = computed(() =>
    Object.values(teacherWorkloads.value).reduce(
      (total, workload: any) => total + (Number(workload.assignedWeeklyHours) || 0),
      0,
    ),
  );
  // 连续授课设置相关
  const showConsecutiveSettingsModal = ref(false);
  const consecutiveSettingsForm = ref({
    maxPeriods: 3, // 默认值为3
    excludedTeachers: [],
  });

  // 计算所有教师的计划周课时总数 (当前仅在导出时使用)
  const totalPlannedWeeklyHours = computed(() =>
    teachers.value.reduce((total, teacher) => total + (Number(teacher.plannedWeeklyHours) || 0), 0),
  );

  const loadTeachingGroups = () => {
    teachingGroups.value = generalSettings.value.teachingGroups;
  };

  const saveTeachingGroups = (groups) => {
    generalSettings.value.teachingGroups = groups;
  };

  // 编辑教研组
  const handleEditGroup = (group) => {
    editingGroup.value = group;
    groupFormData.value = { ...group };
  };

  // 更新教师信息中关联的教研组名称（当教研组名称被编辑时调用）
  const updateTeacherGroups = (oldGroupName, newGroupName) => {
    teachers.value = teachers.value.map((teacher) => {
      const updatedGroups = teacher.teachingGroups.map((group) =>
        group.name === oldGroupName ? { ...group, name: newGroupName } : group,
      );
      return { ...teacher, teachingGroups: updatedGroups };
    });
    generalSettings.value.teachers = teachers.value;
  };

  // 从教师信息中移除指定的教研组（当教研组被删除时调用）
  const removeTeacherGroup = (groupName) => {
    teachers.value = teachers.value.map((teacher) => ({
      ...teacher,
      teachingGroups: teacher.teachingGroups.filter((group) => group.name !== groupName),
    }));
    generalSettings.value.teachers = teachers.value;
  };

  const resetGroupForm = (close: boolean = false) => {
    groupFormData.value = { name: '' };
    editingGroup.value = null;
    if (close) {
      showGroupModal.value = false;
    }
  };

  // 提交添加或编辑的教研组信息
  const handleGroupSubmit = () => {
    if (!groupFormData.value.name) {
      Message.error('请输入教研组名称');
      return;
    }

    if (editingGroup.value) {
      // 编辑模式
      const oldName = editingGroup.value.name;
      const newName = groupFormData.value.name;

      const index = teachingGroups.value.findIndex((g) => g.id === editingGroup.value.id);
      teachingGroups.value[index] = { ...editingGroup.value, ...groupFormData.value };

      // 如果名称改变，更新所有教师关联的教研组名称
      if (oldName !== newName) {
        updateTeacherGroups(oldName, newName);
      }

      Message.success('编辑成功');
    } else {
      // 添加模式
      teachingGroups.value.push({
        id: Date.now(),
        name: groupFormData.value.name,
      });
      Message.success('添加成功');
    }
    saveTeachingGroups(teachingGroups.value);
    // 保存教研组
    generalSettings.value.teachingGroups = teachingGroups.value;
    resetGroupForm();
  };

  // 删除教研组
  const handleDeleteGroup = (group: any) => {
    teachingGroups.value = teachingGroups.value.filter((g) => g.id !== group.id);
    saveTeachingGroups(teachingGroups.value);
    removeTeacherGroup(group.name);
    Message.success('删除成功');
  };

  // 简化 migrateTeacherData 函数
  const migrateTeacherData = () => {
    let dataChanged = false;
    teachers.value = teachers.value.map((teacher) => {
      const migratedTeacher = { ...teacher };

      // 只保留必要的迁移逻辑
      if (typeof migratedTeacher.plannedWeeklyHours === 'undefined') {
        migratedTeacher.plannedWeeklyHours = migratedTeacher.weeklyHours || 0;
        delete migratedTeacher.weeklyHours;
        dataChanged = true;
      }

      // 确保 teachingGroups 字段存在且格式正确
      if (!Array.isArray(migratedTeacher.teachingGroups)) {
        migratedTeacher.teachingGroups = [];
        dataChanged = true;
      }

      // 确保 subjects 字段存在且格式正确
      if (!Array.isArray(migratedTeacher.subjects)) {
        migratedTeacher.subjects = [];
        dataChanged = true;
      }

      // 确保 availableWeekdays  字段存在
      if (!Array.isArray(migratedTeacher.availableWeekdays)) {
        migratedTeacher.availableWeekdays = [];
        dataChanged = true;
      }

      return migratedTeacher;
    });

    if (dataChanged) {
      generalSettings.value.teachers = teachers.value;
    }
  };

  // 优化 loadTeacherWorkloads 函数
  const loadTeacherWorkloads = () => {};

  // 处理课程名称更新事件，同步更新教师任教科目中的名称
  const updateTeacherSubjects = (oldCourseName, newCourseName) => {
    teachers.value = teachers.value.map((teacher) => {
      const updatedSubjects = teacher.subjects.map((subject) =>
        subject.name === oldCourseName ? { ...subject, name: newCourseName } : subject,
      );
      return { ...teacher, subjects: updatedSubjects };
    });
    generalSettings.value.teachers = teachers.value;
  };

  // 处理课程删除事件，移除教师任教科目中对应的课程
  const handleCourseDeleted = (courseName) => {
    teachers.value = teachers.value.map((teacher) => ({
      ...teacher,
      subjects: teacher.subjects.filter((subject) => subject.name !== courseName),
    }));
    generalSettings.value.teachers = teachers.value;
  };

  // 从 localStorage 加载课程数据，并设置监听器以响应课程更新和删除事件
  const loadCourses = () => {
    const savedCourses = generalSettings.value.courses;
    if (savedCourses) {
      courses.value = savedCourses;
    }

    // 监听 Course.vue 发出的课程更新事件
    window.addEventListener('courseUpdated', (event: any) => {
      const { oldName, newName } = event.detail;
      updateTeacherSubjects(oldName, newName);
      loadCourses(); // 重新加载课程以确保数据最新
    });

    // 监听 Course.vue 发出的课程删除事件
    window.addEventListener('courseDeleted', (event: any) => {
      const { courseName } = event.detail;
      handleCourseDeleted(courseName);
      loadCourses(); // 重新加载课程以确保数据最新
    });
  };

  // 格式化星期数字为文本
  const formatWeekday = (day) => {
    const map = { 0: '周一', 1: '周二', 2: '周三', 3: '周四', 4: '周五', 5: '周六', 6: '周日' };
    return map[day] !== undefined ? map[day] : '未知';
  };

  // 从 localStorage 加载连续授课设置
  const loadConsecutiveSettings = () => {
    const savedSettings = generalSettings.value.consecutiveTeachingSettings;
    if (savedSettings) {
      try {
        const settings = savedSettings;
        consecutiveSettingsForm.value = {
          maxPeriods: settings.consecutiveLimit || 3,
          excludedTeachers: settings.excludedTeacherIds || [],
        };
      } catch (e) {
        // console.error('解析连续授课设置失败:', e);
      }
    }
  };

  const saveConsecutiveSettings = () => {
    generalSettings.value.consecutiveTeachingSettings = {
      consecutiveLimit: consecutiveSettingsForm.value.maxPeriods,
      excludedTeacherIds:
        consecutiveSettingsForm.value.excludedTeachers.length > 0
          ? consecutiveSettingsForm.value.excludedTeachers
          : null,
    };
  };

  // 处理连续授课设置的提交
  const handleConsecutiveSettingsSubmit = () => {
    saveConsecutiveSettings();
    showConsecutiveSettingsModal.value = false;

    Message.success('设置保存成功');
  };

  // 重置连续授课设置表单
  const resetConsecutiveSettingsForm = () => {
    loadConsecutiveSettings(); // 重新加载已保存的设置
    showConsecutiveSettingsModal.value = false;
  };

  const teacherOptions = ref([]);
  const optionLoading = ref(false);
  const loadTeacherOptions = async () => {
    optionLoading.value = true;
    const { data: res } = await request('/org/companyUser/allTeachers', {
      /// allTeachers
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
      params: {
        branchOfficeId: props.record.boId,
        classInfo: 'true',
      },
    });
    teacherOptions.value = res?.map((item) => ({ label: item.name, value: item.id, row: item, id: item.id }));
    optionLoading.value = false;
  };
  /* 当变化的时候，应该过滤出新增的来初始化 */
  const handleTeacherChange = async (item: any) => {
    // 原来存在的id
    const containsTeacherIds = teachers.value.map((t) => t.id);
    // 新增的教师
    const newTeacher = item.filter((i) => !containsTeacherIds.includes(i));
    // 删除的教师
    const delTeacher = containsTeacherIds.filter((i) => !item.includes(i));

    teachers.value = teachers.value.filter((t) => !delTeacher.includes(t.id)); // 过滤掉删除的教师

    teacherOptions.value
      .filter((c) => newTeacher.includes(c.value))
      ?.forEach((t) => {
        teachers.value.push({
          id: t.value,
          name: t.label,
          gradeClassId: t?.row?.classInfo?.map((c) => c.id) || [],
          // newTeacher: true, // 新增教师，用于提示用户需要重新分配课时
        });
      }); // 新增的教师

    generalSettings.value.teachers = teachers.value;
  };

  const handleSelectAll = () => {
    const teacherIds = teacherOptions.value.map((option) => option.value) || [];
    handleTeacherChange(teacherIds);
    selectedTeachers.value = teacherIds;
  };
  const classesIdToDisplay = (ids: number[] = []) => {
    return gradeClass.value.filter((g) => ids.includes(g.value)).map((g) => g.label);
  };

  const loadGradeClass = async () => {
    const { data: res } = await request('/resourceRoom/gradeClass', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
      params: {
        branchOfficeId: props.record.boId,
      },
    });
    gradeClass.value = res.items.map((item) => ({ label: item.name, value: item.id, row: item, id: item.id }));
  };

  // 组件挂载时执行初始化操作
  const isReady = ref(false);
  onMounted(async () => {
    // 加载初始数据
    Message.loading('加载中...');
    loadCourses();
    loadTeachingGroups();
    loadTeacherWorkloads();
    loadConsecutiveSettings();
    migrateTeacherData();

    await Promise.all([loadTeacherOptions(), loadGradeClass()]);
    computedTeachers();
    isReady.value = true;
    Message.clear('top');
  });

  watch(
    () => props.setting,
    (newVal) => {
      emits('autoSave');
    },
    { deep: true },
  );
</script>

<style>
  .teachers-container {
    position: relative;
    overflow: auto;
    height: 100%;

    --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    --card-radius: 0.5rem;
    --primary-color: var(--color-primary-6, #165dff);
    --success-color: var(--color-success-6, #00b42a);
    --warning-color: var(--color-warning-6, #ff7d00);
    --danger-color: var(--color-danger-6, #f53f3f);
  }

  .metric-card {
    box-shadow: var(--card-shadow);
    border-radius: var(--card-radius);
    transition:
      transform 0.2s,
      box-shadow 0.2s;
  }

  .metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  @media (max-width: 768px) {
    .grid {
      grid-template-columns: 1fr;
    }
  }
</style>
