<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useRouter } from 'vue-router';
  import { getPeriodsList } from '@repo/infrastructure/utils/scaffolds';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const schema = ref(null);
  const router = useRouter();
  const copyModalVisible = ref(false);
  const currentRecord = ref();
  const handleRowAction = (action, row) => {
    currentRecord.value = row;
    if (action.key === 'arrange') {
      router.push({
        path: '/manage/specialSchool/classSchedule/arrange',
        query: {
          taskId: row.id,
        },
      });
    }
    if (action.key === 'copySchedule') {
      copyModalVisible.value = true;
    }
  };
  const periodsList = getPeriodsList();
  const currentPeriod = ref();
  const modalRef = ref(null);

  const handleSave = async () => {
    currentRecord.value.id = null;
    currentRecord.value.period = currentPeriod.value;
    try {
      await request('/teacher/lessonSchedule', {
        method: 'post',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: currentRecord.value,
      });
      modalRef.value?.loadData();
    } finally {
      /**/
    }
  };
  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/teacher/lessonSchedule');
  });
</script>

<template>
  <table-with-modal-form
    v-if="schema"
    ref="modalRef"
    module-name="排课任务"
    :schema="schema"
    :default-query-params="{ sort: '-id' }"
    :visible-columns="['period', 'description', 'createdBy', 'createdDate']"
    @row-action="handleRowAction"
  />
  <a-modal v-model:visible="copyModalVisible" :on-before-ok="handleSave" title="复制排课">
    <a-select v-model="currentPeriod" placeholder="请选择学期" :options="periodsList" />
  </a-modal>
</template>
