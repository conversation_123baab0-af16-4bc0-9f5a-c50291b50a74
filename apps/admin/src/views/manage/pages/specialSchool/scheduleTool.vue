<template>
  <a-modal
    v-model:visible="toolsVisible"
    fullscreen
    :render-to-body="false"
    :body-style="bodyStyle"
    hide-cancel
    :closable="false"
    ok-text="关闭"
  >
    <div class="home-container">
      <div class="content-wrapper">
        <h1 class="title">课表排课系统</h1>

        <div class="card-container">
          <!-- 进入系统卡片 -->
          <a-card class="action-card" hoverable @click="enterSystem">
            <template #cover>
              <div class="card-icon">
                <icon-apps class="icon" />
              </div>
            </template>
            <a-typography-title :heading="6"> 进入排课工具 </a-typography-title>
            <a-typography-text> 开始使用课表排课系统 </a-typography-text>
          </a-card>

          <!-- 备份数据卡片 -->
          <a-card class="action-card" hoverable @click="backupData">
            <template #cover>
              <div class="card-icon">
                <icon-download class="icon" />
              </div>
            </template>
            <a-typography-title :heading="6"> 备份数据 </a-typography-title>
            <a-typography-text> 导出所有系统数据 </a-typography-text>
          </a-card>

          <!-- 还原数据卡片 -->
          <a-card class="action-card" hoverable @click="showRestoreModal = true">
            <template #cover>
              <div class="card-icon">
                <icon-upload class="icon" />
              </div>
            </template>
            <a-typography-title :heading="6"> 还原数据 </a-typography-title>
            <a-typography-text> 从备份文件恢复数据 </a-typography-text>
          </a-card>
        </div>
      </div>

      <!-- 还原数据的模态框 -->
      <a-modal
        v-model:visible="showRestoreModal"
        title="还原数据"
        :ok-button-props="{ disabled: !selectedFile }"
        :render-to-body="false"
        @cancel="showRestoreModal = false"
        @ok="handleRestore"
      >
        <div class="restore-modal-content">
          <input ref="fileInput" type="file" accept=".json" style="display: none" @change="onFileChange" />
          <a-button @click="triggerFileSelect"> 选择备份文件 </a-button>
          <div v-if="selectedFile" class="selected-file"> 已选择: {{ selectedFile.name }} </div>
        </div>
      </a-modal>
    </div>
  </a-modal>
  <table-with-modal-form
    v-if="schema"
    ref="modalRef"
    module-name="班级排课"
    :schema="schema"
    :default-query-params="{ sort: '-id' }"
    @row-action="handleRowAction"
  />
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { IconApps, IconDownload, IconUpload } from '@arco-design/web-vue/es/icon';
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const router = useRouter();
  const showRestoreModal = ref(false);
  const selectedFile = ref(null);
  const fileInput = ref(null);
  const modalRef = ref();

  const bodyStyle = {
    height: '100%',
    overflow: 'hidden',
  };

  const toolsVisible = ref(false);

  const handleEnterTool = () => {
    toolsVisible.value = true;
  };

  const currentRecord = ref<any>();

  // 进入系统
  const enterSystem = () => {
    router.push({
      path: '/manage/specialSchool/scheduleTool/Layout',
      query: {
        recordId: currentRecord.value.id,
        boName: currentRecord.value.name,
      },
    });
  };

  // 备份数据
  const backupData = () => {
    try {
      const dataToBackup = {
        classrooms: currentRecord.value?.classrooms || [],
        courses: currentRecord.value?.courses || [],
        teachingGroups: currentRecord.value?.teachingGroups || [],
        teachers: currentRecord.value?.teachers || [],
        classes: currentRecord.value?.classes || [],
        weeklySchedule: currentRecord.value?.weeklySchedule || [],
        constraints: currentRecord.value?.constraints || {},
        weeklyScheduleData: currentRecord.value?.weeklyScheduleData || {},
        teacherWeeklyWorkloads: currentRecord.value?.teacherWeeklyWorkloads || {},
        consecutiveTeachingSettings: currentRecord.value?.consecutiveTeachingSettings || {},
        constraintsStats: currentRecord.value?.constraintsStats || { totalAvailablePeriods: 0 },
      };

      // 创建备份数据对象
      const backupFileContent = {
        timestamp: new Date().toISOString(),
        version: '1.1',
        data: dataToBackup,
      };

      // 创建并下载备份文件
      const dataStr = JSON.stringify(backupFileContent, null, 2);
      const blob = new Blob([dataStr], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      const fileName = `课表系统备份_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.json`;
      link.setAttribute('download', fileName);

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      Message.success('数据备份成功');
    } catch (error) {
      Message.error('备份失败，请重试', error);
    }
  };

  // 触发文件选择
  const triggerFileSelect = () => {
    fileInput.value.click();
  };

  // 处理文件选择改变
  const onFileChange = (event: any) => {
    const file = event.target.files[0];
    if (file) {
      selectedFile.value = file;
    } else {
      selectedFile.value = null; // 清除选择如果用户取消
    }
    // 重置 input value 允许选择同一个文件
    if (event.target) {
      event.target.value = null;
    }
  };

  const resetSetting = (data: any) => {
    currentRecord.value.classrooms = data?.classrooms || [];
    currentRecord.value.courses = data?.courses || [];
    currentRecord.value.teachingGroups = data?.teachingGroups || [];
    currentRecord.value.teachers = data?.teachers || [];
    currentRecord.value.classes = data?.classes || [];
    currentRecord.value.weeklySchedule = data?.weeklySchedule || [];
    currentRecord.value.constraints = data?.constraints || {};
    currentRecord.value.weeklyScheduleData = data?.weeklyScheduleData || {};
    currentRecord.value.teacherWeeklyWorkloads = data?.teacherWeeklyWorkloads || {};
    currentRecord.value.consecutiveTeachingSettings = data?.consecutiveTeachingSettings || {};
    currentRecord.value.constraintsStats = data?.constraintsStats || { totalAvailablePeriods: 0 };
  };

  const handleSave = async () => {
    await request(`/teacher/scheduleTool/${currentRecord.value.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'put',
      data: currentRecord.value,
    });
    Message.success({
      content: '数据已还原，建议刷新页面以确保所有组件加载最新数据。',
      duration: 3000,
      closable: true,
    });
  };
  // 处理数据还原
  const handleRestore = () => {
    if (!selectedFile.value) {
      Message.warning('请先选择备份文件');
      return;
    }
    const reader = new FileReader();
    reader.onload = async (e: any) => {
      try {
        const backupFileContent = JSON.parse(e.target.result);

        // 验证备份文件基本格式
        if (!backupFileContent.data || !backupFileContent.version || !backupFileContent.timestamp) {
          Message.error('无效的备份文件格式');
          return;
        }

        const dataToRestore = backupFileContent.data;

        // 验证是否有实际数据
        let hasValidDataToRestore = false;
        Object.values(dataToRestore).forEach((value) => {
          if (
            (Array.isArray(value) && value.length > 0) ||
            (typeof value === 'object' && value !== null && Object.keys(value).length > 0) ||
            (typeof value === 'string' && value.length > 0) ||
            typeof value === 'number'
          ) {
            hasValidDataToRestore = true;
          }
        });

        if (!hasValidDataToRestore) {
          Message.error('备份文件中没有有效数据可供还原');
          return;
        }
        resetSetting(dataToRestore); // 从加载文件读取配置重新赋值

        await handleSave();

        showRestoreModal.value = false;
        selectedFile.value = null;

        // 重置文件输入框
        if (fileInput.value) {
          fileInput.value.value = '';
        }
      } catch (error) {
        Message.error({
          content: `还原失败: ${error.message}`,
          duration: 5000,
          closable: true,
        });
      }
    };

    reader.onerror = () => {
      Message.error('文件读取失败，请检查文件是否有效或重试');
    };

    reader.readAsText(selectedFile.value);
  };
  const schema = ref();

  const handleCopy = async (recordId: number) => {
    if (!recordId) {
      Message.warning('请选择有效数据');
    }
    try {
      await request(`/teacher/scheduleTool/copy/${recordId}`, {
        method: 'get',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      Message.success('拷贝成功，请确认排课任务学期，避免排课结果覆盖');
    } finally {
      await modalRef.value.loadData();
    }
  };

  const handleRowAction = async (action: any, row: any) => {
    currentRecord.value = row;
    switch (action.key) {
      case 'enterSys':
        handleEnterTool();
        break;
      case 'copy':
        await handleCopy(row.id);
        break;
      default:
        break;
    }
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/teacher/scheduleTool');
  });
</script>

<style scoped>
  .home-container {
    min-height: 100vh;
    background-color: #f5f7fa;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .content-wrapper {
    max-width: 1200px;
    width: 100%;
    padding: 40px;
    text-align: center;
  }

  .title {
    font-size: 2.5rem;
    color: #1d2129;
    margin-bottom: 60px;
    font-weight: bold;
  }

  .card-container {
    display: flex;
    gap: 40px;
    justify-content: center;
    flex-wrap: wrap;
  }

  .action-card {
    width: 280px;
    cursor: pointer;
    transition: all 0.3s;
  }

  .action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  .card-icon {
    padding: 40px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f2f3f5;
  }

  .icon {
    font-size: 48px;
    color: #165dff;
  }

  :deep(.arco-typography) {
    margin-bottom: 8px;
  }

  .restore-modal-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
    padding: 20px 0;
  }

  .selected-file {
    color: #4e5969;
    margin-top: 8px;
  }
</style>
