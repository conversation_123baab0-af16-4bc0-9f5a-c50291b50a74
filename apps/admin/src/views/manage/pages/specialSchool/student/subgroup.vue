<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import TeacherSelect from '@repo/components/org/teacherSelect.vue';
  import { computed, onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useRoute } from 'vue-router';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { cloneDeep } from 'lodash';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { getAdminClientNature } from '@repo/components/utils/utils';
  import useCommonStore from '@repo/infrastructure/utils/store';

  const schema = ref(null);

  const route = useRoute();
  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo(route);
  const teacherListVisible = ref(false);
  const studentListVisible = ref(false);
  const currentRow = ref(null);
  const tableRef = ref(null);
  const { loading, setLoading } = useLoading();
  const teacherSelectRef = ref(null);

  const queryParams = {
    schoolNature: menuInfo.app.label,
    sort: '-id',
    orgNature: getAdminClientNature(),
  };
  const studentList = ref([]);
  const filterStudentList = ref([]);
  const targetStudent = ref([]);
  const originalTarget = ref([]);
  const deletedFromOriginalTarget = ref([]);
  const originalTeacher = ref([]);
  const deletedTeacher = ref([]);
  const selectedGradClass = ref();
  const selectedGrad = ref();
  const currentStudentName = ref();

  const handleRowAction = async (action: Record<string, any>, record: any) => {
    currentRow.value = cloneDeep(record);
    switch (action.key) {
      case 'teacherListMaintain':
        teacherListVisible.value = true;
        originalTeacher.value = currentRow.value?.teacherList.map((teacher) => teacher.id) || [];
        break;
      case 'studentListMaintain':
        filterStudentList.value = currentRow.value.studentList.map((student: any) => {
          return { value: student.id, label: student.name };
        });
        targetStudent.value = currentRow.value.studentList.map((item) => item.id);
        originalTarget.value = cloneDeep(targetStudent.value);
        studentListVisible.value = true;
        break;
      default:
        break;
    }
  };

  const resetData = () => {
    currentRow.value = null;
    selectedGrad.value = null;
    selectedGradClass.value = null;
    currentStudentName.value = null;
    targetStudent.value = [];
  };
  const handleClose = () => {
    teacherListVisible.value = false;
    studentListVisible.value = false;
    resetData();
  };

  const handleSelectTeacher = (id, teacher: any) => {
    deletedTeacher.value = deletedTeacher.value.filter((item) => item !== teacher.id);
    const exists = currentRow.value.teacherList.find((item) => item.id === teacher.id);
    if (exists) {
      Message.error('该教师已存在');
      return;
    }
    currentRow.value.teacherList.push({
      id: teacher.id,
      name: teacher.name,
      fixed: false,
      courses: [],
      remark: '',
    });
  };
  const currentRowTeacherIds = computed(() => {
    return currentRow.value.teacherList.map((teacher) => teacher.id);
  });
  const handleSelectAll = (isSelected: boolean, ids: number[], list: any) => {
    if (isSelected) {
      /* 全选 全部添加进去，然后如果已经存在直接跳过 */
      list.forEach((teacher) => {
        if (!currentRowTeacherIds.value.includes(teacher.id))
          currentRow.value.teacherList.push({
            id: teacher.id,
            name: teacher.name,
            fixed: false,
            courses: [],
            remark: '',
          });
      });
    } else {
      /* 取消全选 去除存在于list 里面的数据 且不包含于 originalTeacher || 删除的问题得考虑下  not enough time */
      currentRow.value.teacherList = currentRow.value.teacherList.filter((item) =>
        originalTeacher.value.includes(item.id),
      );
    }
  };

  const deleteTeacher = (record) => {
    deletedTeacher.value.push(record.id);
    currentRow.value.teacherList = currentRow.value.teacherList.filter((item) => item.id !== record.id);
  };

  const handleDeleteTeacher = async () => {
    try {
      await request(`/resourceRoom/subgroup/removeTeacherFromGroup/${currentRow.value.id}`, {
        method: 'delete',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: deletedTeacher.value,
      });
      Message.success('教师移除成功');
    } finally {
      /**/
    }
  };

  const handleSaveTeachersList = async () => {
    setLoading(true);
    try {
      if (deletedTeacher.value.length > 0) await handleDeleteTeacher();

      await request(`/resourceRoom/subgroup/${currentRow.value.id}`, {
        method: 'PUT',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: {
          ...currentRow.value,
          managerUserIds: currentRow.value.teacherList
            .filter((item) => {
              return item.fixed === true;
            })
            .map((item) => {
              return item.id;
            }),
        },
      });
      Message.success('修改任课教师成功');
      tableRef.value.handleLoadData();
      return true;
    } catch (e) {
      Message.error('修改任课教师失败');
      return false;
    } finally {
      resetData();
      setLoading(false);
    }
  };
  const boStore = useCommonStore({
    api: '/resourceRoom/student',
    queryParams: {
      ...queryParams,
    },
  });

  const studentsList = ref();
  const gradeClass = ref();

  const handleRemoveStudentFromGroup = async () => {
    await request(`/resourceRoom/subgroup/removeStudentFromGroup/${currentRow.value.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'delete',
      data: deletedFromOriginalTarget.value,
    });
  };

  const handleSaveStudentList = async () => {
    if (!studentsList.value) {
      studentListVisible.value = false;
      resetData();
      return;
    }
    try {
      if (deletedFromOriginalTarget.value.length) await handleRemoveStudentFromGroup();
      setLoading(true);
      await request(`/resourceRoom/subgroup/${currentRow.value.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'put',
        data: {
          ...currentRow.value,
          studentList: studentsList.value.map((item) => {
            return {
              id: item.id,
              name: item.name,
            };
          }),
        },
      });
      tableRef.value.handleLoadData();
    } finally {
      resetData();
      setLoading(false);
    }
  };

  const grade = computed(() => {
    const uniqueGradeClasses = new Map();
    studentList.value.forEach((item) => {
      if (item.gradeClass && !uniqueGradeClasses.has(item.gradeClass.grade.id)) {
        uniqueGradeClasses.set(item.gradeClass.grade.id, {
          id: item.gradeClass.grade.id,
          name: item.gradeClass.grade.name,
        });
      }
    });
    return Array.from(uniqueGradeClasses.values());
  });

  const handleGradeChange = (val: number) => {
    const uniqueGradeClasses = new Map();
    filterStudentList.value = [];
    selectedGradClass.value = null;
    studentList.value.forEach((item) => {
      if (item.gradeClass && !uniqueGradeClasses.has(item.gradeClass.id) && val === item.gradeClass.grade.id) {
        uniqueGradeClasses.set(item.gradeClass.id, {
          id: item.gradeClass.id,
          name: item.gradeClass.name,
        });
      }
      if (item.gradeClass?.grade.id === val || targetStudent.value.includes(item.id)) {
        filterStudentList.value.push({ label: item.name, value: item.id });
      }
    });
    gradeClass.value = Array.from(uniqueGradeClasses.values());
  };

  const handleGradeClassChange = (val: number) => {
    filterStudentList.value = [];
    studentList.value.forEach((item) => {
      if (
        (item.gradeClass?.id === val && item.gradeClass?.grade?.id === selectedGrad.value) ||
        targetStudent.value.includes(item.id)
      ) {
        filterStudentList.value.push({ label: item.name, value: item.id });
      }
    });
  };
  const filterStudentIds = computed(() => {
    return filterStudentList.value.map((item) => item.value) || [];
  });

  const handleSearch = (val: any) => {
    const students = studentList.value
      .filter((student) => student.name === val && !filterStudentIds.value.includes(student.id))
      .map((item) => {
        return {
          label: item.name,
          value: item.id,
        };
      });
    filterStudentList.value = [...filterStudentList.value, ...students];
  };

  const handleTransferChange = (val: any) => {
    studentsList.value = studentList.value.filter((item) => targetStudent.value.includes(item.id));
    const keep = originalTarget.value.filter((item) => targetStudent.value.includes(item)) || [];
    deletedFromOriginalTarget.value = originalTarget.value.filter((item) => !keep.includes(item));
  };

  onMounted(async () => {
    // await Promise.all([]);
    schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/subgroup');
    studentList.value = await boStore.getList();
  });
</script>

<template>
  <table-with-modal-form
    v-if="schema"
    ref="tableRef"
    module-name="分组"
    :schema="schema"
    :default-query-params="queryParams"
    :visible-columns="['boId', 'name', 'teacherList', 'createdDate']"
    @row-action="handleRowAction"
  >
    <a-modal
      v-model:visible="teacherListVisible"
      :width="800"
      title="分组教师管理"
      :on-before-ok="handleSaveTeachersList"
      :ok-loading="loading"
      :render-to-body="false"
      @close="handleClose"
    >
      <teacher-select
        v-if="currentRow"
        ref="teacherSelectRef"
        show-search
        :show-role="true"
        :multiple="true"
        :default-query-params="{
          branchOffice: currentRow.grade?.school?.branchOfficeId,
          orgNature: currentRow?.orgNature,
        }"
        @change="handleSelectTeacher"
        @select-all="handleSelectAll"
      />
      <a-table :data="currentRow?.teacherList || []" class="mt-2" :pagination="false">
        <template #columns>
          <a-table-column title="教师" data-index="name"></a-table-column>
          <a-table-column title="负责人" data-index="fixed">
            <template #cell="{ record }">
              <a-switch v-model="record.fixed" size="small" type="round" />
            </template>
          </a-table-column>
          <a-table-column title="备注" data-index="remark">
            <template #cell="{ record }">
              <a-input v-model="record.remark" size="mini" />
            </template>
          </a-table-column>
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-button type="text" size="mini" @click="() => deleteTeacher(record)"> 删除</a-button>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-modal>
    <a-modal
      v-model:visible="studentListVisible"
      :width="650"
      title="学生维护"
      :on-before-ok="handleSaveStudentList"
      :render-to-body="false"
      @close="handleClose"
    >
      <a-form auto-label-width>
        <a-row>
          <a-col :span="10">
            <a-form-item label="请选择年级">
              <a-select
                v-model="selectedGrad"
                :options="grade"
                :field-names="{ label: 'name', value: 'id' }"
                allow-search
                size="mini"
                @change="handleGradeChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="请选择班级">
              <div class="flex justify-around">
                <a-select
                  v-model="selectedGradClass"
                  :options="gradeClass"
                  :field-names="{ label: 'name', value: 'id' }"
                  allow-search
                  size="mini"
                  @change="handleGradeClassChange"
                />
                <a-input-search
                  v-model="currentStudentName"
                  size="mini"
                  class="ml-2"
                  placeholder="学生搜索"
                  @search="handleSearch"
                />
              </div>
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="请选择学生">
          <a-transfer v-model="targetStudent" :data="filterStudentList" one-way @change="handleTransferChange">
            <template #source-title="{ onSelectAllChange }">
              <div class="flex justify-start items-center">
                <a-checkbox class="mr-2" @change="onSelectAllChange" />
                <span>学生列表</span>
              </div>
            </template>
            <template #target-title="{ onClear }">
              <div class="flex justify-between items-center">
                <span>分组学生列表</span>
                <icon-delete @click="onClear" />
              </div>
            </template>
          </a-transfer>
        </a-form-item>
      </a-form>
    </a-modal>
  </table-with-modal-form>
</template>

<style scoped lang="scss">
  .tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
</style>
