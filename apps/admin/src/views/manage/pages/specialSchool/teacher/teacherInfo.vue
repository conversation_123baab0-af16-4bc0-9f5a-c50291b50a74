<script setup lang="ts">
  import UserManage from '@repo/components/org/userManage.vue';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { ref } from 'vue';

  const menuStore = useUserMenuStore();

  const queryParams = ref({
    nature: menuStore.getCurrentOrgNature(),
    classInfo: 'true',
  });
</script>

<template>
  <user-manage module-name="特校教师管理" :query-params="queryParams" />
</template>
