<script setup lang="ts">
  import { onMounted, ref, watch } from 'vue';
  import { getPeriodsList } from '@repo/infrastructure/utils/scaffolds';
  import { useList } from '@repo/infrastructure/hooks';
  import TeachingSupervisionArchiveView from '@/views/manage/components/center/teachingSupervisionArchiveView.vue';
  import { getOrgNature } from '@repo/components/utils/utils';
  import { AvatarDisplay } from '@repo/ui/components/data-display/components';
  import SupervisionBySchedule from '@/views/manage/components/instructionalSupervisor/supervisionBySchedule.vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  // import Timetable from '@/views/manage/components/instructionalSupervisor/timetable.vue';
  import { useRoute, useRouter } from 'vue-router';
  // import useSchoolCourseStore from '@repo/components/store/schoolCourseStore';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { Message } from '@arco-design/web-vue';
  import TeacherLessonPlanByWeek from '@/views/manage/components/instructionalSupervisor/teacherLessonPlanByWeek.vue';
  import Plan from '@/views/manage/components/supervision/plan.vue';
  import Evaluate from '@/views/manage/components/supervision/evaluate.vue';
  import SupervisionFilterStatistics from '@/views/manage/components/supervision/supervisionFilterStatistics.vue';
  // import TeacherLessonPlanByWeek from '@/views/manage/components/instructionalSupervisor/teacherLessonPlanByWeek.vue';

  const props = defineProps({
    referenceModule: {
      type: String,
    },
  });
  const pageFilters = ref<any>({});
  let periodsList = getPeriodsList({
    from: 2023,
  });

  const detailVisible = ref(false);
  const currentRow = ref<any>({});

  const {
    listData,
    queryParams,
    loading,
    loadData,
    listInit,
    pagination,
    handlePageChange,
    handlePageSizeChange,
    handleSorterChange,
  } = useList({
    api: '/teacher/teachingSupervision',
    defaultQueryParams: {
      orgNature: getOrgNature(),
    },
  });

  const isTypeByTable = ref<boolean>(true);
  const viewDateRange = ref(null);
  const lessonCount = ref({
    morning: null,
    afternoon: null,
  });
  const allSchools = ref<any[]>([]);
  const allGrades = ref<any[]>([]);
  const currentSchool = ref<any>(null);
  const allGradeClasses = ref<any[]>([]);
  const currentTask = ref();

  const timetableCache = new Map();

  const handleDateByCache = (cacheKey: string): boolean => {
    if (timetableCache.has(cacheKey)) {
      const cachedData = timetableCache.get(cacheKey);
      lessonCount.value.morning = cachedData?.gradeConditions?.reduce(
        (acc, item) => Math.max(acc, item.morningCount),
        0,
      );
      lessonCount.value.afternoon = cachedData?.gradeConditions?.reduce(
        (acc, item) => Math.max(acc, item.afternoonCount),
        0,
      );
      currentTask.value = cachedData;
      return true;
    }
    return false;
  };

  const loadTimeTable = async (schoolId?: number, period?: string) => {
    let id;
    let periods;
    if (schoolId && period) {
      id = schoolId;
      periods = period;
    } else if (currentSchool.value && pageFilters.value.period) {
      id = currentSchool.value;
      periods = pageFilters.value.period;
    }
    if (!id || !periods) {
      Message.info('请正确勾选筛选条件');
      return;
    }

    const cacheKey = `${id}-${periods}`;
    if (handleDateByCache(cacheKey)) return;

    try {
      const { data } = await request(`/teacher/timetable/findByPeriod/${id}/${periods}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      if (!data || !data.assignments?.length) return;
      timetableCache.set(cacheKey, data);
      handleDateByCache(cacheKey);
      /* lessonCount.value.morning = data?.gradeConditions?.reduce((acc, item) => Math.max(acc, item.morningCount), 0);
    lessonCount.value.afternoon = data?.gradeConditions?.reduce((acc, item) => Math.max(acc, item.afternoonCount), 0);
    currentTask.value = data; */
    } catch (e) {
      currentTask.value = null;
      lessonCount.value = {
        morning: null,
        afternoon: null,
      };
    } finally {
      /**/
    }
  };

  const handleLoadData = async () => {
    if (!isTypeByTable.value) {
      await loadTimeTable();
    } else {
      queryParams.value = {
        ...queryParams.value,
        ...pageFilters.value,
      };
      await loadData();
    }
  };

  const handleView = (record) => {
    currentRow.value = record;
    detailVisible.value = true;
  };

  const getCurrentPeriod = (): string => {
    const now = new Date();
    const month = now.getMonth() + 1;
    const year = ref(now.getFullYear());
    const season = month >= 9 || month <= 2 ? '秋' : '春';
    if (month >= 1 && month <= 2) year.value -= 1;
    return `${year.value}年${season}季学期`;
  };

  const handleSwitch = async (val: boolean) => {
    isTypeByTable.value = val;
    // 日历的话需要重新加载学期 periodsList
    if (!val) {
      /**/
    }
  };
  const companyTree = ref([]);
  const companyUsers = ref([]);
  const selectedCompany = ref({
    id: null,
    raw: '',
  });
  const selectedTeacher = ref({
    id: null,
    raw: '',
  });
  const findCompanyById = (boId, companyList) => {
    // eslint-disable-next-line no-restricted-syntax
    for (const company of companyList) {
      if (company.school && company.school.branchOfficeId === boId) return company;

      if (company.children && company.children.length > 0) {
        const result = findCompanyById(boId, company.children);
        if (result) return result;
      }
    }
    return null;
  };

  const loadCompany = async () => {
    const { data: res } = await request('/org/branchOffice/getTree', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'GET',
    });
    companyTree.value = res;
    selectedCompany.value.id = res[0].school.branchOfficeId;
    selectedCompany.value.raw = findCompanyById(selectedCompany.value.id, res);
  };

  const loadCompanyUser = async () => {
    if (!selectedCompany.value.id) return;
    const { data: res } = await request(`/org/companyUser/allTeachers`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'GET',
      params: {
        branchOfficeId: selectedCompany.value.id,
      },
    });
    companyUsers.value = res;
  };
  const loadEnablePeriod = async () => {
    try {
      const { data } = await request(`/teacher/timetable/periods`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          schoolId: currentSchool.value,
        },
      });
      pageFilters.value.period = null;
      periodsList = data;
    } finally {
      /**/
    }
  };

  const filterTreeNode = (searchValue, nodeData) => {
    return nodeData.name?.toLowerCase().indexOf(searchValue.toLowerCase()) > -1;
  };

  const handleCompanyChange = (val: number) => {
    selectedCompany.value.raw = findCompanyById(val, companyTree.value);
  };
  const handleTeacherChange = (val: number) => {
    selectedTeacher.value.raw = companyUsers.value.find((item) => item.id === val);
  };

  const route = useRoute();
  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo(route);

  const getGrades = async () => {
    const { data } = await request('/teacher/schoolGrade', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        pageSize: 999,
        graduate: false,
        orgNature: menuInfo.app?.label,
      },
    });

    const schoolsMap = data.items.reduce((acc, grade) => {
      acc[grade?.school.id] = {
        label: grade?.school?.name,
        value: grade?.school?.id,
        boId: grade?.school?.branchOfficeId,
      };
      return acc;
    }, {});

    allGrades.value = data.items || [];
    allSchools.value = Object.values(schoolsMap);
    currentSchool.value = data.items[0]?.school?.branchOfficeId;
  };

  const getGradeClasses = async () => {
    const { data } = await request('/resourceRoom/gradeClass', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        'pageSize': 999,
        'orgNature': menuInfo.app?.label,
        'grade.graduate': false,
      },
    });

    allGradeClasses.value = data.items || [];
  };

  const updateDateRange = (val: string[]) => {
    viewDateRange.value = val;
  };

  const detailsVisible = ref(false);
  // 查看当前记录& 当前学期的教案情况，根据时间来分化每周记录
  const viewDetails = async (record: any) => {
    currentRow.value = record;
    await loadTimeTable(record?.school.id, pageFilters.value.period);
    detailsVisible.value = true;
  };

  const branchOfficeList = ref([]);
  const userList = ref([]);

  const loadBranchOffice = async () => {
    try {
      const { data: res } = await request('/org/branchOffice/simpleList', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
        params: {
          nature: menuStore.getCurrentOrgNature(),
        },
      });
      branchOfficeList.value = [{ name: '全部', id: -1 }, ...res];
      const boIds = res.map((item) => item.id);
      // 加载所有老师信息
      const { data: result } = await request('/org/companyUser/simpleTeachers', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'put',
        data: boIds,
      });
      userList.value = result?.items || [];
    } catch (e) {
      console.log(e);
    }
  };
  const currentTab = ref('evaluate');

  onMounted(async () => {
    pageFilters.value.period = getCurrentPeriod();
    try {
      await Promise.all([
        getGrades(),
        getGradeClasses(),
        listInit(),
        handleLoadData(),
        loadCompany(),
        loadTimeTable(),
        loadBranchOffice(),
      ]);
    } catch (e) {
      console.error(e);
    }
  });

  watch(
    () => selectedCompany.value,
    async () => {
      await loadCompanyUser();
    },
    { deep: true },
  );
  watch(
    () => currentSchool.value,
    async () => {
      // await loadEnablePeriod();
    },
    { deep: true },
  );

  watch(() => pageFilters.value, handleLoadData, { deep: true });
</script>

<template>
  <a-tabs v-model:active-key="currentTab" default-active-key="evaluate" class="bg-white">
    <a-tab-pane key="evaluate" title="教康评估" class="bg-gray-100">
      <supervisionFilterStatistics
        v-if="currentTab === 'evaluate'"
        type="evaluate"
        :branch-list="branchOfficeList"
        :teacher-list="userList"
      />
      <evaluate v-if="false" />
    </a-tab-pane>
    <a-tab-pane key="plan" title="教康计划" class="bg-gray-100">
      <supervisionFilterStatistics
        v-if="currentTab === 'plan'"
        type="Plan"
        :branch-list="branchOfficeList"
        :teacher-list="userList"
      />
      <plan v-if="false" />
    </a-tab-pane>
    <a-tab-pane key="implement" title="教康实施" class="bg-gray-100">
      <supervisionFilterStatistics
        v-if="currentTab === 'implement'"
        type="implement"
        :branch-list="branchOfficeList"
        :teacher-list="userList"
      />
      <a-card :bordered="false">
        <template #title>
          <span>教学督导</span>
          <a-switch
            v-if="
              !['kindergarten', 'institution', 'compulsoryEducation', 'vocationalEducation'].includes(referenceModule)
            "
            class="ml-4"
            checked-color="#5C6BC0"
            unchecked-color="#B0BEC5"
            size="medium"
            :default-checked="true"
            @change="handleSwitch"
          >
            <template #checked>
              <span class="text-white font-semibold">教师</span>
            </template>
            <template #unchecked>
              <span class="text-white font-semibold">班级</span>
            </template>
          </a-switch>
        </template>
        <template #extra>
          <a-space class="mr-5">
            <a-tree-select
              v-if="false && !isTypeByTable"
              v-model="selectedCompany.id"
              :data="companyTree"
              :field-names="{
                key: 'id',
                value: 'school.branchOfficeId',
                title: 'name',
                children: 'children',
              }"
              placeholder="请选择单位"
              size="mini"
              allow-search
              :filter-tree-node="filterTreeNode"
              @change="handleCompanyChange"
            />
            <a-select
              v-if="!isTypeByTable"
              v-model="currentSchool"
              size="mini"
              class="w-48"
              placeholder="请选择学校"
              :options="allSchools"
              @change="loadTimeTable"
            />
            <a-select
              v-model="pageFilters.period"
              allow-search
              :options="periodsList"
              size="mini"
              placeholder="请选择学期"
              @change="loadTimeTable"
            />
            <a-range-picker
              v-if="!isTypeByTable"
              v-model="viewDateRange"
              size="mini"
              class="w-60"
              :allow-clear="false"
            />
            <a-range-picker
              v-if="isTypeByTable"
              v-model="pageFilters.classDate"
              size="mini"
              format="YYYY-MM-DD HH:mm:ss"
              show-time
              :time-picker-props="{ defaultValue: ['08:00:00', '17:30:00'] }"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
            <a-button size="mini" @click="loadData">
              <template #icon>
                <IconRefresh />
              </template>
            </a-button>
          </a-space>
        </template>
        <a-table
          v-if="isTypeByTable"
          :data="listData"
          size="mini"
          :pagination="pagination"
          :loading="loading"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
          @sorter-change="handleSorterChange"
          @selection-change="onSelectionChange"
        >
          <template #columns>
            <a-table-column title="#" :width="50">
              <template #cell="{ rowIndex }">{{ rowIndex + 1 }}</template>
            </a-table-column>
            <a-table-column title="学校" data-index="school">
              <template #cell="{ record }">
                <a-tooltip :content="record.school?.name">
                  <span>{{ record.school?.name }}</span>
                </a-tooltip>
              </template>
            </a-table-column>
            <a-table-column title="教师姓名" data-index="teacher">
              <template #cell="{ record }">
                <avatar-display :user-info="record.teacher" mode="capsule" />
              </template>
            </a-table-column>
            <a-table-column title="任教班级" data-index="gradeNames">
              <template #cell="{ record }">
                <a-space class="flex-wrap">
                  <a-tag v-for="(item, idx) in record.teachingClasses" :key="idx" size="small">
                    {{ item.name }}
                  </a-tag>
                </a-space>
              </template>
            </a-table-column>
            <a-table-column title="任教年级" data-index="gradeClassNames">
              <template #cell="{ record }">
                <a-space class="flex-wrap">
                  <a-tag v-for="(item, idx) in record.teachingGrades" :key="idx" size="small">
                    {{ item.name }}
                  </a-tag>
                </a-space>
              </template>
            </a-table-column>
            <a-table-column title="已提交教案" data-index="submittedCount" :width="110">
              <template #cell="{ record }">
                <div class="cursor-pointer" @click="viewDetails(record)">
                  <a-tag
                    v-if="record.weekScheduleCount > 0 && record.submittedCount >= record.weekScheduleCount"
                    size="mini"
                    color="green"
                  >
                    {{ record.submittedCount }} 份
                  </a-tag>
                  <a-tag
                    v-else-if="record.weekScheduleCount <= 0 && record.submittedCount > record.weekScheduleCount"
                    size="mini"
                    color="blue"
                  >
                    {{ record.submittedCount }} 份
                  </a-tag>
                  <a-tag v-else-if="record.weekScheduleCount == 0 && record.submittedCount == 0" size="mini">
                    {{ record.submittedCount }} 份
                  </a-tag>
                  <a-tag v-else size="mini" color="red"> {{ record.submittedCount }} 份</a-tag>
                </div>
              </template>
            </a-table-column>
            <a-table-column title="周课时" data-index="originalCount" :width="80">
              <template #cell="{ record }">
                <a-tag size="mini"> {{ record.weekScheduleCount || '未排课' }}</a-tag>
              </template>
            </a-table-column>
            <a-table-column title="原创" data-index="originalCount" :width="80">
              <template #cell="{ record }">
                <a-tag size="mini"> {{ record.originalCount }} 份</a-tag>
              </template>
            </a-table-column>
            <a-table-column title="引用" data-index="referenceCount" :width="80">
              <template #cell="{ record }">
                <a-tag size="mini"> {{ record.referenceCount }} 份</a-tag>
              </template>
            </a-table-column>
            <a-table-column title="操作" :width="80">
              <template #cell="{ record }">
                <a-button size="mini" @click="() => handleView(record)">查看</a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
        <SupervisionBySchedule
          v-if="!isTypeByTable"
          :all-grades="allGrades"
          :all-grade-classes="allGradeClasses"
          :company="currentSchool"
          :period="pageFilters.period"
          :view-date-range="viewDateRange"
          :lesson-count="lessonCount"
          :current-task="currentTask"
          @update-date-range="updateDateRange"
        />

        <teaching-supervision-archive-view
          v-model:visible="detailVisible"
          :record="currentRow"
          :filters="pageFilters"
        />
      </a-card>
    </a-tab-pane>
  </a-tabs>
  <a-modal v-model:visible="detailsVisible" width="60%" cancel-text="关闭">
    <template #title>
      <span>{{ currentRow?.teacher?.name }} - {{ pageFilters?.period }}</span>
    </template>
    <teacherLessonPlanByWeek
      v-if="detailsVisible"
      :record="currentRow"
      :period="pageFilters?.period"
      :lesson-count="lessonCount"
      :current-task="currentTask"
    />
  </a-modal>
</template>

<style scoped lang="scss"></style>
