<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import PosterDesigner from '@/views/manage/components/system/posterDesigner.vue';
  import PosterQrcodeModal from '@/views/manage/components/system/posterQrcodeModal.vue';

  const schema = ref(null);

  const queryParams = {
    sort: '-id',
  };
  const designVisible = ref(false);
  const qrcodeVisible = ref(false);
  const record = ref(null);

  const handleRowAction = (action: string, row: any) => {
    record.value = row;
    switch (action.key) {
      case 'design':
        designVisible.value = true;
        break;
      case 'qrcode':
        qrcodeVisible.value = true;
        break;
      default:
        break;
    }
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/common/poster');
  });
</script>

<template>
  <table-with-modal-form
    v-if="schema"
    module-name="海报管理"
    :schema="schema"
    :default-query-params="queryParams"
    :visible-columns="['name', 'uuid', 'published', 'expireTime']"
    @row-action="handleRowAction"
  />

  <poster-designer v-model:visible="designVisible" :record="record" />
  <poster-qrcode-modal v-model:visible="qrcodeVisible" :record="record" />
</template>
