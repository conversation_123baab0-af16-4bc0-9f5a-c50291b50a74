<script setup lang="ts">
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { ref, onMounted, watch } from 'vue';
  import { Message, Modal } from '@arco-design/web-vue';
  import AddDocTemplate from '@/views/manage/components/system/configuration/addDocTemplate.vue';
  // import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';
  import AttachmentPreviewModal from '@repo/ui/components/data-display/attachmentPreviewModal.vue';

  const sendVersion = {
    SendPlan: 1, // 送教计划
    SendArchives: 2, // 送教档案
  };
  const data = ref<any>({
    sendVersion: 1,
  });
  const size = 'mini';
  const docTemplateList = ref([]);
  const docTemplateVisible = ref(false);
  const currentRecord = ref();
  const filterDocList = ref([]);

  const title = ref('');
  const tabPanes = ref([
    { title: '送教档案', key: 'SendPlan' },
    { title: '送教记录', key: 'SendRecord' },
  ]);
  const type = ref('SendPlan');
  const typeOptions = [
    { label: '送教记录', value: 'SendRecord' },
    { label: '送教档案', value: 'SendPlan' },
  ];
  const columns = [
    { title: '模板名称', dataIndex: 'name' },
    { title: '模板类型', slotName: 'type' },
    { title: '修改人', dataIndex: 'modifiedBy.name' },
    { title: '修改时间', dataIndex: 'modifiedDate' },
    { title: '备注', dataIndex: 'remark', slotName: 'remark' },
    { title: '操作', slotName: 'operation', width: '240', align: 'center' },
  ];
  const addDocTemplateVisible = ref(false);

  const submit = async () => {
    const loading = Message.loading('保存中');
    await request('/resourceRoom/centralConfiguration/save', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'POST',
      data: data.value,
    }).then((res) => {
      loading.close();
      data.value = res.data;
      Message.success('保存成功');
    });
  };
  const loadData = async () => {
    await request('/resourceRoom/centralConfiguration/findByCompany', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'GET',
    }).then((res) => {
      if (res.data?.sendVersion) {
        data.value = res.data;
      }
      title.value = res.data.company?.name;
    });
  };

  const handleAddDoc = () => {
    currentRecord.value = null;
    addDocTemplateVisible.value = true;
  };
  const handleChange = (val) => {
    type.value = tabPanes.value.find((item) => item.key === val).key;
    filterDocList.value = docTemplateList.value.filter((item) => item.type === type.value);
  };
  const flush = async () => {
    await request('/document/docTemplate', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
    }).then((res) => {
      docTemplateList.value = res.data.items;
      docTemplateList.value.sort((a, b) => a.sort - b.sort);
      filterDocList.value = docTemplateList.value.filter((item) => item.type === type.value);
    });
  };
  const handleDownloadDocTemplate = (record: any) => {
    currentRecord.value = record;
  };
  const view = ref(false);
  const viewList = ref<any>(false);
  const handleView = (record: any) => {
    view.value = true;
    viewList.value = [record.attachment];
  };

  const handleEdit = (record: any) => {
    currentRecord.value = record;
    addDocTemplateVisible.value = true;
  };
  const handleDel = async (record: any) => {
    Modal.confirm({
      title: '提示',
      content: `确认删除当前数据 - ${record.name}`,
      onOk: () => {
        request(`/document/docTemplate/${record.id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'DELETE',
        })
          .then(() => {
            flush();
          })
          .catch((e) => {
            Message.error(e.data.message);
          });
      },
    });
  };
  const getType = (file: string): string => {
    const suffix = file.split('.')[file.split('.').length - 1];
    return suffix;
  };

  onMounted(async () => {
    await loadData();
    await flush();
  });
  watch(
    () => data.value.sendVersion,
    (newVal) => {
      docTemplateVisible.value = newVal === 2;
    },
  );
</script>

<!--可以做成一个配置清单-->
<template>
  <div class="p-5" :title="title">
    <a-form auto-label-width>
      <a-form-item required>
        <template #label>
          <span class="ml-2">送教配置</span>
        </template>
        <div class="w-full">
          <div>
            <a-radio-group v-model="data.sendVersion">
              <a-radio :value="sendVersion.SendPlan">简单模式</a-radio>
              <a-radio :value="sendVersion.SendArchives">文档模式</a-radio>
            </a-radio-group>
          </div>
          <div v-if="docTemplateVisible">
            <a-tabs default-active-key="SendPlan" @change="handleChange">
              <template #extra>
                <a-button :size="size" type="primary" @click.stop @click="handleAddDoc">
                  <icon-plus />
                  新建
                </a-button>
              </template>
              <a-tab-pane v-for="item in tabPanes" :key="item.key" :title="item.title">
                <a-table :columns="columns" :data="filterDocList">
                  <template #operation="{ record }">
                    <div class="flex justify-around items-center">
                      <a v-if="false" :href="record.attachment.udf1" download>
                        <a-button :size="size" type="text" @click="handleDownloadDocTemplate(record)">
                          <icon-eye />下载模版
                        </a-button>
                      </a>
                      <a-button :size="size" type="text" @click="handleView(record)"
                        ><template #icon><icon-eye /></template> 预览
                      </a-button>
                      <a-button :size="size" type="text" @click="handleEdit(record)"
                        ><template #icon><icon-edit /></template> 修改
                      </a-button>
                      <a-button :size="size" type="text" status="danger" @click="handleDel(record)">
                        <icon-delete />删除
                      </a-button>
                    </div>
                  </template>
                  <template #remark="{ record }">
                    <span v-if="record.remark">{{ record.remark }}</span>
                    <span v-else>-</span>
                  </template>
                  <template #type="{ record }">
                    <span v-if="record.attachment?.udf1">{{ getType(record.attachment.udf1) }}</span>
                  </template>
                </a-table>
              </a-tab-pane>
            </a-tabs>
          </div>
        </div>
      </a-form-item>
      <a-form-item>
        <a-button size="mini" type="primary" class="mr-8" @click="submit">
          <template #icon>
            <IconSave />
          </template>
          保存</a-button
        >
      </a-form-item>
    </a-form>
  </div>
  <add-doc-template
    v-if="addDocTemplateVisible"
    v-model="addDocTemplateVisible"
    :visible="addDocTemplateVisible"
    :type-options="typeOptions"
    :record="currentRecord"
    :type="type"
    @update:model-value="flush"
  />
  <attachment-preview-modal v-model="view" :current-file-index="0" :files-list="viewList" />
</template>

<style scoped lang="scss"></style>
