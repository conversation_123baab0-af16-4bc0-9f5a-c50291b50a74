<template>
  <div class="p-4 h-full overflow-auto bg-gray-50">
    <div class="flex items-center gap-4 mb-4 pb-3 border-b border-gray-200">
      <a-radio-group v-model="menusKey" type="button">
        <a-radio value="teacher">教师权限</a-radio>
        <a-radio value="admin">管理员权限</a-radio>
      </a-radio-group>
      <a-button type="primary" class="ml-auto" @click="saveRenamedMenus">
        <icon-save class="mr-1" />
        保存修改
      </a-button>
    </div>

    <a-tree
      v-if="isReady && treeData[menusKey]?.length"
      :data="treeData[menusKey]"
      :field-names="fieldNames"
      show-line
      class="bg-transparent"
    >
      <template #extra="nodeData">
        <div class="flex items-center gap-2 text-sm text-blue-600 ml-2" style="position: absolute; left: 30%">
          <a-input
            v-model="nodeData.rename"
            placeholder="新名称"
            allow-clear
            size="small"
            class="w-[]"
            @input="updateLabel(nodeData)"
          />

          <a-button v-if="nodeData.rename" size="mini" class="px-1" @click="resetName(nodeData)">
            <icon-refresh />
          </a-button>
        </div>
      </template>
    </a-tree>

    <a-empty v-else description="暂无权限数据" class="mt-10" />
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { menuService } from '@repo/infrastructure/data';
  import { Message } from '@arco-design/web-vue';
  import { IconRefresh, IconSave } from '@arco-design/web-vue/es/icon';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const adminPermNodes = menuService.adminAllMenus;
  const teacherPermNodes = menuService.teacherAllMenus;

  const treeData = ref({
    teacher: [],
    admin: [],
  });

  const fieldNames = ref({
    key: 'key',
    title: 'label',
    children: 'children',
  });

  const menusKey = ref('teacher');
  const isReady = ref(false);

  const processTreeData = (renameRecord: any, data: any[], parentPath = ''): any[] => {
    return data.map((item) => {
      const currentPath = parentPath ? `${parentPath}/${item.key}` : item.key;

      return {
        key: currentPath,
        originalKey: item.key,
        label: item.label,
        rename: renameRecord?.[currentPath] || '',
        children: item.children ? processTreeData(renameRecord, item.children, currentPath) : undefined,
      };
    });
  };

  const updateLabel = (nodeData: any) => {};

  const resetName = (nodeData: any) => {
    nodeData.rename = '';
  };

  const renamedNodes = ref({
    teacher: {},
    admin: {},
  });

  const collectModifiedNodes = (type, nodes: any[], parentPath = '') => {
    nodes.forEach((item) => {
      const currentPath = parentPath ? `${parentPath}/${item.originalKey}` : item.originalKey;
      if (item.rename && item.rename !== '' && item.rename !== item.originalLabel) {
        renamedNodes.value[type][currentPath] = item.rename;
      }
      if (item.children?.length) {
        collectModifiedNodes(type, item.children, currentPath);
      }
    });
  };

  const renamedRecord = ref<any>({});
  const loadData = async () => {
    const { data } = await request(`/org/customizedName`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    renamedRecord.value = data?.items[0];
  };

  const saveRenamedMenus = async () => {
    const method = renamedRecord.value?.id ? 'put' : 'post';

    const url = renamedRecord.value?.id ? `/org/customizedName/${renamedRecord.value?.id}` : '/org/customizedName';

    try {
      renamedNodes.value = { teacher: {}, admin: {} };
      collectModifiedNodes('teacher', treeData.value.teacher);
      collectModifiedNodes('admin', treeData.value.admin);

      if (!renamedRecord.value?.id) {
        renamedRecord.value = {};
      }

      const data = {
        ...Object.assign(renamedRecord.value, {
          teacherMenus: renamedNodes.value.teacher,
          adminMenus: renamedNodes.value.admin,
        }),
      };
      const { data: res } = await request(url, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method,
        data,
      });
      renamedRecord.value = res;

      Message.success('菜单名称保存成功');
    } catch (e) {
      console.error(e);
      Message.error('保存失败');
    }
  };

  onMounted(async () => {
    await loadData();
    const adminMenus = JSON.parse(JSON.stringify(adminPermNodes || []));
    const teacherMenus = JSON.parse(JSON.stringify(teacherPermNodes || []));

    treeData.value.admin = processTreeData(renamedRecord.value?.adminMenus || {}, adminMenus);
    treeData.value.teacher = processTreeData(renamedRecord.value?.teacherMenus || {}, teacherMenus);
    isReady.value = true;
  });
</script>
