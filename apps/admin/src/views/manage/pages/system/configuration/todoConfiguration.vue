<script setup lang="ts">
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import { onMounted, ref } from 'vue';

  const data = ref<Record<string, any>>();
  const options = ref<any>([]);
  const submit = async () => {
    const loading = Message.loading('保存中');
    await request('/resourceRoom/centralConfiguration/save', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'POST',
      data: data.value,
    }).then((res) => {
      loading.close();
      data.value = res.data;
      Message.success('保存成功');
    });
  };

  const loadData = async () => {
    await request('/resourceRoom/centralConfiguration/findByCompany', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'GET',
    }).then((res) => {
      data.value = res.data;
    });
  };
  const loadTodoOptions = async () => {
    await request('/toDoItems/toDoItems/getTodoOptions', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'GET',
    }).then((res) => {
      options.value = res.data;
    });
  };
  const handleChange = (index, k) => {
    const val = data.value.todoConfiguration[index].fields[k];
    data.value.todoConfiguration[index].fields[k] = !val;
  };
  const getHeader = (arg): string => {
    const val = options.value.find((item) => item.value === arg);
    return val?.label;
  };

  onMounted(async () => {
    await loadTodoOptions();
    await loadData();
  });
</script>

<template>
  <a-card title="待办事项配置中心">
    <a-collapse v-if="data?.todoConfiguration">
      <a-collapse-item v-for="(item, index) in data?.todoConfiguration" :key="index" :header="getHeader(item?.title)">
        <a-checkbox
          v-for="(v, k) in item.fields"
          :key="k"
          v-model="data.todoConfiguration[index].fields[k]"
          :value="v"
          class="ml-4 mb-2"
        >
          <!--@click="handleChange(index, k)"-->
          {{ item?.fieldsLabel[k] }}
        </a-checkbox>
      </a-collapse-item>
    </a-collapse>
    <template #actions>
      <a-button size="mini" type="primary" @click="submit">保存</a-button>
    </template>
  </a-card>
</template>

<style scoped lang="scss"></style>
