<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });
  const emit = defineEmits(['update:modelValue', 'refreshList']);
  const isLoading = ref(false);
  const addOrEditVisible = ref(false);

  const sizeOptions = ref(['2x2', '3x3', '4x4', '5x5', '6x6', '7x7', '8x8', '9x9', '10x10']);
  const groupOptions = ref([1, 2, 3, 4]);
  const ageOptions = ref(['7岁', '8岁', '9岁', '10岁', '11岁', '12岁', '13岁', '14岁', '15岁', '16岁']);
  const addOrEdit = ref('add');
  const currentRecord = ref(null); // 当前记录行
  const currentIndex = ref(0); // 当前索引位置
  const evaluationCriteriaList = ref(); // 当前索引位置

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      render: (text) => text.rowIndex + 1 || 1,
      width: 100,
      align: 'center',
    },
    { title: '年龄段', dataIndex: 'ageBracket', align: 'center', sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '方格大小', dataIndex: 'gridSize', align: 'center', sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '反向计数', dataIndex: 'inverseCount', slotName: 'inverseCount', align: 'center' },
    { title: '发散计数', dataIndex: 'divergentCount', slotName: 'divergentCount', align: 'center' },
    { title: '打乱数字', dataIndex: 'shuffleSymbols', slotName: 'shuffleSymbols', align: 'center' },
    { title: '转动数字', dataIndex: 'turnSymbols', slotName: 'turnSymbols', align: 'center' },
    { title: '旋转数字', dataIndex: 'spinSymbols', slotName: 'spinSymbols', align: 'center' },
    { title: '分组', dataIndex: 'groupCount', align: 'center', sortable: { sortDirections: ['ascend', 'descend'] } },
    { title: '操作', dataIndex: 'actions', slotName: 'actions', align: 'center' },
  ];

  // 获取初始化数据
  const getInitData = () => {
    return {
      start: true, // 开始训练
      student: { age: ageOptions.value[0], id: '' },
      trainingTime: new Date(),
      groupCount: groupOptions.value[0],
      gridSize: '2x2',
      otherArgs: [
        { label: '反向计数', value: false },
        { label: '发散计数', value: false },
        { label: '打乱数字', value: false },
        { label: '转动数字', value: false },
        { label: '旋转数字', value: false },
      ],
    };
  };
  const formData = ref(getInitData());

  const data = ref([]);

  const resetData = () => {
    formData.value = getInitData();
  };

  const loadEvaluationCriteria = async () => {
    try {
      const res = await request('/resourceRoom/attentionTrainingEvaluationCriteria/findByCompanyId', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
      });
      data.value = [];
      res.data.items.forEach((item) => {
        data.value.push({
          id: item.id,
          ageBracket: item.ageBracket,
          gridSize: item.details.gridSize,
          inverseCount: item.details.inverseCount,
          divergentCount: item.details.divergentCount,
          shuffleSymbols: item.details.shuffleSymbols,
          turnSymbols: item.details.turnSymbols,
          spinSymbols: item.details.spinSymbols,
          groupCount: item.details.groupCount,
        });
      });
    } catch (error) {
      /**/
    }
  };

  const handelEdit = (record, index) => {
    currentIndex.value = index;
    addOrEdit.value = 'edit';
    const momentRecord = data.value[currentIndex.value];
    console.log(record, '编辑');
    formData.value = {
      id: record.id,
      start: true,
      student: { age: momentRecord.ageBracket, id: '' },
      trainingTime: new Date(),
      groupCount: momentRecord.groupCount,
      gridSize: momentRecord.gridSize,
      otherArgs: [
        { label: '反向计数', value: momentRecord.inverseCount },
        { label: '发散计数', value: momentRecord.divergentCount },
        { label: '打乱数字', value: momentRecord.shuffleSymbols },
        { label: '转动数字', value: momentRecord.turnSymbols },
        { label: '旋转数字', value: momentRecord.spinSymbols },
      ],
    };
    addOrEditVisible.value = true;
  };
  const handelDel = async (record, index) => {
    await request(`/resourceRoom/attentionTrainingEvaluationCriteria/${record.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'delete',
    });
    await loadEvaluationCriteria();
  };
  const add = () => {
    addOrEdit.value = 'add';
    addOrEditVisible.value = true;
  };

  const getFromFormData = () => {
    return {
      ageBracket: formData.value.student.age,
      gridSize: formData.value.gridSize,
      inverseCount: formData.value.otherArgs[0].value,
      divergentCount: formData.value.otherArgs[1].value,
      shuffleSymbols: formData.value.otherArgs[2].value,
      turnSymbols: formData.value.otherArgs[3].value,
      spinSymbols: formData.value.otherArgs[4].value,
      groupCount: formData.value.groupCount,
    };
  };

  // 判断是否存当前评估结果在了
  const isDataExist = (): boolean => {
    const uniqueKeys = new Set(
      data.value.map((existingItem) =>
        JSON.stringify({
          ageBracket: existingItem.ageBracket,
          gridSize: existingItem.gridSize,
          inverseCount: existingItem.inverseCount,
          divergentCount: existingItem.divergentCount,
          shuffleSymbols: existingItem.shuffleSymbols,
          turnSymbols: existingItem.turnSymbols,
          spinSymbols: existingItem.spinSymbols,
          groupCount: existingItem.groupCount,
        }),
      ),
    );
    const newKey = JSON.stringify(getFromFormData());
    console.log(uniqueKeys.has(newKey), '.。。');
    return uniqueKeys.has(newKey);
  };

  const addPreOk = async () => {
    const saveData = ref();
    const addData = getFromFormData();
    if (isDataExist()) {
      Message.error('存在相同的评估表');
      resetData();
      addOrEditVisible.value = false;
      return;
    }
    const { ageBracket, ...details } = addData; // 解构赋值，排除 ageBracket
    saveData.value = {
      id: null,
      category: '舒尔特方格',
      ageBracket,
      details,
    };
    if (addOrEdit.value === 'add') {
      /**/
    }
    if (addOrEdit.value === 'edit') {
      saveData.value.id = data.value[currentIndex.value].id;
    }
    console.log(saveData, '当前数据');
    await request('/resourceRoom/attentionTrainingEvaluationCriteria/save', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'post',
      data: saveData.value,
    });
    resetData();
    await loadEvaluationCriteria();
    addOrEditVisible.value = false;
  };

  const addCancel = () => {
    resetData();
    addOrEditVisible.value = false;
  };

  const handelPreOpen = async () => {
    formData.value = getInitData();
    await loadEvaluationCriteria();
  };

  const handelPreOk = async () => {
    isLoading.value = false;
    data.value = [];
    emit('update:modelValue', false);
  };

  const handelCancel = () => {
    isLoading.value = false;
    data.value = [];
    emit('update:modelValue', false);
  };
  onMounted(async () => {
    formData.value = getInitData();
    await loadEvaluationCriteria();
  });
</script>

<template>
  <a-modal
    :visible="props.visible"
    fullscreen
    :on-before-ok="handelPreOk"
    :closable="false"
    :is-loading="isLoading"
    @cancel="handelCancel"
    @before-open="handelPreOpen"
  >
    <div class="flex-1 flex justify-between mb-2">
      <span>舒尔特方格（数字）-评估标准</span>
      <a-button type="primary" @click="add">新增标准</a-button>
    </div>
    <a-table :columns="columns" :data="data" column-resizable :bordered="{ cell: true }" :pagination="false">
      <template v-for="(c, index) in columns" :key="index" #[c.slotName]="{ record, rowIndex }">
        <div v-if="c.slotName !== 'actions'" class="flex justify-center">
          <div
            v-if="record[c.dataIndex] === false"
            style="width: 20px; height: 20px; border-radius: 10px; border: #ff814b 2px solid; display: inline-block"
          ></div>
          <div
            v-else
            style="width: 20px; height: 20px; border-radius: 10px; border: #37a8ff 10px solid; display: inline-block"
          ></div>
        </div>
        <div v-else class="flex justify-center">
          <div class="cursor-pointer">
            <span class="mr-4 hover:text-cyan-400" @click="handelEdit(record, rowIndex)">修改</span>
            <a-popconfirm content="确认删除？" type="warning" @ok="handelDel(record, rowIndex)">
              <span class="hover:text-red-400">删除</span>
            </a-popconfirm>
          </div>
        </div>
      </template>
    </a-table>
  </a-modal>

  <a-modal :visible="addOrEditVisible" :closable="false" :on-before-ok="addPreOk" width="600px" @cancel="addCancel">
    <a-form auto-label-width>
      <a-row class="mb-4">
        <a-col :span="12" class="pr-2">
          <a-form-item label="年龄段" :label-col="{ span: 6 }">
            <a-select v-model="formData.student.age" placeholder="请选择年龄段" :options="ageOptions" />
          </a-form-item>
        </a-col>
        <a-col :span="12" class="pl-2">
          <a-form-item label="方格大小" :label-col="{ span: 6 }">
            <a-select v-model="formData.gridSize" :options="sizeOptions" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row class="mb-4">
        <a-col :span="12" class="pr-2">
          <a-form-item label="分组" :label-col="{ span: 6 }">
            <a-select v-model="formData.groupCount" :options="groupOptions" placeholder="请选择分组" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row class="mb-4">
        <a-col :span="24">
          <div class="text-gray-400">其他参数</div>
          <a-divider style="margin: 2px 0" />
          <a-space size="large">
            <a-checkbox v-for="(item, index) in formData.otherArgs" :key="index" v-model="item.value">
              {{ item.label }}
            </a-checkbox>
          </a-space>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<style scoped lang="scss"></style>
