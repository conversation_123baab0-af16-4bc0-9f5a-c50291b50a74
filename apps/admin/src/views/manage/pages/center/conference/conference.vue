<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import MeetingCheckinQrcodeModal from '@/views/manage/components/center/conference/meetingCheckinQrcodeModal.vue';

  const schema = ref(null);

  const queryParams = {
    sort: '-id',
  };
  const currentRecord = ref();
  const QRCodeVisible = ref(false);

  const handleRawAction = (action: any, record: any) => {
    currentRecord.value = record;
    if (action.key === 'meetingCheckinCode') {
      /**/
      QRCodeVisible.value = true;
    }
  };
  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceCenter/conference');
  });
</script>

<template>
  <table-with-modal-form
    v-if="schema"
    module-name="会议培训"
    :schema="schema"
    :default-query-params="queryParams"
    :visible-columns="['subject', 'dateRange', 'address', 'published', 'coverImageId']"
    @row-action="handleRawAction"
  />
  <MeetingCheckinQrcodeModal v-model="QRCodeVisible" :record="currentRecord" />
</template>

<style scoped lang="scss"></style>
