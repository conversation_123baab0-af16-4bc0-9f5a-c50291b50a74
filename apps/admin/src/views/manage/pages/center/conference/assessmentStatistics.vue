<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { request } from '@repo/infrastructure/request';
  import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';

  const size = 'mini';

  // 定义表格列
  const columns = [
    { title: '学校', dataIndex: 'schoolName' },
    { title: '教师姓名', dataIndex: 'teacher.name' },
    { title: '职位名称', dataIndex: 'title', slotName: 'title' },
    { title: '区级教研培训', dataIndex: 'districtTraining', slotName: 'districtTraining' },
    { title: '校级培训', dataIndex: 'schoolTraining', slotName: 'schoolTraining' },
    { title: '课例培训', dataIndex: 'lessonTraining', slotName: 'lessonTraining' },
    { title: '操作', slotName: 'operation' },
  ];
  const detailsColumns = [
    { title: '所属会议', dataIndex: 'conference.subject' },
    { title: '考试结果', dataIndex: 'testResult', slotName: 'testResult' },
    { title: '签到状态', dataIndex: 'signInStatus', slotName: 'signInStatus', align: 'center' },
    { title: '签到时间', dataIndex: 'signInTime' },
    { title: '退签状态', dataIndex: 'signOutStatus', slotName: 'signOutStatus', align: 'center' },
    { title: '退签时间', dataIndex: 'signOutTime' },
  ];
  const detailsColumnsSchool = [
    { title: '标题', dataIndex: 'eventSubject' },
    { title: '标签', dataIndex: 'tag' },
    { title: '事件日期', dataIndex: 'eventDate' },
    { title: '事件地点', dataIndex: 'eventLocation' },
    { title: '活动主题', dataIndex: 'subject', align: 'center' },
    { title: '附件', dataIndex: 'attachments', slotName: 'attachments' }, // AttachmentPreview
  ];
  const tableData = ref([]);

  const detailsData = ref([]);
  const detailsDataSchool = ref([]);
  const termOptions = ref([{ label: '2024', value: 2024 }]);

  const attendStatus = {
    NotParticipated: '未参加',
    Late: '迟到',
    LeaveEarly: '早退',
    NormalSignOut: '正常签退',
    NormalSignIn: '正常签到',
    FullAttendance: '全勤',
    NotSignIn: '未签到',
    NotSignOut: '未签退',
  };
  const testStatus = {
    QUALIFIED: '合格',
    UNQUALIFIED: '不合格',
    EXCELLENT: '优秀',
    NotParticipated: '未参加',
  };

  const currentRecord = ref(null);
  const viewVisible = ref(false);

  const handleView = (record: any) => {
    currentRecord.value = record;
    if (currentRecord.value) viewVisible.value = true;
  };

  const resetData = () => {
    currentRecord.value = null;
  };

  const handlePreOk = async () => {
    viewVisible.value = false;
    resetData();
  };

  const handleCancel = () => {
    viewVisible.value = false;
    resetData();
  };
  const pass = {
    color: '#00aa22',
    backgroundColor: '#eaffef',
  };
  const eliminate = {
    color: '#ff5860',
    backgroundColor: '#fff3f3',
  };
  const getStatus = (code: string, type: string) => {
    if (type === 'attend') {
      if (['NormalSignOut', 'NormalSignIn', 'FullAttendance'].includes(code)) return pass;
    } else if (['QUALIFIED', 'EXCELLENT'].includes(code)) return pass;
    return eliminate;
  };
  const loadDetailsData = async () => {
    // 目前只统计了资源中心的会议记录，还需要加上学校的记录
    const res = await request(`/resourceCenter/conferenceRecord/findByTeacher/${currentRecord.value.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'GET',
    });
    detailsData.value = res.data.items;
    const result = await request(`/resourceRoom/resourceRoomEvent/findByTeacher/${currentRecord.value.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'GET',
    });
    detailsDataSchool.value = result.data.items;
  };
  const dateRange = ref<string[]>([]);
  const loadData = async () => {
    const { data: res } = await request(`/resourceCenter/assessmentStatistics/findAll`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'GET',
      params: {
        start: dateRange.value[0],
        end: dateRange.value[1],
      },
    });
    tableData.value = res;
  };
  const handlePreOpen = async () => {
    await loadDetailsData();
  };

  onMounted(async () => {
    await loadData();
  });
</script>

<template>
  <a-card title="融合教育考核统计">
    <template #extra>
      <!--<a-select placeholder="请选择年份" :options="termOptions" class="w-40 mr-10" />-->
      <div class="flex justify-center space-x-1 mr-4">
        <a-range-picker v-model="dateRange" :size="size" />
        <a-button :size="size" @click="loadData"
          ><template #icon><icon-search /></template>搜索</a-button
        >
      </div>
    </template>
    <a-table :columns="columns" :data="tableData">
      <template v-for="(c, index) in columns" :key="index" #[c.slotName]="{ record }">
        <span
          v-if="c.slotName !== 'operation' && c.slotName !== 'title'"
          class="pl-1 pr-1"
          :style="record[c.dataIndex] > 0.5 ? pass : eliminate"
        >
          {{ record[c.dataIndex] ? record[c.dataIndex] : 0 }}学时
        </span>
        <div v-else-if="c.slotName === 'title'">
          <span v-for="title in record.title" :key="title" class="bg-gray-100 pl-1 pr-1 rounded m-px">
            {{ title }}
          </span>
        </div>
        <a-button v-else size="mini" @click="handleView(record)">查看</a-button>
      </template>
    </a-table>
  </a-card>
  <!--查看-->
  <a-modal
    :title="currentRecord?.teacher.name + ' - 的统计'"
    :visible="viewVisible"
    :closable="false"
    :on-before-ok="handlePreOk"
    width="60%"
    @before-open="handlePreOpen"
    @cancel="handleCancel"
  >
    <div v-if="currentRecord">
      <div class="rounded bg-gray-50 p-4 mb-2">
        <div class="flex justify-start w-2/4 mb-2">
          <span class="flex-1"><span class="font-bold">学校:</span> {{ currentRecord.schoolName }}</span>
        </div>
        <div class="mb-2"><span class="font-bold">教师姓名: </span>{{ currentRecord.teacher.name }}</div>

        <div class="mb-2">
          <span class="font-bold">职位名称:</span>
          <span v-for="title in currentRecord.title" :key="title" class="bg-gray-100 pl-1 pr-1 rounded-sm m-px">
            {{ title }}
          </span>
        </div>
        <div class="flex justify-start w-3/4">
          <span class="flex-1">
            <span class="font-bold">区级教研培训: </span>
            {{ currentRecord.districtTraining ? currentRecord.districtTraining : 0 }}学时
          </span>
          <span class="flex-1">
            <span class="font-bold">校级培训: </span>
            {{ currentRecord.schoolTraining ? currentRecord.schoolTraining : 0 }}学时
          </span>
          <span class="flex-1">
            <span class="font-bold">课例培训:</span>
            {{ currentRecord.lessonTraining ? currentRecord.lessonTraining : 0 }}学时
          </span>
        </div>
      </div>
      <div>
        <a-divider orientation="left"><span class="font-bold">区级</span></a-divider>
        <a-table :columns="detailsColumns" :bordered="{ cell: true }" column-resizable :data="detailsData">
          <template v-for="(c, index) in detailsColumns" :key="index" #[c.slotName]="{ record }">
            <span v-if="c.slotName == 'testResult'">
              <span :style="getStatus(record.testResult, 'test')">{{ testStatus[record[c.dataIndex]] }}</span>
            </span>
            <span v-else-if="record[c.dataIndex] === null || record[c.dataIndex] === ''" :style="eliminate">异常</span>
            <span v-else class="pl-1 pr-1" :style="getStatus(record[c.dataIndex], 'attend')">
              {{ attendStatus[record[c.dataIndex]] }}
            </span>
          </template>
        </a-table>

        <a-divider orientation="left"><span class="font-bold">校级</span></a-divider>
        <a-table :columns="detailsColumnsSchool" :bordered="{ cell: true }" column-resizable :data="detailsDataSchool">
          <template #attachments="{ record }">
            <div>
              <AttachmentsPreviewDisplay v-if="record.attachments?.length" :raw="record.attachments" class="ml-10" />
            </div>
          </template>
        </a-table>
      </div>
    </div>
  </a-modal>
</template>

<style lang="scss" scoped></style>
