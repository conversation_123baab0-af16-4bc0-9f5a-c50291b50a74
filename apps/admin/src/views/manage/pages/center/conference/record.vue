<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';

  const schema = ref(null);

  const queryParams = {
    sort: '-id',
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceCenter/conferenceRecord');
  });
</script>

<template>
  <table-with-modal-form
    v-if="schema"
    module-name="会议培训记录"
    :schema="schema"
    :default-query-params="queryParams"
    :visible-columns="[
      'conference',
      'participant',
      'participantName',
      'branchOfficeName',
      'fullAttendance',
      'testResult',
      'signInStatus',
      'signInTime',
      'signOutStatus',
      'signOutTime',
    ]"
  />
</template>

<style scoped lang="scss"></style>
