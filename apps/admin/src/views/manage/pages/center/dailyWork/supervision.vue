<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { CrudTable, TableAction } from '@repo/ui/components/table';
  import { ModalForm } from '@repo/ui/components/form';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';
  import ViewScores from '@/views/manage/components/supervision/viewScores.vue';
  import viewStatistics from '@/views/manage/components/supervision/viewStatistics.vue';
  import AddCriteria from '@/views/manage/components/supervision/addCriteria.vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { getOrgNature } from '@repo/components/utils/utils';
  import useCommonStore from '@repo/infrastructure/utils/store';
  import ExpertsModal from '../../../components/paperCompetition/expertsModal.vue';
  import PaperCompetitionLinkModal from '../../../components/paperCompetition/paperCompetitionLinkModal.vue';

  const editVisible = ref(false);
  const viewScoresVisible = ref(false);
  const addCriteriaVisible = ref(false);
  const viewStatisticsVisible = ref(false);
  const expertsVisible = ref(false);
  const linkModalVisible = ref(false);
  const specifyVisible = ref(false);
  const displayType = ref('');

  const currentEditRow = ref<any>({});
  const schema = ref<any>(null);
  const tableRef = ref<any>(null);

  const handleShowEdit = (raw?: any) => {
    editVisible.value = true;
    currentEditRow.value = raw || {};
  };

  const columns = ['type', 'name', 'description', 'enabled', 'cbName', 'mbName', 'startTime', 'endTime'];

  const flush = async () => {
    currentEditRow.value = {};
    await tableRef.value.loadData();
  };

  const clone = async (record: any) => {
    const loading = Message.loading('请稍等...');
    await request(`/resourceCenter/evaluationCriterion/copy/${record.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'put',
    }).then(() => {
      loading.close();
      Message.success('克隆成功');
      flush();
    });
  };
  const designatedNatures = ref<string[]>([]);

  const handleRowAction = async (action: any, record?: any) => {
    currentEditRow.value = {};
    displayType.value = '';
    switch (action.key) {
      case 'edit':
        handleShowEdit(record);
        break;
      case 'add':
        displayType.value = 'add';
        addCriteriaVisible.value = true;
        break;
      case 'clone':
        await clone(record);
        break;
      case 'viewScores': // 查看
        currentEditRow.value = record;
        viewScoresVisible.value = true;
        break;
      case 'reviseCriteria': // 修订
        displayType.value = 'reviseCriteria';
        currentEditRow.value = record;
        addCriteriaVisible.value = true;
        break;
      case 'viewStatistics': // 查看统计
        currentEditRow.value = record;
        viewStatisticsVisible.value = true;
        break;
      case 'experts':
        expertsVisible.value = true;
        currentEditRow.value = record;
        break;
      case 'assessmentLink': // 评审链接
        linkModalVisible.value = true;
        currentEditRow.value = record;
        break;
      case 'specify':
        specifyVisible.value = true;
        currentEditRow.value = record;
        designatedNatures.value = currentEditRow.value.specifyNature;
        break;
      default:
      // pass
    }
  };
  const modelUrl =
    'https://static1.tejiaoedu.com/attachments/resource/drive/20250318/督导考核指标模版_20250318104224.xlsx';

  const natures = ref([]);
  const handleChange = (val) => {
    designatedNatures.value = val;
  };
  const handleClose = (val: string) => {
    designatedNatures.value = designatedNatures.value.filter((item) => item !== val);
  };
  const btnLoading = ref(false);
  const handlePreOk = async () => {
    btnLoading.value = true;
    try {
      currentEditRow.value.specifyNature = designatedNatures.value;
      await request(`/resourceCenter/evaluationCriterion/${currentEditRow.value.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'put',
        data: currentEditRow.value,
      });
      Message.success('操作成功');
    } finally {
      btnLoading.value = false;
    }
  };
  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceCenter/evaluationCriterion');
    const { data: res } = await request('/resourceCenter/fusionSchool/getCompanyNatureList', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
    });
    natures.value = res.items;
  });

  defineExpose({
    loadData: async (params?: any) => {
      await tableRef.value.loadData(params);
    },
    handleShowEdit,
  });
</script>

<template>
  <a-card v-if="schema">
    <template #title>
      <div class="flex justify-between">
        <div class="flex-1">常规巡检管理</div>
        <table-action
          v-if="tableRef"
          :schema="schema"
          :table="tableRef"
          component-size="mini"
          @row-action="handleRowAction"
        >
          <template #extra-actions>
            <a-button type="outline" size="mini">
              <a :href="modelUrl" download>模板下载</a>
            </a-button>
          </template>
        </table-action>
      </div>
    </template>
    <crud-table
      ref="tableRef"
      :schema="schema"
      class="mt-2"
      :default-query-params="{ sort: '-id', nature: getOrgNature() }"
      :visible-columns="columns"
      :row-action-width="240"
      @row-action="handleRowAction"
    >
    </crud-table>
    <modal-form
      v-model:visible="editVisible"
      v-model="currentEditRow"
      :schema="schema"
      :modal-width="900"
      modal-name="常规巡检"
      @ok="() => tableRef.loadData({})"
    />
  </a-card>

  <view-scores
    v-model="viewScoresVisible"
    :visible="viewScoresVisible"
    :evaluation-indicator="currentEditRow"
    @update:model-value="flush"
  />
  <add-criteria
    v-model="addCriteriaVisible"
    :visible="addCriteriaVisible"
    :evaluation-criterion="currentEditRow"
    :display-type="displayType"
    @update:model-value="flush"
  />
  <view-statistics
    v-if="viewStatisticsVisible"
    v-model="viewStatisticsVisible"
    :visible="viewStatisticsVisible"
    :record="currentEditRow"
  />
  <experts-modal v-model="currentEditRow" v-model:visible="expertsVisible" @ok="() => tableRef.handleLoadData()" />
  <paper-competition-link-modal v-model="currentEditRow" v-model:visible="linkModalVisible" />

  <a-modal v-if="specifyVisible" v-model:visible="specifyVisible" :on-before-ok="handlePreOk" :ok-loading="btnLoading">
    <a-select
      v-model="designatedNatures"
      size="mini"
      placeholder="请选学校类型"
      :multiple="true"
      @change="handleChange"
    >
      <a-option v-for="(item, index) in natures" :key="index" :label="item" :value="item" />
    </a-select>
    <div class="flex space-x-2 mt-2 flex-grow flex-wrap">
      <a-tag
        v-for="item in designatedNatures"
        :key="item"
        color="blue"
        class="mb-2"
        closable
        @close="handleClose(item)"
      >
        {{ item }}
      </a-tag>
    </div>
  </a-modal>
</template>

<style scoped lang="scss">
  .topUp {
    position: fixed;
    z-index: 9999;
  }
</style>
