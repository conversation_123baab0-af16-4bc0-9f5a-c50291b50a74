<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';

  const schema = ref(null);

  const queryParams = {
    sort: '-id',
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceCenter/resourceCenterEvent');
  });
</script>

<template>
  <table-with-modal-form
    v-if="schema"
    module-name="中心大事记"
    :schema="schema"
    :default-query-params="queryParams"
    :visible-columns="['subject', 'tag', 'eventDate', 'eventLocation', 'eventSubject', 'participants']"
  />
</template>

<style scoped lang="scss"></style>
