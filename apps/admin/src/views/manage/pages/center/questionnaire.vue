<script setup lang="ts">
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import QuestionnaireManageModal from '@/views/manage/components/questionnaire/questionnaireManageModal.vue';
  import QuestionnaireFormPreview from '@repo/components/questionnaireForm/questionnaireFormPreview.vue';
  import { ref } from 'vue';
  import InvestigationResultModal from '@/views/manage/components/questionnaire/investigationResultModal.vue';
  import TestResultModal from '@/views/manage/components/questionnaire/testResultModal.vue';
  import RecordListModal from '@/views/manage/components/questionnaire/recordListModal.vue';

  const schema = SchemaHelper.getInstanceByApi('/questionnaire/form');

  const questionnaireManageVisible = ref(false);
  const previewVisible = ref(false);
  const investigationResultVisible = ref(false);
  const testResultVisible = ref(false);

  const currentForm = ref<any>(null);
  const tableRef = ref<any>(null);
  const recordListVisible = ref(false);

  const handleRowActions = (action: any, record: any) => {
    currentForm.value = record;
    switch (action.key) {
      case 'preview':
        previewVisible.value = true;
        break;
      case 'questionnaire':
        questionnaireManageVisible.value = true;
        break;
      case 'testManagement':
        questionnaireManageVisible.value = true;
        break;
      case 'investigationResult':
        investigationResultVisible.value = true;
        break;
      case 'testResult':
        testResultVisible.value = true;
        break;
      case 'recordList':
        recordListVisible.value = true;
        break;
      default:
        break;
    }
  };

  const handleRefresh = () => {
    tableRef.value?.handleLoadData();
  };
</script>

<template>
  <table-with-modal-form
    v-if="schema"
    ref="tableRef"
    module-name="问卷表单"
    :schema="schema"
    :visible-columns="['scene', 'name', 'published', 'startTime', 'endTime']"
    @row-action="handleRowActions"
  />

  <questionnaire-manage-modal
    v-model:form="currentForm"
    v-model:visible="questionnaireManageVisible"
    :record="currentForm"
    @refresh="handleRefresh"
  />

  <a-modal v-model:visible="previewVisible" :width="700" :title="`${currentForm?.name}`" hide-cancel>
    <questionnaire-form-preview v-if="previewVisible" :form="currentForm" :question-ids="currentForm?.questionIds" />
  </a-modal>

  <investigation-result-modal v-model:visible="investigationResultVisible" :form="currentForm" />
  <test-result-modal v-model:visible="testResultVisible" :form="currentForm" />
  <record-list-modal v-model:visible="recordListVisible" :form-id="currentForm?.id" />
</template>

<style scoped lang="scss"></style>
