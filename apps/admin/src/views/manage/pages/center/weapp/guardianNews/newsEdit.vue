<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { CrudForm } from '@repo/ui/components/form';
  import Uploader from '@repo/ui/components/upload/uploader.vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { request } from '@repo/infrastructure/request';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    modelData: {
      type: Object,
      required: true,
    },
  });

  const { loading, setLoading } = useLoading();
  const schema = SchemaHelper.getInstanceByApi('/guardian/news');
  const emit = defineEmits(['update:modelValue', 'refreshList']);

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  });

  const data = ref<any>(props.modelData);

  const handleCancel = () => {
    visible.value = false;
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      if (data.value.id) {
        await request(`${schema.api}/${data.value.id}`, {
          method: 'PUT',
          data: data.value,
        });
      } else {
        await request(schema.api, {
          method: 'POST',
          data: data.value,
        });
      }

      visible.value = false;
      emit('refreshList');
    } finally {
      setLoading(false);
    }
  };

  const handleFileUploaded = (files: any[]) => {
    data.value.url = files[0]?.url;
  };
</script>

<template>
  <a-modal
    v-model:visible="visible"
    width="800px"
    :title="data.id ? '编辑资讯' : '新建资讯'"
    :unmount-on-close="true"
    :esc-to-close="false"
    :closable="false"
    :mask-closable="false"
    ok-text="保存"
    :ok-loading="loading"
    :render-to-body="false"
    @ok="handleSave"
    @cancel="handleCancel"
  >
    <CrudForm v-model="data" :schema="schema" :show-actions="false">
      <template #custom-input-url>
        <uploader :limit="1" accept="video/*" sub-folder="guardian-course" @uploaded="handleFileUploaded" />
      </template>
    </CrudForm>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSave">保存</a-button>
    </template>
  </a-modal>
</template>

<style scoped lang="less"></style>
