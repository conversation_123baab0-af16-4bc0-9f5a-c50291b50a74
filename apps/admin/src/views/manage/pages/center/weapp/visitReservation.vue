<script setup lang="ts">
  import { CrudTable } from '@repo/ui/components/table';
  import { ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import dayjs from 'dayjs';
  import { Modal } from '@arco-design/web-vue';

  const tableRef = ref<any>(null);
  const schema = SchemaHelper.getInstanceByApi('/guardian/visitReservation');

  const handleShowReason = (reason: string, title?: string) => {
    Modal.info({
      title: title || '到访事由',
      content: reason,
      hideCancel: true,
    });
  };
</script>

<template>
  <a-card class="m-2 h-full">
    <template #title>
      <div class="flex justify-between">
        <div>预约到校</div>
        <div v-if="tableRef">
          <a-button size="mini" @click="tableRef.loadData">
            <template #icon>
              <IconRefresh />
            </template>
            刷新
          </a-button>
        </div>
      </div>
    </template>
    <CrudTable
      ref="tableRef"
      :column-resizable="true"
      :default-query-params="{
        preload: 'Guardian',
      }"
      :schema="schema"
      :row-action-width="200"
    >
      <template #custom-column-guardian="{ record }">
        <div>{{ record.guardian?.name }}</div>
        <div v-if="record?.guardian?.currentStudent" class="text-sm">
          <div>{{ record?.guardian?.currentStudent?.fusionSchoolName }}</div>
          <div>
            {{ record?.guardian?.currentStudent?.grade }}
          </div>
          <div> {{ record?.guardian?.currentStudent?.name }}的家长 </div>
        </div>
      </template>
      <template #custom-column-visitor="{ record }">
        <div>{{ record.visitor }}</div>
        <div class="text-sm">{{ record.visitorMobile }}</div>
      </template>
      <template #custom-column-visitDate="{ record }">
        <div>{{ dayjs(record.visitDate).format('YYYY-MM-DD') }}</div>
        <div>时间: {{ record.visitTime }}</div>
      </template>
      <template #custom-column-respondent="{ record }">
        <div>{{ record.respondent }}</div>
        <div class="text-sm">{{ record.respondentMobile }}</div>
      </template>
      <template #custom-column-status="{ record }">
        <a-tag v-if="record.status === 'pending'" color="orange">待处理</a-tag>
        <a-tag v-else-if="record.status === 'approved'" color="green">已确认</a-tag>
        <a-tag v-else color="red">已拒绝</a-tag>
      </template>
      <template #custom-column-reason="{ record }">
        <div class="text-sm cursor-pointer" @click="() => handleShowReason(record.reason)">
          {{ record.reason }}
        </div>
      </template>

      <template #custom-column-auditRemark="{ record }">
        <div class="text-sm cursor-pointer" @click="() => handleShowReason(record.auditRemark, '审核备注')">
          {{ record.auditRemark }}
        </div>
      </template>
    </CrudTable>
  </a-card>
</template>

<style scoped lang="less"></style>
