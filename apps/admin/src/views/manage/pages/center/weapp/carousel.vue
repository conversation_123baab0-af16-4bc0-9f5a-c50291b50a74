<script setup lang="ts">
  import { CrudTable } from '@repo/ui/components/table';
  import { ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useRouter } from 'vue-router';
  import { request } from '@repo/infrastructure/request';
  import {Message, Modal} from '@arco-design/web-vue';

  const tableRef = ref<any>(null);
  const schema = SchemaHelper.getInstanceByApi('/guardian/weappCarousel');

  const router = useRouter();

  const handleDelete = async (id: number) => {
    await request(`/guardian/weappCarousel/${id}`, {
      method: 'DELETE',
    });

    Message.success('删除成功');

    tableRef.value.loadData();
  };

  const handleRowAction = (action, record) => {
    switch (action.key) {
      case 'goEdit':
        router.push(`/manage/center/weapp/carousel/edit?id=${record.id}`);
        break;
      case 'goDelete':
        Modal.confirm({
          title: '删除小程序轮播',
          content: '确定删除该小程序轮播吗？',
          onOk: () => handleDelete(record.id),
        });
        break;
      default:
        break;
    }
  };
</script>

<template>
  <a-card>
    <template #title>
      <div class="flex justify-between">
        <div>小程序轮播图</div>
        <div>
          <a-button size="mini" type="primary" @click="() => router.push('/manage/center/weapp/carousel/edit')">
            <template #icon>
              <IconPlus />
            </template>
            新增小程序轮播
          </a-button>
        </div>
      </div>
    </template>
    <crud-table ref="tableRef" :schema="schema" @row-action="handleRowAction">
      <template #custom-column-image="{ record }">
        <a-popconfirm>
          <a-button size="mini">
            <template #icon>
              <IconImage />
            </template>
            查看
          </a-button>
          <template #content>
            <img :src="record.image" alt="轮播图" style="max-width: 100%" />
          </template>
        </a-popconfirm>
      </template>
    </crud-table>
  </a-card>
</template>

<style scoped lang="less"></style>
