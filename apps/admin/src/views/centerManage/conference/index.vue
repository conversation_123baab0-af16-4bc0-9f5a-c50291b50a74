<script setup lang="ts">
  import ModuleLeftNav from '@/common/layout/components/moduleLeftNav.vue';
  import { useUserStore } from '@repo/infrastructure/store';

  const userStore = useUserStore();
  const subTabs = [
    {
      key: 'manage',
      title: '会议培训管理',
      visible: () => {
        return userStore.isAuthorized('admin_org:workRecord:conference:manage');
      },
    },
    {
      key: 'record',
      title: '会议培训记录',
      visible: () => {
        return userStore.isAuthorized('admin_org:workRecord:conference:manage');
      },
    },
    {
      key: 'certificate',
      title: '会议培训证明',
      visible: () => {
        return userStore.isAuthorized('admin_org:workRecord:conference:certificate');
      },
    },
    {
      key: 'statistics',
      title: '会议培训统计',
      visible: () => {
        return userStore.isAuthorized('admin_org:workRecord:conference:');
      },
    },
  ].filter((tab) => tab.visible?.() ?? true);
</script>

<template>
  <div class="flex gap-2">
    <module-left-nav :tabs="subTabs" url-prefix="/centerManage/conference" />
    <router-view />
  </div>
</template>
