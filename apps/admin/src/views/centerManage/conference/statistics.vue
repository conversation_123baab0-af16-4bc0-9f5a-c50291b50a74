<script setup lang="ts">
  import { useList } from '@repo/infrastructure/crud';
  import { PROJECT_URLS } from '@repo/env-config';
  import { onMounted } from 'vue';

  const { listData, loading, loadData, pagination, handlePageSizeChange, handlePageChange } = useList({
    api: '/resourceCenter/conference/statistics',
    defaultQueryParams: {
      sort: '-id',
    },
    axiosConfig: {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    },
  });

  onMounted(async () => {
    await loadData({});
  });
</script>

<template>
  <a-card class="mr-2 my-2 flex-1" title="会议培训统计" :loading="loading">
    <template #title>
      <div class="flex justify-between items-center">
        <div>会议培训统计</div>
        <div>
          <a-button size="mini" @click="loadData({})">
            <template #icon>
              <IconRefresh />
            </template>
          </a-button>
        </div>
      </div>
    </template>
    <a-table
      :data="listData"
      :pagination="pagination"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    >
      <template #columns>
        <a-table-column title="会议培训主题" data-index="subject" />
        <a-table-column title="会议时间" data-index="dateRange">
          <template #cell="{ record }">
            {{ record.dateRange?.join(' ~') }}
          </template>
        </a-table-column>
        <a-table-column title="应参会" data-index="shouldSignCount" />
        <a-table-column title="实际参会" data-index="participantCount" />
        <a-table-column title="迟到" data-index="lateCount" />
        <a-table-column title="早退" data-index="leaveEarlyCount" />
        <a-table-column title="未签退" data-index="unSignOutCount" />
        <a-table-column title="全勤" data-index="fullAttendanceCount" />
      </template>
    </a-table>
  </a-card>
</template>

<style scoped lang="scss"></style>
