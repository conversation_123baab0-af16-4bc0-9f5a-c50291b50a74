<script setup lang="ts">
  import { PropType } from 'vue';
  import { usePrompt } from '@repo/ui/components';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    raw: {
      type: Array as PropType<any[]>,
      required: true,
    },
    record: {
      type: Object,
      required: true,
    },
  });

  const emit = defineEmits(['refresh']);
  const { prompt } = usePrompt();

  const handleReturn = async (record: any) => {
    const count = await prompt({
      title: '请输入归还数量',
      placeholder: '请输入归还数量',
      raw: record.numUdf2,
      inputPattern: /^\d+$/,
      validate(val) {
        if (Number.parseInt(val, 10) > record.numUdf2) {
          Message.error('归还数量不能大于未归还数量');
          return false;
        }
        return true;
      },
    });
    if (!count) {
      return;
    }

    await request('/asset/assetBorrowApplication/return', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'PUT',
      data: {
        id: props.record.id,
        numUdf2: record.id,
        numUdf1: count,
      },
    });

    Message.success('归还成功');

    emit('refresh');
  };
</script>

<template>
  <a-table :data="raw" :bordered="false" :stripe="true">
    <template #columns>
      <a-table-column title="序号" :width="60" align="center">
        <template #cell="{ rowIndex }">{{ rowIndex + 1 }}</template>
      </a-table-column>
      <a-table-column title="资产名称" data-index="name" />
      <a-table-column title="借用数量" data-index="numUdf1" />
      <a-table-column title="未归还数量" data-index="numUdf2" />
      <a-table-column title="操作" data-index="numUdf2">
        <template #cell="scope">
          <a-button
            v-if="scope.record.numUdf2 > 0"
            v-permission="'center:dailyWork:asset:applicationVerify'"
            type="outline"
            size="mini"
            @click="() => handleReturn(scope.record)"
          >
            归还
          </a-button>
        </template>
      </a-table-column>
    </template>
  </a-table>
</template>

<style scoped lang="scss"></style>
