<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { CrudTable, TableAction } from '@repo/ui/components/table';
  import { ModalForm } from '@repo/ui/components/form';
  import { Message, Modal } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { usePrompt } from '@repo/ui/components';

  const editVisible = ref(false);
  const currentEditRow = ref<any>({});
  const schema = ref<any>(null);
  const tableRef = ref<any>(null);
  const { prompt } = usePrompt();

  const handleShowEdit = (raw?: any) => {
    editVisible.value = true;
    currentEditRow.value = raw || {};
  };

  const handleCloseEdit = () => {
    editVisible.value = false;
    currentEditRow.value = {};
  };

  const columns = [
    'symbol',
    'status',
    'operator',
    'borrowCount',
    'unreturnedCount',
    'planBorrowDate',
    'borrowDate',
    'lastReturnDate',
    'applyComment',
    'verifyComment',
  ];

  const handleRowAction = async (action: any, record?: any) => {
    switch (action.key) {
      case 'add':
      case 'edit':
        if (record?.id && record?.status !== 'NotSubmitteed') {
          Modal.info({
            title: '提示',
            content: '已提交的借用申请不允许编辑',
            hideCancel: true,
          });
          return;
        }
        handleShowEdit(record || {});
        break;
      case 'submit':
      case 'withdraw':
        // eslint-disable-next-line no-case-declarations
        const msg = action.key === 'submit' ? '确认提交该借用申请吗？' : '确认撤回该借用申请吗？';
        // eslint-disable-next-line no-case-declarations
        const status = action.key === 'submit' ? 'Waiting' : 'NotSubmitted';
        Modal.confirm({
          title: '提示',
          content: msg,
          onOk: async () => {
            await request(`/asset/assetBorrowApplication/changeStatus`, {
              method: 'PUT',
              baseURL: PROJECT_URLS.MAIN_PROJECT_API,
              data: {
                id: record.id,
                udf1: status,
              },
            });
            Message.success(action.key === 'submit' ? '提交成功，请等待审核' : '撤回成功');
            tableRef.value.loadData({});
          },
        });
        break;
      case 'approve':
      case 'reject':
        // eslint-disable-next-line no-case-declarations
        const remark = await prompt({
          title: '审核备注',
          placeholder: '请输入审核备注',
        });
        if (remark === undefined) {
          Message.error('请输入审核备注');
          return;
        }
        await request(`/asset/assetBorrowApplication/changeStatus`, {
          method: 'PUT',
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          data: {
            id: record.id,
            udf1: action.key === 'approve' ? 'Approved' : 'Rejected',
            udf2: remark,
          },
        });
        Message.success('操作成功');
        tableRef.value.loadData({});
        break;
      case 'outbound':
        Modal.confirm({
          title: '提示',
          content: '确认出库该资产吗？',
          onOk: async () => {
            await request(`/asset/assetBorrowApplication/changeStatus`, {
              method: 'PUT',
              baseURL: PROJECT_URLS.MAIN_PROJECT_API,
              data: {
                id: record.id,
                udf1: 'Borrowed',
              },
            });
            Message.success('出库成功');
            tableRef.value.loadData({});
          },
        });
        break;
      default:
    }
  };

  const handleShowReason = (reason: string, title: string) => {
    Modal.info({
      title,
      content: reason,
      hideCancel: true,
    });
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/asset/assetBorrowApplication');
  });

  defineExpose({
    loadData: async (params?: any) => {
      await tableRef.value.loadData(params);
    },
    handleShowEdit,
  });
</script>

<template>
  <a-card v-if="schema">
    <template #title>
      <div class="flex justify-between">
        <div class="flex-1">资产借用登记</div>
        <table-action
          v-if="tableRef"
          :schema="schema"
          :table="tableRef"
          component-size="mini"
          @row-action="handleRowAction"
        />
      </div>
    </template>
    <crud-table
      ref="tableRef"
      :schema="schema"
      class="mt-2"
      :default-query-params="{ sort: '-id' }"
      :visible-columns="columns"
      @row-action="handleRowAction"
    >
      <template #custom-column-applyComment="{ record }">
        <div class="text-sm cursor-pointer" @click="() => handleShowReason(record.applyComment, '申请备注')">
          {{ record.applyComment }}
        </div>
      </template>
      <template #custom-column-verifyComment="{ record }">
        <div class="text-sm cursor-pointer" @click="() => handleShowReason(record.verifyComment, '审核备注')">
          {{ record.verifyComment }}
        </div>
      </template>
    </crud-table>
    <modal-form
      v-model:visible="editVisible"
      v-model="currentEditRow"
      :schema="schema"
      modal-name="资产借用登记"
      @ok="() => tableRef.loadData({})"
      @close="() => handleCloseEdit()"
    />
  </a-card>
</template>

<style scoped lang="scss"></style>
