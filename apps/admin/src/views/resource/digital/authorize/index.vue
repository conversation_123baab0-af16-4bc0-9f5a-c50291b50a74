<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import openapi from '@repo/infrastructure/openapi';
  import { useRouter } from 'vue-router';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { getOssProcessor } from '@repo/infrastructure/upload';
  import { Message, Modal } from '@arco-design/web-vue';

  const authorizedOrgList = ref<any[]>([]);
  const currentOrg = ref<any>({});
  const { loading, setLoading } = useLoading();
  const router = useRouter();
  const listData = ref<any[]>([]);
  const thumbWidth = 133;
  const thumbHeight = 180;
  let ossProcessor;

  const handleGoAuthorize = (id?: number) => {
    router.push({
      path: '/resource/digital/authorize',
      query: {
        id,
      },
    });
  };

  const handleSelectOrg = async (org: any) => {
    setLoading(true);
    currentOrg.value = org;
    try {
      const { data } = await openapi.resource.getDigitalResourceAuthorizedCourse({
        orgId: org.id,
      });

      listData.value = data || [];
    } finally {
      setLoading(false);
    }
  };

  const handleCancelAuthorize = async (id: number) => {
    Modal.confirm({
      title: '取消授权',
      content: '确定要取消这个资源课程的使用授权吗？',
      onOk: async () => {
        setLoading(true);
        try {
          await openapi.resource.removeDigitalResourceCourseAuthorization({
            id,
          });
          Message.success('取消授权成功');
          await handleSelectOrg(currentOrg.value);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  onMounted(async () => {
    ossProcessor = getOssProcessor({
      saveTarget: 'AliyunOSS',
    });
    const { data } = await openapi.resource.getDigitalResourceAuthorizedOrg({});
    authorizedOrgList.value = data || [];
    if (authorizedOrgList.value.length > 0) {
      await handleSelectOrg(authorizedOrgList.value[0]);
    }
  });
</script>

<template>
  <div class="flex gap-2">
    <a-card v-if="authorizedOrgList.length" class="w-64" title="已授权单位">
      <template #extra>
        <a-button size="mini" type="text" @click="() => handleGoAuthorize()">
          <template #icon><IconPlus /></template>
          授权
        </a-button>
      </template>
      <ul>
        <li
          v-for="item in authorizedOrgList"
          :key="item.id"
          class="flex items-center gap-2 cursor-pointer hover:bg-slate-200 px-2 py-1 rounded"
          :class="{
            'bg-slate-100 ': item.id === currentOrg.id,
          }"
        >
          <div class="dot bg-blue-400 rounded-full w-2 h-2"></div>
          <div class="text-ellipsis">{{ item.name }}</div>
        </li>
      </ul>
    </a-card>
    <a-card class="flex-1">
      <template v-if="authorizedOrgList.length" #title> 课程资源授权 {{ currentOrg.name }} </template>
      <template v-if="authorizedOrgList.length" #extra>
        <a-space>
          <a-button type="primary" size="mini" @click="() => handleGoAuthorize(currentOrg.id)">
            <template #icon><IconPlus /></template>
            为 {{ currentOrg.name }} 新增课程授权
          </a-button>
          <a-button type="outline" size="mini" @click="router.back()">
            <template #icon><IconArrowLeft /></template>
            返回
          </a-button>
        </a-space>
      </template>
      <div v-if="!authorizedOrgList.length" class="flex flex-col py-20 items-center justify-center">
        <a-empty description="暂未授权任何单位使用您的数字数据" />
        <a-space class="flex mt-2">
          <a-button type="primary" @click="() => handleGoAuthorize()">新增授权</a-button>
          <a-button type="outline" @click="router.back()">取消</a-button>
        </a-space>
      </div>
      <a-spin v-else :loading="loading" class="w-full">
        <div class="course-list">
          <div v-for="(item, index) in listData as any[]" :key="index" class="course-item relative">
            <div class="thumb">
              <a-image
                alt="暂无封面"
                :width="thumbWidth"
                :height="thumbHeight"
                fit="contain"
                :src="ossProcessor.thumbUrl(item.resourceCourse.coverImage, thumbWidth, thumbHeight, 'fill')"
              />
            </div>
            <div class="title">
              <div class="name">{{ item.resourceCourse.category }}</div>
              <div class="description">
                {{ item.resourceCourse.grade }}
                {{ item.resourceCourse.period }}</div
              >
              <div class="buttons">
                <div v-if="!item.isCopy" class="flex flex-col gap-3">
                  <a-button size="mini" type="primary" disabled>至：{{ item.expiredAt }}</a-button>
                  <a-button
                    size="mini"
                    type="primary"
                    status="danger"
                    class="w-full"
                    @click="() => handleCancelAuthorize(item.id)"
                    >取消授权</a-button
                  >
                </div>
                <div v-else class="p-2 text-center bg-slate-600 text-white text-sm">
                  <div>已授权复制使用</div>
                </div>
              </div>
            </div>
            <div v-if="item.isCopy" class="text-sm bg-amber-700 text-white absolute top-0 right-0 px-2 py-1">
              <IconPaste />
              已复制
            </div>
            <div v-else class="text-sm bg-green-800 text-white absolute top-0 right-0 px-2 py-1">
              <IconCheck />
              已授权
            </div>
          </div>
        </div>
      </a-spin>
    </a-card>
  </div>
</template>

<style scoped lang="less">
  .course-list {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    @import '@/assets/style/courseItem.less';
  }
</style>
