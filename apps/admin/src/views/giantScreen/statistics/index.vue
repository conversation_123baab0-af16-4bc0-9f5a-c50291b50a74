<script setup lang="ts">
  import ScreenAdapter from '@/views/giantScreen/screenAdapter.vue';
  import giantScreenTheme from '@/views/giantScreen/chart-theme/giant-screen.json';
  import { defineAsyncComponent, onMounted, ref } from 'vue';
  import { PROJECT_URLS } from '@repo/env-config';

  import { G2 } from '@antv/g2plot';
  import { request } from '@repo/infrastructure/request';

  const { registerTheme } = G2;

  const cmp = defineAsyncComponent(() => import('@/views/giantScreen/templates/default/index.vue'));
  const ready = ref(false);
  const rawData = ref({});
  const width = ref(document.documentElement.clientWidth);
  const height = ref(document.documentElement.clientHeight);

  const loadData = async () => {
    const { data: res } = await request('/resourceCenter/screenStatistics', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        securityCode: '',
      },
    });

    width.value = res.screenConfig?.screenWidth || width.value;
    height.value = res.screenConfig?.screenHeight || height.value;
    rawData.value = res;
  };

  onMounted(async () => {
    registerTheme('giant-screen', giantScreenTheme);
    await loadData();
    ready.value = true;
  });
</script>

<template>
  <a-modal :visible="!ready" :closable="false" title="加载中" simple :esc-to-close="false" :mask-closable="false">
    <IconLoading />
    正在加载数据，请稍后...
    <template #footer>
      <div></div>
    </template>
  </a-modal>

  <screen-adapter>
    <component :is="cmp" v-if="ready" :raw-data="rawData" />
  </screen-adapter>
</template>

<style scoped lang="scss"></style>
