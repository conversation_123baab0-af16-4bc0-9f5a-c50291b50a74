<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { CrudTable } from '@repo/ui/components/table';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { SchemaHelper } from '@repo/infrastructure/schema';

  const props = defineProps({
    queryParams: {
      type: Object,
      default: () => ({}),
    },
  });

  const lists = ref([]);
  const loading = ref(false);

  // const visibleColumns = ['name', 'branchOffice', 'title', 'highestEducation', 'proTitle'];

  onMounted(async () => {
    loading.value = true;

    try {
      const { data } = await request('/resourceCenter/screenStatistics/teacherList', {
        method: 'GET',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          ...props.queryParams,
        },
      });
      lists.value = data;
    } finally {
      loading.value = false;
    }
  });
</script>

<template>
  <a-table :data="lists" :pagination="false" :loading="loading" size="mini">
    <template #columns>
      <a-table-column title="序号" width="80" align="center">
        <template #cell="{ rowIndex }">{{ rowIndex + 1 }}</template>
      </a-table-column>
      <a-table-column title="姓名" data-index="name" />
      <a-table-column title="学校" data-index="branchOffice.name" />
      <a-table-column title="职务" data-index="title" />
      <a-table-column title="最高学历" data-index="highestEducation" />
      <a-table-column title="职称" data-index="proTitle" />
      <!--      teachType teachGrade-->
      <a-table-column title="从教类别" data-index="teachType" />
      <a-table-column title="任教学段" data-index="teachGrade" />
    </template>
  </a-table>
</template>

<style scoped lang="scss"></style>
