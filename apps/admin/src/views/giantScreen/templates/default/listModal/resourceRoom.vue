<template>
  <div>
    <a-table :data="list" border size="small" :pagination="false">
      <template #columns>
        <a-table-column title="序号" width="80" align="center">
          <template #cell="{ rowIndex }">{{ rowIndex + 1 }}</template>
        </a-table-column>
        <a-table-column data-index="name" title="学校名称" width="250px" />
        <a-table-column data-index="nature" title="学校性质" />
        <a-table-column data-index="operationType" title="办学类型" />
        <a-table-column data-index="type" title="学校类型" />
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
  import { PROJECT_URLS } from '@repo/env-config';
  import { onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';

  const emit = defineEmits(['loading']);

  const list = ref([]);

  const loadList = async () => {
    emit('loading', true);
    try {
      const { data: res } = await request('/resourceCenter/fusionSchool', {
        params: {
          page: 1,
          pageSize: 999,
          nature: '@普通教育学校,幼儿园,职业教育学校',
          hasResourceRoom: true,
        },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      list.value = res.items;
    } finally {
      emit('loading', false);
    }
  };

  onMounted(async () => {
    await loadList();
  });
</script>
