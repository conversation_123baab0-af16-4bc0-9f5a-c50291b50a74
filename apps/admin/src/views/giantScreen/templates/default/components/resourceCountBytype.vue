<template>
  <div id="resourceCountByTypePie" style="height: 100%; width: 100%"></div>
</template>

<script>
  import { Pie } from '@antv/g2plot';
  import giantScreen from '@/views/giantScreen/chart-theme/giant-screen.json';

  export default {
    name: 'ResourceCountBytype',
    props: {
      rawData: {
        type: Array,
      },
    },
    mounted() {
      const piePlot = new Pie('resourceCountByTypePie', {
        appendPadding: 10,
        data: this.rawData,
        angleField: 'num',
        colorField: 'label',
        radius: 1,
        innerRadius: 0.7,
        theme: {
          ...giantScreen,
          colors10: ['#1AAF8B', '#fe5c5c', '#2FB8FC', '#4435FF', '#FF5CA2', '#BBE800', '#FE8A26'],
        },
        label: {
          type: 'spider',
          formatter: (item) => {
            return `${item.label}\n${item.num}个`;
          },
          style: {
            fontSize: 14,
            color: '#ffffff',
          },
        },
        legend: {
          visible: true,
          title: {
            style: {
              color: '#ffffff',
            },
          },
        },
        statistic: {
          title: false,
          content: false,
        },
      });
      piePlot.render();
    },
  };
</script>
