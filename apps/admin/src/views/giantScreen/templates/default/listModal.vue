<template>
  <a-modal
    v-if="dialogVisible"
    v-model:visible="dialogVisible"
    :title="title"
    :width="width"
    modal-append-to-body
    append-to-body
  >
    <a-spin class="w-full" :loading="loading">
      <component :is="contentComponent" v-if="contentComponent" v-model:raw-data="data" @loading="handleLoading" />
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
  import { computed, defineAsyncComponent, onMounted, ref } from 'vue';

  const props = defineProps<{
    title: string;
    width: string;
    value: boolean;
    modalConfig: Record<string, any>;
    rawData: Record<string, any>;
  }>();

  const emit = defineEmits(['update:modelValue', 'update:modalConfig', 'update:rawData']);
  const loading = ref(false);

  const dialogVisible = computed({
    get: () => props.value,
    set: (val) => emit('update:modelValue', val),
  });

  const contentComponent = computed({
    get: () => {
      if (!props.modalConfig.type) {
        return null;
      }
      return defineAsyncComponent(() => import(`./listModal/${props.modalConfig.type}.vue`));
    },
    set: (val) => {
      emit('update:modalConfig', val);
    },
  });

  const data = computed({
    get: () => props.rawData,
    set: (val) => emit('update:rawData', val),
  });

  const handleLoading = (val: boolean) => {
    loading.value = val;
  };


</script>

<style lang="scss">
  .el-dialog__wrapper {
    background: rgba(0, 0, 0, 0.6);
  }
  .el-dialog__body {
    padding-top: 10px;
  }
  .el-dialog {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    .el-table {
      background: rgba(255, 255, 255, 0.8);
      border-radius: 8px;
      tr,
      td {
        background: transparent;
      }
    }

    .el-dialog__headerbtn .el-dialog__close {
      color: #000;
    }
    .el-dialog__title {
      color: #141d37;
    }
  }
</style>
