<script lang="ts" setup>
  import { useUserStore, useUserMenuStore } from '@repo/infrastructure/store';
  import { computed, onMounted, ref, toRaw } from 'vue';
  import NavBarRight from '@/common/layout/components/navBarRight.vue';
  import { useRouter } from 'vue-router';
  import { menuService } from '@repo/infrastructure/data';
  import useCommonStore from '@repo/infrastructure/utils/store';

  const userStore = useUserStore();
  const loading = ref<any>(true);
  const router = useRouter();

  const menuStore = useUserMenuStore();
  const menus = menuStore.getUserMenus('admin');

  const handleAppClick = (app: menuService.UserMenu) => {
    router.push(`${app.link}`);
  };

  const siteTitle = computed(() => {
    return userStore?.userInfo?.branchOffice?.name;
  });

  const teacherStore = useCommonStore({
    api: '/org/companyUser/allTeachers',
  });

  const getIconStyle = (label: string): any => {
    let item = {};
    switch (label) {
      case 'center':
        item = {
          color: 'from-blue-300/60 to-blue-400/30',
          label: '资源中心',
        };
        break;
      case 'specialSchool':
        item = {
          color: 'from-purple-300/60 to-purple-400/30',
          label: '特殊教育',
        };
        break;
      case 'kindergarten':
        item = {
          color: 'from-green-300/60 to-green-400/30',
          label: '学前教育',
        };
        break;
      case 'compulsoryEducation':
        item = {
          color: 'from-cyan-300/60 to-cyan-400/30',
          label: '普通教育',
        };
        break;
      case 'vocationalEducation':
        item = {
          color: 'from-amber-300/60 to-amber-400/30',
          label: '职业教育',
        };
        break;
      case 'institution':
        item = {
          color: 'from-indigo-300/60 to-indigo-400/30',
          label: '机构单位',
        };
        break;
      case 'specialEduCommittee':
        item = {
          color: 'from-rose-300/60 to-rose-400/30',
          label: '专委会',
        };
        break;
      case 'system':
        item = {
          color: 'from-blue-300/60 to-blue-400/30',
        };
        break;
      default:
        item = {
          color: 'from-blue-300/60 to-blue-400/30',
        };
        break;
    }
    return item;
  };

  const getIconPath = (label: string): string => {
    let iconName = 'generalEducation.svg';
    switch (label) {
      case 'center':
        iconName = 'resourceCenter.svg';
        break;
      case 'specialSchool':
        iconName = 'specialEducation.svg';
        break;
      case 'kindergarten':
        iconName = 'preEducation.svg';
        break;
      case 'compulsoryEducation':
        iconName = 'generalEducation.svg';
        break;
      case 'vocationalEducation':
        iconName = 'vocationalEducation.svg';
        break;
      case 'institution':
        iconName = 'institutionalUnit.svg';
        break;
      case 'specialEduCommittee':
        iconName = 'specialCommittee.svg';
        break;
      case 'system':
        iconName = 'sysSetting.svg';
        break;
      case 'sendEducation':
        iconName = 'sendEducation.svg';
        break;
      default:
        break;
    }
    return new URL(`../../assets/images/companyIcons/${iconName}`, import.meta.url).href;
  };

  const iconClass = {
    iconBox:
      'group flex flex-col items-center transform transition-all cursor-pointer duration-300 hover:scale-125 hover:-translate-y-2 hover:z-10',
    card: 'flex flex-col items-center justify-center w-36 h-[178px] bg-white/25 backdrop-blur-md rounded-[7px] shadow-[rgba(0,0,0,0.15)_0px_5px_15px_0px] hover:shadow-[rgba(0,0,0,0.3)_0px_15px_30px_-5px] transition-all duration-300 hover:bg-white/35 border border-white/40',
    mask: 'relative mb-3 transform transition-all duration-300 group-hover:scale-110',
    ring: 'absolute inset-0 bg-gradient-to-br rounded-full blur-md opacity-40 group-hover:opacity-80 transition-opacity duration-300',
    icon: 'relative flex items-center justify-center w-16 h-16 bg-white rounded-full transition-all duration-300 backdrop-blur-xl border border-white/30 shadow-[0_0_10px_rgba(255,255,255,0.5)]',
    label:
      'text-white text-base font-medium text-center px-2 drop-shadow-sm transform transition-all duration-300 group-hover:scale-110 group-hover:drop-shadow-[0_0_4px_rgba(255,255,255,0.3)]',
  };
  onMounted(async () => {
    loading.value = false;
    if (menus.length === 1) {
      handleAppClick(menus[0]);
    }

    await teacherStore.getMap();
  });
</script>

<template>
  <a-spin :loading="loading" class="landing-wrapper">
    <header class="header">
      <div class="title">
        <!--{{ userInfo?.company?.name }}-->
      </div>
      <nav-bar-right>
        <template #prepend></template>
      </nav-bar-right>
    </header>
    <section class="body">
      <h1 class="site-title mt-20"> {{ siteTitle }} </h1>
      <div v-if="false" class="apps">
        <div
          v-for="(app, index) in menus.filter((item) => item.children?.length)"
          :key="index"
          class="app-item"
          @click="() => handleAppClick(app)"
        >
          <a-badge :count="Number(toRaw(app.badgeCount)) || 0">
            <div class="icon">
              <div v-if="app.iconText" class="icon-text">
                {{ app.iconText }}
              </div>
              <component :is="app.icon" v-else />
            </div>
          </a-badge>
          <div class="text">{{ app.label }}</div>
        </div>
      </div>
      <div class="flex justify-center space-x-8 absolute top-1/3">
        <div
          v-for="(app, index) in menus.filter((item) => item.children?.length && item.key !== 'system')"
          :key="index"
          :class="iconClass.iconBox"
          @click="() => handleAppClick(app)"
        >
          <div :class="iconClass.card">
            <div :class="iconClass.mask">
              <div :class="(iconClass.ring, getIconStyle(app.key).color)"></div>
              <div :class="iconClass.icon">
                <div class="w-14 h-14 flex items-center justify-center scale-110">
                  <img
                    :src="getIconPath(app.key)"
                    alt=""
                    class="w-[30px] h-[30px] transform transition-all duration-300 group-hover:scale-110"
                  />
                </div>
              </div>
            </div>
            <span :class="iconClass.label">{{ getIconStyle(app.key).label || app.label }}</span>
          </div>
        </div>
      </div>
    </section>
    <svg style="width: 1px; height: 1px; overflow: hidden">
      <defs>
        <linearGradient
          id="icon-gradient"
          gradientTransform="matrix(-0.5, -0.99, 0.99, -0.5, 642.56, 510.976)"
          gradientUnits="userSpaceOnUse"
          x1="0"
          x2="100%"
          y1="0"
          y2="0"
        >
          <stop offset="0.1" stop-color="#165dff" stop-opacity="1"></stop>
          <stop offset="1" stop-color="#165dff" stop-opacity="1"></stop>
        </linearGradient>
      </defs>
    </svg>
  </a-spin>
</template>

<style lang="less" scoped>
  @import '@/assets/style/apps.less';

  .landing-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    color: #fff;

    .header {
      height: 60px;
      padding: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      text-shadow: 0 0 5px rgba(@textShadowColor, 1);

      .actions {
        display: flex;
        align-items: center;
      }
    }

    .footer {
      height: 100px;
      padding-top: 80px;
    }

    .body {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 50px;

      .site-title {
        font-size: 32px;
        font-weight: bold;
        text-shadow: 0 0 5px rgba(@textShadowColor, 0.8);
      }
    }
  }

  .icon-text {
    color: #165dff;
    font-weight: 800;
    font-size: 46px;
  }
</style>

<style>
  body {
    overflow-x: hidden;
  }

  #app {
    min-width: 100%;
    min-height: 100%;
  }

  body {
    background-image: url('/assets/global-bg.svg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
</style>
