import { useRouter } from 'vue-router';
import { Message, Modal } from '@arco-design/web-vue';

import { useUserStore } from '@repo/infrastructure/store';

export default function useUser() {
  const router = useRouter();
  const userStore = useUserStore();
  const logout = async (logoutTo?: string) => {
    Modal.confirm({
      title: '请确认',
      content: '确定要退出登录吗？',
      onOk: async () => {
        await userStore.logout();
        const currentRoute = router.currentRoute.value;
        Message.success('登出成功');
        router.push({
          name: logoutTo && true ? logoutTo : 'login',
          query: {
            ...router.currentRoute.value.query,
            redirect: currentRoute.name as string,
          },
        });
      },
    });
  };
  return {
    logout,
  };
}
