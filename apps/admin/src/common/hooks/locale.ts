import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { Message } from '@arco-design/web-vue';
import constants from '@repo/infrastructure/constants';
import { defaultLocale } from '@repo/infrastructure/locale';
import { extend } from 'lodash';

export default function useLocale() {
  const i18 = useI18n();
  const currentLocale = computed(() => {
    return i18.locale.value;
  });
  const changeLocale = (value: string, messages: any) => {
    messages = extend(messages, i18.getLocaleMessage(value)); // load locale message
    i18.setLocaleMessage(value, messages);
    if (i18.locale.value === value) {
      return;
    }
    i18.locale.value = value;
    localStorage.setItem(Constants.LOCALE_CACHE_KEY, value);
    Message.success(i18.t('navbar.action.locale'));
  };
  return {
    currentLocale,
    changeLocale,
    defaultLocale,
    i18,
  };
}
