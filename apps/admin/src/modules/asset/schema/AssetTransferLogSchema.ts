import { CustomSchema } from '@repo/infrastructure/types';

const AssetTransferLogSchema: CustomSchema = {
  api: '/asset/assetTransferLog',
  modalEdit: true,
  quickSearchProps: {
    enabled: true,
    placeholder: '按编码/名称/类型搜索',
    fields: ['assetInfo.name', 'assetInfo.symbol', 'assetInfo.propertyType', 'cbName'],
  },
  listViewProps: {
    searchType: 'column',
  },
  fieldsMap: {
    createdBy: {
      label: '操作人',
      listProps: {
        visible: true,
      },
    },
    beforeQuantity: {
      listProps: {
        columnWidth: 120,
      },
    },
    afterQuantity: {
      listProps: {
        columnWidth: 120,
      },
    },
    quantity: {
      listProps: {
        columnWidth: 120,
      },
    },
    operator: {
      listProps: {
        columnWidth: 120,
      },
    },
  },
};

export default { AssetTransferLogSchema };
