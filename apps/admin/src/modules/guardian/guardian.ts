const weappVisibleInOptions: any[] = [
  { label: '家长端', value: 'guardian' },
  { label: '教师端', value: 'teacher' },
];

const weappVisibleInDisplayProps = {
  toDisplay: (value: string[]) => {
    return value
      .map((v) => {
        const option = weappVisibleInOptions.find((o) => o.value === v);
        return option ? option.label : '';
      })
      .join('、');
  },
};

export { weappVisibleInOptions, weappVisibleInDisplayProps };
