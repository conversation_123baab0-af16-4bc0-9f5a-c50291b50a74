import { CustomSchema } from '@repo/infrastructure/types';
import { weappVisibleInDisplayProps, weappVisibleInOptions } from '../guardian';

const guardianWeappCarouselSchema: CustomSchema = {
  api: '/guardian/weappCarousel',
  rowActions: [
    {
      key: 'goEdit',
      label: '编辑',
      expose: true
    },
    {
      key: 'goDelete',
      expose: true,
      label: '删除'
    },
    {
      key: 'delete',
      visible: false
    },
    {
      key: 'view',
      visible: false,
    },
    {
      key: 'edit',
      visible: false
    }
  ],
  fieldsMap: {
    image: {
      inputWidget: 'coverUploadInput',
    },
    visibleIn: {
      inputWidget: 'checkboxInput',
      inputWidgetProps: {
        options: weappVisibleInOptions,
      },
      displayProps: {
        ...weappVisibleInDisplayProps,
      },
    },
  },
};

export default { guardianWeappCarouselSchema };
