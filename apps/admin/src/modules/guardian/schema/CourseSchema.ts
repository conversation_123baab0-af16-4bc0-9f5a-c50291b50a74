import { CustomSchema } from '@repo/infrastructure/types';
import { PROJECT_URLS } from '@repo/env-config';

const courseSchema: CustomSchema = {
  api: '/guardian/course',
  fieldsMap: {
    catalogId: {
      dataType: 'Foreign',
      foreignField: {
        api: '/guardian/courseCatalog',
        apiBaseUrl: PROJECT_URLS.GO_PROJECT_API,
        preload: true,
      },
    },
    coverImage: {
      inputWidget: 'coverUploadInput',
    },
    description: {
      inputWidget: 'textareaInput',
    },
  },
};

export default { courseSchema };
