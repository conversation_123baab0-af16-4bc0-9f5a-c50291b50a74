import { CustomSchema } from '@repo/infrastructure/types';
import { Message } from '@arco-design/web-vue';
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';
import { useUserStore } from '@repo/infrastructure/store';

const conferenceSchema: CustomSchema = {
  api: '/resourceCenter/conference',
  listViewProps: {
    rowActionWidth: 180,
  },
  rowActions: [
    {
      key: 'meetingCheckinCode',
      label: '签到码',
      handler: () => {},
      expose: true,
    },
  ],
  detailViewProps: {
    columns: 2,
    fieldsGrouping: [
      {
        label: '会议信息',
        fields: ['subject', 'address', 'dateRange', 'signInDateRange', 'signOutDateRange', 'coverImageId', 'published'],
      },
      {
        colSpan: 24,
        label: '会议内容',
        fields: ['content'],
      },
    ],
  },
  formViewProps: {
    fullscreen: true,
    colSpan: 8,
    fieldsGrouping: [
      {
        label: '会议信息',
        fields: ['subject', 'address', 'dateRange', 'signInDateRange', 'signOutDateRange', 'coverImageId', 'published'],
      },
      {
        colSpan: 24,
        label: '会议内容',
        fields: ['content'],
      },
    ],
  },
  fieldsMap: {
    content: {
      inputWidget: 'richInput',
    },
    coverImageId: {
      inputWidget: 'coverUploadInput',
      displayProps: {
        component: 'ImagePreviewDisplay',
      },
    },

    address: {
      key: 'address',
      label: '会议地址',
      inputWidget: 'textInput',
    },
    coordinate: {
      inputWidget: 'coordinateSelectInput',
      displayProps: {
        toDisplay: (value: any, record: any) => {
          return value?.formattedAddress || record?.address;
        },
      },
      visibleInForm: false,
      visibleInTable: false,
    },

    signDistance: {
      displayProps: {
        toDisplay: (value: any) => {
          return value ? `${value}米` : '-';
        },
      },
      inputWidgetProps: {
        suffix: '米',
      },
      visibleInForm: false,
    },
    dateRange: {
      inputWidget: 'dateRangeInput',
      inputWidgetProps: {
        showTime: true,
      },
      displayProps: {
        toDisplay: (value: any) => {
          return value ? value.join(' 至 ') : '-';
        },
      },
    },
    signInDateRange: {
      inputWidget: 'dateRangeInput',
      inputWidgetProps: {
        showTime: true,
      },
      displayProps: {
        toDisplay: (value: any) => {
          return value ? value.join(' 至 ') : '-';
        },
      },
    },
    signOutDateRange: {
      inputWidget: 'dateRangeInput',
      inputWidgetProps: {
        showTime: true,
      },
      displayProps: {
        toDisplay: (value: any) => {
          return value ? value.join(' 至 ') : '-';
        },
      },
    },
    published: {
      inputWidgetProps: {
        colSpan: 4,
      },
      displayProps: {
        detailSpan: 2,
        booleanDisplayOptions: {
          mode: 'switch',
          allowUpdate: true,
          confirmMessage: (val) => `确定要${val ? '发布' : '取消发布'}这个会议吗？`,
          handler: async (val, record) => {
            Message.info(`正在${val ? '发布' : '撤销发布'}... 请稍后`);
            try {
              const { data } = await request(`/resourceCenter/conference/${record.id}`, {
                data: {
                  ...record,
                  published: val,
                },
                method: 'PUT',
                baseURL: PROJECT_URLS.MAIN_PROJECT_API,
              });

              Message.clear();
              Message.success(`${val ? '发布' : '撤销发布'}成功`);

              return val;
            } catch (e) {
              return false;
            }
          },
        },
      },
    },
  },
};

export default {
  conferenceSchema,
};
