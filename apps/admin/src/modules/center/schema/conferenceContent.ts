import { CustomSchema } from '@repo/infrastructure/types';
import { PROJECT_URLS } from '@repo/env-config';

const conferenceContent: CustomSchema = {
  api: '/resourceCenter/conferenceContent',
  fieldsMap: {
    conference: {
      label: '111',
      labelField: 'subject',
      valueField: 'id',
      foreignField: {
        api: '/resourceCenter/conferenceRecord',
        apiBaseUrl: PROJECT_URLS.GO_PROJECT_API,
        preload: true,
      },
    },
  },
};

export default { conferenceContent };
