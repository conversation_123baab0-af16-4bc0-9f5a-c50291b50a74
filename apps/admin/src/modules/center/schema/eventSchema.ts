import { CustomSchema } from '@repo/infrastructure/types';
import { PROJECT_URLS } from '@repo/env-config';
import { useConfigureStore } from '@repo/infrastructure/store';

const EventSchema: CustomSchema = {
  api: '/resourceCenter/resourceCenterEvent',
  baseURL: PROJECT_URLS.MAIN_PROJECT_API,
  fieldsMap: {
    tag: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        allowSearch: true,
        allowUpdate: true,
        allowCreate: true,
        options: ['工作会', '教学研讨', '培训', '论文评比', '优质课比赛', '对外交流', '文件', '其他'],
      },
    },
    eventLocation: {
      displayProps: {
        detailSpan: 2,
      },
    },
    subject: {
      displayProps: {
        detailSpan: 2,
      },
    },
    targetContent: {
      inputWidget: 'textareaInput',
      displayProps: {
        detailSpan: 2,
      },
    },
    participants: {
      inputWidget: 'textareaInput',
      displayProps: {
        detailSpan: 2,
      },
    },
    result: {
      inputWidget: 'textareaInput',
      displayProps: {
        detailSpan: 2,
      },
    },
    record: {
      inputWidget: 'richInput',
      displayProps: {
        detailSpan: 2,
      },
    },
    attachments: {
      inputWidget: 'uploadInput',
      inputWidgetProps: {
        limit: 8,
        showFileList: true,
        multiple: true,
      },
      displayProps: {
        component: 'AttachmentsPreviewDisplay',
        detailSpan: 2,
      },
    },
  },
};

export default { ConferenceSchema: EventSchema };
