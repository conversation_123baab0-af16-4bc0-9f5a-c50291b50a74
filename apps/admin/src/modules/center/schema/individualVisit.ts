import { CustomSchema } from '@repo/infrastructure/types';
import useCommonStore from '@repo/infrastructure/utils/store';
import { PROJECT_URLS } from '@repo/env-config';
import { request } from '@repo/infrastructure/request';
import { defineAsyncComponent } from 'vue';

const individualVisitSchema: CustomSchema = {
  api: '/resourceCenter/individualVisit',
  detailViewProps: {
    columns: 1,
  },
  listViewProps: {
    rowActionWidth: 200,
  },
  rowActions: [{ key: 'followUp', label: '回访记录', handler() {}, expose: true }],
  formViewProps: {
    fullscreen: true,
    fieldsGrouping: [
      {
        label: '来访信息',
        fields: ['fusionSchool', 'fusionSchoolName', 'student', 'studentName'],
      },
      {
        label: '接访信息',
        fields: ['receptionDate', 'receptionPerson', 'participatePerson', 'mainReceptionPerson', 'recordPerson'],
      },
      {
        label: '详情',
        colSpan: 24,
        fields: ['basicInformation', 'archives', 'mainProblem', 'recommendation', 'tracking'],
      },
    ],
  },
  fieldsMap: {
    fusionSchool: {
      valueType: 'Object',
      dataType: 'Object',
      foreignField: {},
      inputWidgetProps: {
        allowSearch: true,
        allowClear: true,
        async getOptions() {
          const store = useCommonStore({
            api: '/resourceCenter/fusionSchool',
          });

          return store.getOptions();
        },
        valueType: (value, computedOptions) => {
          if (!value) {
            return undefined;
          }
          const school = computedOptions.find((item) => item.raw?.value === value);
          return {
            id: value,
            ...school?.raw,
          };
        },
      },
    },
    fusionSchoolName: {
      inputWidgetProps: {
        queryDepends: {
          onFusionSchoolChange(val, raw, formData) {
            formData.value.fusionSchoolName = val?.label;
          },
        },
      },
    },
    student: {
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/components/student/studentDetailButton.vue')),
      },
      dataType: 'Integer',
      inputWidgetProps: {
        disabled: true,
        allowSearch: false,
        allowClear: true,
        valueType: (value, computedOptions) => {
          if (!value) {
            return undefined;
          }
          const dataItem = computedOptions.find((item) => item.raw?.value === value);
          return {
            id: value,
            ...dataItem?.raw,
          };
        },
        queryDepends: {
          async onFusionSchoolChange(val, raw, formData) {
            const id = val?.value || val?.id;
            if (!id) {
              return { inputWidgetProps: { options: [] } };
            }
            const { data } = await request('/resourceRoom/student', {
              params: {
                fusionSchool: id,
              },
              baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            });

            return {
              inputWidgetProps: {
                disabled: false,
                options: data?.items?.map((item) => {
                  return {
                    value: item.id,
                    label: item.name,
                    raw: item,
                  };
                }),
              },
            };
          },
        },
      },
    },
    studentName: {
      inputWidgetProps: {
        queryDepends: {
          onStudentChange(val, raw, formData) {
            const name = val?.label || val?.name;
            formData.value.studentName = name;
          },
        },
      },
    },
    archives: {
      inputWidget: 'uploadInput',
      inputWidgetProps: {
        colSpan: 24,
        limit: 8,
        showFileList: true,
        multiple: true,
      },
      displayProps: {
        component: 'AttachmentsPreviewDisplay',
        detailSpan: 2,
      },
    },
    basicInformation: {
      inputWidget: 'textareaInput',
      inputWidgetProps: {
        colSpan: 24,
      },
    },
    mainProblem: {
      inputWidget: 'textareaInput',
      inputWidgetProps: {
        colSpan: 24,
      },
    },
    recommendation: {
      inputWidget: 'textareaInput',
      inputWidgetProps: {
        colSpan: 24,
      },
    },
    tracking: {
      inputWidget: 'textareaInput',
      inputWidgetProps: {
        colSpan: 24,
      },
    },
  },
};

export default { individualVisitSchema };
