import { CustomSchema } from '@repo/infrastructure/types';
import { PROJECT_URLS } from '@repo/env-config';
import { useUserStore } from '@repo/infrastructure/store';
import { request } from '@repo/infrastructure/request';
import { Message } from '@arco-design/web-vue';

const rawPermissions = [
  'specialSchool:teachingSubject:',
  'kindergarten:teachingSubject:',
  'compulsoryEducation:teachingSubject:',
  'vocationalEducation:teachingSubject:',
  'institution:teachingSubject:',
];

const schoolCourse: CustomSchema = {
  api: '/teacher/schoolCourse',
  baseURL: PROJECT_URLS.MAIN_PROJECT_API,
  permissions: {
    create: rawPermissions.map((item) => `${item}create`),
    update: rawPermissions.map((item) => `${item}edit`),
    delete: rawPermissions.map((item) => `${item}delete`),
    import: rawPermissions.map((item) => `${item}import`),
    export: rawPermissions.map((item) => `${item}export`),
  },
  fieldsMap: {
    // groupTeachers: {
    //   inputWidget: defineAsyncComponent(() => import('@/views/manage/components/school/groupTeacherSelect.vue')),
    //   displayProps: {
    //     toDisplay(value) {
    //       return value?.map((item) => item.name).join('、');
    //     },
    //   },
    // },
    orgNature: {
      key: 'orgNature',
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    required: {
      key: 'required',
      inputWidget: 'switchInput',
      listProps: {
        columnWidth: 120,
      },
      displayProps: {
        booleanDisplayOptions: {
          mode: 'switch',
          allowUpdate: () => {
            return true;
          },
          confirmMessage(value: boolean, record: any) {
            if (value) {
              return `确认将【${record.name}】改为必填学科？`;
            }
            return `确认将【${record.name}】改为非必填学科？`;
          },
          async handler(value: boolean, record: any) {
            try {
              await request(`/teacher/schoolCourse/${record.id}`, {
                method: 'PUT',
                data: {
                  ...record,
                  required: value,
                },
                baseURL: PROJECT_URLS.MAIN_PROJECT_API,
              });
              Message.success('操作成功');
              return value;
            } catch (error) {
              Message.error('操作失败');
              return !value;
            }
          },
        },
      },
    },
  },
};

export default { schoolCourse };
