import { CustomSchema, DataTypes } from '@repo/infrastructure/types';
import { COURSE_CATEGORIES, COURSE_GRADES, COURSE_PERIODS } from '@repo/infrastructure/constants';

const resourceCourseSchema: CustomSchema = {
  api: '/resource/course',
  fieldsMap: {
    fromOrgId: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
    },
    category: {
      valueType: DataTypes.Enum.name,
      inputWidgetProps: {
        options: COURSE_CATEGORIES,
      },
    },
    grade: {
      valueType: DataTypes.Enum.name,
      inputWidgetProps: {
        options: COURSE_GRADES,
      },
    },
    period: {
      valueType: DataTypes.Enum.name,
      inputWidgetProps: {
        options: COURSE_PERIODS,
      },
    },
    description: {
      inputWidget: 'textareaInput',
    },
    coverImage: {
      visibleInForm: false,
      inputWidget: 'uploadInput',
    },
  },

  listViewProps: {
    showIndex: false,
    visibleColumns: ['name', 'localeKey', 'sort'],
  },
};

export default { courseSchema: resourceCourseSchema };
