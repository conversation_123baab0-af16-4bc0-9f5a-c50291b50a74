import { CustomSchema } from '@repo/infrastructure/types';

const rawPermissions = [
  'specialSchool:assessment:criterionCategory:',
  'kindergarten:assessment:criterionCategory:',
  'compulsoryEducation:assessment:criterionCategory:',
  'vocationalEducation:assessment:criterionCategory:',
  'institution:assessment:criterionCategory:',
];

const updatePermissions = rawPermissions.map((item) => `${item}edit`);

const criterionCategory: CustomSchema = {
  api: '/evaluation/evaluationCategory',
  permissions: {
    create: rawPermissions.map((item) => `${item}create`),
    update: updatePermissions,
    delete: rawPermissions.map((item) => `${item}delete`),
  },
  fieldsMap: {
    orgNature: {
      key: 'orgNature',
      visibleInForm: false,
    },
  },
};

export default {
  criterionCategory,
};
