import { CustomSchema } from '@repo/infrastructure/types';
import { Message } from '@arco-design/web-vue';
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';

const PaperCompetitionSchema: CustomSchema = {
  api: '/paper/paperCompetition',
  modalEdit: true,
  quickSearchProps: {
    enabled: true,
    placeholder: '按主题/类型搜索',
    fields: ['name', 'type'],
  },
  listViewProps: {
    searchType: 'column',
  },
  rowActions: [
    {
      key: 'experts',
      label: '评审专家',
      handler() {},
    },
    {
      key: 'awards',
      label: '奖项设置',
      handler() {},
    },
    {
      key: 'criterion',
      label: '评分标准',
      handler() {},
    },
    {
      key: 'assessmentLink',
      label: '获取评审链接',
      handler() {},
    },
    {
      key: 'entries',
      label: '参赛作品',
      expose: true,
      handler() {},
    },
    {
      key: 'printDesign',
      label: '证书模板',
      handler() {},
    },
  ],
  fieldsMap: {
    coverImage: {
      inputWidget: 'coverUploadInput',
      displayProps: {
        detailSpan: 2,
        component: 'ImagePreviewDisplay',
      },
    },
    type: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: ['课例类', '论文类'],
        allowClear: true,
        colSpan: 6,
      },
      listProps: {
        columnWidth: 150,
      },
    },
    name: {
      unique: true,
      displayProps: {
        detailSpan: 2,
      },
    },
    requirements: {
      inputWidget: 'richInput',
      displayProps: {
        detailSpan: 2,
      },
    },
    attachments: {
      inputWidget: 'uploadInput',
      displayProps: {
        detailSpan: 2,
        component: 'AttachmentsPreviewDisplay',
      },
    },
    dateRange: {
      inputWidget: 'dateRangeInput',
      inputWidgetProps: {
        colSpan: 12,
        format: 'YYYY-MM-DD HH:mm:ss',
        showTime: true,
      },
      displayProps: {
        toDisplay(value) {
          if (value?.length === 2) {
            return `${value[0]} 至 ${value[1]}`;
          }
          return '';
        },
      },
    },
    assessmentDateRange: {
      inputWidget: 'dateRangeInput',
      inputWidgetProps: {
        colSpan: 12,
        format: 'YYYY-MM-DD HH:mm:ss',
        showTime: true,
      },
      displayProps: {
        toDisplay(value) {
          if (value?.length === 2) {
            return `${value[0]} 至 ${value[1]}`;
          }
          return '';
        },
      },
    },
    published: {
      inputWidgetProps: {
        colSpan: 4,
      },
      displayProps: {
        detailSpan: 2,
        booleanDisplayOptions: {
          mode: 'switch',
          allowUpdate: true,
          confirmMessage: (val) => `确定要${val ? '发布' : '取消发布'}这个赛课吗？`,
          handler: async (val, record) => {
            Message.info(`正在${val ? '发布' : '取消发布'}... 请稍后`);
            try {
              const { data } = await request(`/paper/paperCompetition/${record.id}`, {
                data: {
                  ...record,
                  published: val,
                },
                method: 'PUT',
                baseURL: PROJECT_URLS.MAIN_PROJECT_API,
              });

              Message.clear();
              Message.success(`${val ? '发布' : '取消发布'}成功`);

              return val;
            } catch (e) {
              return false;
            }
          },
        },
      },
    },
    awards: {
      visibleInForm: false,
      visibleInTable: false,
      displayProps: {
        detailSpan: 2,
        toDisplay(value: any[]) {
          if (!value?.length) {
            return '暂未设置奖项';
          }
          const res = [];
          // eslint-disable-next-line no-restricted-syntax
          for (const item of value) {
            res.push(`${item.name}(${item.count}个)`);
          }
          return res.join('、');
        },
      },
    },
    criterionList: {
      visibleInForm: false,
      visibleInTable: false,
      displayProps: {
        detailSpan: 2,
        component: 'ListTableDisplay',
        columns: [
          { key: 'criterion', label: '评分标准' },
          { key: 'recommendScore', label: '推荐分值' },
          { key: 'sort', label: '排序' },
        ],
      },
    },
    experts: {
      visibleInForm: false,
      visibleInTable: false,
      displayProps: {
        detailSpan: 2,
        component: 'ListTableDisplay',
        columns: [
          { key: 'name', label: '专家姓名' },
          { key: 'mobile', label: '手机号码' },
          { key: 'email', label: '邮箱' },
          { key: 'organization', label: '单位' },
          { key: 'title', label: '职务' },
          { key: 'remark', label: '备注' },
        ],
      },
    },
  },
};

export default { PaperCompetitionSchema };
