<script setup lang="ts">
  import { CrudTable } from '@repo/ui/components/table';
  import { CrudForm } from '@repo/ui/components/form';
  import { computed, reactive, ref, toRaw } from 'vue';
  import { useSchemaStore } from '@repo/infrastructure/store';
  import { Schema, TableRowAction } from '@repo/infrastructure/types';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { Message } from '@arco-design/web-vue';
  import { translate } from '@repo/infrastructure/utils';

  let editData = reactive<any>({});

  const schemaStore = useSchemaStore();
  const editVisible = ref<any>(false);
  const schema: Schema = SchemaHelper.getInstance(schemaStore.getSchemaByApi('/org/department'));
  const editTitle = computed(() => {
    return editData.id ? translate('common.edit') : translate('common.add');
  });

  const tableRef = ref<InstanceType<any>>();

  const handleRefresh = () => {
    tableRef.value!.loadData();
  };
  const handleShowAdd = (editRaw?: any) => {
    if (editRaw) {
      editData = reactive(editRaw);
    } else {
      editData = reactive({});
    }
    editVisible.value = true;
  };
  const handleSubmit = async () => {
    // await create(editData);
    Message.success(translate('common.success'));
    handleRefresh();
    editVisible.value = false;
  };

  const handleCancel = () => {
    editVisible.value = false;
  };

  const handleRowAction = (action: TableRowAction, record: Record<string, any>) => {
    if (action.key === 'edit') {
      handleShowAdd(toRaw(record));
    }
  };
</script>

<template>
  <div>
    <a-card>
      <div class="flex-container space-between">
        <div>
          <a-button type="primary" @click="() => handleShowAdd()">
            <template #icon>
              <icon-plus />
            </template>
            {{ $t('common.add') }}
          </a-button>
        </div>
        <div>
          <a-button @click="handleRefresh"> {{ $t('common.refresh') }}</a-button>
        </div>
      </div>
    </a-card>
    <a-card class="mt">
      <CrudTable
        ref="tableRef"
        :expandable="{ defaultExpandAllRows: true }"
        :default-expand-all-rows="true"
        :row-selection="false"
        :schema="schema"
        @row-action="handleRowAction"
      />
    </a-card>

    <a-drawer
      v-model:visible="editVisible"
      width="600px"
      :title="editTitle"
      @cancel="handleCancel"
      @ok="handleSubmit"
      @before-close="handleCancel"
    >
      <CrudForm v-if="editVisible" v-model="editData" :schema="schema" :show-actions="false"> </CrudForm>
    </a-drawer>
  </div>
</template>

<style scoped lang="less"></style>
