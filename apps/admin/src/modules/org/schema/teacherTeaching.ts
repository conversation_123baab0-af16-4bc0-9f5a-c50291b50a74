import { CustomSchema } from '@repo/infrastructure/types';

const teacherTeaching: CustomSchema = {
  api: '/teacher/teacherTeachingConfig',
  rowActions: [
    {
      key: 'edit',
      visible: false,
    },
  ],
  fieldsMap: {
    teacher: {
      listProps: {
        columnWidth: 120,
      },
    },
    teachingSubject: {
      displayProps: {
        toDisplay(value) {
          return value?.map((item: any) => item.name).join(', ');
        },
      },
    },
    teachingResearchGroup: {
      displayProps: {
        toDisplay(value) {
          return value?.map((item: any) => item.name).join(', ');
        },
      },
    },
  },
};

export default { teacherTeaching };
