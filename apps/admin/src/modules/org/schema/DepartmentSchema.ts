import { CustomSchema } from '@repo/infrastructure/types';

const departmentSchema: CustomSchema = {
  api: '/org/department',
  fieldsMap: {
    parentId: {
      editable: true,
      label: 'common.parentNode',
      valueType: 'Integer',
      inputWidget: 'treeSelectInput',
      treeSourceApi: '/org/department',
      inputWidgetProps: {
        allowClear: true,
      },
    },
  },

  rowActions: [
    {
      key: 'edit',
      label: 'common.edit',
      icon: 'icon-edit',
    },
  ],

  listViewProps: {
    showIndex: false,
    visibleColumns: ['name', 'localeKey', 'sort'],
  },
};

export default { departmentSchema };
