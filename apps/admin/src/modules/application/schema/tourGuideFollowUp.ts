import { CustomSchema } from '@repo/infrastructure/types';
import { defineAsyncComponent } from 'vue';
import useCommonStore from '@repo/infrastructure/utils/store';
import { PROJECT_URLS } from '@repo/env-config';

const tourGuideFollowUp: CustomSchema = {
  api: '/resourceRoom/tourGuideFollowUp',
  fieldsMap: {
    attachments: {
      label: '附件',
      key: 'attachments',
      inputWidget: defineAsyncComponent(
        () => import('@repo/ui/components/form/inputComponents/inputControl/uploadInput.vue'),
      ),
      displayProps: {
        component: 'AttachmentsPreviewDisplay',
      },
    },
    tourGuide: {
      visibleInForm: true,
      inputWidgetProps: {
        placeholder: '请先选择巡回指导',
        valueType: (value: any, computedOptions: any[]) => {
          const selected = computedOptions.find((item) => item.value === value);
          if (!selected) {
            return undefined;
          }
          return {
            id: selected.value,
            subject: selected.label,
            students: selected?.raw?.raw.students,
          };
        },

        getOptions: async (val: any) => {
          const store = useCommonStore({
            api: 'resourceRoom/tourGuide',
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          });
          const raw = await store.getList();

          return raw.map((item) => {
            return {
              label: item.subject,
              value: item.id,
              raw: item,
            };
          });
        },
      },
      displayProps: {
        toDisplay: (value) => {
          return value?.subject;
        },
      },
    },
    status: {
      inputWidget: 'textareaInput',
      inputWidgetProps: {
        maxLength: 9999,
        rows: 3,
      },
      displayProps: {
        detailSpan: 2,
      },
    },
    measure: {
      inputWidget: 'textareaInput',
      inputWidgetProps: {
        maxLength: 9999,
        rows: 3,
      },
      displayProps: {
        detailSpan: 2,
      },
    },
    student: {
      labelField: 'name',
      valueField: 'id',
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/components/student/studentDetailButton.vue')),
      },
      inputWidgetProps: {
        placeholder: '请选择学生',
        disabled: true,
        valueType: (value: any, computedOptions: any[]) => {
          const selected = computedOptions.find((item) => item.raw?.value === value);
          if (!selected) {
            return undefined;
          }
          return {
            id: selected.raw?.value,
            name: selected.raw?.label,
          };
        },
        queryDepends: {
          onTourGuideChange(tourGuide) {
            const options =
              tourGuide.students?.map((item) => {
                return {
                  value: item.studentId,
                  label: item.studentName,
                };
              }) || [];

            return {
              inputWidgetProps: {
                disabled: false,
                options,
              },
            };
          },
        },
      },
    },
  },
};

export default {
  tourGuideFollowUp,
};
