import type { RouteRecordRaw } from 'vue-router';
import { REDIRECT_ROUTE_NAME } from '@/router/constants';

export const DEFAULT_LAYOUT = () => import('@/common/layout/default-layout.vue');

export const REDIRECT_MAIN: RouteRecordRaw = {
  path: '/redirect',
  name: 'redirectWrapper',
  component: DEFAULT_LAYOUT,
  meta: {
    requiresAuth: true,
    hideInMenu: true,
  },
  children: [
    {
      path: '/redirect/:path',
      name: REDIRECT_ROUTE_NAME,
      component: () => import('@/views/redirect/index.vue'),
      meta: {
        requiresAuth: true,
        hideInMenu: true,
      },
    },
  ],
};

export const NOT_FOUND_ROUTE: RouteRecordRaw = {
  path: '/:pathMatch(.*)*',
  name: 'notFound',
  component: () => import('@/views/not-found/index.vue'),
};
export const COMMON_VIEW_ROUTE = [
  {
    path: '/module/:moduleName',
    component: DEFAULT_LAYOUT,
    meta: {
      requiresAuth: true,
    },
    children: [
      {
        component: () => import('@/views/functionWrapper.vue'),
        path: ':funcName',
        meta: {
          requiresAuth: true,
          roles: ['*'],
        },
        children: [
          // {
          //   component: () => import('@/views/table/index.vue'),
          //   path: '',
          //   meta: {
          //     requiresAuth: true,
          //     roles: ['*'],
          //   },
          // },
          // {
          //   path: 'add',
          //   component: () => import('@/views/form/index.vue'),
          //   meta: {
          //     requiresAuth: true,
          //     roles: ['*'],
          //   },
          // },
          // {
          //   path: 'edit/:id',
          //   component: () => import('@/views/form/index.vue'),
          //   meta: {
          //     requiresAuth: true,
          //     roles: ['*'],
          //   },
          // },
        ],
      },
    ],
  },
];
