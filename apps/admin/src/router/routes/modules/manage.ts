import { AppRouteRecordRaw } from '../types';

const ManageRoutes: AppRouteRecordRaw = {
  path: '/manage',
  component: () => import('@/views/manage/layout.vue'),
  meta: {
    requiresAuth: true,
    order: 0,
    redirect: '/',
  },
  children: [
    {
      path: 'userProfile',
      component: () => import('@/views/manage/userProfile.vue'),
    },
    {
      path: 'orgProfile',
      component: () => import('@/views/manage/orgProfile.vue'),
    },
    {
      path: ':roleIndex',
      component: () => import('@/views/manage/roleIndex.vue'),
      children: [
        {
          path: ':moduleIndex',
          component: () => import('@/views/manage/moduleIndex.vue'),
          children: [{ path: ':subModules+', component: () => import('@/views/manage/subModules.vue') }],
        },
      ],
    },
  ],
};
export default ManageRoutes;
