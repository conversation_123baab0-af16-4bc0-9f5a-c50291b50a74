import { AppRouteRecordRaw } from '../types';

const EduDevRoutes: AppRouteRecordRaw = {
  path: '/edk',
  component: () => import('@/views/edk/index.vue'),
  meta: {
    requiresAuth: true,
    order: 0,
  },
  children: [
    {
      path: 'questionLibrary',
      component: () => import('@/views/edk/questionLibrary/index.vue'),
      meta: {
        title: '题库 - 开发工具',
      },
    },
    {
      path: 'questionLibrary/add',
      component: () => import('@/views/edk/questionLibrary/edit.vue'),
      meta: {
        title: '新增题库题目',
      },
    },
    {
      path: 'questionLibrary/edit/:id',
      component: () => import('@/views/edk/questionLibrary/edit.vue'),
      meta: {
        title: '修改题库题目',
      },
    },
  ],
};
export default EduDevRoutes;
