@import url('variables');

* {
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 14px;
  background-color: var(--color-bg-1);
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

.general-card {
  border: none;
  border-radius: 4px;

  & > .arco-card-header {
    height: auto;
    padding: 20px;
    border: none;
  }

  & > .arco-card-body {
    padding: 0 20px 20px;
  }
}

.split-line {
  border-color: rgb(var(--gray-2));
}

.arco-table-cell {
  .circle {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 4px;
    background-color: rgb(var(--blue-6));
    border-radius: 50%;

    &.pass {
      background-color: rgb(var(--green-6));
    }
  }
}

.container {
  padding: 0 20px 20px;
}

.mt {
  margin-top: @marginSpace;
}

.ml {
  margin-left: @marginSpace;
}

.mb {
  margin-bottom: @marginSpace;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.w-100-percent {
  width: 100%;
}

.flex-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.gap-10 {
  gap: 10px;
}

.flex-1 {
  flex: 1;
}

.align-items-start {
  align-items: start;
  justify-content: start;
}

.space-between {
  justify-content: space-between;
}

.space-around {
  justify-content: space-around;
}

.is-flex {
  display: flex;
}

*::-webkit-scrollbar {
  width: 10px;
  // height: 10px; // 高度写不写，都不影响，因为会根据内容的长度自动计算
}

*::-webkit-scrollbar-thumb {
  background: #ccc; // 滑块颜色
  border-radius: 5px; // 滑块圆角
}

.arco-checkbox-disabled.arco-checkbox-indeterminate .arco-checkbox-icon {
  background-color: var(--color-primary-light-3) !important;
  border-color: var(--color-primary-light-3) !important;
}