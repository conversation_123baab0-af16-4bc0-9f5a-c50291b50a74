.course-item {
  margin-right: 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f2f2f2;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  .thumb {
    overflow: hidden;
    border-radius: 4px;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.1);
    img {
      width: 100%;
      height: 100%;
    }
  }
  .title {
    transition: bottom 0.3s;
    position: absolute;
    bottom: -100%;
    height: 100%;
    width: 100%;
    margin-top: 8px;
    background: linear-gradient(
            180deg,
            rgba(#444, 0.6) 0%,
            rgba(#444, 1) 100%
    );
    .name {
      margin: 20px 10px 10px;
      font-size: 14px;
      color: #f2f2f2;
      text-shadow: 0px -2px 2px rgba(0, 0, 0, 0.6);
    }
    .description {
      color: #f2f2f2;
      font-size: 11px;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      margin: 0 10px;
    }
  }

  .buttons {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin: 30px 10px 10px;

    .arco-button {
      margin-bottom: 10px;
    }
  }

  &:hover {
    border-color: var(--color-primary-light-4);
    .thumb {
      box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.2);
    }
    .title {
      bottom: 0;
    }
  }
}