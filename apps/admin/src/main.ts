import { createApp } from 'vue';
import ArcoVue from '@arco-design/web-vue';
import ArcoVueIcon from '@arco-design/web-vue/es/icon';
import autoFocus from '@/common/plugins/autoFocus';
import { i18n } from '@repo/infrastructure/locale';
import directive from '@repo/ui/directive';
// import { axiosInstance } from '@repo/infrastructure/request';
import { showError, initWindowErrorHandler } from '@repo/ui/components/utils/errorHandler.ts';
import { appGlobalConfig } from '@repo/infrastructure/utils';
import router from './router';
import piniaInstance from './common/store';
import App from './App.vue';
import '@arco-design/web-vue/dist/arco.css';
import '@/assets/style/global.less';
import '@repo/infrastructure/iconfont-online-css';
import '@repo/infrastructure/iconfont';
import './index.css';

appGlobalConfig();

const app = createApp(App);

app.use(ArcoVue, {});
app.use(ArcoVueIcon);

app.use(router);
app.use(piniaInstance);
app.use(i18n);
app.use(directive);
app.use(autoFocus);
// app.use(axiosInstance);

router.beforeEach((to, from, next) => {
  if (to.meta?.title) {
    document.title = `${to.meta.title} | 牧云融教`;
  }
  next();
});

// app.config.errorHandler = (err, vm, info) => {
//   showError(err);
// };

initWindowErrorHandler();

app.provide('router', router);

app.mount('#app');
export default app;
