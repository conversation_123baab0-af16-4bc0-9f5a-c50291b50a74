import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from "node:path";

// https://vite.dev/config/
export default defineConfig({
  server: {
    port: 4236
  },
  base: process.env.VITE_APP_TARGET_RELEASE === 'pc' ? './' : '/',
  plugins: [vue()],
  resolve: {
    alias: {
      '@/': path.resolve(__dirname, 'src') + '/',
    }
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
      scss: {
        api: 'modern-compiler',
      },
    },
  },
})
