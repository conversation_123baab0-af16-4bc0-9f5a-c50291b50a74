import { RouteRecordRaw } from 'vue-router';

const routes: Readonly<RouteRecordRaw[]> = [
  {
    path: '/widget',
    component: () => import('@/views/widget.vue'),
    children: [
      {
        path: 'resources',
        component: () => import('@/views/resources/index.vue'),
      },
      {
        path: 'resourcePreview',
        component: () => import('@/views/resources/preview.vue'),
      },
      {
        path: 'timetable',
        component: () => import('@/views/timetable/index.vue'),
      },
      {
        path: 'classBehaviorRecord',
        component: () => import('@/views/classBehaviorRecord/index.vue'),
      }
    ],
  },
];

export default routes;
