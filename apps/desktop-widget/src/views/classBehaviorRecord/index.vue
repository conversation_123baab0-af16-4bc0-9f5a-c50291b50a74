<script setup lang="ts">
import WidgetWrapper from "@/components/widgetWrapper.vue";
import { onMounted, ref } from "vue";
import { getElectronApi } from "@repo/infrastructure/electron";
import { getChapter } from "@repo/infrastructure/openapi/course";
import ClassBehaviorRecord from "@repo/components/course/edit/classBehaviorRecord.vue";
import { debounce } from "lodash";
import { request } from "@repo/infrastructure/request";
import { Message } from "@arco-design/web-vue";

const currentChapter = ref<any>({})
const loading = ref(false)

const handleSave = debounce(async () => {
  if (!currentChapter.value?.id) {
    return;
  }
  loading.value = true;
  try {
    await request(`/course/chapter/${currentChapter.value.id}`, {
      method: 'PUT',
      data: currentChapter.value,
    });
    // /course/chapter/content/{id} [patch]
    await request(`/course/chapter/content/${currentChapter.value?.id}`, {
      method: 'PATCH',
      data: {
        ...currentChapter.value?.content,
      },
    });
    
    Message.clear();
    // Message.success('保存成功');
  } finally {
    loading.value = false;
  }
}, 1000);

onMounted(async () => {
  const chapterId = await getElectronApi().send('getCurrentChapterId')
  const {data} = await getChapter({
    id: chapterId
  })
  currentChapter.value = data
})
</script>

<template>
<widget-wrapper title="课堂行为记录" path="/widget/classBehaviorRecord">
  <div class="p-2">
    <class-behavior-record v-model:chapter="currentChapter" v-if="currentChapter?.id" @save="handleSave" />
  </div>
</widget-wrapper>
</template>

<style scoped lang="scss">

</style>