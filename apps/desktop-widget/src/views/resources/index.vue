<script setup lang="ts">
  import { computed, ref } from 'vue';
  import WidgetWrapper from '@/components/widgetWrapper.vue';
  import { IconSettings } from '@arco-design/web-vue/es/icon';
  import ChapterSelectModal from '@/views/resources/chapterSelectModal.vue';
  import DigitalResourceIcon from '@repo/components/resource/digital/components/digitalResourceIcon.vue';
  import { getElectronApi } from '@repo/infrastructure/electron';
  import { resourceType } from '@repo/components/resource/constants.ts';

  const activeTabKey = ref('all');
  const chapter = ref<any>(null);
  const chapterSelectVisible = ref(false);

  const handleChapterSwitch = async (selected?: any) => {
    chapter.value = selected;
  };

  const title = computed(() => {
    if (!chapter.value?.id) {
      return '资源 [请先点击右侧设置当前章节 -> ]';
    }
    return chapter.value?.name;
  });

  const resourcesList = computed(() => {
    if (activeTabKey.value === 'all') {
      return chapter.value?.content?.lessonResourcePrepare || [];
    } else {
      return (
        chapter.value?.content?.lessonResourcePrepare?.filter((item: any) => item.type === activeTabKey.value) || []
      );
    }
  });

  const handlePreview = async (item: any) => {
    await getElectronApi().send('closeDesktopWidget', '/widget/resourcePreview');
    await getElectronApi().send('openDesktopWidget', '/widget/resourcePreview', {
      resourceId: item.resourceId,
      name: item.name,
    });
  };
</script>

<template>
  <widget-wrapper :title="title" path="/widget/resources">
    <div class="px-4">
      <a-tabs v-if="chapter?.id" v-model:active-key="activeTabKey" position="bottom" size="large" type="rounded">
        <a-tab-pane key="all" title="全部">
          <div
            class="rounded-lg p-4 mb-2 bg-white/70 resource-content flex flex-wrap gap-2"
            v-if="activeTabKey === 'all'"
          >
            <digital-resource-icon
              v-for="resource in resourcesList"
              :key="resource.id"
              :resource="resource"
              @click="() => handlePreview(resource)"
            >
              <template #actions>
                <div></div>
              </template>
            </digital-resource-icon>
          </div>
        </a-tab-pane>
        <a-tab-pane v-for="type in resourceType" :key="type.id" :title="type.name">
          <template #title>
            <component :is="type.icon" />
            {{ type.name }}
          </template>
          <div
            class="rounded-lg p-4 mb-2 bg-white/70 resource-content flex flex-wrap gap-2"
            v-if="activeTabKey === type.id"
          >
            <digital-resource-icon
              v-for="resource in resourcesList"
              :key="resource.id"
              :resource="resource"
              @click="() => handlePreview(resource)"
            >
              <template #actions>
                <div></div>
              </template>
            </digital-resource-icon>
          </div>
        </a-tab-pane>
      </a-tabs>
      <a-empty v-else class="mt-20" description="请先设置当前上课章节" />
    </div>
    <template #extra-actions>
      <a-button size="small" @click="() => (chapterSelectVisible = true)">
        <template #icon>
          <IconSettings />
        </template>
      </a-button>
    </template>

    <chapter-select-modal v-model:visible="chapterSelectVisible" @select="handleChapterSwitch" />
  </widget-wrapper>
</template>

<style scoped lang="scss">
  .resource-content {
    height: calc(100vh - 140px);
  }
</style>
