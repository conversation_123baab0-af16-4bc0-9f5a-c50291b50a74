<script setup lang="ts">
  import { IconClose, IconToTop, IconFullscreen, IconFullscreenExit, IconMinus } from '@arco-design/web-vue/es/icon';
  import { nextTick, onMounted, ref } from 'vue';
  import { getElectronApi, handleTriggerDevTools, setWindowDraggableArea } from '@repo/infrastructure/electron';

  const props = defineProps({
    title: {
      type: String,
      default: 'Widget',
    },
    path: {
      type: String,
      default: '/widget',
    },
    canStickTop: {
      type: Boolean,
      default: false,
    },
    canFullscreen: {
      type: Boolean,
      default: false,
    },
  });

  const fullScreen = ref(false);

  const handleCloseWidget = () => {
    window.close();
  };

  const handleWidgetMinimize = () => {
    getElectronApi().send('handleMinimizeDesktopWidget', props.path);
  };

  const toggleFullscreen = async () => {
    await getElectronApi().send('handleToggleFullscreenDesktopWidget', props.path);
    fullScreen.value = !fullScreen.value;
  };

  onMounted(() => {
    nextTick(() => {
      const el = document.getElementsByClassName('electron-draggable-area')[0];
      setWindowDraggableArea(el, 'desktop-widget', props.path);
    });
  });
</script>

<template>
  <div class="widget-wrapper">
    <div class="bg"></div>
    <div
      class="header text-white pb-4 electron-draggable-area px-4 flex items-center justify-between"
      @click="() => handleTriggerDevTools('desktop-widget', path)"
    >
      <div class="text-lg">
        {{ title }}
      </div>
      <a-space class="widget-actions">
        <slot name="extra-actions"></slot>
        <a-button v-if="canStickTop" size="small">
          <template #icon>
            <IconToTop />
          </template>
        </a-button>
        <a-button v-if="canFullscreen" size="small" @click="toggleFullscreen">
          <template #icon>
            <IconFullscreenExit v-if="fullScreen" />
            <IconFullscreen v-else />
          </template>
        </a-button>
        <a-button size="small" @click="handleWidgetMinimize">
          <template #icon>
            <IconMinus />
          </template>
        </a-button>
        <a-button size="small" @click="handleCloseWidget">
          <template #icon>
            <IconClose />
          </template>
        </a-button>
      </a-space>
    </div>
    <div class="content-section">
      <slot />
    </div>
  </div>
</template>

<style scoped lang="scss">
  .widget-wrapper {
    user-select: none;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
    height: calc(100vh - 20px);
    width: calc(100vw - 20px);
    margin: 10px;
    position: relative;
    .bg {
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, rgba(12, 125, 128, 0.9) 100%);
      border-radius: 8px;
      height: 200px;
      z-index: 1;
      position: fixed;
      bottom: 10px;
      width: calc(100vw - 20px);
    }
    .header {
      position: fixed;
      bottom: 10px;
      left: 10px;
      z-index: 2;
      width: calc(100vw - 20px);
    }
    .content-section {
      position: relative;
      z-index: 2;
      //margin: -184px 16px 16px;
      height: calc(100vh - 70px);
      overflow-x: hidden;
      overflow-y: auto;
      // can't select
      user-select: none;

      // scrollbar style
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;

        &-track {
          background: none;
        }

        &-thumb {
          background: rgba(0, 0, 0, 0.05);
          border-radius: 4px;
        }

        &-corner {
          background: #f1f1f1;
        }

        &:hover {
          &-thumb {
            background: #a1a1a1;
          }
        }
      }
    }
  }
</style>
