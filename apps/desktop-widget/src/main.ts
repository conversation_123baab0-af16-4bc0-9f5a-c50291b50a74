import { createApp } from 'vue'
import App from './App.vue'
import routes from "@/routes";
import {createRouter, createWebHashHistory} from "vue-router";
import ArcoVue from '@arco-design/web-vue';
import '@arco-design/web-vue/dist/arco.css';
import {createPinia} from "pinia";

const piniaInstance = createPinia();

const router = createRouter({
  routes,
  history: createWebHashHistory()
})

createApp(App)
  .use(piniaInstance)
  .use(router)
  .use(ArcoVue)
  .mount('#app')
