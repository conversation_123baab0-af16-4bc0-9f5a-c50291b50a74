{"name": "@repo/lbs", "version": "2.1.52", "private": true, "exports": {".": "./index.ts", "./utils": "./src/utils.ts", "./types": "./types.ts", "./tiandiMap": "./src/tiandiMap.ts"}, "dependencies": {"@repo/config": "workspace:*", "@repo/env-config": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/infrastructure": "workspace:*", "@repo/typescript-config": "workspace:*"}, "devDependencies": {"@jhqn/tmap-jsapi-types": "0.0.1-beta.6", "i": "^0.3.7", "npm": "^10.8.1", "vue": "^3.0.0"}}