<script setup lang="ts">
  import { computed, onMounted, onUnmounted, PropType, ref, watch } from 'vue';
  import useSchoolCourseStore from '@repo/components/store/schoolCourseStore';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import ReferFromMyLibraryModal from './components/referFromMyLibraryModal.vue';

  const props = defineProps({
    iep: {
      type: Object as PropType<any>,
      required: true,
    },
    longTermTarget: {
      type: Object as PropType<any>,
      required: true,
    },
    visible: {
      type: Boolean,
      required: true,
    },
    editable: {
      type: Boolean,
      default: true,
    },
    canEdit: {
      type: Boolean,
      default: true,
    },
  });

  const emit = defineEmits(['update:visible', 'ok']);
  const { loading, setLoading } = useLoading();

  const shortTermTargets = ref<any[]>([]);

  const modalVisible = computed({
    get() {
      return props.visible;
    },
    set(val) {
      emit('update:visible', val);
    },
  });

  const selectedKeys = ref<any>();
  const rowSelection = {
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
  };

  const handleAddManual = () => {
    shortTermTargets.value = [...shortTermTargets.value, { preScore: 0 }];
  };

  const handleClose = () => {
    modalVisible.value = false;
    shortTermTargets.value = [];
  };

  const handleDelete = (rowIndex) => {
    shortTermTargets.value.splice(rowIndex, 1);
  };

  const handleOpen = () => {
    shortTermTargets.value = (props.longTermTarget.shortTermTargets || []).map((item) => {
      return {
        ...item,
        expand: item?.chapterContents?.length > 0 ? '1' : undefined,
      };
    });
  };

  const handleOk = async () => {
    if (!props.editable) {
      emit('ok');
      return true;
    }
    setLoading(true);
    try {
      if (shortTermTargets.value.some((t) => !t.content || t.preScore === undefined || !t.dateRange)) {
        throw new Error('请完善短期目标信息');
      }

      if (props.iep.submitStatus !== 'Draft' && props.iep.submitStatus !== 'Rejected') {
        throw new Error('个别化教育计划已经提交，不能修改');
      }

      const data: any = [];
      shortTermTargets.value?.forEach((item) => {
        data.push({
          ...item,
          individualizedEducationId: props.iep.id,
          longTermTargetId: props.longTermTarget.id,
          termType: 'ShortTerm',
          content: item.content,
          preScore: item.preScore,
          afterScore: item.afterScore,
          courseId: props.longTermTarget.courseId,
        });
      });

      await request(
        `/resourceRoom/individualizedEducationTarget/batchCreate?longTermTargetId=${props.longTermTarget.id}`,
        {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'POST',
          data,
        },
      );

      emit('ok');
      modalVisible.value = false;
      return true;
    } catch (e: any) {
      Message.error(e.message);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const referFromMyLibVisible = ref(false);
  const handleReferFromMyLib = () => {
    referFromMyLibVisible.value = true;
  };

  const disabledDate = (current) => {
    const dateRange = props.longTermTarget.dateRange?.map((item) => new Date(item));
    return current && (current < dateRange[0] || current > dateRange[1]);
  };

  const isInLongTermDateRange = (range: string[]) => {
    if (!props.longTermTarget?.dateRange || !props.longTermTarget.dateRange.length) {
      return range;
    }

    if (!range || range.length < 2) {
      return null;
    }

    try {
      const longTermStart = new Date(props.longTermTarget.dateRange[0]);
      const longTermEnd = new Date(props.longTermTarget.dateRange[1]);
      const inputStart = new Date(range[0]);
      const inputEnd = new Date(range[1]);

      const result = inputStart >= longTermStart && inputEnd <= longTermEnd;
      return result ? range : null;
    } catch (e) {
      console.error('Error parsing dates:', e);
      return null;
    }
  };
  const handleImportFromLibrary = (item: any) => {
    shortTermTargets.value = [
      ...shortTermTargets.value,
      {
        content: item.content,
        teachingScene: item.teachingScene,
        dateRange: isInLongTermDateRange(item?.dateRange || []),
        preScore: item?.preScore || 0,
      },
    ];
  };

  const copyContentVisible = ref(false);
  const isSearching = ref(false);
  const referencedSameLongTargetIep = ref<any>([]);

  const handleSearch = async () => {
    isSearching.value = true;
    copyContentVisible.value = true;
    try {
      const { data: res } = await request(
        `/resourceRoom/individualizedEducationTarget/searchIepByLongTargetId/${props.longTermTarget.id}`, // 有问题！！
        {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          params: {
            iepLongTermTargetId: props.longTermTarget.id,
            period: props.iep?.gradePeriod,
          },
        },
      );
      referencedSameLongTargetIep.value = res.items;
    } finally {
      isSearching.value = false;
    }
  };

  const selectedIEP = ref([]);
  const handleReference = async () => {
    try {
      await request(
        `/resourceRoom/individualizedEducationTarget/batchPushShortTargetToIep/${selectedIEP.value.join(',')}`,
        {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'put',
          params: {
            courseId: props.longTermTarget.courseId,
            longTargetId: props.longTermTarget.id,
          },
          data: selectedKeys.value || [], // shorTargetIds
        },
      );
      Message.success('操作成功');
    } finally {
      /**/
    }
  };

  const existNotSavedShortTarget = computed(() => {
    return shortTermTargets.value?.filter((item) => !item?.id)?.length > 0;
  });

  onUnmounted(() => {
    copyContentVisible.value = false;
    selectedIEP.value = [];
  });
</script>

<template>
  <a-modal v-model:visible="modalVisible" :width="1000" @open="handleOpen" @close="handleClose">
    <!--
    :ok-loading="loading"
    :on-before-ok="handleOk"
-->
    <template #title> 添加短期目标（领域： {{ longTermTarget.domain }}）</template>
    <div> 长期目标： {{ longTermTarget.content }}</div>
    <a-space v-if="editable">
      <a-button size="mini" type="outline" class="mt-2" @click="handleAddManual">
        <template #icon>
          <IconPlus />
        </template>
        添加短期目标
      </a-button>
      <a-button size="mini" type="outline" class="mt-2" @click="handleReferFromMyLib">
        <template #icon>
          <IconPlus />
        </template>
        从短期目标库引用
      </a-button>
      <a-button
        v-if="selectedKeys?.length"
        size="mini"
        type="dashed"
        status="success"
        class="mt-2"
        @click="handleSearch"
      >
        <template #icon>
          <icon-filter />
        </template>
        检索
      </a-button>
    </a-space>

    <a-table
      v-model:selected-keys="selectedKeys"
      class="mt-2"
      size="small"
      :data="shortTermTargets"
      :pagination="false"
      row-key="id"
      :row-selection="rowSelection"
    >
      <template #columns>
        <a-table-column title="时间范围">
          <template #cell="{ record }">
            <a-range-picker v-model="record.dateRange" :disabled-date="disabledDate" size="mini" />
          </template>
        </a-table-column>
        <a-table-column title="目标">
          <template #cell="{ record }">
            <a-textarea v-model.trim="record.content" size="mini" />
          </template>
        </a-table-column>
        <a-table-column title="教学情境">
          <template #cell="{ record }">
            <a-textarea v-model.trim="record.teachingScene" size="mini" />
          </template>
        </a-table-column>
        <a-table-column title="前测" :width="120">
          <template #cell="{ record }">
            <a-input-number v-model="record.preScore" size="mini" :min="0" :max="5">
              <template #suffix>分</template>
            </a-input-number>
          </template>
        </a-table-column>
        <a-table-column v-if="editable" title="操作" :width="80">
          <template #cell="{ rowIndex }">
            <a-button type="text" size="mini" status="danger" @click="() => handleDelete(rowIndex)"> 删除</a-button>
          </template>
        </a-table-column>
      </template>
    </a-table>
    <a-spin v-if="copyContentVisible" class="mt-2 w-full" :loading="isSearching">
      <div
        v-if="referencedSameLongTargetIep?.length && selectedKeys?.length"
        class="flex justify-start flex-wrap items-center gap-2 mb-2 mt-4 rounded-lg px-2 py-2"
      >
        <div
          v-for="item in referencedSameLongTargetIep"
          :key="item?.id"
          class="flex justify-start items-center space-x-1"
        >
          <a-checkbox v-model="selectedIEP" :value="item.id" />
          <a-tag color="blue">
            {{ item?.student?.name }}
            <span class="text-xs ml-2">{{ item?.gradePeriod }}</span>
          </a-tag>
        </div>
      </div>
      <a-empty v-else />
    </a-spin>

    <refer-from-my-library-modal
      v-model:visible="referFromMyLibVisible"
      :short-term-targets="shortTermTargets"
      :course-id="longTermTarget.courseId"
      :long-term-targetdate-range="longTermTarget.dateRange"
      @import="handleImportFromLibrary"
    />
    <template #footer>
      <div class="flex justify-end space-x-3 items-center">
        <a-tooltip
          :content="
            !selectedKeys?.length
              ? '请先勾选需要复制的目标'
              : existNotSavedShortTarget
                ? '请先保存所有短期目标后在复制'
                : !selectedIEP?.length
                  ? '请检索并选择需要推送的IEP'
                  : '复制目标'
          "
        >
          <a-button
            status="success"
            size="mini"
            type="primary"
            :disabled="!selectedIEP?.length || existNotSavedShortTarget"
            @click="handleReference"
          >
            <template #icon>
              <icon-copy />
            </template>
            复制目标
          </a-button>
        </a-tooltip>
        <a-button size="mini" type="primary" :loading="loading" @click="handleOk">确定</a-button>
        <a-button size="mini" @click="handleClose">关闭</a-button>
      </div>
    </template>
  </a-modal>
</template>

<style scoped lang="scss"></style>

<style scoped lang="scss"></style>
