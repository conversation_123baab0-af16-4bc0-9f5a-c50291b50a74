import { h, ref } from 'vue';
import { Modal, Message } from '@arco-design/web-vue';
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';
import { cloneDeep } from 'lodash';
import { TableRowAction } from '@repo/infrastructure/types';
import { collaborateActions } from '@repo/infrastructure/data/collaborate';
import CollaboratorSettings from '../collaborate/collaboratorSettings.vue';

const getCollaborateRowAction = (module: string, options?: Record<string, any>): TableRowAction => {
  return {
    label: '分享',
    key: 'collaborate',
    collaborate: 'Owner',
    icon: 'icon-share-alt',
    expose: true,
    /* btnProps: {
      status: 'success',
      type: 'outline',
    }, */
    // disabled: (record: any) => {
    //   if (!record.submitStatus) {
    //     return false;
    //   }
    //   return record.submitStatus !== 'Draft' && record.submitStatus !== 'Rejected';
    // },
    handler: async (record: any, loadData: any) => {
      const selectedCollaborator = ref([...cloneDeep(record.collaborators || [])]);
      const saving = ref(false);

      const modalContent = {
        setup() {
          return () =>
            h(CollaboratorSettings, {
              record,
              'modelValue': selectedCollaborator.value as any,
              'onUpdate:modelValue': (value: any) => {
                selectedCollaborator.value = value;
              },
              module,
              'okText': '保存',
              'okLoading': saving,
              'onUpdate:record': (value: any) => {
                record = value;
              },
            });
        },
      };
      Modal.open({
        title: '分享设置',
        content: () => h(modalContent),
        async onBeforeOk() {
          saving.value = true;
          try {
            await request('/common/collaborate/save', {
              method: 'POST',
              baseURL: PROJECT_URLS.MAIN_PROJECT_API,
              data: {
                recordId: record.id,
                module,
                recordCollaborationConfig: record.recordCollaborationConfig || {},
                collaborates: selectedCollaborator.value
                  ?.map((item) => {
                    return {
                      ...item,
                      module,
                    };
                  })
                  .filter((item) => item.userSet !== false),
              },
            });

            Message.success('修改分享者成功');
            await loadData();
            return true;
          } catch (e) {
            console.error(e);
            return false;
          } finally {
            saving.value = false;
          }
        },
      });
    },
    ...(options || {}),
  };
};

const getDataSubmitAction = (api: string, options?: Record<string, any>): TableRowAction => {
  return {
    label: '归档',
    key: 'submit',
    // icon: 'icon-check',
    icon: 'icon-folder',
    collaborate: 'Owner',
    disabled: (record: any) => {
      return record.submitStatus !== 'Draft' && record.submitStatus !== 'Rejected';
    },
    handler: async (record: any, loadData: any) => {
      Modal.confirm({
        title: '提交确认',
        content: '确认要将此数据提交至管理端吗？提交后不可修改！',
        onOk: async () => {
          Message.loading('正在提交, 请稍后...');
          const { id } = record;
          const data = {
            id,
            submitStatus: 'Submitted',
          };
          try {
            await request(`${api}/submit`, {
              method: 'PUT',
              data,
              baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            });
            Message.success('操作成功');
            await loadData();
          } catch (e) {
            console.error(e);
          }
        },
      });
    },
    ...(options || {}),
  };
};

const getSubmittableRowClass = (record) => {
  switch (record.submitStatus) {
    case 'Submitted':
      return 'submitted-row';
    case 'Approved':
      return 'approved-row';
    case 'Rejected':
      return 'rejected-row';
    default:
      return '';
  }
};

const getCollaboratorField = () => {
  return {
    collaborators: {
      visibleInForm: false,
      displayProps: {
        component: 'UserAvatarListDisplay',
        tooltip: {
          visible: true,
          getContent: (item) => {
            const action = collaborateActions[item.action];
            return `${item.name} ${action}`;
          },
        },
      },
    },
    recordCollaborationConfig: {
      visibleInTable: false,
      visibleInDetail: false,
      visibleInForm: false,
    },
    submitStatus: {
      listProps: {
        columnWidth: 90,
      },
      visibleInForm: false,
    },
  };
};

const getCollaborateDataOwner = (record) => {
  return record.collaborators?.find((item) => item.action === 'Owner');
};

export {
  getCollaborateRowAction,
  getDataSubmitAction,
  getSubmittableRowClass,
  getCollaboratorField,
  getCollaborateDataOwner,
};
