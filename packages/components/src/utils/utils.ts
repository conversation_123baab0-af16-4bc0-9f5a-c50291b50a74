import { useUserMenuStore, useUserStore } from '@repo/infrastructure/store';
import { getClientRole } from '@repo/env-config';

export const getYears = (start: number, end: number) => {};

export const getAdminClientNature = () => {
  const menuStore = useUserMenuStore();
  return menuStore.getCurrentMenuInfo().app?.label;
};

export const getOrgNature = () => {
  if (getClientRole() === 'Company') {
    const userStore = useUserStore();
    return userStore.getUserNature();
  }
  const menuStore = useUserMenuStore();
  return menuStore.getCurrentMenuInfo().app?.label;
};
