import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';

export type HighlightedSectionMeta = {
  parentTagName: string;
  parentIndex: number;
  textOffset: number;
};

export type HighlightedSection = {
  id: string;
  startMeta: HighlightedSectionMeta;
  endMeta: HighlightedSectionMeta;
  text: string;
};

export type AnnotationModule = 'CHAPTER_CONTENT_DOCUMENT';

export type AnnotationItem = {
  section: HighlightedSection;
  module: AnnotationModule;
  sourceId: number;
  paragraphId?: string;
  content: string;
  [key: string]: any;
};

export type AnnotationModuleSource = {
  module: AnnotationModule;
  sourceId: number;
  paragraphId?: string;
};

export const getAnnotationBlocks = async (module: AnnotationModuleSource): Promise<AnnotationItem[]> => {
  const { data } = await request('/document/selectedTextAnnotation', {
    baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    params: {
      ...module,
      pageSize: 999,
    },
  });

  return data.items || [];
};

export const saveAnnotation = async (annotation: AnnotationItem): Promise<AnnotationItem> => {
  const { data } = await request('/document/selectedTextAnnotation', {
    baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    method: 'POST',
    data: annotation,
  });

  return data;
};
