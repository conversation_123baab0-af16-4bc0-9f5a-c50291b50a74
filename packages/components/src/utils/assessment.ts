import { ref } from 'vue';

interface TreeNode {
  id: number;
  parentId: number | null; // 根节点的 parentId 可能为 null
  children?: TreeNode[];
}

const allStudentScoresMap = ref({});

const getItemInfoByScore = (snapshot, score, field) => {
  if (!snapshot) {
    return undefined;
  }

  const scoreItem = snapshot.scores?.find((item) => item.score === score) || {};
  return scoreItem[field];
};

const addToStudentScoreMap = (snapshot, raw) => {
  raw = raw || snapshot?.details || [];
  raw?.forEach((item) => {
    if (item.score !== undefined) {
      allStudentScoresMap.value[item.id] = {
        score: item.score,
        targetLevel: getItemInfoByScore(snapshot, item.score, 'targetLevel'),
        targetStandard: getItemInfoByScore(snapshot, item.score, 'targetStandard'),
        supportLevel: getItemInfoByScore(snapshot, item.score, 'supportLevel'),
      };
    }
    if (item.children && item.children.length) {
      addToStudentScoreMap(snapshot, item.children);
    }
  });

  return raw;
};

const treeToNodesMap = (tree: TreeNode[]) => {
  const nodeMap = new Map<number, TreeNode>();

  // 遍历树，将所有节点存入 Map
  function buildMap(nodes: TreeNode[]) {
    nodes?.forEach((node) => {
      nodeMap.set(node.id, node);
      if (node.children && node.children.length > 0) {
        buildMap(node.children);
      }
    });
  }

  buildMap(tree);
  return nodeMap;
};

const getTopLevelNodeByLeafId = (tree: TreeNode[], leafId: number): TreeNode | null => {
  const nodeMap = treeToNodesMap(tree);

  // 通过 leafId 获取该节点
  let currentNode = nodeMap.get(leafId);

  if (!currentNode) {
    return null; // 如果 leafId 对应的节点不存在，返回 null
  }

  // 向上遍历，直到找到最上层的祖先节点
  while (currentNode && currentNode.parentId) {
    const parentNode = nodeMap.get(currentNode.parentId);
    if (!parentNode) {
      break; // 如果找不到父节点，跳出循环
    }
    currentNode = parentNode;
  }

  return currentNode; // 返回最上层的祖先节点
};

/**
 * 获取叶子节点的所有祖先节点
 * @param tree
 * @param leafId
 */
const getLeafParentPath = (tree: TreeNode[], leafId: number): TreeNode[] => {
  const path: TreeNode[] = [];

  const nodeMap = treeToNodesMap(tree);

  let currentNode = nodeMap.get(leafId);
  while (currentNode) {
    path.unshift(currentNode);
    if (currentNode.parentId === null) {
      break;
    }
    currentNode = nodeMap.get(currentNode.parentId);
  }

  return path;
};

export { allStudentScoresMap, addToStudentScoreMap, getTopLevelNodeByLeafId, getLeafParentPath };
