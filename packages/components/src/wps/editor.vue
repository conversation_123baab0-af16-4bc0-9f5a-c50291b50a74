<script lang="ts" setup>
  import { getToken } from '@repo/infrastructure/auth';
  import { onMounted, PropType, ref } from 'vue';
  import { ENV } from '@repo/env-config';
  import WebOfficeSDK from './vendor/web-office-sdk-solution-v2.0.5.es.js';
  import { OfficeType } from './types';

  const props = defineProps({
    officeType: {
      type: String as PropType<OfficeType>,
      required: true,
    },
    fileId: {
      type: String,
      required: true,
    },
    elementId: {
      type: String,
      required: true,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  });

  const appId = ENV.VITE_APP_WPS_API_KEY;

  const options = {
    officeType: WebOfficeSDK.OfficeType[props.officeType],
    appId,
    fileId: props.fileId,
    isListenResize: true,
    mount: `#wps-editor-${props.elementId}`,
    token: getToken(),
    wpsOptions: {
      isShowDocMap: false, // 是否开启目录功能，默认开启
      isBestScale: true, // 打开文档时，默认以最佳比例显示
    },
    cooperUserAttribute: {
      isCooperUsersAvatarVisible: true,
    },
  };
  const instance = ref<any>();
  const app = ref<any>();

  defineExpose({
    instance,
    app,
  });

  const emits = defineEmits(['editorReady']);

  onMounted(async () => {
    instance.value = WebOfficeSDK.init(options);
    await instance.value.ready();
    app.value = instance.value.Application;

    if (props.readonly) {
      await app.value.ActiveDocument.SetReadOnly({
        Value: true,
      });
    }

    emits('editorReady', app.value, instance.value);
  });
</script>

<template>
  <div :id="`wps-editor-${elementId}`" class="wps-editor-wrapper"></div>
</template>

<style lang="less" scoped>
  .wps-editor-wrapper {
    height: 100%;
  }
</style>
