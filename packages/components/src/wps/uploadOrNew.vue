<script lang="ts" setup>
  import { computed, PropType, ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { createNewEmptyDocument, uploadNewDocument } from './api';
  import { OfficeType } from './types';

  const props = defineProps({
    modelValue: {
      type: String,
      required: true,
    },
    officeType: {
      type: String as PropType<OfficeType>,
      default: 'Writer',
    },
    defaultFileName: {
      type: String,
      default: '新建文档',
    },
  });

  const accept = computed(() => {
    switch (props.officeType) {
      case 'Writer':
        return '.doc,.docx';
      case 'Spreadsheet':
        return '.xls,.xlsx';
      case 'Presentation':
        return '.ppt,.pptx';
      default:
        return '.notAny';
    }
  });

  const ext = computed(() => {
    switch (props.officeType) {
      case 'Writer':
        return '.docx';
      case 'Spreadsheet':
        return '.xlsx';
      case 'Presentation':
        return '.pptx';
      default:
        return '.notAny';
    }
  });

  const { loading, setLoading } = useLoading();

  const emits = defineEmits(['update:modelValue']);

  const filename = ref<any>('');
  const filenameEditVisible = ref<any>(false);

  const handleShowCreateEmpty = () => {
    filename.value = props.defaultFileName;
    filenameEditVisible.value = true;
  };

  const handleCreateNew = async () => {
    if (!filename.value) {
      Message.error('请输入文件名');
      return;
    }
    setLoading(true);
    const fileInfo = await createNewEmptyDocument(`${filename.value}${ext.value}`, props.officeType);
    setLoading(false);
    emits('update:modelValue', fileInfo.id);
    filenameEditVisible.value = false;
    filename.value = props.defaultFileName;
  };
  const handleUpload = async (option: any) => {
    setLoading(true);
    const fileInfo = await uploadNewDocument(option.fileItem.name!, option.fileItem.file!);
    option.onSuccess?.(fileInfo.id);
    setLoading(false);
    emits('update:modelValue', fileInfo.id);
  };
</script>

<template>
  <a-spin :loading="loading" style="width: 100%">
    <a-empty class="wrapper">
      <div>还没有相关文档，您可以</div>
      <div class="buttons">
        <a-button type="primary" @click="handleShowCreateEmpty">
          <template #icon>
            <IconPlus />
          </template>
          创建空白文档
        </a-button>
        <div>
          <a-upload :accept="accept" :custom-request="handleUpload as any">
            <template #upload-button>
              <a-button class="ml" type="secondary">
                <template #icon>
                  <IconUpload />
                </template>
                从本地上传文档
              </a-button>
            </template>
          </a-upload>
        </div>
      </div>
    </a-empty>

    <a-modal
      v-model:visible="filenameEditVisible"
      :ok-text="filename ? '确定' : '创建'"
      title="请输入文件名"
      @ok="handleCreateNew"
    >
      <a-input v-model="filename" placeholder="请输入文件名" />
    </a-modal>
  </a-spin>
</template>

<style lang="less" scoped>
  .wrapper {
    padding: 60px 0;
  }

  .buttons {
    margin-top: 10px;
    display: flex;
    justify-content: center;
  }
</style>
