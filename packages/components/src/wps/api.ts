import axios from 'axios';
import { PROJECT_URLS } from '@repo/env-config';
import { axiosInstance } from '@repo/infrastructure/request';
import { FileInfo, OfficeType } from '@/common/types/wps';

export const createNewEmptyDocument = async (filename: string, type: OfficeType): Promise<FileInfo> => {
  const formData = new FormData();
  formData.append('filename', filename);
  formData.append('type', type);

  const { data } = await axiosInstance.post('/document/document/userCreateEmpty', formData, {
    baseURL: PROJECT_URLS.MAIN_PROJECT_API,
  });

  return data;
};

export const uploadNewDocument = async (filename: string, file: File): Promise<FileInfo> => {
  const formData = new FormData();
  formData.append('filename', filename);
  formData.append('file', file);

  const { data } = await axiosInstance.post('/document/document/userCreate', formData, {
    baseURL: PROJECT_URLS.MAIN_PROJECT_API,
  });

  return data;
};
