<script setup lang="ts">
  import { onMounted, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import BorrowListViewModal from './borrowListViewModal.vue';

  const emits = defineEmits(['updateBorrowInfo']);

  const size = 'mini';
  const cardConfiguration = {
    imgWidth: '100%',
    imgHeight: '114px',
    descriptionHeight: '0px',
  };

  const paginationConfig = ref({
    pageSize: 20,
    page: 1,
  });
  const hoveredImageIndex = ref<any>(-1);

  const handleImageMouseEnter = (index: number) => {
    hoveredImageIndex.value = index;
  };

  const handleImageMouseLeave = () => {
    hoveredImageIndex.value = null;
  };

  const selectedItems = ref<any>({});
  const details = ref<any>([]);
  const recourseList = ref();
  const total = ref(0);
  const loadRecourse = async () => {
    try {
      const { data: res } = await request(`/asset/assetInfo`, {
        method: 'get',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          ...paginationConfig.value,
        },
      });
      recourseList.value = res.items;
      recourseList.value.forEach((item: any) => {
        item.numUdf1 = 0;
      });
      total.value = res.total;
    } finally {
      /**/
    }
  };
  const formData = ref<any>({
    planBorrowDate: new Date(),
    details: [],
  });
  const handleSelect = (event: boolean, item: any) => {
    if (event) {
      formData.value.details.push(item);
    } else {
      formData.value.details = formData.value.details.filter((record: any) => record?.id !== item.id);
    }
  };

  const handleSelectCard = (item: any) => {
    if (!item?.availableInventory) {
      Message.warning('暂无库存');
      return;
    }
    item.numUdf1 = 0;
    if (selectedItems.value?.[item.id]) {
      selectedItems.value[item.id] = false;
      formData.value.details = formData.value.details.filter((record: any) => record?.id !== item.id);
    } else {
      selectedItems.value[item.id] = true;
      formData.value.details.push(item);
    }
    emits('updateBorrowInfo', formData.value);
  };

  const listVisible = ref(false);

  const showList = () => {
    listVisible.value = true;
  };

  const handleSetNum = (e, item) => {
    item.numUdf1 = Number(e | 0);

    const existing = formData.value.details.find((record: any) => record?.id === item.id);
    if (e) {
      if (existing) {
        existing.numUdf1 = item.numUdf1;
      } else {
        formData.value.details.push({ ...item });
      }
    } else {
      formData.value.details = formData.value.details.filter((record: any) => record?.id !== item.id);
    }
    emits('borrowList', formData.value);
  };

  const getBorrowNum = (item: any) => {
    const record = formData.value.details.find((record) => record.id === item.id);
    if (record) {
      return record?.numUdf1 || 0;
    }
    return 0;
  };

  onMounted(async () => {
    await loadRecourse();
  });

  defineExpose({
    showList,
    loadRecourse,
  });
</script>

<template>
  <div class="w-full h-full relative px-10 py-5">
    <div
      class="card-container grid gap-4"
      :style="{
        gridTemplateColumns: 'repeat(auto-fill, minmax(220px, 1fr))',
        justifyContent: 'center',
        justifyItems: 'start',
      }"
    >
      <a-card
        v-for="(item, index) in recourseList"
        :key="index"
        hoverable
        class="shadow-xl overflow-hidden"
        :style="{
          width: '220px',
          height: '230px',
          position: 'relative',
        }"
        @mouseenter="handleImageMouseEnter(index)"
        @mouseleave="handleImageMouseLeave"
      >
        <!--@click="handleSelectCard(item)"-->
        <template #cover>
          <a-carousel
            indicator-type="slider"
            :style="{
              height: cardConfiguration.imgHeight,
              position: 'relative',
              overflow: 'hidden',
              background: 'white',
            }"
            :default-current="1"
          >
            <a-carousel-item class="w-full h-full relative p-[10px]">
              <div class="flex flex-col justify-end">
                <div class="flex mb-1 justify-between" @click.stop>
                  <span class="text-xs text-gray-400">可用数量：{{ item?.availableInventory }}</span>
                  <span v-if="getBorrowNum(item)" class="text-xs text-blue-400">
                    {{ '借用数量：' + getBorrowNum(item) }}
                  </span>
                  <span v-else class="text-xs text-gray-400" />
                  <a-checkbox
                    v-if="false"
                    v-model="selectedItems[item.id]"
                    :disabled="!item?.availableInventory"
                    @change="handleSelect($event, item)"
                  />
                </div>
                <a-image
                  v-if="item?.coverImage"
                  :src="item?.coverImage"
                  :class="{ 'image-hovered': hoveredImageIndex === index }"
                  :style="{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    objectPosition: 'center',
                    transition: 'all 0.3s ease',
                  }"
                />
                <div
                  v-else
                  class="w-full bg-gray-100 flex flex-col items-center justify-center"
                  :style="{ height: cardConfiguration.imgHeight }"
                >
                  <icon-image-close class="bad-img" />
                  <span class="text-xs text-gray-400">暂无资产图片</span>
                </div>
              </div>
            </a-carousel-item>
          </a-carousel>
        </template>
        <a-card-meta class="-mt-3 px-1">
          <template #description>
            <a-tooltip :content="item?.name">
              <div class="text-xs px-2" style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis">
                {{ item?.name }}
              </div>
            </a-tooltip>
            <div class="w-full absolute bottom-10 flex items-center justify-center" @click.stop>
              <div class="w-1/2 bg-red-100 mr-8">
                <a-input-number
                  :min="0"
                  mode="button"
                  :max="item?.availableInventory"
                  :model-value="item.numUdf1"
                  @change="handleSetNum($event, item)"
                >
                </a-input-number>
              </div>
            </div>

            <!--<div v-else class="h-[20px] w-full absolute bottom-[3px] right-0 flex justify-start pl-2">-->
            <!--  <span class="text-xs text-gray-400">{{ '借用数量：' + getBorrowNum(item) }}</span>-->
            <!--</div>-->
          </template>
        </a-card-meta>
      </a-card>
    </div>

    <div class="flex justify-end w-full mt-4 pr-10">
      <a-pagination
        v-model:page-size="paginationConfig.pageSize"
        v-model:current="paginationConfig.page"
        :total="total || 0"
        @change="loadRecourse"
      />
    </div>
  </div>

  <!--<borrowListViewModal
    v-model="listVisible"
    :form-data="formData"
    :selected-items="selectedItems"
    @flush="loadRecourse"
  />-->
</template>

<style scoped lang="scss">
  .pagination-center {
    position: absolute;
    bottom: 10px;
    right: 20px;
  }
  .image-hovered {
    filter: brightness(1.1) contrast(1.1) saturate(1.1);
  }
  .bad-img {
    width: 50px;
    height: 50px;
  }
  .card-container {
    margin: 0 auto;
    max-width: 1200px; /* 设置一个合适的最大宽度 */
  }
</style>
