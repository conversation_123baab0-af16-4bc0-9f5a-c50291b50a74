<script setup lang="ts">
  import { computed, onMounted, ref, watch } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { CrudTable, TableAction } from '@repo/ui/components/table';
  import { ModalForm } from '@repo/ui/components/form';
  import { Message, Modal } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { getClientRole, PROJECT_URLS } from '@repo/env-config';
  import { usePrompt } from '@repo/ui/components';
  import RecordDetail from '@repo/ui/components/record-detail/index.vue';
  import RecycleBinButton from '@repo/ui/components/table/recycleBinButton.vue';
  import { useUserStore } from '@repo/infrastructure/store';
  import ViewRecourseModal from './viewRecourseModal.vue';
  import borrowListViewModal from './borrowListViewModal.vue';

  const props = defineProps({
    modulePath: {
      type: String,
    },
  });

  const editVisible = ref(false);
  const currentEditRow = ref<any>({});
  const schema = ref<any>(null);
  const tableRef = ref<any>(null);
  const { prompt } = usePrompt();

  const handleShowEdit = (raw?: any) => {
    editVisible.value = true;
    currentEditRow.value = raw || {};
  };

  const isTeacherClient = computed(() => {
    const role = getClientRole();
    return role === 'Company';
  });

  const columns = computed(() => {
    if (isTeacherClient.value) {
      return [
        'symbol',
        'status',
        'operator',
        'borrowCount',
        'unreturnedCount',
        'planBorrowDate',
        'borrowDate',
        'lastReturnDate',
        'applyComment',
        'verifyComment',
      ];
    }
    return [
      'symbol',
      'status',
      'operator',
      'cbUserMobile',
      'cbUserBoName',
      'borrowCount',
      'unreturnedCount',
      'planBorrowDate',
      'borrowDate',
      'lastReturnDate',
      'applyComment',
      'verifyComment',
    ];
  });

  const visibleComponents = ['quickSearch', 'refresh', 'layout', 'recycleBin'];

  const dataList = ref();
  const loadDataList = async () => {
    const { data: res } = await request('/asset/assetBorrowApplication', {
      method: 'get',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    dataList.value = res.items;
  };

  const handleRowAction = async (action: any, record?: any) => {
    switch (action.key) {
      case 'add':
      case 'edit':
        if (record?.id && record?.status !== 'NotSubmitted') {
          Modal.info({
            title: '提示',
            content: '已提交的借用申请不允许编辑',
            hideCancel: true,
          });
          return;
        }
        handleShowEdit(record || {});
        break;
      case 'submit':
      case 'withdraw':
        // eslint-disable-next-line no-case-declarations
        const msg = action.key === 'submit' ? '确认提交该借用申请吗？' : '确认撤回该借用申请吗？';
        // eslint-disable-next-line no-case-declarations
        const status = action.key === 'submit' ? 'Waiting' : 'NotSubmitted';
        Modal.confirm({
          title: '提示',
          content: msg,
          renderToBody: false,
          onOk: async () => {
            await request(`/asset/assetBorrowApplication/changeStatus`, {
              method: 'PUT',
              baseURL: PROJECT_URLS.MAIN_PROJECT_API,
              data: {
                id: record.id,
                udf1: status,
              },
            });
            Message.success(action.key === 'submit' ? '提交成功，请等待审核' : '撤回成功');
            // tableRef.value.loadData({});
            await loadDataList();
          },
        });
        break;
      case 'approve':
      case 'reject':
        // eslint-disable-next-line no-case-declarations
        const remark = await prompt({
          title: '审核备注',
          placeholder: '请输入审核备注',
        });
        if (remark === undefined) {
          Message.error('请输入审核备注');
          return;
        }
        await request(`/asset/assetBorrowApplication/changeStatus`, {
          method: 'PUT',
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          data: {
            id: record.id,
            udf1: action.key === 'approve' ? 'Approved' : 'Rejected',
            udf2: remark,
          },
        });
        Message.success('操作成功');
        tableRef.value.loadData({});
        break;
      case 'outbound':
        Modal.confirm({
          title: '提示',
          renderToBody: false,
          content: '确认出库该资产吗？',
          onOk: async () => {
            await request(`/asset/assetBorrowApplication/changeStatus`, {
              method: 'PUT',
              baseURL: PROJECT_URLS.MAIN_PROJECT_API,
              data: {
                id: record.id,
                udf1: 'Borrowed',
              },
            });
            Message.success('出库成功');
            tableRef.value.loadData({});
          },
        });
        break;
      default:
    }
  };

  const handleShowReason = (reason: string, title: string) => {
    Modal.info({
      title,
      content: reason,
      hideCancel: true,
    });
  };

  const size = 'mini';
  const resourceVisible = ref(false);
  const borrowListVisible = ref(false);
  const bodyStyle = {
    overflow: 'auto',
    height: '100%',
    padding: 0,
  };

  const viewRecourseRef = ref();
  const showList = () => {
    viewRecourseRef.value?.showList();
  };
  const currentRecord = ref();

  const borrowInfo = ref({});
  const btnLoading = ref(false);
  const handleBorrow = async () => {
    resourceVisible.value = true;
  };

  const updateBorrowInfo = (val: any) => {
    borrowInfo.value = val;
  };
  const handleFlush = async () => {
    resourceVisible.value = false;
    await tableRef.value.loadData();
  };

  const handlePreOk = () => {
    borrowListVisible.value = true;
    return false;
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/asset/assetBorrowApplication');
    await loadDataList();
  });

  defineExpose({
    loadData: async (params?: any) => {
      await tableRef.value.loadData(params);
    },
    handleShowEdit,
  });
  const school = computed(() => {
    return window.location.href;
  });
</script>

<template>
  <a-card v-if="schema">
    <template #title>
      <div class="flex justify-between">
        <div class="flex-1">资产借用登记</div>
        <table-action
          v-if="tableRef"
          :schema="schema"
          :table="tableRef"
          :module-path="modulePath"
          component-size="mini"
          :visible-components="visibleComponents"
          @row-action="handleRowAction"
        >
          <template #extra-actions>
            <div class="flex justify-center space-x-2">
              <a-button v-if="isTeacherClient" :size="size" type="primary" @click="handleBorrow">
                <template #icon>
                  <icon-plus />
                </template>
                借用资产
              </a-button>
            </div>
          </template>
        </table-action>
      </div>
    </template>
    <crud-table
      ref="tableRef"
      :schema="schema"
      class="mt-2"
      :default-query-params="{ sort: '-id' }"
      :visible-columns="columns"
      @row-action="handleRowAction"
    >
      <template #custom-column-applyComment="{ record }">
        <div class="text-sm cursor-pointer" @click="() => handleShowReason(record.applyComment, '申请备注')">
          {{ record.applyComment }}
        </div>
      </template>
      <template #custom-column-verifyComment="{ record }">
        <div class="text-sm cursor-pointer" @click="() => handleShowReason(record.verifyComment, '审核备注')">
          {{ record.verifyComment }}
        </div>
      </template>
    </crud-table>
  </a-card>
  <a-modal
    v-if="resourceVisible"
    v-model:visible="resourceVisible"
    fullscreen
    :body-style="bodyStyle"
    :on-before-ok="handlePreOk"
    ok-text="借用"
    :ok-loading="btnLoading"
  >
    <template #title>
      <div class="flex flex-grow items-center w-full justify-between">
        <div class="flex-1 text-center">资产借用</div>
      </div>
    </template>
    <viewRecourseModal v-if="resourceVisible" ref="viewRecourseRef" @borrow-list="updateBorrowInfo" />
    <borrowListViewModal
      ref="viewRecourseRef"
      v-model="borrowListVisible"
      :form-data="borrowInfo"
      @flush="handleFlush"
    />
  </a-modal>
</template>

<script lang="ts">
  export default {
    name: 'AssetBorrowApplication',
  };
</script>

<style scoped lang="scss">
  .image-hovered {
    filter: brightness(1.1) contrast(1.1) saturate(1.1);
  }

  .bad-img {
    width: 50px;
    height: 50px;
  }

  .details-icon {
    width: 20px;
    height: 20px;
    color: whitesmoke;
  }
</style>
