<script setup lang="ts">
  import { computed, nextTick, onBeforeUnmount, onMounted, onUnmounted, PropType, ref, watch } from 'vue';
  import { debounce, groupBy } from 'lodash';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    modelValue: {
      type: Array as PropType<any[]>,
    },
  });

  const emit = defineEmits(['update:modelValue']);
  const rawSearchList = ref<Record<string, any>>({});
  const searchList = ref<Record<string, any>>({});
  const { loading: searchLoading, setLoading: setSearchLoading } = useLoading();

  const assetOptions = ref([]);

  const handleSearch = debounce(async (keyword: string) => {
    if (!keyword) {
      searchList.value = {};
      return;
    }
    setSearchLoading(true);
    await request('/asset/assetInfo', {
      params: {
        filters: JSON.stringify([{ property: 'name|symbol|propertyType|tag', operator: 'Like', value: keyword }]),
        pageSize: 99,
      },
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    }).then((res) => {
      assetOptions.value.unshift(...res.data.items);

      setSearchLoading(false);
    });
    // rawSearchList.value = {
    //   ...rawSearchList.value,
    //   ...data.items.reduce((prev, item) => {
    //     prev[item.id] = item;
    //     return prev;
    //   }, {}),
    // };
    // searchList.value = groupBy(data.items, (item) => item.propertyType);
  }, 500);

  const details = ref(props.modelValue || []);

  const handleAddItem = (item: any) => {
    const exist = details.value.find((d) => d.id === item.id);
    if (exist) {
      if (exist.numUdf1 + 1 > item.availableInventory) {
        Message.error(`${item.name}库存不足，当前可借数量为${item.availableInventory}`);
        return;
      }
      exist.numUdf1 += 1;
    } else {
      if (item.availableInventory < 1) {
        Message.error(`${item.name}库存不足，当前可借数量为${item.availableInventory}`);
        return;
      }
      details.value.push({ id: item.id, name: `${item.propertyType} | ${item.name}`, numUdf1: 1 });
    }
  };

  const handleNumChange = (record: any, num: number) => {
    const item = rawSearchList.value[record.id] || {};
    if (num > item.availableInventory) {
      Message.error(`${item.name}库存不足，当前可借数量为${item.availableInventory}`);
      record.numUdf1 = item.availableInventory;
    } else {
      record.numUdf1 = num;
    }
  };

  const handleRemoveItem = (record: any) => {
    details.value = details.value.filter((d) => d.id !== record.id);
  };

  const assetOptionsInit = async () => {
    const store = await request('/asset/assetInfo', {
      params: {
        pageSize: 80,
      },
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    assetOptions.value = store.data.items;
  };

  onMounted(async () => {
    await handleSearch('');
    await assetOptionsInit();
  });

  watch(
    () => details.value,
    (val) => {
      emit('update:modelValue', val);
    },
    { deep: true, immediate: true },
  );
</script>

<template>
  <div class="w-full">
    <a-select
      allow-search
      allow-clear
      placeholder="输入资产名称、编码等进行搜索"
      :loading="searchLoading"
      :filter-option="false"
      @search="handleSearch"
    >
      <!-- <a-optgroup v-for="(items, key) in searchList" :key="key" :label="key">
        <a-option v-for="item in items" :key="item.id" :value="item.id" @click="() => handleAddItem(item)">
          {{ item.propertyType }} | {{ item.name }} <span v-if="item.tag"> [{{ item.tag }}] </span> 可用:
          {{ item.availableInventory }}
          {{ item.unit }}
        </a-option>
      </a-optgroup>-->
      <a-optgroup v-for="(items, key) in assetOptions" :key="key" :label="key">
        <a-option :key="items.id" :value="items.name" @click="() => handleAddItem(items)">
          {{ items?.propertyType }} | {{ items.name }} <span v-if="items.tag"> [{{ items.tag }}] </span> 可用:
          {{ items?.availableInventory }}
        </a-option>
      </a-optgroup>
    </a-select>

    <a-table class="mt-2 w-full" :data="details">
      <template #columns>
        <a-table-column title="序号" :width="60" align="center">
          <template #cell="{ rowIndex }">
            {{ rowIndex + 1 }}
          </template>
        </a-table-column>
        <a-table-column title="资产" data-index="name" />
        <a-table-column title="数量" data-index="numUdf1">
          <template #cell="{ record }">
            <a-input-number
              class="w-32"
              :model-value="record.numUdf1"
              :min="0"
              :max="rawSearchList[record.id]?.availableInventory || 99"
              :step="1"
              size="mini"
              mode="button"
              @update:model-value="(val) => handleNumChange(record, val)"
            />
          </template>
        </a-table-column>
        <a-table-column title="操作" :width="100">
          <template #cell="{ record }">
            <a-button type="primary" status="danger" size="mini" @click="() => handleRemoveItem(record)">
              删除
            </a-button>
          </template>
        </a-table-column>
      </template>
    </a-table>
  </div>
</template>

<style scoped lang="scss"></style>
