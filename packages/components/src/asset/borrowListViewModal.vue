<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    selectedItems: {
      type: Object,
    },
    formData: {
      type: Object,
    },
  });
  const emit = defineEmits(['update:modelValue', 'flush']);
  const modelValue = computed({
    get: () => props.modelValue,
    set: (val) => {
      emit('update:modelValue', val);
    },
  });
  const selectedItems = ref(props.selectedItems || {});
  // const formData = ref<any>({ ...props.formData } || { details: [] });
  const formData = computed({
    get: () => {
      return props.formData;
    },
    set: (val) => {},
  });

  const size = 'mini';
  const formRef = ref();
  const btnLoading = ref(false);

  const handleDelList = (record: any) => {
    selectedItems[record?.id] = false;
    formData.value.details = formData.value.details.filter((item: any) => item.id !== record.id);
  };

  const handlePreOk = async () => {
    const valid = await formRef.value?.validate();
    if (valid === undefined || Object.keys(valid).length === 0) {
      try {
        const notSetCountItems = formData.value.details.filter((item: any) => {
          return !item?.numUdf1;
        });
        if (notSetCountItems.length > 0) {
          Message.warning('请完善借阅数量');
          return false;
        }
        btnLoading.value = true;
        await request('/asset/assetBorrowApplication', {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'post',
          data: formData.value,
        });
        emit('flush');
        Message.success('操作成功');
        modelValue.value = false;
      } finally {
        btnLoading.value = false;
      }
    }
    return true;
  };
</script>

<template>
  <a-modal
    v-if="modelValue"
    v-model:visible="modelValue"
    title="资产借用清单"
    width="60%"
    :on-before-ok="handlePreOk"
    :render-to-body="false"
    :ok-loading="btnLoading"
  >
    <a-form ref="formRef" :model="formData" auto-label-width>
      <a-form-item label="计划借用日期" field="planBorrowDate" :rules="{ required: true, message: '请选择日期' }">
        <a-date-picker v-model="formData.planBorrowDate" :size="size" />
      </a-form-item>
      <a-form-item label="申请备注" field="applyComment" :rules="{ required: false, message: '请填写备注' }">
        <a-input v-model="formData.applyComment" :max-length="255" show-word-limit />
      </a-form-item>
      <a-form-item field="details" :rules="{ required: true, message: '请选择资产' }">
        <a-table :data="formData.details" :bordered="{ cell: true }" class="w-full">
          <template #columns>
            <a-table-column title="序号">
              <template #cell="{ rowIndex }">
                {{ rowIndex + 1 }}
              </template>
            </a-table-column>
            <a-table-column title="名称">
              <template #cell="{ record }">
                {{ record.name }}
              </template>
            </a-table-column>
            <a-table-column title="数量">
              <template #cell="{ record }">
                <a-input-number
                  v-model="record['numUdf1']"
                  mode="button"
                  :placeholder="`当前可用数量${record?.availableInventory}`"
                  :max="record?.availableInventory"
                  :min="1"
                />
              </template>
            </a-table-column>
            <a-table-column title="操作">
              <template #cell="{ record }">
                <a-button type="text" status="danger" :size="size" @click="handleDelList(record)">删除</a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style scoped lang="scss"></style>
