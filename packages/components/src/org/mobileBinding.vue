<script setup lang="ts">
  import { useUserStore } from '@repo/infrastructure/store';
  import { computed, ref } from 'vue';
  import { Message, Modal } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import SendVerifyCode from '../common/sendVerifyCode.vue';

  defineProps({
    visibleWhenCertified: {
      type: Boolean,
      default: false,
    },
  });

  const userStore = useUserStore();
  const { userInfo } = userStore;

  const title = computed(() => {
    if (userInfo.mobile && userInfo.certifiedAt) {
      return '重新认证手机';
    }
    return !userInfo?.mobile ? '绑定手机' : '手机认证';
  });

  const msg = computed(() => {
    return `尚未绑定手机号码，点此绑定`;
  });

  const shortMsg = computed(() => {
    return `绑定手机`;
  });

  const loading = ref(false);

  const visible = ref(false);

  const formData = ref<any>({
    mobile: userInfo.mobile,
    code: '',
  });
  const rules = ref<any>({
    mobile: [
      { required: true, message: '请输入手机号码', trigger: 'blur' },
      {
        match: /^1[3-9]\d{9}$/,
        message: '手机号码格式不正确',
        trigger: 'blur',
      },
    ],
    code: [
      { required: true, message: '请输入验证码', trigger: 'blur' },
      { match: /^\d{6}$/, message: '验证码格式不正确', trigger: 'blur' },
    ],
  });

  const handleSubmit = async () => {
    loading.value = true;

    if (!formData.value.mobile || !formData.value.code) {
      loading.value = false;
      Message.error('请填写完整信息');
      return;
    }

    try {
      await request('/org/session/bindMobile', {
        method: 'PUT',
        data: formData.value,
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });

      userStore.setInfo({
        ...userInfo,
        mobile: formData.value.mobile,
      });

      userInfo.certifiedAt = new Date().toISOString();
      userInfo.mobile = formData.value.mobile;

      Modal.success({
        title: '绑定成功',
        onOk() {
          window.location.reload();
        },
        content:
          '手机号码绑定成功，您可以使用手机号码+密码或验证码登陆，同时可通过右上角帐号切换功能切换相同手机号码下的不同帐号',
      });
      visible.value = false;
    } finally {
      loading.value = false;
    }
  };
</script>

<template>
  <div v-if="!userInfo.mobile || !userInfo.certifiedAt || visibleWhenCertified">
    <div @click.stop="() => (visible = true)">
      <slot :message="msg" :short-msg="shortMsg">
        <a-button v-bind="$attrs">
          <template #icon>
            <IconQuestionCircleFill v-if="userInfo.certifiedAt" class="text-red-500" />
          </template>
          {{ msg }}
        </a-button>
      </slot>
    </div>
    <a-modal
      v-model:visible="visible"
      :title="title"
      simple
      :render-to-body="false"
      :footer="false"
      :mask-closable="false"
    >
      <a-form auto-label-width size="small" :model="formData" :rules="rules" @submit="handleSubmit">
        <a-form-item required label="手机号码" field="mobile">
          <a-input v-model.trim="formData.mobile" />
        </a-form-item>
        <a-form-item required label="验证码" field="code">
          <a-space class="w-full flex-1">
            <a-input v-model.trim="formData.code" />
            <send-verify-code :mobile="formData.mobile" type="UserVerify" />
          </a-space>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button @click="() => (visible = false)">取消</a-button>
            <a-button html-type="submit" type="primary" :loading="loading">确定</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<style scoped lang="scss"></style>
