<script setup lang="ts">
  import { useLoading } from '@repo/infrastructure/hooks';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { computed, inject, onMounted, ref, watch } from 'vue';
  import { useTeacherStore } from '@repo/infrastructure/store';
  import useCommonStore from '@repo/infrastructure/utils/store';

  const { loading: searchLoading, setLoading: setSearchLoading } = useLoading();
  const teachersList = ref<any>([]);
  const filterTeacherList = ref<any>([]);

  const teacherStore = useTeacherStore();
  const router = inject('router');
  const route = router?.currentRoute;

  const storeTeachers = ref([]);
  const useStoreData = ref(false);

  const props = defineProps({
    defaultQueryParams: {
      type: Object,
      default: () => ({}),
    },
    autoLoad: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    showRole: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['change', 'selectAll']);
  const queryParams = ref({});
  const hasQueryParams = computed(() => Object.keys(queryParams.value || {})?.length);

  watch(
    () => props.defaultQueryParams,
    (value) => {
      queryParams.value = value;
    },
    {
      immediate: true,
      deep: true,
    },
  );

  const handleSearchTeacher = async (value) => {
    if (useStoreData.value && !hasQueryParams.value) {
      if (value) {
        teachersList.value = storeTeachers.value.filter((item: any) => item.name.includes(value));
      } else {
        teachersList.value = storeTeachers.value;
      }
      return;
    }
    setSearchLoading(true);
    try {
      const { data } = await request('/org/companyUser/', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          ...queryParams.value,
          'name|mobile|username': `%${value}%`,
        },
      });
      teachersList.value = data.items || [];
      filterTeacherList.value = data.items || [];
    } finally {
      setSearchLoading(false);
    }
  };
  const values = ref();

  const handleSelectTeacher = (teacher) => {
    const { value } = values;
    emit('change', teacher.id, teacher, value, teachersList.value);
  };
  const cleanValue = () => {
    values.value = null;
  };

  const roleSet = ref();
  const getSysRole = (value): string => {
    if (!props.showRole) return '';
    const result = roleSet.value
      .filter((item) => value?.includes(item.id))
      .map((item) => {
        return item.name;
      })
      .join('、');
    return `（${result}）`;
  };

  const schoolOptions = computed(() => {
    const boIdSet = new Set();
    const schools: any = [];
    teachersList.value.forEach((item: any) => {
      const itemBoId = item?.branchOffice.id;
      if (!boIdSet.has(itemBoId)) {
        boIdSet.add(itemBoId);
        schools.push({
          label: item.branchOffice.name,
          value: item.branchOffice.id,
          raw: item.branchOffice,
        });
      }
    });
    return schools;
  });

  const isFilterModule = ref(false);
  const handleChangeFilter = () => {
    isFilterModule.value = !isFilterModule.value;
    if (!isFilterModule.value) {
      filterTeacherList.value = teachersList.value;
    }
  };

  const handleFilterTeacher = (val: number) => {
    values.value = [];
    filterTeacherList.value = teachersList.value.filter((item) => item?.branchOffice.id === val);
  };

  const isSelectAll = ref(false);
  const handleSelect = (val: boolean) => {
    isSelectAll.value = val;
    const ids = filterTeacherList.value.map((item) => item.id);
    if (val) {
      values.value = ids;
    } else {
      values.value = [];
    }
    emit('selectAll', val, ids, filterTeacherList.value);
  };

  const ready = ref(false);
  onMounted(async () => {
    if (props.showRole) {
      const sysRoleStore = useCommonStore({
        api: '/org/sysRole',
      });
      roleSet.value = await sysRoleStore.getList();
    }
    ready.value = true;

    if (props.autoLoad) {
      if (!hasQueryParams.value) {
        const teachers = await teacherStore.getTeachersList(route);
        if (teachers?.length) {
          teachersList.value = teachers;
          storeTeachers.value = teachers;
          useStoreData.value = true;
        } else {
          await handleSearchTeacher('');
        }
      } else {
        await handleSearchTeacher('');
      }
    }
  });

  defineExpose({
    handleSearchTeacher,
    cleanValue,
  });
</script>

<template>
  <div class="flex justify-start space-x-2">
    <a-select
      v-if="isFilterModule"
      :options="schoolOptions || []"
      placeholder="请选择学校"
      size="mini"
      @change="handleFilterTeacher"
    >
    </a-select>
    <a-select
      v-if="ready"
      v-bind="$attrs"
      v-model="values"
      :loading="searchLoading"
      placeholder="请选择或搜索"
      :filter-option="false"
      allow-search
      :multiple="multiple || false"
      :max-tag-count="2"
      @search="handleSearchTeacher"
    >
      <template #header>
        <div v-if="isFilterModule" class="px-2 py-1 flex justify-end">
          <a-checkbox value="1" @change="handleSelect">{{ isSelectAll ? '取消全选' : '全选' }}</a-checkbox>
        </div>
      </template>
      <template #footer>
        <div class="cursor-pointer flex justify-end mr-2">
          <a-button class="" type="text" @click="handleChangeFilter">
            <template #icon>
              <icon-filter />
            </template>
            {{ isFilterModule ? '关闭筛选' : '筛选' }}
          </a-button>
        </div>
      </template>
      <a-option
        v-for="item in filterTeacherList"
        :key="item.id"
        :value="item.id"
        :label="item.name"
        @click="() => handleSelectTeacher(item)"
      >
        {{ item.name }}{{ getSysRole(item.sysRoleSet) }}
      </a-option>
    </a-select>
  </div>
</template>

<style scoped lang="scss"></style>
