<script lang="ts" setup>
  import { useUserStore } from '@repo/infrastructure/store';
  import { computed, inject, onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import AvatarUploader from '@repo/ui/components/upload/avatarUploader.vue';
  import { CrudForm } from '@repo/ui/components';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { Message } from '@arco-design/web-vue';
  import GlobalBgSvg from '../../assets/images/global-bg.svg';
  import MobileDisplay from '../teacher/mobileDisplay.vue';
  import MobileBinding from './mobileBinding.vue';

  const { loading, setLoading } = useLoading();
  const userStore = useUserStore();
  const allUserTitles = ref([]);
  const schema = ref();
  const formData = ref<any>({});
  const visibleColumns = [
    'name',
    // 'mobile',
    'highestEducation',
    'inPosition',
    'title',
    'proTitle',
    'dateOfWorkStarting',
    'dateOfSpecialEduStarting',
    'hasSpecialEducationExp',
    'establishment',
    'teachType',
    'teachGrade',
    'teachingSubjects',
    'itemAttachments',
    'teacherType',
    'idCard',
  ];
  const userInfo = ref<any>(userStore.userInfo);

  const userTitles = computed(() => {
    return allUserTitles.value.filter((item: any) => userStore.userTitleIds?.includes(item.id));
  });

  const handleSaveProfile = async () => {
    setLoading(true);

    if (!formData.value.name) {
      Message.error('姓名不能为空');
      setLoading(false);
      return;
    }

    if (!formData.value.highestEducation) {
      Message.error('最高学历不能为空');
      setLoading(false);
      return;
    }

    if (!formData.value.proTitle) {
      Message.error('职称等级不能为空');
      setLoading(false);
      return;
    }

    try {
      await request('/org/session/profile', {
        method: 'PUT',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: {
          ...formData.value,
        },
      });

      userStore.setInfo({
        ...userStore.userInfo,
        ...formData.value,
      });

      Message.success('保存个人资料成功');
    } finally {
      setLoading(false);
    }
  };

  const router: any = inject('router');

  onMounted(async () => {
    const { data } = await request('/org/userTitle', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        pageSize: 999,
      },
    });
    allUserTitles.value = data.items || [];

    schema.value = await SchemaHelper.getInstanceByDs('/org/companyUser');
    formData.value = {
      ...userStore.userInfo,
    };
  });
</script>

<template>
  <div class="flex gap-2 mt-2">
    <div class="header flex justify-center px-16">
      <div class="bg !bg-white" :style="`background-image: url(${GlobalBgSvg})`" />
      <div class="flex items-center flex-col py-8 content gap-4">
        <avatar-uploader />
        <div class="flex flex-col gap-4 justify-center items-center">
          <div>
            {{ userStore.name }}
          </div>
          <div v-if="userInfo?.mobile" class="flex items-center gap-1">
            <mobile-binding :visible-when-certified="true">
              <a-button size="mini">
                <template #icon>
                  <IconMobile />
                </template>
                <mobile-display :record="userInfo" :raw="userInfo?.mobile" />
              </a-button>
            </mobile-binding>
          </div>
          <div v-else class="flex items-center gap-2">
            <mobile-binding />
          </div>
          <div>
            <IconUserGroup />
            {{ userStore.branchOffice.name }}
          </div>
          <div v-if="userTitles?.length">
            <a-tag color="blue" size="small">{{ userTitles.join('、') }}</a-tag>
          </div>
        </div>
      </div>
    </div>
    <a-card title="个人资料" class="flex-1" size="small">
      <template #extra>
        <a-space>
          <a-button type="primary" size="mini" :loading="loading" @click="handleSaveProfile">
            <template #icon>
              <IconCheck />
            </template>
            保存个人资料
          </a-button>
          <a-button size="mini" @click="() => router.back()">返回</a-button>
        </a-space>
      </template>
      <crud-form
        v-if="schema"
        v-model="formData"
        :visible-columns="visibleColumns"
        :show-actions="false"
        :schema="schema"
      />
    </a-card>
  </div>
</template>

<style scoped lang="scss">
  .header {
    position: relative;
    .bg {
      width: 100%;
      height: 100%;
      background-size: cover;
      background-repeat: no-repeat;
      position: absolute;
      z-index: 1;
      opacity: 0.3;
    }
    .content {
      position: relative;
      z-index: 2;
    }
  }
</style>
