<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import SelectedActions from '@repo/components/common/selectedActions.vue';
  import { inject, onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { Message, Modal } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { ENV, PROJECT_URLS } from '@repo/env-config';
  import { usePrompt } from '@repo/ui/components';
  import { useLoading } from '@repo/infrastructure/hooks';
  import useCommonStore from '@repo/infrastructure/utils/store';
  import { isArray } from 'lodash';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { setToken } from '@repo/infrastructure/auth';

  const props = defineProps({
    moduleName: {
      type: String,
      default: '系统用户',
    },
    queryParams: {
      type: {},
      default: {},
    },
    visibleComponent:{
      type:Array,
      default:['add', 'quickSearch', 'refresh', 'layout', 'recycleBin']
    }
  });

  const schema = ref(<any>null);
  const tableRef = ref<any>(null);
  const menuStore = useUserMenuStore();

  const router: any = inject('router');

  const queryParams: any = {
    sort: ['+validated', '-id'],
    orgNature: menuStore.getCurrentOrgNature(),
    ...props.queryParams,
  };

  const { prompt } = usePrompt();
  const { loading, setLoading } = useLoading();
  const sysRoleStore = useCommonStore({
    api: '/org/sysRole',
    queryParams: {
      unitNature: menuStore.getCurrentUnitNature(),
    },
  });

  const teachersMap = ref<any>({});
  const teacherStore = useCommonStore({
    api: '/org/companyUser/allTeachers',
  });

  const sysRolesOptions = ref<any[]>([]);
  const updateSysRoleVisible = ref(false);
  const selectedRoles = ref<any[]>([]);

  const selectedItems = ref<any[]>([]);

  const teacherToDisplay = (item) => {
    return item.name || teachersMap.value[item.id || item]?.name;
  };

  const handleValidate = async (user) => {
    if (user.validated) {
      return;
    }

    Modal.confirm({
      title: '确认',
      content: '确认要验证该用户吗？',
      onOk: async () => {
        await request(`/org/companyUser/${user.id}`, {
          method: 'PUT',
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          data: {
            ...user,
            validated: true,
          },
        });
        Message.success('验证成功');
        await tableRef.value?.loadData();
      },
    });
  };

  const randomInt = (min, max) => Math.floor(Math.random() * (max - min + 1) + min);

  const handleShowSetSysRole = (users) => {
    if (users.length === 1) {
      selectedRoles.value = users[0].sysRoleSet || [];
    } else {
      selectedRoles.value = [];
    }

    selectedItems.value = users;

    updateSysRoleVisible.value = true;
  };

  const handleResetPassword = async (user) => {
    const newPassword = await prompt({
      title: '重置密码',
      message: '请输入新密码',
      raw: randomInt(100000, 999999).toString(),
      placeholder: '请输入新密码',
      // 6位不为空的任意字符
      inputPattern: /^.{6,}$/,
      inputErrorMessage: '密码长度至少6位',
    });

    if (!newPassword) {
      return;
    }

    Message.info('正在重置密码，请稍后...');

    await request('/org/companyUser/adminResetPassword', {
      method: 'PUT',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      data: {
        id: user.id,
        name: newPassword?.trim(),
      },
    });

    Modal.info({
      title: '重置密码成功',
      content: `${user.name}的新密码为：${newPassword}，请提醒用户尽快修改`,
      hideCancel: true,
      okText: '知道了',
    });
  };

  const refresh = async()=>{
    await tableRef.value?.loadData();
  }
  const handleBatchUpdateRoles = async () => {
    setLoading(true);
    const roleIds = selectedRoles.value;
    const userIds = selectedItems.value.map((user) => user.id || user);

    try {
      await request(`/org/companyUser/batchUpdateRole/${roleIds}`, {
        method: 'PUT',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: userIds,
      });

      Message.success('更新成功');
      updateSysRoleVisible.value = false;
      await tableRef.value?.loadData();
    } finally {
      setLoading(false);
    }
  };
  const handleUnfrozen =async (val:any)=>{
    if(!Array.isArray(val)){
      Message.warning('请先选择需要解冻的账号')
      return;
    }
    await request(`/org/companyUser/batchUnFrozen`, {
      method: 'PUT',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      data: val,
    });
    Message.success('操作成功')
    await tableRef.value?.loadData();
  }

  const handleRowAction = async (action, row) => {
    switch (action.key) {
      case 'validate':
        await handleValidate(row);
        break;
      case 'resetPassword':
        await handleResetPassword(row);
        break;
      case 'setSysRole':
        // eslint-disable-next-line no-case-declarations
        const rows = isArray(row) ? row : [row];
        selectedItems.value = rows;
        handleShowSetSysRole(rows);
        break;
      case 'unFrozen':
        await handleUnfrozen(row)
        break;
      case 'mockUser':
        Modal.confirm({
          title: '请确认',
          content: '确认要模拟该用户身份登录吗？（如该用户已登陆，其登陆状态将被挤下线）',
          onOk: async () => {
            const { data } = await request(`/org/companyUser/mock/${row.id}`, {
              method: 'PUT',
              baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            });

            if (data) {
              setToken(data);
              window.location.href = `${PROJECT_URLS.MAIN_PROJECT}?token=${data}`;
            }
          },
        });
      case 'disable':
        break;
      default:
        break;
    }
  };

  const handleFormOk = (item, isEdit: boolean) => {
    if (!isEdit) {
      Modal.info({
        title: '创建成功',
        content: '默认密码为 `用户名 + 123456`， 请提醒用户尽快修改密码',
        okText: '知道了',
      });
    }
  };

  const allSysRoleStore = useCommonStore({
    api: '/org/sysRole',
  });

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/org/companyUser');

    [sysRolesOptions.value, teachersMap.value] = await Promise.all([
      sysRoleStore.getOptions(),
      teacherStore.getMap(),
      allSysRoleStore.getList(),
    ]);
  });
  defineExpose({refresh})
</script>

<template>
  <table-with-modal-form
    v-if="schema"
    ref="tableRef"
    :module-name="moduleName"
    :schema="schema"
    :visible-component="visibleComponent"
    :default-query-params="queryParams"
    :modal-form-property="{ fullscreen: true }"
    :visible-columns="['name','username', 'branchOffice', 'sysRoleSet', 'validated', 'lastLoginAt', 'sendTeacher', 'unitInfo','frozen']"
    @row-action="handleRowAction"
    @form-ok="handleFormOk"
  >
    <template #supplementary-button>
      <slot name="supplementary-button"></slot>
    </template>
  </table-with-modal-form>

  <selected-actions
    v-if="updateSysRoleVisible"
    v-model="updateSysRoleVisible"
    :ok-loading="loading"
    title="批量更新教师岗位"
    :submit-value="selectedRoles"
    :selected-items="selectedItems"
    :to-display="teacherToDisplay"
    @submit="handleBatchUpdateRoles"
  >
    <template #selected-items-title>将被更新的教师：</template>
    <div class="flex gap-2">
      <div class="w-24">岗位：</div>
      <a-select v-model="selectedRoles" size="small" :multiple="false" :options="sysRolesOptions" />
    </div>
  </selected-actions>
</template>

<style scoped lang="scss"></style>
