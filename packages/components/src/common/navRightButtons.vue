<script lang="ts" setup>
  import { AvatarDisplay } from '@repo/ui/components/data-display/components';
  import { useFullscreen } from '@vueuse/core';
  import { inject, onMounted, PropType, ref } from 'vue';
  import '@repo/infrastructure/iconfont-online-css';
  import { useNotificationStore, useUserStore } from '@repo/infrastructure/store';
  import { PROJECT_URLS } from '@repo/env-config';
  import { getToken } from '@repo/infrastructure/auth';
  import { Iconfont } from '@repo/infrastructure/ui';
  import { useWebSocket } from '@repo/infrastructure/hooks';
  import { Message, Modal } from '@arco-design/web-vue';
  import {
    clientRedirectTo,
    isElectron,
    handleTriggerDevTools,
    setClientToken,
    getElectronApi,
  } from '@repo/infrastructure/electron';
  // import newMessageSound from '../../assets/sound/newMessage.wav';
  import NotificationPopover from './navRight/notificationPopover.vue';
  import SwitchCompany from './navRight/switchCompany.vue';
  import Ai from './ai.vue';
  import ExportsResultDownload from './navRight/exportsResultDownload.vue';
  import PasswordResetModal from './navRight/passwordResetModal.vue';
  import PasswordForceResetModal from './navRight/passwordForceResetModal.vue';
  import bindMobileModal from './navRight/bindMobileModal.vue';
  import ConsultingSwitchButton from './navRight/consultingSwitchButton.vue';
  import ToDoItems from './navRight/toDoItems.vue';
  import AccountSwitchModal from './accountSwitchModal.vue';

  const props = defineProps({
    showUserAvatar: {
      type: Boolean,
      default: true,
    },
    client: {
      type: String as PropType<'admin' | 'teacher'>,
      default: 'admin',
    },
  });

  const router: any = inject('router');
  const notificationStore = useNotificationStore();
  const userStore = useUserStore();
  const { isFullscreen, toggle: toggleFullScreen } = useFullscreen();
  const ws = useWebSocket();
  const unreadMessageCount = ref<any>(0);

  const switchCompanyVisible = ref(false);
  const aiChatVisible = ref(false);
  const exportDownloadVisible = ref(false);
  const passwordResetVisible = ref(false);
  const toDoItemsVisible = ref(false);
  const accountSwitchVisible = ref(false);

  const emit = defineEmits(['logout']);

  const handleGoEarlyWarning = () => {
    window.open(PROJECT_URLS.GIANT_SCREEN + getToken());
  };

  const handleGoGiantScreen = () => {
    window.open(`${PROJECT_URLS.GO_PROJECT}/#/giantScreen/statistics`);
  };

  const handleSwitchClient = (client) => {
    if (isElectron) {
      clientRedirectTo(client, getToken() as any);
    } else {
      window.location.href = client === 'admin' ? userStore.getGoProjectUrl() : userStore.getMainProjectUrl();
    }
  };

  const handleLogout = async (logoutTo?: string) => {
    Modal.confirm({
      title: '请确认',
      content: '确定要退出登录吗？',
      onOk: async () => {
        await userStore.logout();
        const { currentRoute } = router;
        Message.success('登出成功');
        if (isElectron) {
          setClientToken('');
          getElectronApi().send('logout');
        } else {
          router.push({
            name: logoutTo && true ? logoutTo : 'login',
            query: {
              ...currentRoute.query,
              redirect: currentRoute.name as string,
            },
          });
        }
      },
    });
  };

  const handleShowSwitchCompany = () => {
    switchCompanyVisible.value = true;
  };

  const handleGoProfile = async () => {
    switch (props.client) {
      case 'admin':
        await router.push('/manage/userProfile');
        break;
      case 'teacher':
        await router.push('/teacher/profile');
        break;
      default:
        break;
    }
  };

  const handleGoOrgProfile = async () => {
    await router.push('/manage/orgProfile');
  };

  const handleGoSystemSetting = async () => {
    await router.push('/manage/system/org/user');
  };

  const handleOpenManual = () => {
    window.open('https://zvtrrnopt3x.feishu.cn/docx/NIjadO9ihopY9uxwGSRc2ZSinuc');
  };

  onMounted(async () => {
    await ws.init();
    ws.onConversationMessage(async () => {
      await notificationStore.refresh();
      unreadMessageCount.value = notificationStore.unreadConversation || 0;
      // if (notificationStore.unreadConversation && notificationStore.unreadConversation > 0) {
      //   const sound = new Audio(newMessageSound);
      //   await sound.play();
      // }
    });
  });
</script>

<template>
  <div class="navbar-actions h-full flex gap-4 items-center">
    <slot name="prepend"></slot>
    <a-tooltip content="系统设置">
      <!--v-if="userStore?.userInfo?.companyList?.length > 1"-->
      <a-button size="small" shape="circle" class="nav-btn" type="outline" @click="handleGoSystemSetting">
        <template #icon>
          <icon-settings />
        </template>
      </a-button>
    </a-tooltip>
    <a-tooltip v-if="userStore?.userInfo?.companyList?.length > 1" content="切换单位">
      <a-button size="small" shape="circle" class="nav-btn" type="outline" @click="handleShowSwitchCompany">
        <template #icon>
          <IconSwap />
        </template>
      </a-button>
    </a-tooltip>
    <a-tooltip v-if="client === 'admin' && userStore.isAuthorized('teacherAbility:teacherClient')" content="进入教师端">
      <a-button size="small" shape="circle" class="nav-btn" type="outline" @click="() => handleSwitchClient('teacher')">
        <template #icon>
          <iconfont name="qiehuanyonghu" />
        </template>
      </a-button>
    </a-tooltip>
    <a-tooltip
      v-else-if="client === 'teacher' && userStore.isAuthorized('adminAbility:managerClient')"
      content="进入管理端"
    >
      <a-button size="small" shape="circle" class="nav-btn" type="outline" @click="() => handleSwitchClient('admin')">
        <template #icon>
          <iconfont name="qiehuanyonghu" />
        </template>
      </a-button>
    </a-tooltip>
    <notification-popover />
    <a-tooltip content="待办事项">
      <a-button size="small" shape="circle" class="nav-btn" type="outline" @click="() => (toDoItemsVisible = true)">
        <template #icon>
          <icon-notification />
        </template>
      </a-button>
    </a-tooltip>
    <a-tooltip content="牧云融教AI">
      <a-button size="small" shape="circle" class="nav-btn" type="outline" @click="() => (aiChatVisible = true)">
        <template #icon>
          <iconfont name="rengongzhinengdanao" />
        </template>
      </a-button>
    </a-tooltip>
    <a-tooltip content="导出结果下载">
      <a-button
        size="small"
        shape="circle"
        class="nav-btn"
        type="outline"
        @click="() => (exportDownloadVisible = true)"
      >
        <template #icon>
          <IconDownload />
        </template>
      </a-button>
    </a-tooltip>
    <a-tooltip v-if="userStore.isAuthorized('adminAbility:giantScreen')" content="数据大屏">
      <a-button size="small" shape="circle" class="nav-btn" type="outline" @click="() => handleGoGiantScreen()">
        <template #icon>
          <iconfont name="shuju" />
        </template>
      </a-button>
    </a-tooltip>
    <a-tooltip v-if="userStore.isAuthorized('adminAbility:giantScreen')" content="预警系统">
      <a-button size="small" shape="circle" class="nav-btn" type="outline" @click="() => handleGoEarlyWarning()">
        <template #icon>
          <icon-exclamation-circle-fill />
        </template>
      </a-button>
    </a-tooltip>
    <a-tooltip v-if="false" content="使用手册">
      <a-button size="small" shape="circle" class="nav-btn" type="outline" @click="handleOpenManual">
        <template #icon>
          <IconQuestion />
        </template>
      </a-button>
    </a-tooltip>
    <slot name="append"></slot>
    <a-tooltip :content="isFullscreen ? '退出全屏' : '全屏'">
      <a-button size="small" :shape="'circle'" class="nav-btn" type="outline" @click="toggleFullScreen">
        <template #icon>
          <icon-fullscreen-exit v-if="isFullscreen" />
          <icon-fullscreen v-else />
        </template>
      </a-button>
    </a-tooltip>
    <consulting-switch-button />
    <div>
      <a-dropdown trigger="click">
        <avatar-display :user-info="userStore.userInfo" @click="handleTriggerDevTools" />
        <template #content>
          <a-doption>
            <a-space @click="handleGoProfile">
              <icon-user />
              <span> 个人资料 </span>
            </a-space>
          </a-doption>
          <a-doption>
            <a-space
              v-if="client === 'admin' && userStore.isAuthorized('adminAbility:orgProfile')"
              @click="handleGoOrgProfile"
            >
              <icon-home />
              <span> 单位资料 </span>
            </a-space>
          </a-doption>
          <a-divider :margin="2" />
          <a-doption v-if="userStore.userInfo.certifiedAt && userStore.userInfo.mobile">
            <a-space @click="() => (accountSwitchVisible = true)">
              <IconSwap />
              <span> 帐号切换 </span>
            </a-space>
          </a-doption>
          <a-doption>
            <a-space @click="() => (passwordResetVisible = true)">
              <IconEdit />
              <span> 修改密码 </span>
            </a-space>
          </a-doption>
          <a-doption>
            <a-space @click="handleLogout">
              <icon-export />
              <span> 退出登录 </span>
            </a-space>
          </a-doption>
        </template>
      </a-dropdown>
    </div>

    <switch-company v-if="switchCompanyVisible" v-model="switchCompanyVisible" />
    <ai v-if="aiChatVisible" v-model="aiChatVisible" />
    <exports-result-download v-if="exportDownloadVisible" v-model="exportDownloadVisible" />
    <password-reset-modal v-if="passwordResetVisible" v-model:visible="passwordResetVisible" />
    <password-force-reset-modal @logout="handleLogout" />
    <bindMobileModal @logout="handleLogout" />
    <to-do-items
      v-if="toDoItemsVisible"
      v-model="toDoItemsVisible"
      :visible="toDoItemsVisible"
      :user-store="userStore"
    />
    <account-switch-modal v-if="accountSwitchVisible" v-model:visible="accountSwitchVisible" />
  </div>
</template>

<style lang="less" scoped>
  .navbar-actions {
    :deep(.nav-btn) {
      color: rgb(var(--gray-8));
      font-size: 16px;
      border-color: rgb(var(--gray-2));
      background: #fff;
      &:hover {
        color: rgb(var(--gray-8));
        background: rgb(var(--gray-1));
        border-color: rgb(var(--gray-5));
      }
    }
  }
  .gap-4 {
    gap: 0.5rem;
  }
</style>
