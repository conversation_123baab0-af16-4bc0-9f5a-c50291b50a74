<script setup lang="ts">
  import { computed } from 'vue';
  import StudentSelect from '../../../student/studentSelect.vue';
  import StudentDetailButton from '../../../student/studentDetailButton.vue';

  const props = defineProps({
    annotation: {
      type: Object,
      required: true,
    },
  });

  const emit = defineEmits(['update:annotation']);
  const students = computed({
    get: () => props.annotation.relatedData?.student || [],
    set: (value) => {
      emit('update:annotation', {
        ...props.annotation,
        relatedData: {
          ...props.annotation.relatedData,
          student: value,
        },
      });
    },
  });

  const handleStudentSelected = (id, student) => {
    if (students.value.some((s) => s.id === id)) {
      return;
    }
    students.value = [...students.value, student];
  };

  const handleStudentRemoved = (id) => {
    students.value = students.value.filter((student) => student.id !== id);
  };
</script>

<template>
  <div class="flex gap-2 flex-wrap">
    <student-detail-button
      v-for="student in students"
      :key="student.id"
      :raw="student"
      :eager="true"
      closable
      @remove="handleStudentRemoved"
      @close="() => handleStudentRemoved(student.id)"
    />
    <div>
      <student-select class="w-36" size="mini" @change="handleStudentSelected" />
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
