<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { extend, merge } from 'lodash';
  import { MyAvatar } from '@repo/ui/components/data-display';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import AnnotationStudentRelated from './relation/annotationStudentRelated.vue';

  const props = defineProps({
    category: {
      type: String,
      required: true,
    },
    annotationModuleSource: {
      type: Object,
      required: true,
    },
    annotation: {
      type: Object,
    },
    relatedModules: {
      type: Array,
      default: () => [],
    },
  });

  const visible = ref(false);
  const emit = defineEmits(['update:annotation', 'createRange']);
  const item = ref<any>(null);
  const selection = ref<any>(null);

  const title = computed(() => {
    return props.annotation?.id ? `编辑${props.category}` : `添加${props.category}`;
  });

  const handleShowModal = () => {
    visible.value = true;
  };

  const handleOpen = () => {
    selection.value = window.getSelection()?.getRangeAt(0);
    item.value = merge(
      {
        relatedData: {
          student: [],
        },
      },
      props.annotation || {},
    );
  };

  const handleClose = () => {
    visible.value = false;
    item.value = null;
  };

  const handleBeforeOk = async () => {
    if (!item.value.content) {
      Message.error('请输入内容');
      return false;
    }

    const callback = async (section?: any) => {
      const url = `/document/selectedTextAnnotation${item.value.id ? `/${item.value.id}` : ''}`;
      const postData = {
        ...item.value,
        module: props.annotationModuleSource.module,
        sourceId: props.annotationModuleSource.sourceId,
        paragraphId: props.annotationModuleSource.paragraphId,
      };

      if (!item.value.id) {
        postData.section = section;
      }

      const { data } = await request(url, {
        method: item.value.id ? 'PUT' : 'POST',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: postData,
      });

      Message.success('保存成功');
      emit('update:annotation', data);

      handleClose();

      return data;
    };
    try {
      if (item.value.id) {
        await callback();
      } else {
        emit('createRange', selection.value, callback);
      }
      return true;
    } catch (e: any) {
      console.error(e);
      return false;
    }
  };

  watch(
    () => props.annotation,
    (newVal) => {
      item.value = merge(
        {
          relatedData: {
            student: [],
          },
        },
        newVal || {},
      );
    },
    { immediate: true, deep: true },
  );
</script>

<template>
  <div class="cursor-pointer" @click.stop="handleShowModal">
    <slot>
      <a-button size="small">
        <template #icon>
          <IconPlus />
        </template>
        {{ title }}
      </a-button>
    </slot>
  </div>
  <a-modal
    v-model:visible="visible"
    :mask-closable="false"
    :esc-to-close="false"
    simple
    :on-before-ok="handleBeforeOk"
    :title="title"
    @open="handleOpen"
    @close="handleClose"
  >
    <div v-if="item" class="flex gap-2 items-start">
      <div><my-avatar :size="40" /></div>
      <div class="flex-1">
        <a-textarea v-model.trim="item.content" show-word-limit placeholder="请在此输入" />

        <annotation-student-related
          v-if="visible && relatedModules.includes('student') && item.relatedData.student"
          v-model:annotation="item"
          class="mt-2"
        />
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
