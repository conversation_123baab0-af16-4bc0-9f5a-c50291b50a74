<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import { AvatarDisplay, DateDisplay } from '@repo/ui/components/data-display/components';
  import { useUserStore } from '@repo/infrastructure/store';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message, Modal } from '@arco-design/web-vue';
  import { AnnotationItem, AnnotationModuleSource } from '../../utils/annotationBlock';
  import AnnotationModal from './annotationModal.vue';
  import StudentDetailButton from '../../student/studentDetailButton.vue';

  const props = defineProps({
    annotations: {
      type: Array as PropType<AnnotationItem[]>,
      default: () => [],
    },
    annotationModule: {
      type: Object as PropType<AnnotationModuleSource>,
      required: true,
    },
    annotationCategory: {
      type: String,
      default: '想法',
    },
    relatedModules: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    fixed: {
      type: Boolean,
      default: true,
    },
  });

  const emit = defineEmits(['update:annotations']);
  const items = computed({
    get: () => {
      return props.annotations;
    },
    set: (value) => emit('update:annotations', value),
  });

  const hasRelatedData = (annotation: AnnotationItem) => {
    let res = false;
    props.relatedModules.forEach((module) => {
      if (annotation.relatedData[module]?.length) {
        res = true;
      }
    });

    return res;
  };

  const userStore = useUserStore();
  const { userInfo } = userStore;

  const collapsed = ref(false);
  const replyContent = ref('');
  const replySubmitting = ref(false);

  const handleToggle = () => {
    collapsed.value = !collapsed.value;
  };

  const handleDelete = async (id: number) => {
    await request(`/document/selectedTextAnnotation/${id}`, {
      method: 'DELETE',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    items.value = items.value.filter((item) => item.id !== id);

    Message.success('删除成功');
  };

  const modulesMap = {
    student: '学生',
  };

  const handleCreateReply = async (annotation, replyId?: string) => {
    replySubmitting.value = true;
    try {
      const { data } = await request(`/document/selectedTextAnnotation/reply/${annotation.id}`, {
        method: 'POST',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: {
          name: replyContent.value,
        },
        params: {
          replyId,
        },
      });

      annotation.repling = false;
      replyContent.value = '';

      if (!replyId) {
        annotation.replies = [...(annotation.replies || []), data];
      } else {
        const { replies } = annotation;
        replies.splice(replies.findIndex((item) => item.id === replyId) + 1, 0, data);
        annotation.replies = replies;
      }

      annotation.showReplies = true;
    } finally {
      replySubmitting.value = false;
    }
  };

  const handleCancelReply = (annotation) => {
    replyContent.value = '';
    annotation.repling = false;
  };

  const handleDeleteReply = async (annotation, replyId) => {
    Modal.confirm({
      title: '删除回复',
      content: '确定要删除这个回复吗？',
      onOk: async () => {
        await request(`/document/selectedTextAnnotation/reply/${annotation.id}`, {
          method: 'DELETE',
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          params: {
            replyId,
          },
        });

        annotation.replies = annotation.replies.filter((item) => item.uuid !== replyId);
      },
    });
  };

  const handleToggleAnnotationRepling = (annotation) => {
    items.value.forEach((item) => {
      if (item.id !== annotation.id) {
        item.repling = false;
      }
    });
    annotation.repling = !annotation.repling;
  };
</script>

<template>
  <div class="relative w-0 right-0 annotations-wrapper" :class="{ 'collapsed': collapsed, 'fixed-wrapper': fixed }">
    <a-button size="mini" type="text" class="toggle-button" :class="fixed ? 'fixed' : 'absolute'" @click="handleToggle">
      <template #icon>
        <IconDoubleLeft v-if="collapsed" />
        <IconDoubleRight v-else />
      </template>
      {{ collapsed ? '展开' : '收起' }}
    </a-button>
    <a-card
      size="small"
      class="annotations-card"
      :title="annotationCategory"
      :class="{ collapsed: collapsed, fixed: fixed }"
    >
      <div v-if="!annotations?.length" class="flex flex-col justify-center gap-4 items-center">
        <a-empty />
        <!--        <a-button type="primary" size="mini">-->
        <!--          <template #icon>-->
        <!--            <IconPlus />-->
        <!--          </template>-->
        <!--          添加新的{{ annotationCategory }}-->
        <!--        </a-button>-->
      </div>
      <div
        v-for="(annotation, idx) in items"
        :key="idx"
        class="mb-2 rounded-lg border border-slate-200 p-2 annotation-item"
      >
        <div class="border-l-4 border-l-slate-200 pl-2 text-xs text-gray-400"> {{ annotation?.section?.text }} </div>
        <div class="mt-2 flex gap-2 items-start">
          <div>
            <avatar-display :user-info="annotation.createdBy" :user-card="true" />
          </div>
          <div class="flex-1">
            <div class="flex justify-between items-center h-4">
              <a-space class="text-xs items-end">
                {{ annotation.createdBy?.name }}
                <small class="text-gray-400">
                  <date-display :raw="annotation.createdDate" :humanize-date="true" />
                </small>

                <a-popover v-if="relatedModules?.length && hasRelatedData(annotation)" class="w-64">
                  <span class="cursor-pointer ml-2r">@相关</span>
                  <template #content>
                    <div class="flex flex-col gap-2">
                      <div v-for="module in relatedModules" :key="module">
                        <div class="text-sm">@相关{{ modulesMap[module] }}</div>
                        <div v-if="module === 'student'" class="flex mt-2 gap-2">
                          <student-detail-button
                            v-for="student in annotation.relatedData[module]"
                            :key="student.id"
                            :raw="student"
                            :eager="true"
                          />
                        </div>
                        <a-empty v-if="!annotation.relatedData[module]?.length" />
                      </div>
                    </div>
                  </template>
                </a-popover>
              </a-space>
              <a-space v-if="userInfo.id === annotation.createdBy?.id" class="annotation-actions items-center">
                <annotation-modal
                  :annotation-module-source="annotationModule"
                  :category="annotationCategory"
                  :annotation="annotation"
                  :related-modules="relatedModules"
                  @update:annotation="(item) => (items[idx] = item)"
                >
                  <IconEdit />
                </annotation-modal>
                <a-popconfirm
                  :content="`确定要删除这个${annotationCategory}吗?`"
                  @ok="() => handleDelete(annotation.id)"
                >
                  <IconDelete class="cursor-pointer" />
                </a-popconfirm>
              </a-space>
            </div>

            <div class="mt-1 text-sm">
              <div>{{ annotation.content }}</div>
              <div
                v-if="annotation.replies?.length"
                class="cursor-pointer mt-1 text-xs text-gray-400 flex justify-between"
                @click="() => (annotation.showReplies = !annotation.showReplies)"
                >{{ annotation.showReplies ? '收起' : '查看' }} {{ annotation.replies?.length }} 条回复</div
              >

              <div v-if="annotation.showReplies">
                <div v-for="reply in annotation.replies" :key="reply.id" class="mt-2 flex items-start gap-2">
                  <div>
                    <avatar-display :user-info="reply.user" :size="24" user-card />
                  </div>
                  <div class="flex-1">
                    <a-space class="items-center justify-between w-full">
                      <a-space class="text-xs items-end">
                        {{ reply.user?.name }}
                        <small class="text-gray-400">
                          <date-display :raw="reply.createTime" :humanize-date="true" />：
                        </small>
                      </a-space>
                      <a-dropdown v-if="reply.user?.id === userInfo.id">
                        <div class="cursor-pointer text-sm">
                          <IconMore />
                        </div>
                        <template #content>
                          <a-doption @click="() => handleDeleteReply(annotation, reply.uuid)"> 删除 </a-doption>
                        </template>
                      </a-dropdown>
                    </a-space>
                    <div class="text-xs">{{ reply.content }}</div>
                  </div>
                </div>
              </div>
              <div class="mt-2 text-xs text-gray-400 flex justify-between">
                <div
                  :class="{ 'reply-btn': !annotation.repling }"
                  class="cursor-pointer"
                  @click="() => handleToggleAnnotationRepling(annotation)"
                >
                  {{ annotation.repling ? '取消回复' : '新增回复' }}
                </div>
              </div>
            </div>

            <div v-if="annotation.repling">
              <a-divider :margin="8" />
              <a-textarea
                v-model.trim="replyContent"
                :disabled="replySubmitting"
                size="mini"
                class="mt-1"
                placeholder="请在此输入回复，按回车提交"
                @keydown.enter="() => handleCreateReply(annotation)"
                @keydown.escape="() => handleCancelReply(annotation)"
              ></a-textarea>
            </div>
          </div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<style scoped lang="scss">
  $cardWidth: 300px;
  .annotations-wrapper {
    width: $cardWidth;
    transition: all linear 0.2s;
    &.collapsed {
      width: 80px;
      overflow: hidden;
    }
    &.fixed-wrapper.collapsed {
      right: 42px;
    }
    .toggle-button {
      z-index: 99;
      background: #fff;
      &.fixed {
        position: fixed;
        margin-top: 8px;
        right: 42px;
      }
      &.absolute {
        position: absolute;
        right: 10px;
        top: 14px;
      }
    }
  }
  .annotations-card {
    height: calc(100vh - 200px);
    z-index: 1;
    width: $cardWidth;
    overflow: hidden;
    transition: all linear 0.2s;
    :deep {
      .arco-card-body {
        height: calc(100% - 40px);
        overflow-y: auto;

        > div {
          height: auto !important;
        }
      }
    }
    &.collapsed {
      width: 0;
      overflow: hidden;
      border: none;
    }
  }

  .annotation-actions {
    visibility: hidden;
  }
  .reply-btn {
    visibility: hidden;
  }

  .annotation-item:hover {
    .annotation-actions,
    .reply-btn {
      visibility: visible;
    }
  }
</style>
