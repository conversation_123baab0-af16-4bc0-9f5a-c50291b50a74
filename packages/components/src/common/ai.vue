<script setup lang="ts">
  import { computed } from 'vue';
  import SparkChat from './navRight/sparkChat.vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:modelValue']);

  const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val),
  });
</script>

<template>
  <a-modal v-model:visible="visible" width="800px" title="牧云融教AI" hide-cancel ok-text="关闭">
    <spark-chat />
  </a-modal>
</template>

<style scoped lang="scss"></style>
