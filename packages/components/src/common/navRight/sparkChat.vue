<template>
  <div class="contents">
    <div id="conversations-list" class="conversations-list">
      <div
        v-for="(conversation, index) in conversations"
        :key="index"
        class="conversation-item"
        :class="`role-${conversation.role}`"
      >
        <div v-if="conversation.role === 'assistant'" class="avatar">
          <a-avatar :size="35" :src="logoInner" alt="avatar" />
        </div>
        <div class="content-wrapper">
          <div class="content">
            {{ conversation.content }}
          </div>
        </div>
        <div v-if="conversation.role === 'user'" class="avatar">
          <avatar-display :user-info="currentUser" :size="35" />
        </div>
      </div>

      <div v-if="responding" class="responding">
        <div class="conversation-item role-assistant">
          <div class="avatar">
            <a-avatar :size="35" :src="logoInner" alt="avatar" />
          </div>
          <div class="content-wrapper">
            <div class="content">
              {{ currentResponse.join('') }}
              <span class="cursor"></span>
            </div>
          </div>
        </div>

        <a-button
          size="mini"
          status="danger"
          type="outline"
          class="stop-button"
          shape="round"
          @click="handleCloseConnection"
        >
          <template #icon>
            <IconPause />
          </template>
          停止回答
        </a-button>
      </div>
    </div>
    <div class="input-wrapper flex">
      <textarea
        v-model.trim="currentInput"
        :disabled="responding"
        placeholder="请输入您的问题"
        @keydown.enter.stop="handleSendMsg"
      />
      <a-button type="outline" :disabled="!currentInput || responding" class="send-btn" @click="handleSendMsg">
        <template #icon>
          <IconSend />
        </template>
        发送
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { AvatarDisplay } from '@repo/ui/components/data-display/components';
  import { computed, onMounted, ref } from 'vue';
  import { useUserStore } from '@repo/infrastructure/store';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import logoInner from '../../../assets/images/logo-inner.png';

  const props = defineProps({
    showGoApplication: {
      type: Boolean,
      default: true,
    },
  });

  const conversations = ref<any>([
    {
      role: 'assistant',
      content: '您好，我是牧云融教 AI，有什么可以帮您的吗？',
    },
  ]);
  const currentInput = ref<any>('');
  const appId = ref<any>(null);
  const wssUrl = ref<any>(null);
  const conversationDomain = ref<any>(null);
  const wsClient = ref<any>(null);
  const currentResponse = ref<any[]>(null);
  const userIdPrefix = 'teacher_';
  const avatar = ref<any>(null);
  const listElement = ref<any>(null);
  const textareaElement = ref<any>(null);
  const currentUser = ref<any>(null);

  const userStore = useUserStore();

  const responding = computed(() => currentResponse.value !== null);

  const init = async () => {
    const { data: res } = await request('/ai/xunfei/spark/init', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    appId.value = res.appId;
    conversationDomain.value = res.domain;
    wssUrl.value = res.url.replace('https://', 'wss://');
  };

  const packResponse = (rawQuestion) => {
    const fullResContent = currentResponse.value.join('');
    currentResponse.value = null;
    conversations.value.push({
      role: 'assistant',
      content: fullResContent,
    });

    request('/ai/conversationRecord', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      data: {
        ask: rawQuestion,
        answer: fullResContent,
      },
    });
  };

  const packRequest = () => {
    const texts: any[] = [];
    // last three conversations
    const lastThreeConversations = conversations.value.slice(-5);
    lastThreeConversations.forEach((conversation) => {
      texts.push({
        role: conversation.role,
        content: conversation.content,
      });
    });
    return {
      header: {
        app_id: appId.value,
        uid: userIdPrefix + currentUser.value?.id,
      },
      parameter: {
        chat: {
          domain: conversationDomain.value,
          temperature: 0.5,
          max_tokens: 1024,
        },
      },
      payload: {
        message: {
          text: texts,
        },
      },
    };
  };

  const createWssConnection = async (requestData, rawQuestion) => {
    wsClient.value = new WebSocket(wssUrl.value);
    wsClient.value.onopen = () => {
      wsClient.value.send(JSON.stringify(requestData));
    };
    wsClient.value.onmessage = (e) => {
      // console.log('wsClient onmessage', e)
      currentResponse.value = currentResponse.value || [];
      const data = JSON.parse(e.data);
      const { choices } = data.payload;
      if (data.header.code === 0) {
        currentResponse.value.push(choices.text[0]?.content);
        if (data.header.status === 2) {
          packResponse(rawQuestion);
          textareaElement.value.focus();
          // eslint-disable-next-line @typescript-eslint/no-this-alias
          setTimeout(() => {
            textareaElement.value.focus();
          }, 500);
        }
      }
      listElement.value.scrollTop = listElement.value.scrollHeight + 20;
    };

    wsClient.value.onerror = (e) => {
      console.log('wsClient onerror', e);
    };
    wsClient.value.onclose = (e) => {
      console.log('wsClient onclose', e);
      wsClient.value = null;
    };
  };

  const handleCloseConnection = () => {
    packResponse();
    wsClient.value.close();
    currentResponse.value = null;
  };

  const handleSendMsg = async () => {
    if (!currentInput.value) return;
    if (responding.value) {
      this.$message.warning('请等待回答结束');
      return;
    }
    conversations.value.push({
      role: 'user',
      content: currentInput.value,
    });
    listElement.value.scrollTop = listElement.value.scrollHeight + 80;
    const requestData = packRequest();
    console.log('req', requestData);
    await createWssConnection(requestData, currentInput.value);
    currentInput.value = '';
  };

  onMounted(async () => {
    currentUser.value = userStore.userInfo;

    await init();

    textareaElement.value = document.querySelector('.input-wrapper textarea');
    listElement.value = document.getElementById('conversations-list');
    listElement.value.scrollTop = listElement.value.scrollHeight + 20;
  });
</script>

<style lang="scss" scoped>
  .wrapper {
    display: flex;
    flex: 1 auto;
    height: auto;
    flex-direction: column;
    :deep .el-card__body {
      flex: 1;
    }
  }
  .contents {
    height: 100%;
    display: flex;
    flex-direction: column;
    .conversations-list {
      max-height: calc(100vh - 305px);
      overflow-y: scroll;
      flex: 1;
      background-color: #f5f7fa;
      margin-right: -10px;
      padding-bottom: 15px;
    }
    .input-wrapper {
      height: 80px;
      background-color: #fff;

      textarea {
        flex: 1;
        border: none;
        outline: none;
        resize: none;
        padding: 10px;
        font-size: 14px;
        line-height: 20px;
        border-left: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
        border-top: 1px solid #ddd;
      }
    }
  }
  .conversation-item {
    display: flex;
    justify-content: flex-start;
    font-size: 14px;
    margin-top: 10px;
    .avatar {
      margin: 10px 0 10px 10px;
    }
    .content-wrapper {
      position: relative;
      max-width: 80%;
      margin: 10px 10px 0 10px;
      .content {
        padding: 8px;
        line-height: 180%;
        border-radius: 4px;
        background-color: #fff;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
        word-break: break-all;
        position: relative;
      }
      &:after {
        // 给个 角
        content: '';
        position: absolute;
        top: 10px;
        left: -5px;
        width: 0;
        height: 0;
        border-right: 6px solid #fff;
        border-top: 6px solid transparent;
        border-bottom: 6px solid transparent;
      }
    }

    &.role-user {
      justify-content: flex-end;
      .avatar {
        margin: 10px 10px 10px 0;
      }
      .content-wrapper {
        .content {
          background-color: #409eff;
          color: #fff;
        }
        &:after {
          left: auto;
          right: -5px;
          border-right: none;
          border-left: 6px solid #409eff;
        }
      }
    }

    .responding {
      .cursor {
        display: inline-block;
        width: 10px;
        height: 2px;
        background-color: #333;
        animation: blink 1s infinite;
      }
    }
  }
  .stop-button {
    margin-left: 55px;
    margin-top: 5px;
  }
  .send-btn {
    height: 100%;
  }
</style>
