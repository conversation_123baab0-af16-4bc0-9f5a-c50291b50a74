<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { request } from '@repo/infrastructure/request';
  import { ENV, PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:visible']);
  const formData = ref<any>({});
  const { loading, setLoading } = useLoading();

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const newPwdValidated = computed(() => {
    const newPwd = formData.value.pwdNew;
    if (!newPwd) {
      return false;
    }

    return newPwd.length >= 6;
  });

  const pattern = new RegExp(ENV.VITE_PASSWORD_PATTERN);
  const patternTip = ENV.VITE_PASSWORD_TIP;

  const handleSubmit = async () => {
    if (!newPwdValidated.value) {
      Message.error(patternTip);
      return false;
    }

    if (pattern && !pattern.test(formData.value.pwdNew)) {
      Message.error(patternTip);
      return false;
    }
    setLoading(true);
    try {
      await request('/org/session/setPassword', {
        method: 'PUT',
        data: {
          oldPassword: formData.value.pwdOld,
          newPassword: formData.value.pwdNew,
        },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      Message.success('密码修改成功');
      modalVisible.value = false;

      return true;
    } finally {
      setLoading(false);
    }
  };
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    title="修改密码"
    :on-before-ok="handleSubmit"
    :ok-button-props="{
      disabled: !newPwdValidated || !formData.pwdOld,
    }"
    :ok-loading="loading"
  >
    <a-form :model="formData" size="mini" auto-label-width>
      <a-form-item label="原密码" prop="oldPassword" required>
        <a-input v-model.trim="formData.pwdOld" />
      </a-form-item>
      <a-form-item label="新密码" prop="newPassword" required>
        <a-input v-model.trim="formData.pwdNew" />
      </a-form-item>
      <a-form-item>
        <div>
          <small>* 如果忘记了原密码，请退出登录后使用忘记密码功能重置密码（需绑定手机） </small>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style scoped lang="scss"></style>
