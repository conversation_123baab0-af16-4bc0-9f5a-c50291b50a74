<script setup lang="ts">
  import { CONSULTING_STATUS } from '@repo/infrastructure/constants';
  import { computed, onMounted, ref } from 'vue';
  import { useUserStore } from '@repo/infrastructure/store';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const userStore = useUserStore();
  const currentStatus = ref('OFFLINE');
  const currentStatusText = computed(() => {
    return CONSULTING_STATUS.find((item) => item.id === currentStatus.value)?.name;
  });

  const buttonStatusClass = computed(() => {
    switch (currentStatus.value) {
      case 'GLOBAL_ONLINE':
        return 'text-green-600 font-bold';
      case 'ONLINE':
        return 'text-green-600 font-bold';
      default:
        return '';
    }
  });

  const handleStatusSwitch = async (status) => {
    Message.info('正在切换咨询状态...');
    try {
      await request('/org/session/profile', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'PUT',
        data: {
          ...userStore.userInfo,
          consultingStatus: status,
        },
      });

      userStore.setInfo({
        ...userStore.userInfo,
        consultingStatus: status,
      });

      currentStatus.value = status;
      Message.clear();
      Message.success(`切换成功，当前 ${CONSULTING_STATUS.find((item) => item.id === status)?.name}`);
    } catch (e) {
      Message.error('切换失败');
    }
  };

  onMounted(() => {
    currentStatus.value = userStore.userInfo?.consultingStatus;
  });
</script>

<template>
  <a-dropdown hide-on-select @select="handleStatusSwitch">
    <a-tooltip :content="`咨询状态：${currentStatusText}`">
      <a-button size="small" shape="circle" class="nav-btn" type="outline">
        <template #icon>
          <div :class="buttonStatusClass">
            <icon-message v-if="currentStatus === 'GLOBAL_ONLINE'" />
            <icon-clock-circle v-else-if="currentStatus === 'ONLINE'" />
            <icon-message-banned v-else />
          </div>
        </template>
      </a-button>
    </a-tooltip>
    <template #content>
      <a-doption v-for="(status, idx) in CONSULTING_STATUS" :key="idx" :value="status.id">{{ status.name }}</a-doption>
    </template>
  </a-dropdown>
</template>

<style scoped lang="scss"></style>
