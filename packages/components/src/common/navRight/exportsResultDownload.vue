<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { useList } from '@repo/infrastructure/hooks';
  import { PROJECT_URLS } from '@repo/env-config';
  import { getToken } from '@repo/infrastructure/auth';
  import { Message } from '@arco-design/web-vue';
  import { getRequestClientRole } from '@repo/infrastructure/adapter';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:modelValue']);
  const t = ref<any>(null);

  const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val),
  });

  const { listData, loadData, refresh, loading } = useList({
    api: '/common/exportTask',
    pageSize: 30,
    axiosConfig: {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    },
  });

  const handleBeforeClose = () => {
    if (t.value) {
      clearInterval(t.value);
    }
  };

  const handleBeforeOpen = async () => {
    handleBeforeClose();
    await loadData();
    t.value = setInterval(async () => {
      await loadData();
    }, 8000);
  };

  const getProgressStatus = (status: string): any => {
    if (status === 'Success') {
      return 'success';
    }
    if (status === 'Failure') {
      return 'danger';
    }
    return '';
  };

  const getFileSizeDisplay = (value: number) => {
    if (!value) {
      return '-';
    }
    if (value > 1024 * 1024) {
      return `${(value / 1024 / 1024).toFixed(2)}M`;
    }
    return `${(value / 1024).toFixed(2)}K`;
  };

  const doExportDownload = (row: any) => {
    Message.success('正在准备下载文件，稍后下载将自动开始');
    const token = getToken();
    window.location.href = `${PROJECT_URLS.MAIN_PROJECT_API}/common/exportTask/download/${row.id}?token=${token}&requestClient=${getRequestClientRole()}`;
  };
</script>

<template>
  <a-modal
    v-model:visible="visible"
    width="1000px"
    title="导出结果下载"
    @open="handleBeforeOpen"
    @before-close="handleBeforeClose"
  >
    <a-table :data="listData" :loading="loading">
      <template #columns>
        <a-table-column title="#">
          <template #cell="{ rowIndex }">{{ rowIndex + 1 }}</template>
        </a-table-column>
        <a-table-column title="文件名" data-index="name" />
        <a-table-column title="条目数" data-index="itemsLength" />
        <a-table-column title="大小" data-index="size">
          <template #cell="{ record }">{{ getFileSizeDisplay(record.filesize) }}</template>
        </a-table-column>
        <a-table-column title="状态" :width="100" data-index="exportTaskStatus">
          <template #cell="{ record }">
            <a-tag v-if="record.exportTaskStatus === 'InProgress'" size="small" color="arcoblue">进行中</a-tag>
            <a-tag v-else-if="record.exportTaskStatus === 'Success'" size="small" color="green">成功</a-tag>
            <a-tag v-else-if="record.exportTaskStatus === 'Failure'" size="small" color="red">失败</a-tag>
          </template>
        </a-table-column>
        <a-table-column title="进度" data-index="percentage" :width="150">
          <template #cell="{ record }">
            <a-progress
              v-if="record.exportTaskStatus !== 'Success'"
              :status="getProgressStatus(record.exportTaskStatus)"
              :percent="Number(Number(record.percentage).toFixed(2))"
            />
          </template>
        </a-table-column>
        <a-table-column title="操作">
          <template #cell="{ record }">
            <a-button
              v-if="record.exportTaskStatus === 'Success'"
              type="text"
              size="mini"
              @click="() => doExportDownload(record)"
            >
              下载
            </a-button>
          </template>
        </a-table-column>
      </template>
    </a-table>
    <template #footer>
      <div class="flex justify-end gap-2">
        <a-button :loading="loading" @click="refresh">
          <template #icon>
            <IconRefresh />
          </template>
          刷新
        </a-button>
        <a-button type="primary" @click="visible = false">关闭</a-button>
      </div>
    </template>
  </a-modal>
</template>

<style scoped lang="scss"></style>
