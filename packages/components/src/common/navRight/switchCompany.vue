<script setup lang="ts">
  import { useUserStore } from '@repo/infrastructure/store';
  import { computed, ref } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { PROJECT_URLS } from '@repo/env-config';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:modelValue']);
  const { loading, setLoading } = useLoading();

  const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val),
  });

  const userStore = useUserStore();
  const { userInfo } = userStore;
  const currentCompany = ref(userInfo.company?.id);

  const companyMap = ref(
    (userInfo.companyList &&
      userInfo.companyList.reduce((map, com) => {
        map[com.id] = com;
        return map;
      }, {})) ||
      {},
  );

  const handleSwitchCompany = async (com) => {
    setLoading(true);
    try {
      await request(`/org/session/switchCompany/${com.id}`, {
        method: 'put',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      Message.success('正在重新载入，请稍后');
      window.location.reload();
    } finally {
      setLoading(false);
    }
  };
</script>

<template>
  <a-modal v-if="userInfo.company" v-model:visible="visible" :ok-loading="loading" title="切换单位" width="30%">
    <div v-if="currentCompany && userInfo.companyList.length > 1" class="flex gap-2 items-center">
      <a-select v-model="currentCompany" size="small" filterable>
        <a-option
          v-for="bo in userInfo.companyList || []"
          :key="bo.id"
          :label="bo.name"
          :value="bo.id"
          :disabled="bo.deleted"
        ></a-option>
      </a-select>
      <a-popconfirm
        v-if="companyMap[currentCompany]"
        :content="`确定要切换到 ${companyMap[currentCompany].name} 吗？`"
        class="mt-2"
        @ok="handleSwitchCompany(companyMap[currentCompany])"
      >
        <a-button size="small" type="primary" class="ml-2">切换单位</a-button>
      </a-popconfirm>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <a-button size="small" @click="() => (visible = false)">取 消</a-button>
      </span>
    </template>
  </a-modal>
</template>

<style scoped lang="scss"></style>
