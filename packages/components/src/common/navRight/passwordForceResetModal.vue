<script setup lang="ts">
  import { useUserStore } from '@repo/infrastructure/store';
  import { computed, ref } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { ENV, PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';

  const userStore = useUserStore();

  const passwordAlreadySet = computed(() => userStore.userInfo?.passwordAlreadySet);
  const passwordExpired = computed(() => {
    return !userStore.userInfo?.passwordValid;
  });

  const msg = computed(() => {
    if (!passwordAlreadySet.value) {
      return '您尚未设置系统密码，请先设置';
    }
    if (passwordExpired.value) {
      return '您的密码已过期，需要重新修改后方可继续使用';
    }
    return '';
  });

  const emit = defineEmits(['logout']);
  const modalVisible = computed(() => !passwordAlreadySet.value || passwordExpired.value);

  const formData = ref<any>({});
  const { loading, setLoading } = useLoading();

  const newPwdValidated = computed(() => {
    const newPwd = formData.value.pwdNew;
    if (!newPwd) {
      return false;
    }

    return newPwd.length >= 6;
  });

  const pattern = new RegExp(ENV.VITE_PASSWORD_PATTERN);
  const patternTip = ENV.VITE_PASSWORD_TIP;

  const handleSubmit = async () => {
    if (!newPwdValidated.value) {
      Message.error(patternTip);
      return false;
    }

    if (pattern && !pattern.test(formData.value.pwdNew)) {
      Message.error(patternTip);
      return false;
    }

    if (!passwordAlreadySet.value && formData.value.pwdOld !== formData.value.pwdNew) {
      Message.error('两次输入的密码不一致');
      return false;
    }

    setLoading(true);
    try {
      await request('/org/session/setPassword', {
        method: 'PUT',
        data: {
          oldPassword: formData.value.pwdOld,
          newPassword: formData.value.pwdNew,
        },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      Message.success('密码修改成功');
      window.location.reload();

      return true;
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    emit('logout');
  };
</script>

<template>
  <a-modal
    :visible="modalVisible"
    title="账号风险提示"
    :closable="false"
    :esc-to-close="false"
    :mask-closable="false"
    hide-cancel
  >
    <a-alert type="warning">
      {{ msg }}
    </a-alert>
    <a-form :model="formData" size="mini" auto-label-width class="mt-4">
      <a-form-item :label="passwordAlreadySet ? '原密码' : '设置密码'" prop="oldPassword" required>
        <a-input v-model.trim="formData.pwdOld" type="password" />
      </a-form-item>
      <a-form-item :label="passwordAlreadySet ? '新密码' : '重复一次'" prop="newPassword" required>
        <a-input v-model.trim="formData.pwdNew" type="password" />
      </a-form-item>
      <a-form-item v-if="passwordAlreadySet">
        <div>
          <small>* 如果忘记了原密码，请退出登录后使用忘记密码功能重置密码（需绑定手机） </small>
        </div>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-space>
        <a-button @click="handleLogout">退出登录</a-button>
        <a-button
          type="primary"
          :disabled="!newPwdValidated || !formData.pwdOld"
          :loading="loading"
          @click="handleSubmit"
          >确定</a-button
        >
      </a-space>
    </template>
  </a-modal>
</template>

<style scoped lang="scss"></style>
