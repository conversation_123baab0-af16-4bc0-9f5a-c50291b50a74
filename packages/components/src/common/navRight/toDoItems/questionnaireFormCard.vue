<script setup lang="ts">
  import { computed, ref } from 'vue';
  import QuestionnaireFormPreview from '@repo/components/questionnaireForm/questionnaireFormPreview.vue';
  import { getMyFormAnswer, saveMyFormAnswer } from '@repo/infrastructure/openapi/questionnaireRecordController';
  import { Message, Modal } from '@arco-design/web-vue';
  import UsageTimer from '@repo/components/common/usageTimer.vue';

  const props = defineProps({
    dataList: {
      type: Array,
      default: () => [],
    },
  });

  const viewModalVisible = ref(false);
  const currentItem = ref<any>({});
  const answers = ref<any>({});
  const saving = ref(false);
  const response = ref<any>({});
  const questionnaireFormPreviewRef = ref<any>(null);

  const lists = computed(() => {
    return props.dataList?.slice(0, 10) || [];
  });

  const handleShowDetail = async (item) => {
    const { data } = await getMyFormAnswer({
      formId: item.id,
    });

    response.value = data || {};
    answers.value = data?.answers || {};

    currentItem.value = item;
    viewModalVisible.value = true;
  };

  const handleSave = async (submit?: boolean) => {
    if (!questionnaireFormPreviewRef.value) {
      return;
    }
    if (submit && questionnaireFormPreviewRef.value.answersNotAvailable?.length) {
      const items = questionnaireFormPreviewRef.value.answersNotAvailable?.map((item) => (item + 1).toString());
      Modal.error({
        title: '请填写完整',
        content: `第 ${items?.join('、')} 题尚未填写，请检查后提交`,
      });
      return;
    }

    saving.value = true;
    try {
      const { data } = await saveMyFormAnswer(
        {
          formId: currentItem.value.id,
        },
        {
          ...response.value,
          answers: answers.value,
          submitted: !!submit,
        },
      );

      response.value = data;

      Message.success(submit ? '提交成功' : '暂存成功');
      if (submit) {
        viewModalVisible.value = false;
      }
    } finally {
      saving.value = false;
    }
  };
</script>

<template>
  <a-card v-if="dataList?.length" size="mini">
    <template #title> 问卷表单 </template>
    <div v-for="item in lists" :key="item.id" class="pr-2">
      <a-link class="text-ellipsis overflow-hidden truncate w-full" @click="() => handleShowDetail(item)">{{
        item.name
      }}</a-link>
    </div>
  </a-card>

  <a-modal
    v-model:visible="viewModalVisible"
    :width="800"
    hide-cancel
    closable
    :esc-to-close="false"
    :mask-closable="false"
  >
    <template #title>
      <div>{{ currentItem?.name }}</div>
    </template>
    <div v-if="currentItem">
      <questionnaire-form-preview
        v-if="viewModalVisible && currentItem"
        ref="questionnaireFormPreviewRef"
        v-model:answer="answers"
        :form="currentItem"
        :question-ids="currentItem.questionIds"
        :mode="response?.submitted ? 'resultView' : 'default'"
        class="mt-4"
      />
    </div>
    <template #footer>
      <div v-if="response" class="flex justify-between items-center">
        <usage-timer v-if="!response?.submitted" v-model="response.timeCost" />
        <div v-else> </div>
        <a-space>
          <a-space v-if="!response?.submitted">
            <a-button type="primary" :loading="saving" @click="() => handleSave()"> 暂存 </a-button>
            <a-popconfirm content="提交后将不可修改，确定要提交吗？" @ok="() => handleSave(true)">
              <a-button type="primary" :loading="saving"> 提交 </a-button>
            </a-popconfirm>
          </a-space>
          <a-button @click="() => (viewModalVisible = false)">关闭</a-button>
        </a-space>
      </div>
    </template>
  </a-modal>
</template>

<style scoped lang="scss">
  .arco-link {
    justify-content: flex-start;
  }
</style>
