<script setup lang="ts">
  import { useUserStore } from '@repo/infrastructure/store';
  import { computed } from 'vue';
  import { useRouter } from 'vue-router';

  const props = defineProps({
    dataList: {
      type: Array,
    },
  });
  const emits = defineEmits(['closeDrawer']);
  const num = computed((): number => {
    if (props.dataList?.content[0]?.content) return props.dataList?.content[0]?.content;
    return 0;
  });
  const router = useRouter();
  const fixInfo = () => {
    emits('closeDrawer', false);
    router.push(`/teacher/profile`);
  };
</script>

<template>
  <a-card>
    <template #title>个人信息完善</template>
    <template #extra>
      <a-badge :count="1" :max-count="5" :dot-style="{ background: '#ffe1e3', color: '#ff6c74' }" />
    </template>
    <div>{{ num }}项待完成</div>
    <div class="flex items-center justify-start text-blue-600 mt-5 cursor-pointer" @click="fixInfo">
      去完成
      <icon-double-right />
    </div>
  </a-card>
</template>

<style scoped lang="scss"></style>
