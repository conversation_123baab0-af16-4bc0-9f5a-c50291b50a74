<script setup lang="ts">
  const props = defineProps({
    dataList: {
      type: Array,
    },
  });
  const handleClick = (item: any) => {};
</script>

<template>
  <a-card>
    <template #title>IEP计划待处理</template>
    <template #extra>
      <a-badge :count="dataList?.num" :max-count="5" :dot-style="{ background: '#ffe1e3', color: '#ff6c74' }" />
    </template>
    <div v-for="item in dataList?.content" :key="item.id" class="pr-2">
      <a-link class="text-ellipsis w-full" @click="() => handleClick(item)">
        <div style="width: 100%">
          {{ item.name }}
        </div>
      </a-link>
    </div>
  </a-card>
</template>

<style scoped lang="scss"></style>
