<script setup lang="ts">
  import { useUserStore } from '@repo/infrastructure/store';
  import { computed } from 'vue';
  import { useRouter } from 'vue-router';

  const props = defineProps({
    dataList: {
      type: Array,
    },
  });
  const emits = defineEmits(['closeDrawer']);
  const num = computed((): number => {
    if (props.dataList?.content.length) return props.dataList?.content.length;
    return 0;
  });
  const router = useRouter();
  const handleClick = (id) => {
    emits('closeDrawer', false);
    router.push(`/teacher/student/edit?id=${id}`);
  };
</script>

<template>
  <a-card>
    <template #title>学生信息待完善</template>
    <template #extra>
      <a-badge :count="num" :max-count="5" :dot-style="{ background: '#ffe1e3', color: '#ff6c74' }" />
    </template>
    <div v-for="item in dataList?.content" :key="item.id" class="pr-2">
      <a-link class="text-ellipsis w-full" @click="() => handleClick(item.id)">
        <div style="width: 100%">
          {{ item.name }}
          <span v-if="item?.content" class="ml-4 text-gray-300 text-xs">{{ item.content }}项待完成</span>
        </div>
      </a-link>
    </div>
  </a-card>
</template>

<style scoped lang="scss"></style>
