<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const props = defineProps({
    dataList: {
      type: Array,
      default: () => [],
    },
  });

  const viewModalVisible = ref(false);
  const currentItem = ref<any>({});

  const lists = computed(() => {
    return props.dataList?.slice(0, 4) || [];
  });

  const handleShowDetail = (item) => {
    currentItem.value = item;
    viewModalVisible.value = true;
  };

  const handleGoParticipant = () => {
    router.push({
      path: '/teacher/dailyWork/paperCompetition',
      query: {
        id: currentItem.value.id,
      },
    });
  };
</script>

<template>
  <a-card v-if="dataList?.length" size="mini">
    <template #title>
      <IconStar spin />
      论文赛课
      <IconStar spin />
    </template>
    <div v-for="item in lists" :key="item.id" class="pr-2">
      <a-link class="text-ellipsis overflow-hidden truncate w-full" @click="() => handleShowDetail(item)">{{
        item.name
      }}</a-link>
    </div>
  </a-card>

  <a-modal v-model:visible="viewModalVisible" :width="900" simple hide-cancel>
    <template #title>
      <div>论文赛课详情</div>
    </template>
    <div v-if="currentItem">
      <div class="text-lg font-medium">
        {{ currentItem.name }}
      </div>
      <a-divider :margin="10" />
      <a-space class="w-full justify-between">
        <div> <strong>提交时间：</strong>{{ currentItem.dateRange?.join(' 至 ') }} </div>
        <div> <strong>评审时间：</strong>{{ currentItem.assessmentDateRange?.join(' 至 ') }} </div>
      </a-space>
      <a-space class="mt-2 w-full justify-between">
        <div>
          <strong>奖项设置：</strong>
          <span v-for="(a, i) in currentItem.awards" :key="i" class="mr-4"> {{ a.name }} ({{ a.count }}名) </span>
        </div>
        <div></div>
      </a-space>
      <a-divider :margin="10" />
      <div v-html="currentItem.requirements"></div>
    </div>
    <template #footer>
      <a-space>
        <a-button type="primary" @click="handleGoParticipant">
          <template #icon>
            <IconThumbUp />
          </template>
          我的作品
        </a-button>
        <a-button @click="() => (viewModalVisible = false)">关闭</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<style scoped lang="scss">
  .arco-link {
    justify-content: flex-start;
  }
</style>
