<script setup lang="ts">
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { ref, onMounted } from 'vue';
  import { getAvailableQuestionnaireForms } from '@repo/infrastructure/openapi/questionnaireFormController';
  import PaperCompetitionCard from './toDoItems/paperCompetitionCard.vue';
  import QuestionnaireFormCard from './toDoItems/questionnaireFormCard.vue';
  import SupervisionEvaluationCard from './toDoItems/supervisionEvaluationCard.vue';
  import SelfInfoCard from './toDoItems/selfInfoCard.vue';
  import StudentInfoCard from './toDoItems/studentInfoCard.vue';
  import SendPlanCard from './toDoItems/sendPlanCard.vue';
  import IepCard from './toDoItems/IepCard.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    userStore: {
      type: Object,
    },
  });
  const emits = defineEmits(['update:modelValue']);

  const todoTypes = {
    STUDENT_INFO: { title: '学生信息待完善', sort: 1 },
    STUDENT_ASSESSMENT: { title: '学生评估待完成', sort: 2 },
    STUDENT_IEP: { title: 'IEP计划待处理', sort: 3 },
    SELF_INFO: { title: '个人信息待完善', sort: 4 },
    SUPERVISION: { title: '督导考核材料待上传', sort: 5 },
    QUESTIONNAIRE: { title: '问卷待填写', sort: 6 },
    SEND_PLAN: { title: '送教计划待完善', sort: 7 },
  };
  const widgetData = ref({
    newsList: [],
    announcementList: [],
    carouselList: [],

    paperCompetitionList: [],
    questionnaireFormList: [],
  });
  const loadPaperCompetition = async () => {
    const { data } = await request('/paper/paperCompetition', {
      params: {
        published: true,
        limit: 5,
        sort: '-id',
      },
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    widgetData.value.paperCompetitionList = data.items || [];
  };
  const loadQuestionnaireForm = async () => {
    const { data } = await getAvailableQuestionnaireForms();

    widgetData.value.questionnaireFormList = data || [];
  };

  const handleCancel = () => {
    emits('update:modelValue', false);
  };
  const handlePreOK = async () => {
    emits('update:modelValue', false);
  };
  const todoInfo = ref();
  const page = ref(1);
  const searchTodoInfo = () => {
    request('/toDoItems/toDoItems/search', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
      params: {
        page: page.value,
        pageSize: 10,
      },
    }).then((res) => {
      todoInfo.value = res.data;
    });
  };
  const willShow = (key: string): boolean => {
    return Object.keys(todoInfo.value).includes(key);
  };
  onMounted(async () => {
    // await loadPaperCompetition();
    // await loadQuestionnaireForm();
    // await searchTodoInfo();
    await Promise.all([loadPaperCompetition(), loadQuestionnaireForm(), searchTodoInfo()]);
  });
</script>

<template>
  <a-drawer
    width="auto"
    :header="false"
    :visible="visible"
    unmount-on-close
    :closable="false"
    @ok="handlePreOK"
    @cancel="handleCancel"
  >
    <template #footer>
      <div class="flex justify-start">
        <a-button type="outline" size="small" @click="handleCancel">关闭</a-button>
        <a-button type="outline" size="small" @click="searchTodoInfo">测试</a-button>
      </div>
    </template>
    <div class="w-full h-full rounded">
      <div v-if="todoInfo" class="w-96 flex flex-col gap-2">
        <paper-competition-card :data-list="widgetData.paperCompetitionList" />
        <questionnaire-form-card :data-list="widgetData.questionnaireFormList" />
        <self-info-card
          v-if="willShow(todoTypes.SELF_INFO.title)"
          :data-list="todoInfo[todoTypes.SELF_INFO.title]"
          @close-drawer="handleCancel"
        />
        <student-info-card
          v-if="willShow(todoTypes.STUDENT_INFO.title)"
          :data-list="todoInfo[todoTypes.STUDENT_INFO.title]"
          @close-drawer="handleCancel"
        />
        <supervision-evaluation-card
          v-if="willShow(todoTypes.SUPERVISION.title)"
          :data-list="todoInfo[todoTypes.SUPERVISION.title]"
        />
        <send-plan-card v-if="willShow(todoTypes.SEND_PLAN.title)" :data-list="todoInfo[todoTypes.SEND_PLAN.title]" />
        <iep-card v-if="willShow(todoTypes.STUDENT_IEP.title)" :data-list="todoInfo[todoTypes.STUDENT_IEP.title]" />
      </div>
    </div>
  </a-drawer>
</template>

<style scoped lang="scss"></style>
