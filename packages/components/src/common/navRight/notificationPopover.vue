<script setup lang="ts">
  import { useNotificationStore } from '@repo/infrastructure/store';
  import { inject, onMounted } from 'vue';

  const notificationStore = useNotificationStore();
  const router = inject('router');
  const handleGo = (item: any) => {
    if (typeof item.link === 'function') {
      item.link();
      return;
    }
    router.push(item.link);
  };

  onMounted(() => {
    notificationStore.refresh();
  });
</script>

<template>
  <a-popover v-if="notificationStore.total" trigger="hover">
    <a-badge dot :count="notificationStore.total">
      <a-button shape="circle" class="nav-btn" type="outline">
        <template #icon>
          <IconNotification :class="{ 'notification-shake': notificationStore.total > 0 }" />
        </template>
      </a-button>
    </a-badge>
    <template #content>
      <div class="grid grid-cols-1 divide-y">
        <div
          v-for="(item, index) in notificationStore.types"
          v-show="item.count && item.count > 0"
          :key="index"
          class="min-w-40 cursor-pointer text-sm"
          @click="() => handleGo(item)"
        >
          <div class="flex justify-between py-2">
            <div>
              {{ item.label }}
            </div>
            <div class="bg-fuchsia-500 font-bold text-white w-10 text-center rounded">
              {{ item.count }}
            </div>
          </div>
        </div>
      </div>
    </template>
  </a-popover>
</template>

<style scoped lang="less">
  @keyframes shake {
    10% {
      transform: rotate(25deg);
    }
    20% {
      transform: rotate(-10deg);
    }
    30% {
      transform: rotate(5deg);
    }
    40% {
      transform: rotate(-5deg);
    }
    50% {
      transform: rotate(0deg);
    }
  }

  .notification-shake {
    animation: shake 1s infinite;
  }
</style>
