<script setup lang="ts">
  import { useUserStore } from '@repo/infrastructure/store';
  import { computed, ref } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message, Modal } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import SendVerifyCode from '../sendVerifyCode.vue';

  const emit = defineEmits(['logout']);

  const userStore = useUserStore();
  const { userInfo } = userStore;
  const formRef = ref();

  const passwordAlreadySet = computed(() => userStore.userInfo?.passwordAlreadySet);
  const passwordExpired = computed(() => {
    return !userStore.userInfo?.passwordValid;
  });

  const modalVisible = computed(() => !passwordAlreadySet.value || passwordExpired.value);
  const bindVisible = computed(() => !modalVisible.value && !userStore.userInfo?.mobile);

  const formData = ref<any>({});
  const { loading, setLoading } = useLoading();

  const handleSubmit = async () => {
    const info = formRef.value.validate();

    if (info === undefined || Object.keys(info).length === 0) {
      setLoading(true);
      if (!formData.value?.mobile || !formData.value?.code) {
        loading.value = false;
        Message.error('请填写完整信息');
        return false;
      }

      try {
        await request('/org/session/bindMobile', {
          method: 'PUT',
          data: formData.value,
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        });
        userStore.setInfo({
          ...userInfo,
          mobile: formData.value.mobile,
        });

        userInfo.certifiedAt = new Date().toISOString();
        userInfo.mobile = formData.value.mobile;

        Modal.success({
          title: '绑定成功',
          onOk() {
            window.location.reload();
          },
          content:
            '手机号码绑定成功，您可以使用手机号码+密码或验证码登陆，同时可通过右上角帐号切换功能切换相同手机号码下的不同帐号',
        });
        return true;
      } finally {
        setLoading(false);
      }
    }
    return false;
  };

  const handleLogout = async () => {
    emit('logout');
  };
</script>

<template>
  <a-modal
    :visible="bindVisible"
    title="请先绑定手机号"
    :closable="false"
    :esc-to-close="false"
    :mask-closable="false"
    hide-cancel
  >
    <a-alert type="warning">
      {{ '请绑定手机号，以便获取更好的用户体验' }}
    </a-alert>

    <a-form ref="formRef" :model="formData" size="mini" auto-label-width class="mt-4">
      <a-form-item
        label="手机号"
        field="mobile"
        required
        :rules="[
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          {
            match: /^1[3-9]\d{9}$/,
            message: '手机号码格式不正确',
            trigger: 'blur',
          },
        ]"
      >
        <a-input v-model.trim="formData.mobile" :max-length="11" show-word-limit />
      </a-form-item>
      <a-form-item
        label="验证码"
        field="code"
        required
        :rules="[
          { required: true, message: '请输入验证码', trigger: 'blur' },
          { match: /^\d{6}$/, message: '验证码格式不正确', trigger: 'blur' },
        ]"
      >
        <a-input v-model.trim="formData.code" class="mr-2" />
        <send-verify-code :mobile="formData.mobile" size="mini" type="UserVerify" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-space>
        <a-button size="mini" @click="handleLogout">退出登录</a-button>
        <a-button type="primary" size="mini" :loading="loading" @click="handleSubmit">确定</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<style scoped lang="scss"></style>
