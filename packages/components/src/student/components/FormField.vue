<script setup lang="ts">
  interface Props {
    label?: string;
    required?: boolean;
    description?: string;
    fullWidth?: boolean;
  }

  defineProps<Props>();
</script>

<template>
  <div :class="['form-field', fullWidth ? 'col-span-full' : '']">
    <label class="form-label">
      {{ label }}
      <span v-if="required" class="text-red-500 ml-1">*</span>
    </label>
    <p v-if="description" class="text-xs text-gray-500 mb-2">{{ description }}</p>
    <slot />
  </div>
</template>

<style scoped>
  .form-label {
    @apply block text-sm font-semibold text-gray-700 mb-2;
  }

  .form-field {
    @apply space-y-1;
  }

  :deep(.arco-input-wrapper),
  :deep(.arco-select-view-single),
  :deep(.arco-picker),
  :deep(.arco-textarea-wrapper) {
    @apply rounded-lg border-gray-200 shadow-sm transition-all duration-200;
    background-color: #fff !important;
  }

  :deep(.arco-input-wrapper:hover),
  :deep(.arco-select-view-single:hover),
  :deep(.arco-picker:hover),
  :deep(.arco-textarea-wrapper:hover) {
    @apply border-gray-300 shadow-md;
  }

  :deep(.arco-input-wrapper.arco-input-focus),
  :deep(.arco-select-view-single.arco-select-view-focus),
  :deep(.arco-picker.arco-picker-focus),
  :deep(.arco-textarea-wrapper.arco-textarea-focus) {
    @apply border-blue-500 shadow-lg;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  :deep(.arco-input),
  :deep(.arco-select-view-value),
  :deep(.arco-picker-input),
  :deep(.arco-textarea-inner) {
    @apply text-gray-900 font-medium;
  }

  :deep(.arco-input::placeholder),
  :deep(.arco-select-view-value.arco-select-view-value-placeholder),
  :deep(.arco-picker-placeholder),
  :deep(.arco-textarea-inner::placeholder) {
    @apply text-gray-400;
  }
</style>
