import { CustomSchema } from '@repo/infrastructure/types';
import useCommonStore from '@repo/infrastructure/utils/store';
import { CONSULTING_STATUS } from '@repo/infrastructure/constants';
import { defineAsyncComponent } from 'vue';
import { useUserMenuStore } from '@repo/infrastructure/store';
import { getItemAttachmentsColumns } from '@repo/ui/components/utils/extraContent';
import dayjs from 'dayjs';
import { Message } from '@arco-design/web-vue';
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';
import { useRoute } from 'vue-router';
import useSchoolCourseStore from '../store/schoolCourseStore';

const companyUserItemAttachmentsColumns = getItemAttachmentsColumns({
  defaultOptions: ['教师资格证', '康复证书', '送教承诺书'],
});

const companyUserSchema: CustomSchema = {
  api: '/org/companyUser',
  quickSearchProps: {
    enabled: true,
    placeholder: '学校/姓名/用户名/手机号',
    fields: ['name', 'mobile', 'username', 'branchOffice.name'],
  },
  exportable: {
    enabled: () => {
      const route = useRoute();
      return !route?.path?.includes('sendEducation');
    },
    columns: ['name', 'mobile', 'username', 'branchOffice.name'],
  },
  importable: {
    enabled: () => {
      const route = useRoute();
      return !route?.path?.includes('sendEducation');
    },
    columns: ['name', 'mobile', 'username', 'branchOffice.name'],
    template: 'https://tejiao-prod-static1.oss-cn-chengdu.aliyuncs.com/templates/用户导入模板.xlsx',
    api: '/org/companyUser/import',
  },
  listViewProps: {
    searchType: 'advance',
    filterFields: ['validated', 'mobile', 'name'],
  },
  rowActions: [
    {
      key: 'validate',
      label: '审核',
      visible: (record) => !record.validated,
      expose: true,
      btnProps: { type: 'outline' },
      handler() {},
    },
    {
      key: 'resetPassword',
      label: '重置密码',
      icon: 'icon-eye',
      btnProps: { status: 'danger' },
      handler() {},
    },
    {
      key: 'setSysRole',
      label: '修改岗位',
      icon: 'icon-edit',
      multiple: true,
      handler() {},
    },
    {
      key: 'unFrozen',
      label: '批量解冻',
      icon: 'icon-unlock',
      multiple: true,
      handler() {},
    },
    {
      key: 'mockUser',
      label: '模拟用户',
      icon: 'icon-user',
      permNode: 'adminAbility:mockUser',
      handler() {},
    },
    // {
    //   key: 'disable',
    //   label: '禁用',
    //   icon: 'icon-stop',
    //   visible: (record) => record.validated,
    //   btnProps: { type: 'primary', status: 'danger' },
    //   handler() {},
    // },
  ],
  detailViewProps: {
    columns: 3,
    fieldsGrouping: [
      {
        label: '基本信息',
        fields: [
          'branchOffice',
          'name',
          'mobile',
          'validated',
          'username',
          'consultingStatus',
          'sysRoleSet',
          'inPosition',
          'userTitleIds',
          'idCard',
        ],
      },
      {
        label: '教师信息',
        fields: [
          'highestEducation',
          'proTitle',
          'establishment',
          'hasSpecialEducationExp',
          'teachType',
          'teachGrade',
          'teachingSubjects',
          'teacherType',
          'title',
          'dateOfWorkStarting',
          'dateOfSpecialEduStarting',
        ],
      },
      {
        label: '证件资料',
        fields: ['itemAttachments'],
      },
    ],
  },
  formViewProps: {
    fieldsGrouping: [
      {
        label: '基本信息',
        fields: [
          'branchOffice',
          'name',
          'mobile',
          'username',
          'consultingStatus',
          'sysRoleSet',
          'validated',
          'idCard',
          'inPosition',
          'userTitleIds',
        ],
      },
      {
        label: '教师信息',
        fields: [
          'highestEducation',
          'proTitle',
          'establishment',
          'hasSpecialEducationExp',
          'teachType',
          'teachGrade',
          'teachingSubjects',
          'teacherType',
          'title',
          'dateOfWorkStarting',
          'dateOfSpecialEduStarting',
        ],
      },
      {
        label: '证件资料',
        fields: ['itemAttachments'],
      },
    ],
  },
  fieldsMap: {
    username: {
      inputWidgetProps: {
        placeholder: '2-16位字母、数字、下划线、-，不能为纯数字',
      },
    },
    itemAttachments: {
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        colSpan: 24,
        columns: companyUserItemAttachmentsColumns,
      },
      displayProps: {
        component: 'ListTableDisplay',
        columns: companyUserItemAttachmentsColumns,
      },
    },
    branchOffice: {
      displayProps: { detailSpan: 4 },
      inputWidget: 'selectInput',
      dataType: 'number',
      foreignField: undefined,
      inputWidgetProps: {
        allowSearch: true,
        valueType: 'idName',
        getOptions: async () => {
          const store = useCommonStore({
            api: '/org/branchOffice/simpleList',
            queryParams: {
              nature: useUserMenuStore().getCurrentOrgNature(),
            },
          });
          const raw = await store.getList();
          return raw.map((item) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
        },
      },
    },
    name: {
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/ui/components/data-display/components/avatarDisplay.vue')),
        mode: 'capsule',
      },
    },
    avatar: { visibleInForm: false },
    mobile: {
      listProps: { columnWidth: 150 },
      inputWidget: defineAsyncComponent(() => import('../src/teacher/mobileInput.vue')),
      displayProps: {
        component: defineAsyncComponent(() => import('../src/teacher/mobileDisplay.vue')),
      },
      inputWidgetProps: {
        disabled: true,
      },
      editable: true,
      disabled: true,
    },
    lastLoginAt: {
      visibleInForm: false,
      displayProps: {
        humanizeDate: true,
      },
    },
    frozen: {
      visibleInForm: false,
      displayProps: {
        booleanDisplayOptions: {
          mode: 'switch',
          allowUpdate: true,
          confirmMessage: (val, record) => `确定要${val ? '冻结' : '解冻'} [${record.name}] 的账号吗？`,
          handler: async (val, record) => {
            Message.info(`正在${val ? '冻结' : '解冻'} [${record.name}] 的账号... 请稍后`);
            try {
              await request(`/org/companyUser/${val ? 'frozen' : 'unFrozen'}/${record.id}`, {
                method: 'PUT',
                baseURL: PROJECT_URLS.MAIN_PROJECT_API,
              });

              Message.clear();
              Message.success(`${val ? '冻结' : '解冻'}成功`);

              return val;
            } catch (e) {
              return false;
            }
          },
        },
      },
    },
    sysRoleSet: {
      inputWidget: 'selectInput',
      required: true,
      inputWidgetProps: {
        multiple: true,
        getOptions: async () => {
          const menuStore = useUserMenuStore();
          const sysRoleStore = useCommonStore({
            api: '/org/sysRole',
            queryParams: {
              unitNature: menuStore.getCurrentUnitNature(),
            },
          });
          const raw = await sysRoleStore.getList();
          return raw.map((item) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
        },
      },
      displayProps: {
        toDisplay: async (value) => {
          const sysRoleStore = useCommonStore({
            api: '/org/sysRole',
          });
          const raw = await sysRoleStore.getList();
          return raw
            .filter((item) => value?.indexOf(item.id) >= 0)
            .map((item) => {
              return item.name;
            })
            .join('、');
        },
      },
    },
    userTitleIds: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        multiple: true,
        getOptions: async () => {
          const store = useCommonStore({
            api: '/org/userTitle',
          });
          const raw = await store.getList();
          return raw.map((item) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
        },
      },
      displayProps: {
        toDisplay: async (value) => {
          const store = useCommonStore({
            api: '/org/userTitle',
          });
          const raw = await store.getList();
          return raw
            .filter((item) => value?.indexOf(item.id) >= 0)
            .map((item) => {
              return item.name;
            })
            .join('、');
        },
      },
    },
    hasSpecialEducationExp: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: ['有', '无'],
      },
    },
    consultingStatus: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        labelField: 'name',
        valueField: 'id',
        options: CONSULTING_STATUS,
      },
    },
    establishment: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: ['编制内', '编制外'],
      },
    },
    highestEducation: {
      inputWidget: 'selectInput',
      // required: true,
      inputWidgetProps: {
        options: ['博士', '硕士', '本科', '专科', '高职', '中职'],
      },
    },
    proTitle: {
      inputWidget: 'selectInput',
      // required: true,
      inputWidgetProps: {
        options: ['正高级', '副高级', '一级教师', '二级教师', '三级教师', '其他'],
      },
    },
    teachType: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: ['视力障碍', '听力障碍', '智力障碍', '言语障碍', '肢体障碍', '多重障碍', '孤独症', '情绪障碍', '其他'],
      },
    },
    teacherType: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: ['随班就读教师', '特殊教育学校(中心)教师', '康复机构教师', '资源教室教师', '资源中心教师'],
      },
    },
    idCard: {
      inputWidget: 'textInput',
      inputWidgetProps: {
        maxLength: 18,
      },
    },
    teachGrade: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: ['学前', '小学', '初中', '普高', '中职', '高职', '专科', '本科', '研究生', '其他'],
      },
    },
    teachingSubjects: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        multiple: true,
        getOptions: async () => {
          const schoolCourseStore = useSchoolCourseStore();
          const raw = await schoolCourseStore.getSchoolCourses();
          return raw.map((item) => {
            return {
              label: item.name,
              value: item.name,
            };
          });
        },
      },
    },
    rehabilitationInstitution: {
      visibleInForm: false,
    },
    institutionAccount: {
      visibleInForm: false,
      visibleInDetail: false,
    },
    roleType: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
    },
    companyList: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    cloudOrganization: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    company: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    su: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    authorities: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    passwordAlreadySet: {
      visibleInDetail: false,
      visibleInTable: false,
    },
    sysRoles: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    certified: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    certifiedAt: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    wxBinded: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    userTitleNames: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    app: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    loginAttempts: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    permissionCodes: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    passwordLastUpdateAt: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    passwordValid: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    subgroupIds: {
      visibleInForm: false,
    },
    unitInfo: {
      key: 'additionalData.unitInfo',
      label: '单位信息',
      visibleInForm: false,
    },
    additionalData: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
    },
    sendTeacher: {
      displayProps: {
        booleanDisplayOptions: {
          mode: 'switch',
          allowUpdate: true,
          confirmMessage: (val, record) => `确定要将[${record.name}] 设置为送教教师吗？`,
          handler: async (val, record) => {
            Message.info(`请稍后...`);
            try {
              await request(`/org/companyUser/setSendTeacher/${record.id}`, {
                method: 'get',
                baseURL: PROJECT_URLS.MAIN_PROJECT_API,
              });

              Message.clear();
              Message.success(`设置成功`);

              return val;
            } catch (e) {
              return false;
            }
          },
        },
      },
    },
    classInfo: {
      label: '班级',
      visibleInForm: false,
      displayProps: {
        toDisplay: (val: any) => {
          return val.map((item) => item?.name)?.join('，');
        },
      },
    },
  },
};

export default { companyUserSchema };
