import { CustomSchema } from '@repo/infrastructure/types';
import { getCollaborateRowAction, getDataSubmitAction, getCollaboratorField } from '@repo/components/utils/collaborate';
import { PROJECT_URLS } from '@repo/env-config';
import { getPeriodsList } from '@repo/infrastructure/utils/scaffolds';
import { defineAsyncComponent } from 'vue';
import { useUserStore } from '@repo/infrastructure/store';

const planDetailColumns = [
  { key: 'date', label: '送教上门日期', inputWidget: 'dateInput' },
  { key: 'target', label: '教学目标', inputWidget: 'textareaInput' },
  { key: 'content', label: '送教内容安排', inputWidget: 'textareaInput' },
  { key: 'personInCharge', label: '负责人', inputWidget: 'textInput' },
  { key: 'remark', label: '资源需求与服务', inputWidget: 'textareaInput' },
];
const planDetailColumnsDisplay = [
  { key: 'date', label: '送教上门日期', inputWidget: 'dateInput', width: 160 },
  { key: 'target', label: '教学目标', inputWidget: 'textareaInput' },
  { key: 'content', label: '送教内容安排', inputWidget: 'textareaInput' },
  { key: 'personInCharge', label: '负责人', inputWidget: 'textInput' },
  { key: 'remark', label: '资源需求与服务', inputWidget: 'textareaInput' },
];
const sepSchema: CustomSchema = {
  api: '/resourceRoom/sendEducationPlan',
  modalEdit: true,
  listViewProps: {
    size: 'mini',
    rowActionWidth: 260,
  },
  rowActions: [
    { key: 'viewDetails', label: '查看', handler() {}, expose: true },
    { key: 'records', label: '送教记录', handler() {}, expose: true, collaborate: 'Edit' },
    { key: 'view', visible: false },
    getCollaborateRowAction('SendEducationPlan'),
    getDataSubmitAction('/resourceRoom/sendEducationPlan'),
  ],
  disableRowActionHandler: (record, op) => {
    const userInfo: any = useUserStore();
    const isOwner = userInfo?.id === record.collaborators.filter((r) => r.action === 'Owner')?.[0]?.userId;
    switch (op.key) {
      case 'edit':
        if (typeof op.disabled === 'function') return op.disabled(record) || (!isOwner && !userInfo?.sendTeacher); // 不是数据拥有者，不是送教教师
        break;
      case 'delete':
        if (typeof op.disabled === 'function') return op.disabled(record) || (!isOwner && !userInfo?.sendTeacher);
        break;
      default:
        break;
    }
    return false;
  },
  formViewProps: {
    layout: 'horizontal',
    fullscreen: true,
    fieldsGrouping: [
      {
        label: '基本信息',
        colSpan: 6,
        fields: [
          'student',
          'period',
          'personInCharge',
          'school',
          'studentSymbol',
          'disabilityCertificateNo',
          'gender',
          'birthday',
          'age',
          'dateRange',
          'planTeacher',
        ],
      },
      {
        label: '送教措施',
        colSpan: 24,
        fields: ['sendEducationMeasures', 'planDetails'],
      },
      {
        label: '附件',
        colSpan: 24,
        fields: ['attachments'],
      },
      {
        label: '其他信息',
        colSpan: 24,
        fields: ['detailContent'],
      },
    ],
  },
  detailViewProps: {
    columns: 4,
    fullscreen: true,
    fieldsGrouping: [
      {
        label: '基本信息',
        colSpan: 6,
        fields: [
          'student',
          'period',
          'personInCharge',
          'school',
          'studentSymbol',
          'gender',
          'birthday',
          'age',
          'dateRange',
          'planTeacher',
        ],
      },
      {
        label: '送教措施',
        fields: ['sendEducationMeasures', 'planDetails'],
      },
      {
        label: '送教计划',
        colSpan: 24,
        fields: ['attachments'],
      },
      {
        label: '其他信息',
        colSpan: 24,
        fields: ['detailContent'],
      },
    ],
  },
  fieldsMap: {
    additionalData: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
    },
    student: {
      dataType: 'Foreign',
      foreignField: {
        api: '/resourceRoom/student',
        preload: true,
        apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
        loadPageSize: 50,
        queryParams: {
          hasSendPlan: true,
        },
      },
      inputWidgetProps: {
        valueType: (val, inputValue, options) => {
          return options.find((option) => option.id === val);
        },
        allowSearch: true,
      },
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/components/student/studentDetailButton.vue')),
      },
    },
    period: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: getPeriodsList(),
      },
      displayProps: {
        setStyle: (option: any) => {
          if (option.label.includes('春')) return { color: 'green' };
          if (option.label.includes('秋')) return { color: 'red' };
          return null;
        },
      },
    },
    gender: {
      inputWidget: 'displayInput',
      inputWidgetProps: {
        queryDepends: {
          onStudentChange(student, raw, formData) {
            formData.value.gender = student?.gender;
          },
        },
      },
    },
    disabilityCertificateNo: {
      key: 'disabilityCertificateNo',
      label: '残疾证号',
      inputWidget: 'displayInput',
      inputWidgetProps: {
        queryDepends: {
          onStudentChange(student, raw, formData) {
            formData.value.disabilityCertificateNo = student?.disabilityCertificateNo;
          },
        },
      },
    },
    birthday: {
      inputWidget: 'displayInput',
      inputWidgetProps: {
        queryDepends: {
          onStudentChange(student, raw, formData) {
            formData.value.birthday = student?.birthday;
          },
        },
      },
    },
    age: {
      inputWidget: 'displayInput',
      inputWidgetProps: {
        queryDepends: {
          onStudentChange(student, raw, formData) {
            formData.value.age = student?.age;
          },
        },
      },
    },
    studentSymbol: {
      inputWidget: 'displayInput',
      inputWidgetProps: {
        queryDepends: {
          onStudentChange(student, raw, formData) {
            formData.value.studentSymbol = student?.symbol;
          },
        },
      },
    },
    school: {
      inputWidget: 'displayInput',
      inputWidgetProps: {
        queryDepends: {
          onStudentChange(student, raw, formData) {
            formData.value.school = student?.fusionSchool?.name;
          },
        },
      },
    },
    sendArchiveSymbol: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
    },
    dateRange: {
      inputWidget: 'dateRangeInput',
      inputWidgetProps: {
        colSpan: 12,
      },
      displayProps: {
        detailSpan: 2,
        toDisplay: (val: any, record: any) => {
          if (val) return `${val}`;
          return '';
        },
      },
    },
    planTeacher: {
      inputWidgetProps: {
        colSpan: 12,
      },
    },
    detailContent: {
      inputWidget: 'richInput',
    },
    attachments: {
      inputWidget: 'uploadInput',
      displayProps: {
        component: 'AttachmentsPreviewDisplay',
      },
    },
    finished: {
      visibleInForm: false,
    },
    sendEducationMeasures: {
      inputWidget: 'textareaInput',
      displayProps: {
        detailSpan: 24,
      },
    },
    planDetails: {
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        columns: planDetailColumns,
      },
      displayProps: {
        component: 'ListTableDisplay',
        columns: planDetailColumnsDisplay,
      },
    },
    sendArchiveId: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
    },
    ...getCollaboratorField(),
  },
};

export default { sepSchema };
