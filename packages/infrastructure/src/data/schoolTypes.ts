type SchoolNature =
  | '资源中心'
  | '幼儿园'
  | '普通教育学校'
  | '特殊教育学校'
  | '职业教育学校'
  | '机构'
  | '残疾人联合委员会'
  | '特教专委会'
  | '其他';

type NormalSchoolType = '小学' | '初中' | '高中' | '教学点' | '一贯制' | '其他';

type VocationalSchoolType = '中职' | '高职';

type InstitutionType = '残疾儿童康复机构' | '福利机构' | '其他机构';

const natures: SchoolNature[] = [
  // '资源中心',
  '幼儿园',
  '普通教育学校',
  '特殊教育学校',
  '职业教育学校',
  '机构',
  '残疾人联合委员会',
  '特教专委会',
  '其他',
];

const normalSchoolGrades: string[] = [
  '一年级',
  '二年级',
  '三年级',
  '四年级',
  '五年级',
  '六年级',
  '七年级（初一）',
  '八年级（初二）',
  '九年级（初三）',
  '高一',
  '高二',
  '高三',
];

const kindergartenGrades: string[] = ['小班', '中班', '大班'];

const vocationalSchoolGrades: string[] = ['七年级', '八年级', '九年级', '高一', '高二', '高三'];

// all grades for institution
const institutionGrades: string[] = [
  '小班',
  '中班',
  '大班',
  '一年级',
  '二年级',
  '三年级',
  '四年级',
  '五年级',
  '六年级',
  '七年级（初一）',
  '八年级（初二）',
  '九年级（初三）',
  '高一',
  '高二',
  '高三',
];

const getGradesList = (schoolNature: SchoolNature): string[] => {
  switch (schoolNature) {
    case '幼儿园':
      return kindergartenGrades;
    case '普通教育学校':
      return normalSchoolGrades;
    case '职业教育学校':
      return vocationalSchoolGrades;
    case '机构':
      return institutionGrades;
    default:
      return normalSchoolGrades;
  }
};

const getGradesListOptions = (schoolNature: SchoolNature): { label: string; value: string }[] => {
  return getGradesList(schoolNature).map((grade) => ({ label: grade, value: grade }));
};

const normalSchoolTypes: NormalSchoolType[] = ['小学', '初中', '高中', '教学点', '一贯制', '其他'];

// 通用学校类型
const commonSchoolTypes: string[] = ['小学', '中学', '九年一贯制', '十二年一贯制', '其他'];

const vocationalSchoolTypes: VocationalSchoolType[] = ['中职', '高职'];

const institutionTypes: InstitutionType[] = ['残疾儿童康复机构', '福利机构', '其他机构'];

export {
  natures,
  getGradesList,
  getGradesListOptions,
  normalSchoolTypes,
  commonSchoolTypes,
  vocationalSchoolTypes,
  institutionTypes,
  SchoolNature,
  NormalSchoolType,
  VocationalSchoolType,
  InstitutionType,
};
