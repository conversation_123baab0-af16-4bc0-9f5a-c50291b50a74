{"name": "@repo/ui", "version": "2.1.52", "private": true, "exports": {"./iconfont": "./src/assets/iconfont/iconfont.js", "./components": "./src/components/index.ts", "./components/*.vue": "./src/components/*.vue", "./components/icon/*.vue": "./src/components/icon/*.vue", "./components/form": "./src/components/form/index.ts", "./components/form/inputComponents": "./src/components/form/inputComponents/index.ts", "./components/form/inputComponents/inputControl": "./src/components/form/inputComponents/inputControl/*.vue", "./components/data-display": "./src/components/data-display/index.ts", "./components/data-display/components": "./src/components/data-display/components/index.ts", "./components/data-display/components/*.ts": "./src/components/data-display/components/*.ts", "./components/data-display/components/*.vue": "./src/components/data-display/components/*.vue", "./components/description": "./src/components/description/index.ts", "./components/record-detail": "./src/components/record-detail/index.ts", "./components/table": "./src/components/table/index.ts", "./components/table/*.vue": "./src/components/table/*.vue", "./components/customizeComponent/*.vue": "./src/components/customizeComponent/*.vue", "./components/common/*.vue": "./src/components/common/*.vue", "./components/utils/*.vue": "./src/components/utils/*.vue", "./components/utils/*.ts": "./src/components/utils/*.ts", "./components/utils/*": "./src/components/utils/*.ts", "./components/upload/*.vue": "./src/components/upload/*.vue", "./directive": "./src/directive/index.ts", "./lbs/*.vue": "./src/lbs/*.vue"}, "scripts": {"lint": "eslint . --max-warnings 0", "generate:component": "turbo gen react-component"}, "dependencies": {"@repo/lbs": "workspace:*", "dayjs": "^1.11.13"}, "peerDependencies": {"@arco-design/web-vue": "^2.54.6", "@arco-plugins/vite-vue": "^1.0.0", "@repo/config": "workspace:*", "@repo/env-config": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/infrastructure": "workspace:*", "@repo/rich-editor": "workspace:*", "@repo/typescript-config": "workspace:*", "@vime/core": "^5.4.1", "@vime/vue-next": "^5.4.1", "@vueuse/core": "^10.7.2", "crypto-js": "^4.2.0", "sortablejs": "^1.15.2", "vue": "^3.0.0", "vue-cropper": "^1.1.3", "vue-print-next": "^1.0.8", "vue-router": "^4.2.5"}, "peerDependenciesMeta": {}}