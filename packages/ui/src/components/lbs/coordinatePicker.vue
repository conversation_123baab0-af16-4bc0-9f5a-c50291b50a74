<script setup lang="ts">
  import { computed, nextTick, onBeforeMount, onMounted, PropType, ref } from 'vue';
  import { debounce } from 'lodash';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { Message } from '@arco-design/web-vue';
  import { IconSearch } from '@arco-design/web-vue/es/icon';
  import { coordinateToAddress, locationSearch, tiandiMapInit } from '@repo/lbs/tiandiMap';
  import { getCurrentLocation } from '@repo/lbs/utils';
  import { Coordinate } from '@repo/lbs/types';

  const props = defineProps({
    modelValue: {
      type: Object as PropType<Coordinate>,
      default: () => ({ latitude: 0.0, longitude: 0.0 }),
    },
  });

  const searching = ref(false);
  const emit = defineEmits(['update:modelValue']);
  const coordinate = computed({
    get: () => props.modelValue,
    set: (value) => {
      emit('update:modelValue', value);
    },
  });

  const ready = ref(false);
  const mapInstance = ref<T.map>();
  const { loading, setLoading } = useLoading();
  const locationList = ref<any[]>([]);

  const handleUpdateGeoCoder = debounce(async (lng: number, lat: number) => {
    setLoading(true);
    try {
      const address = await coordinateToAddress(lng, lat);
      coordinate.value = {
        ...coordinate.value,
        ...address.addressComponent,
        formattedAddress: `${address.formatted_address}(${address.addressComponent?.poi})`,
        district: address.addressComponent?.county,
      };
    } catch (e: any) {
      Message.error('获取坐标位置名称失败');
    } finally {
      setLoading(false);
    }
  }, 1000);

  const handlePick = async (point: any) => {
    coordinate.value = {
      latitude: point.lat,
      longitude: point.lng,
    };
    handleUpdateGeoCoder(point.lng, point.lat);
    // 移除所有标记物
    mapInstance.value.clearOverLays();
    const marker = new T.Marker(new T.LngLat(point.lng, point.lat));
    mapInstance.value.addOverLay(marker);

    await nextTick(() => {
      mapInstance.value.centerAndZoom(new T.LngLat(coordinate.value.longitude, coordinate.value.latitude), 16);
      marker.enableDragging();
      // marker 拖动时间
      marker.addEventListener('dragend', async () => {
        const lnglat = marker.getLngLat();
        coordinate.value = {
          latitude: lnglat.lat,
          longitude: lnglat.lng,
        };
        handleUpdateGeoCoder(lnglat.lng, lnglat.lat);
      });
    });
  };

  const selectedLocation = ref<any>();
  const handleSearch = debounce(async (keyword: string) => {
    searching.value = true;
    try {
      if (!keyword) {
        locationList.value = [];
        return;
      }

      locationList.value = await locationSearch(mapInstance.value, keyword);

      if (locationList.value.length === 0) {
        Message.info('未找到相关地址');
      }
    } catch (e: any) {
      selectedLocation.value = undefined;
      locationList.value = [];
    } finally {
      searching.value = false;
    }
  }, 500);

  const handleSearchSelect = async (lnglat: any) => {
    const item = locationList.value.find((i) => i.value === lnglat);

    await handlePick({
      lat: Number(item.latitude),
      lng: Number(item.longitude),
    });
  };

  onBeforeMount(async () => {
    await tiandiMapInit(() => {
      ready.value = true;
    });
  });

  onMounted(async () => {
    mapInstance.value = new T.Map('map-wrapper');
    if (!coordinate.value?.latitude) {
      let currentLocation;
      try {
        currentLocation = await getCurrentLocation();
      } catch (e: any) {
        Message.error('获取当前位置失败');
        // 使用天安门位置
        currentLocation = {
          coords: {
            latitude: 39.90498,
            longitude: 116.39135,
          },
        };
      }

      try {
        await coordinateToAddress(currentLocation.coords?.longitude, currentLocation.coords?.latitude);
        coordinate.value = {
          latitude: currentLocation.coords?.latitude,
          longitude: currentLocation.coords?.longitude,
        };
      } finally {
        // ignore
      }
    }

    await nextTick(() => {
      if (coordinate.value?.latitude) {
        mapInstance.value.centerAndZoom(new T.LngLat(coordinate.value.longitude, coordinate.value.latitude), 16);
      }

      mapInstance.value.addControl(new T.Control.Zoom());
      // 地图拾取
      const cp = new T.CoordinatePickup(mapInstance.value, { callback: handlePick });
      cp.addEvent();

      // 添加标记物
      if (coordinate.value.latitude) {
        handlePick({ lat: coordinate.value.latitude, lng: coordinate.value.longitude });
      }
    });
  });
</script>

<template>
  <div v-if="ready">
    <a-form-item :required="false" style="width: 300px">
      <a-select
        v-model="selectedLocation"
        size="small"
        placeholder="输入地址搜索"
        class="flex-1"
        :data="locationList"
        :filter-option="false"
        allow-search
        allow-clear
        :loading="searching"
        @search="handleSearch"
        @change="handleSearchSelect"
      >
        <template #prefix>
          <IconSearch />
        </template>
        <a-option v-for="item in locationList" :key="item.value" :label="item.label" :value="item.value">
          <div class="leading-7 py-2">
            <div>{{ item.label }}</div>
            <small class="text-gray-500">
              <IconLocation />
              {{ item.raw.address }}
            </small>
          </div>
        </a-option>
      </a-select>
    </a-form-item>

    <div id="map-wrapper" class="w-full mt-2"></div>
    <a-spin :loading="loading" class="mt-2">
      <a-divider orientation="left">当前选中坐标信息</a-divider>
      <a-form :model="coordinate" size="small">
        <a-row :gutter="8">
          <a-col :span="6">
            <a-form-item label="经度" prop="longitude" label-col-flex="60px">
              <a-input-number v-model="coordinate.longitude" readonly :min="-180" :max="180" :step="0.01" />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="纬度" prop="latitude" label-col-flex="40px">
              <a-input-number v-model="coordinate.latitude" readonly :min="-90" :max="90" :step="0.01" />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="省份" prop="province" label-col-flex="40px">
              <a-input v-model="coordinate.province" readonly />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="城市" prop="city" label-col-flex="40px">
              <a-input v-model="coordinate.city" readonly />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="区县" prop="district" label-col-flex="40px">
              <a-input v-model="coordinate.district" readonly />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="详细地址" prop="address" label-col-flex="60px">
              <a-input v-model="coordinate.address" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="显示地址" prop="address" label-col-flex="60px">
              <a-input v-model="coordinate.formattedAddress" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </div>
</template>

<style scoped lang="scss">
  #map-wrapper {
    min-height: 400px;
    :deep .arco-form-item {
      width: 30%;
    }
  }
</style>

<style lang="scss">
  .arco-trigger-popup {
    z-index: 3002 !important;
    line-height: 10px !important;
  }
</style>
