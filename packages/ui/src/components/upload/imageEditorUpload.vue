<script lang="ts" setup>
  import { computed, nextTick, ref, watch } from 'vue';
  import { getOssProcessor, IAttachmentProcessor } from '@repo/infrastructure/upload';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    imgInfo: {
      type: String,
      default: null,
    },
    config: {
      type: Object,
      default: () => {},
    },
    description: {
      type: String,
      default: '上传图片',
    },
    maxSize: {
      type: Number,
      default: 2,
    },
    fullScreen: {
      type: Boolean,
      default: false,
    },
  });

  const emits = defineEmits(['update:imgInfo']);

  const imgInfo = computed({
    get: () => {
      return props.imgInfo || {};
    },
    set: (val) => {
      emits('update:imgInfo', val);
    },
  });
  const editVisible = ref(false);
  const croppedLoading = ref(false);
  const currentFile = ref<File>();
  const imgRef = ref<HTMLImageElement | null>(null);
  const containerRef = ref<HTMLElement | null>(null); // 外部容器

  const imageData = ref<string>('');
  const currentType = ref<string>('customized');
  const croppedImage = ref<string>(null);
  const oss: IAttachmentProcessor = getOssProcessor();

  const cropBox = ref({
    left: 0,
    top: 0,
    width: 100,
    height: 100,
  });

  const imgRect = ref({
    left: 0,
    top: 0,
    width: 100,
    height: 100,
  });

  const cropBoxStyle = computed(() => ({
    left: `${cropBox.value.left}px`,
    top: `${cropBox.value.top}px`,
    width: `${cropBox.value.width}px`,
    height: `${cropBox.value.height}px`,
  }));

  let isDragging = false;
  let isResizing = false;
  let startX = 0;
  let startY = 0;

  let animationFrameId: number | null = null;
  const getEventClientPosition = (e: MouseEvent | TouchEvent) => {
    if (e instanceof MouseEvent) {
      return { x: e.clientX, y: e.clientY };
    }
    const touch = e.touches[0] || e.changedTouches[0];
    return { x: touch.clientX, y: touch.clientY };
  };
  const onDrag = (e: MouseEvent | TouchEvent) => {
    if (!isDragging) return;
    const { x, y } = getEventClientPosition(e);
    const dx = x - startX;
    const dy = y - startY;

    const update = () => {
      let newLeft = cropBox.value.left + dx;
      let newTop = cropBox.value.top + dy;

      newLeft = Math.max(imgRect.value.left, newLeft);
      newTop = Math.max(imgRect.value.top, newTop);
      newLeft = Math.min(newLeft, imgRect.value.left + imgRect.value.width - cropBox.value.width);
      newTop = Math.min(newTop, imgRect.value.top + imgRect.value.height - cropBox.value.height);

      cropBox.value.left = newLeft;
      cropBox.value.top = newTop;

      startX = x;
      startY = y;
    };

    if (animationFrameId) cancelAnimationFrame(animationFrameId);
    animationFrameId = requestAnimationFrame(update);
  };

  const onResize = (e: MouseEvent | TouchEvent) => {
    if (!isResizing) return;
    const { x, y } = getEventClientPosition(e);
    const dx = x - startX;
    const dy = y - startY;

    const update = () => {
      const maxWidth = imgRect.value.left + imgRect.value.width - cropBox.value.left;
      const maxHeight = imgRect.value.top + imgRect.value.height - cropBox.value.top;

      if (currentType.value === 'idPhoto') {
        const ratio = 25 / 35;
        let newWidth = cropBox.value.width + dx;
        let newHeight = newWidth / ratio;

        if (newWidth > maxWidth) {
          newWidth = maxWidth;
          newHeight = newWidth / ratio;
        }
        if (newHeight > maxHeight) {
          newHeight = maxHeight;
          newWidth = newHeight * ratio;
        }
        cropBox.value.width = Math.max(30, newWidth);
        cropBox.value.height = Math.max(30, newHeight);
      } else {
        const newWidth = cropBox.value.width + dx;
        const newHeight = cropBox.value.height + dy;
        cropBox.value.width = Math.max(30, Math.min(newWidth, maxWidth));
        cropBox.value.height = Math.max(30, Math.min(newHeight, maxHeight));
      }

      startX = x;
      startY = y;
    };

    if (animationFrameId) cancelAnimationFrame(animationFrameId);
    animationFrameId = requestAnimationFrame(update);
  };

  const stopAction = () => {
    isDragging = false;
    isResizing = false;

    document.removeEventListener('mousemove', onDrag);
    document.removeEventListener('mousemove', onResize);
    document.removeEventListener('mouseup', stopAction);

    document.removeEventListener('touchmove', onDrag);
    document.removeEventListener('touchmove', onResize);
    document.removeEventListener('touchend', stopAction);
  };

  const loadImagePreview = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      imageData.value = e.target?.result as string;
    };
    reader.readAsDataURL(file);
  };

  const startDrag = (e: MouseEvent | TouchEvent) => {
    const { x, y } = getEventClientPosition(e);
    isDragging = true;
    startX = x;
    startY = y;
    document.addEventListener('mousemove', onDrag);
    document.addEventListener('mouseup', stopAction);

    document.addEventListener('touchmove', onDrag);
    document.addEventListener('touchend', stopAction);
  };

  const startResize = (e: MouseEvent | TouchEvent) => {
    const { x, y } = getEventClientPosition(e);
    isResizing = true;
    startX = x;
    startY = y;
    document.addEventListener('mousemove', onResize);
    document.addEventListener('mouseup', stopAction);

    document.addEventListener('touchmove', onResize);
    document.addEventListener('touchend', stopAction);
  };

  const validatedImgSize = (size: number): boolean => {
    if (!size) return false;
    if (size / 1024 / 1024 > props.maxSize) {
      Message.error(`文件大小不能超过 ${props.maxSize}MB`);
      return false;
    }
    return true;
  };

  const handlePreUpload = (file: File) => {
    const isPass = validatedImgSize(file?.size);
    if (!isPass) return;
    currentFile.value = file;
    loadImagePreview(file);
    editVisible.value = true;
  };

  const handleClick = (type: 'customized' | 'idPhoto') => {
    currentType.value = type;

    const img = imgRef.value;
    if (!img) return;

    const imgWidth = img.clientWidth;
    const imgHeight = img.clientHeight;

    if (type === 'idPhoto') {
      const targetRatio = 25 / 35;

      let cropW = imgWidth;
      let cropH = cropW / targetRatio;

      if (cropH > imgHeight) {
        cropH = imgHeight;
        cropW = cropH * targetRatio;
      }

      cropBox.value.width = cropW;
      cropBox.value.height = cropH;

      cropBox.value.left = img.offsetLeft + (imgWidth - cropW) / 2;
      cropBox.value.top = img.offsetTop + (imgHeight - cropH) / 2;
    } else {
      cropBox.value.left = img.offsetLeft;
      cropBox.value.top = img.offsetTop;
      cropBox.value.width = imgWidth;
      cropBox.value.height = imgHeight;
    }
  };

  const dataURLtoFile = async (dataUrl: string, filename: string): Promise<File> => {
    const res = await fetch(dataUrl);
    const blob = await res.blob();
    return new File([blob], filename, { type: blob.type });
  };

  const uploadImage = async (): string => {
    // const file: File = await dataURLtoBlob(croppedImage.value, 'test.png');
    const file: File = await dataURLtoFile(croppedImage.value, currentFile.value?.name);
    const url = await oss.uploadSimply({ file }, 'attachment/', { headers: props.config });
    return url;
  };

  const cropImage = async () => {
    if (!imgRef.value || !imageData.value) return;
    try {
      croppedLoading.value = true;
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      const img = imgRef.value;

      const scaleX = img.naturalWidth / img.clientWidth;
      const scaleY = img.naturalHeight / img.clientHeight;

      const cropX = (cropBox.value.left - imgRect.value.left) * scaleX;
      const cropY = (cropBox.value.top - imgRect.value.top) * scaleY;
      const cropWidth = cropBox.value.width * scaleX;
      const cropHeight = cropBox.value.height * scaleY;

      canvas.width = cropWidth;
      canvas.height = cropHeight;

      ctx.drawImage(img, cropX, cropY, cropWidth, cropHeight, 0, 0, cropWidth, cropHeight);

      croppedImage.value = canvas.toDataURL('image/png');

      const url = await uploadImage();
      imgInfo.value = {
        url,
        name: currentFile.value.name.split('.')?.[0] || '',
      };

      editVisible.value = false;
    } finally {
      croppedLoading.value = false;
    }
  };

  const resetParams = () => {
    editVisible.value = false;
    currentType.value = 'customized';
    currentFile.value = undefined;
    imageData.value = '';

    cropBox.value = {
      left: 0,
      top: 0,
      width: 100,
      height: 100,
    };

    imgRect.value = {
      left: 0,
      top: 0,
      width: 100,
      height: 100,
    };

    isDragging = false;
    isResizing = false;
    startX = 0;
    startY = 0;

    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
      animationFrameId = null;
    }
  };

  watch(imageData, async (val) => {
    if (!val) return;
    await nextTick();

    const img = imgRef.value;
    const container = containerRef.value;
    if (!img || !container) return;

    cropBox.value.left = img.offsetLeft;
    cropBox.value.top = img.offsetTop;
    cropBox.value.width = img.clientWidth;
    cropBox.value.height = img.clientHeight;

    imgRect.value.left = img.offsetLeft;
    imgRect.value.top = img.offsetTop;
    imgRect.value.width = img.clientWidth;
    imgRect.value.height = img.clientHeight;
  });

  watch(editVisible, (visible) => {
    if (!visible) {
      currentFile.value = undefined;
      imageData.value = '';
      cropBox.value.left = 0;
      cropBox.value.top = 0;
      cropBox.value.width = 100;
      cropBox.value.height = 100;
    }
  });
  const defaultClass = 'bg-blue-500/80 border-white text-white font-semibold';
  const activeClass = 'bg-gray-700 border-gray-600 hover:border-blue-300 hover:bg-blue-400/30';
</script>

<template>
  <a-upload
    :show-file-list="false"
    accept="image/*"
    :limit="1"
    :on-before-upload="handlePreUpload"
    class="w-full h-full"
  >
    <template #upload-button>
      <div
        :class="[
          'w-full h-full rounded-lg  transition-all duration-200  ',
          'flex flex-col justify-center items-center cursor-pointer hover:border-blue-500 hover:bg-blue-50 bg-gray-50',
          imgInfo?.url || croppedImage ? 'shadow-lg' : 'border-2 border-dashed border-gray-300',
        ]"
      >
        <div v-if="!croppedImage && !imgInfo?.url" class="text-center text-gray-400">
          <icon-plus size="24" />
          <div class="text-xs mt-1">{{ description }}</div>
        </div>
        <img
          v-else-if="imgInfo?.url || croppedImage"
          :src="imgInfo?.url || croppedImage"
          class="w-full h-full object-cover rounded-lg"
          alt=""
        />
      </div>
    </template>
  </a-upload>

  <a-modal
    v-model:visible="editVisible"
    :footer="false"
    :title="false"
    :closable="false"
    :width="fullScreen ? '100%' : '60%'"
    :body-style="{ padding: 0 }"
    @close="resetParams"
  >
    <div :class="fullScreen ? 'h-screen' : 'h-[600px]'">
      <div class="text-white flex justify-end bg-gray-900 pt-2 pr-2">
        <icon-close class="hover:text-red-500 cursor-pointer" @click="resetParams" />
      </div>
      <div class="w-full flex justify-center h-full relative select-none p-4 space-x-4 bg-gray-900">
        <!-- 左侧：图像和裁剪框 -->
        <div
          ref="containerRef"
          class="w-3/4 h-full relative overflow-hidden flex justify-center items-center shadow-2xl rounded-lg"
        >
          <img ref="imgRef" :src="imageData" class="object-contain max-w-full max-h-full rounded" alt="待裁剪图片" />
          <div
            class="absolute border-2 border-dashed border-cyan-400 transition-all duration-100"
            :style="cropBoxStyle"
            @mousedown="startDrag"
            @touchstart.prevent="startDrag"
          >
            <div
              class="absolute w-3 h-3 rounded-full border border-black bg-white cursor-se-resize shadow-sm"
              style="bottom: -5px; right: -5px"
              @mousedown.stop="startResize"
              @touchstart.stop.prevent="startResize"
            />
          </div>
        </div>

        <!-- 右侧：操作栏 -->
        <div
          :class="[
            'text-white text-center flex flex-col space-y-4 relative',
            'w-1/4 h-full shadow-xl p-4 bg-gray-800 rounded-lg',
          ]"
        >
          <div
            class="w-full py-2 px-4 rounded-lg cursor-pointer border-2 transition-all duration-150"
            :class="currentType === 'idPhoto' ? defaultClass : activeClass"
            @click="handleClick('idPhoto')"
          >
            证件照
          </div>

          <div
            class="w-full py-2 px-4 rounded-lg cursor-pointer border-2 transition-all duration-150"
            :class="currentType === 'customized' ? defaultClass : activeClass"
            @click="handleClick('customized')"
          >
            自定义
          </div>

          <div class="absolute bottom-4 right-4 left-4">
            <div class="flex justify-center">
              <div
                type="primary"
                :class="[
                  '!bg-gradient-to-r !from-cyan-500 !to-blue-500 !text-white',
                  '!shadow-lg px-6 py-2 rounded-full cursor-pointer max-w-[120px]',
                ]"
                @click="cropImage"
              >
                <icon-loading v-if="croppedLoading" />
                确认裁剪
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
