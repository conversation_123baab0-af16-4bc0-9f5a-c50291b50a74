<script setup lang="ts">
  import { computed, onBeforeUnmount, PropType, ref } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { cloneDeep } from 'lodash';
  import CrudForm from './form.vue';

  const props = defineProps({
    modelValue: {
      type: Object as PropType<Record<string, any>>,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    modalName: {
      type: String,
      default: '',
    },
    schema: {
      type: Object as PropType<any>,
    },
    modalWidth: {
      type: [String, Number],
      default: 700,
    },
    defaultRef: {
      type: Object as PropType<HTMLElement | null>,
      default: () => null,
    },
  });

  const { loading, setLoading } = useLoading();

  const emit = defineEmits(['update:visible', 'update:modelValue', 'close', 'ok']);

  const editVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val),
  });

  const crudFormRef = ref<any>(null);
  const editData = ref<any>({});
  const ready = ref(false);
  const modalTitle = computed(() => {
    return editData.value?.id ? `编辑${props.modalName}` : `新增${props.modalName}`;
  });

  const fullscreen = ref(props.schema?.formViewProps?.fullscreen);

  const handleToggleFullscreen = () => {
    fullscreen.value = !fullscreen.value;
  };

  const handleClose = () => {
    editData.value = {};
    ready.value = false;
    emit('close');
    return true;
  };

  const handleOk = (res) => {
    emit('ok', res, !!editData.value?.id);
    editVisible.value = false;
    editData.value = {};
    ready.value = false;
    return true;
  };

  const handleOpen = () => {
    editVisible.value = true;
    editData.value = cloneDeep(props.modelValue);
    ready.value = true;
  };

  const handleSubmit = async (done: any) => {
    setLoading(true);
    if (!crudFormRef.value && props.defaultRef?.handleSubmit) {
      try {
        await props.defaultRef.handleSubmit();
        return true;
      } finally {
        setLoading(false);
      }
    }

    try {
      const form = crudFormRef.value.formRef;
      const errors = await form.validate();
      if (errors) {
        return false;
      }
      await crudFormRef.value.handleSubmit(editData.value);
      emit('update:modelValue', editData.value);
      done();

      return true;
    } finally {
      setLoading(false);
    }
  };

  defineExpose({
    editVisible,
    editData,
    handleClose,
    handleOpen,
    handleSubmit,
    crudFormRef,
  });
</script>

<template>
  <a-modal
    v-model:visible="editVisible"
    :title="modalTitle"
    :ok-loading="loading"
    :on-before-ok="handleSubmit"
    :width="modalWidth"
    :mask-closable="false"
    :esc-to-close="false"
    :on-before-cancel="handleClose"
    :on-before-close="handleClose"
    :render-to-body="false"
    v-bind="$attrs"
    :fullscreen="fullscreen"
    @open="handleOpen"
  >
    <template #title>
      <div class="flex-1 flex mr-4 gap-2 justify-between">
        {{ modalTitle }}
        <a-button size="mini" type="secondary" @click="handleToggleFullscreen">
          <template #icon>
            <IconFullscreen v-if="!fullscreen" />
            <IconFullscreenExit v-else />
          </template>
          {{ fullscreen ? '退出全屏' : '全屏' }}
        </a-button>
      </div>
      <div style="width: 20px"></div>
    </template>
    <slot>
      <crud-form
        v-if="editVisible && ready"
        v-bind="$attrs"
        ref="crudFormRef"
        v-model="editData"
        :schema="schema"
        :show-actions="false"
        :callback="handleOk"
      />
    </slot>
  </a-modal>
</template>

<style scoped lang="scss"></style>
