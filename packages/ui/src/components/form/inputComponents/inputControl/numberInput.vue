<template>
  <a-input-number v-model="inputValue" v-bind="extend(defaultProps, schemaField.inputWidgetProps || {}, $attrs)">
    <template v-if="schemaField.inputWidgetProps?.suffix" #suffix>
      {{ schemaField.inputWidgetProps?.suffix }}
    </template>

    <template v-if="schemaField.inputWidgetProps?.prefix" #prefix>
      {{ schemaField.inputWidgetProps?.prefix }}
    </template>
  </a-input-number>
</template>

<script setup lang="ts">
  import { cloneDeep, extend } from 'lodash';
  import { SchemaField } from '@repo/infrastructure/types';
  import { computed } from 'vue';
  import { defaultInputWidgetAttributes } from '../../inputWidgetAttributes';

  const defaultProps = cloneDeep(defaultInputWidgetAttributes);
  const props = defineProps<{
    modelValue: any;
    schemaField: SchemaField;
  }>();

  const emits = defineEmits(['update:modelValue']);

  const inputValue = computed({
    get() {
      if (props.modelValue === undefined) {
        return NaN as number;
      }
      return props.modelValue as number;
    },
    set(val: any) {
      emits('update:modelValue', val);
    },
  });
</script>
