<template>
  <a-select
    v-model="inputValue"
    :placeholder="attributes.placeholder || '请选择'"
    v-bind="attributes"
    show-footer-on-empty
    :allow-create="schemaField.inputWidgetProps?.allowCreate"
    @search="handleSearch"
    @focus="handleFocus"
  >
    <a-option v-for="(option, index) in computedOptions" :key="index" :value="isBaseTypeList ? option : option.value">
      <span :style="setStyle(option)">{{ isBaseTypeList ? option : option.label }}</span>
      <a-divider direction="vertical" />
      <icon-edit
        v-if="schemaField.inputWidgetProps?.allowEdit"
        class="text-blue-500"
        @click.stop="handleEdit(option)"
      />
      <icon-delete
        v-if="schemaField.inputWidgetProps?.allowDelete"
        class="ml-2 text-red-500"
        @click.stop="handleDelete(option)"
      />
    </a-option>
    <template #footer>
      <div v-if="attributes.createRemote">
        <a-button class="w-full" @click="handleShowCreate">
          <IconPlus />
          创建新的
        </a-button>
      </div>
    </template>
  </a-select>
  <!--<modal-form
    v-if="attributes.createRemote"
    ref="modalFormRef"
    v-model="newRecord"
    v-model:visible="createNewVisible"
    :schema="attributes.createRemote?.schema"
    @ok="handleAfterCreate"
  />-->
  <teleport to="body">
    <modal-form
      v-if="attributes.createRemote"
      ref="modalFormRef"
      v-model="newRecord"
      v-model:visible="createNewVisible"
      :schema="attributes.createRemote?.schema"
      @ok="handleAfterCreate"
    />
  </teleport>
</template>

<script lang="ts" setup>
  import { computed, inject, nextTick, onMounted, PropType, ref } from 'vue';
  import { Message, Modal, SelectOptionData } from '@arco-design/web-vue';
  import { SchemaField } from '@repo/infrastructure/types';
  import { cloneDeep, extend, isArray, isFunction, isObject, isPlainObject, merge } from 'lodash';
  import { CommonApi } from '@repo/infrastructure/crud';
  import { request } from '@repo/infrastructure/request';
  import { defaultInputWidgetAttributes } from '../../inputWidgetAttributes';
  import ModalForm from '../../modalForm.vue';

  const defaultProps = cloneDeep(defaultInputWidgetAttributes);

  const props = defineProps({
    modelValue: {
      type: [String, Number, Object, Boolean] as PropType<any>,
      required: true,
      default: '',
    },
    schemaField: {
      type: Object as PropType<SchemaField>,
      required: true,
    },
    labelField: {
      type: String,
    },
    valueField: {
      type: String,
    },
    record: {
      type: Object,
    },
  });

  const emits = defineEmits(['update:modelValue', 'delete']);
  const computedOptions = ref<any[]>([] as SelectOptionData[]);
  const rawOptions = ref<any[]>([]);
  const computedAttrs = ref<any>({});
  const inputWidgetProps = ref(cloneDeep(props.schemaField?.inputWidgetProps || {}));

  const createNewVisible = ref(false);
  const newRecord = ref({});
  const isEdit = ref(false);

  const attributes = computed(() => {
    return merge({}, defaultProps, inputWidgetProps.value, computedAttrs.value);
  });

  delete inputWidgetProps.value.options;

  const inputValue = computed({
    get() {
      if (props.modelValue === '' || JSON.stringify(props.modelValue) === '{}') {
        return undefined;
      }
      if (isArray(props.modelValue)) {
        return props.modelValue.map((item) => {
          return item?.id || item?.value || item;
        });
      }
      return props.modelValue?.id || props.modelValue?.value || props.modelValue;
    },
    set(val) {
      if (!val) {
        emits('update:modelValue', undefined);
        return;
      }
      if (props.schemaField?.inputWidgetProps?.valueType === 'raw') {
        val = computedOptions.value.find((item) => item.value === val)?.raw;
      } else if (props.schemaField?.inputWidgetProps?.valueType === 'idName') {
        const resItem = computedOptions.value.find((item) => item.value === val)?.raw;
        val = {
          id: resItem.value === undefined ? resItem?.id : resItem.value,
          name: resItem.label === undefined ? resItem?.name : resItem.label,
        };
      } else if (typeof props.schemaField?.inputWidgetProps?.valueType === 'function') {
        val = props.schemaField?.inputWidgetProps?.valueType(val, computedOptions.value, rawOptions.value);
      }

      emits('update:modelValue', val);
    },
  });

  const formData: any = inject('formData');

  const handleSearch = async (keyword: string) => {
    if (!props.schemaField.foreignField?.api) {
      return;
    }
    computedAttrs.value = extend(computedAttrs.value, {
      loading: true,
    });
    const foreignApi: CommonApi<any, Record<string, any>> = CommonApi.getInstanceByApi(
      props.schemaField.foreignField?.api,
    );
    const foreignApiQueryParams = {};
    if (typeof props.schemaField?.foreignField?.queryParams === 'function') {
      Object.assign(foreignApiQueryParams, await props.schemaField?.foreignField?.queryParams(formData.value));
    } else if (isObject(props.schemaField?.foreignField?.queryParams)) {
      Object.assign(foreignApiQueryParams, props.schemaField?.foreignField?.queryParams || {});
    }
    const res: any = await foreignApi.fetchList(
      {
        page: 1,
        pageSize: props.schemaField.foreignField?.loadPageSize || 999,
        filters: [
          {
            field: props.schemaField.foreignField?.labelField,
            operator: 'Like',
            value: `${keyword}`,
          },
        ],
        ...foreignApiQueryParams,
      },
      {
        baseURL: props.schemaField.foreignField?.apiBaseUrl,
      },
    );
    computedOptions.value = [];
    rawOptions.value = res.data?.items || res.data?.list || [];

    // 手动处理选项数据
    if (
      props.schemaField.foreignField?.foreignOptionsHandler &&
      isFunction(props.schemaField.foreignField.foreignOptionsHandler)
    ) {
      const rawList = res.data?.items || res.data?.list || [];
      rawOptions.value = (await props.schemaField?.foreignField.foreignOptionsHandler(rawList)) || rawList;
    }

    const options: any[] = [];
    await nextTick(() => {
      rawOptions.value.forEach((item: any) => {
        options.push({
          value: item.id,
          label: item[props.schemaField.foreignField?.labelField || 'name'],
          raw: item,
        });
      });
      computedOptions.value = options;
      computedAttrs.value = extend(computedAttrs.value, {
        loading: false,
      });
    });
  };

  const handleShowCreate = () => {
    createNewVisible.value = true;
    newRecord.value = {};
  };

  const handleAfterCreate = (data: any) => {
    createNewVisible.value = false;
    if (isEdit.value) {
      computedOptions.value.forEach((item) => {
        if (item.value === data?.id) {
          item.label = data[props.schemaField.foreignField?.labelField || 'name'];
          item.raw = data;
        }
      });
    } else {
      computedOptions.value.push({
        value: data?.id,
        label: data[props.schemaField.foreignField?.labelField || 'name'],
        raw: data,
      });
      if (attributes.value.createRemote?.onCreated) {
        attributes.value.createRemote.onCreated(data);
      }
    }
    isEdit.value = false;

    if (attributes.value.multiple) {
      inputValue.value = [...inputValue.value, data?.id];
    } else {
      inputValue.value = data?.id;
    }
  };

  const loadOptions = async () => {
    const schemaOptions: any = props.schemaField.options || props.schemaField?.inputWidgetProps?.options;
    const labelField = props.labelField || props.schemaField?.inputWidgetProps?.labelField || 'label';
    const valueField = props.valueField || props.schemaField?.inputWidgetProps?.valueField || 'value';
    if (isArray(schemaOptions)) {
      // schma define options array
      if (isPlainObject(schemaOptions[0])) {
        computedOptions.value = schemaOptions.map((item: any) => {
          return {
            value: item[valueField],
            label: item[labelField],
            raw: item,
          };
        });
      } else {
        computedOptions.value = schemaOptions.map((item: any) => {
          return {
            value: item,
            label: item,
            raw: item,
          };
        });
      }
    } else if (isFunction(props.schemaField?.inputWidgetProps?.getOptions)) {
      // schema define getOptions function
      // defined on schemaField
      computedAttrs.value = extend(computedAttrs.value, {
        loading: true,
      });
      try {
        const raw = (await props.schemaField?.inputWidgetProps?.getOptions(props.record)) || [];
        computedOptions.value = raw.map((item) => {
          return {
            value: item[valueField],
            label: item[labelField],
            raw: item,
          };
        });
      } finally {
        computedAttrs.value = extend(computedAttrs.value, {
          loading: false,
        });
      }
    } else if (isPlainObject(schemaOptions)) {
      // schema define options as map
      computedOptions.value = Object.entries(schemaOptions).map(([value, label]: [any, any]) => {
        if (isPlainObject(label)) {
          label = label as object;
          label = label[labelField] || label.toString();
        }
        return {
          value,
          label,
        };
      });
    } else if (props.schemaField.dataType === 'Foreign' && props.schemaField.foreignField?.api) {
      computedOptions.value = [];
      if (props.schemaField.foreignField?.preload) {
        await handleSearch('');
      }
    }
  };

  const isBaseTypeList = computed(() => {
    return computedOptions?.value.every((item) => !isPlainObject(item));
  });
  const setStyle = (option: any) => {
    const styleFunc = props.schemaField.displayProps?.setStyle;
    if (typeof styleFunc === 'function') {
      return styleFunc(option);
    }
    return null;
  };
  const handleEdit = (option: any) => {
    newRecord.value = option.raw;
    createNewVisible.value = true;
    isEdit.value = true;
  };
  const handleDelete = async (option: any) => {
    Modal.confirm({
      title: '删除确认',
      content: '确定删除该选项吗？',
      onOk: async () => {
        const { api, destroy, baseURL } = attributes.value.deleteProps;
        try {
          await request(`${api}/${destroy ? 'destroy/' : ''}${option.value}`, {
            baseURL,
            method: 'delete',
          });

          if (attributes.value.multiple) {
            const val = inputValue.value;
            if (isArray(val)) {
              inputValue.value = val.filter((item) => item !== option.value);
            } else {
              inputValue.value = [];
            }
          } else {
            inputValue.value = option.value === inputValue.value ? '' : inputValue.value;
          }

          computedOptions.value = computedOptions.value.filter((item) => item.value !== option.value);
          emits('delete', option, computedOptions.value);
          Message.success('删除成功');
        } finally {
          /**/
        }
      },
    });
  };

  const handleFocus = async () => {
    if (props.schemaField?.inputWidgetProps?.reloadOptionOnFocus) {
      await loadOptions();
    }
  };

  onMounted(async () => {
    await loadOptions();
  });
</script>

<style scoped></style>
