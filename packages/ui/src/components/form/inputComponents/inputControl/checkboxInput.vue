<template>
  <a-checkbox-group
    v-model="inputValue"
    :options="schemaField.options"
    v-bind="extend(defaultProps, schemaField.inputWidgetProps || {}, $attrs)"
  />
</template>

<script lang="ts" setup>
  import { computed, PropType, useAttrs } from 'vue';
  import { SchemaField } from '@repo/infrastructure/types';
  import { cloneDeep, extend } from 'lodash';
  import { defaultInputWidgetAttributes } from '../../inputWidgetAttributes';

  const defaultProps = cloneDeep(defaultInputWidgetAttributes);

  const props = defineProps({
    modelValue: {
      type: Array as PropType<string[]>,
      required: true,
      default: () => [],
    },
    schemaField: {
      type: Object as PropType<SchemaField>,
      required: true,
    },
  });
  const attrs = useAttrs();
  const emits = defineEmits(['update:modelValue']);
  // const computedOptions = ref<SelectOptionData>([]);
  // const selectProps = computed<SelectProps>(() => {
  //   return {
  //     ...props.schemaField.props,
  //     ...attrs,
  //   };
  // });
  const inputValue = computed({
    get() {
      return props.modelValue;
    },
    set(val: any) {
      emits('update:modelValue', val);
    },
  });
</script>

<script lang="ts">
  export default {
    name: 'CheckboxInput',
  };
</script>

<style scoped></style>
