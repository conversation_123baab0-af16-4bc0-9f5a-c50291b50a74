<template>
  <a-cascader
    v-model="inputValue"
    v-bind="extend(defaultProps, schemaField.inputWidgetProps || {}, $attrs)"
    :options="cityData"
    expand-trigger="hover"
  />
</template>

<script setup lang="ts">
  import { cloneDeep, extend } from 'lodash';
  import { SchemaField } from '@repo/infrastructure/types';
  import { computed } from 'vue';
  import { cityData } from '@repo/infrastructure/data';
  import { defaultInputWidgetAttributes } from '../../inputWidgetAttributes';

  const defaultProps = {
    ...cloneDeep(defaultInputWidgetAttributes),
    allowSearch: true,
    placeholder: '请选择',
  };
  const props = defineProps<{
    modelValue: any;
    schemaField: SchemaField;
  }>();

  const emits = defineEmits(['update:modelValue']);

  const inputValue = computed({
    get() {
      if (props.modelValue === undefined) {
        return null;
      }
      return props.modelValue as string;
    },
    set(val: any) {
      emits('update:modelValue', val);
    },
  });
</script>
