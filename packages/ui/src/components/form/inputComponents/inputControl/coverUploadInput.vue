<template>
  <a-upload
    :file-list="inputValue ? [inputValue] : []"
    :accept="schemaField?.accept || 'image/*'"
    :show-file-list="false"
    :draggable="true"
    :limit="2"
    :custom-request="handleUpload as any"
  >
    <template #upload-button>
      <a-spin :loading="loading" style="width: 100%">
        <slot>
          <div
            class="cover-preview"
            :style="{
              width: `${boxWidth}px`,
              height: `${boxHeight}px`,
            }"
          >
            <a-tooltip v-if="inputValue" content="删除" class="cursor-pointer">
              <a-button shape="circle" class="close-btn" status="danger" @click.stop="handleRemove">
                <template #icon>
                  <IconClose />
                </template>
              </a-button>
            </a-tooltip>
            <div class="empty" :class="{ uploaded: !!inputValue }">
              <IconPlus :size="50" />
              <div class="tips">
                <slot class="tips">{{ tips }}</slot>
              </div>
            </div>
            <div class="preview">
              <a-image v-if="inputValue" :width="boxWidth" :height="boxHeight" fit="contain" :src="inputValue" />
            </div>
          </div>
        </slot>
      </a-spin>
    </template>
  </a-upload>
</template>

<script setup lang="ts">
  import { computed, onMounted } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { getOssProcessor, IAttachmentProcessor } from '@repo/infrastructure/upload';

  const props = defineProps({
    modelValue: {
      type: String,
      default: '',
    },
    schemaField: {
      type: Object,
      default: () => ({}),
    },
    boxWidth: {
      type: Number,
      default: undefined,
    },
    boxHeight: {
      type: Number,
      default: 100,
    },
    tips: {
      type: String,
      default: '点击或拖拽到此处上传',
    },
  });

  const { setLoading, loading } = useLoading();

  const emits = defineEmits(['update:modelValue']);
  let attachmentProcessor: IAttachmentProcessor;

  const inputValue = computed({
    get() {
      return props.modelValue;
    },
    set(val: any) {
      emits('update:modelValue', val);
    },
  });
  const handleUpload = async (options: any) => {
    setLoading(true);

    try {
      inputValue.value = await attachmentProcessor.uploadSimply(options.fileItem);
    } finally {
      setLoading(false);
    }
  };

  const handleRemove = () => {
    inputValue.value = undefined;
  };

  onMounted(async () => {
    attachmentProcessor = await getOssProcessor();
  });
</script>

<style scoped lang="less">
  .a-upload {
    width: 100%;
    display: block;
    text-align: center;
  }
  .cover-preview {
    height: 260px;
    width: 100%;
    border: 2px dashed #d9d9d9;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    border-radius: 4px;
    position: relative;

    &:hover {
      border-color: #1890ff;
      .empty {
        display: flex !important;
      }
    }

    .empty {
      position: absolute;
      background: rgba(var(--primary-6), 0.06);
      left: 0;
      top: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      z-index: 2;
      transition: all linear 0.2s;
      &.uploaded {
        background: rgba(#fff, 0.8);
        display: none;
      }
      .tips {
        color: #666;
        font-size: 12px;
        margin-top: 8px;
      }
    }
  }

  .close-btn {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 999;
  }
</style>
