<template>
  <div class="flex gap-2 flex-1">
    <a-input v-if="inputValue" v-model="inputValue.formattedAddress" class="flex-1" @change="handleChange" />
    <coordinate-picker-button v-model="inputValue" />
  </div>
  {{ inputValue }}
</template>

<script setup lang="ts">
  import { cloneDeep } from 'lodash';
  import { SchemaField } from '@repo/infrastructure/types';
  import { computed } from 'vue';
  import CoordinatePickerButton from '../../../lbs/coordinatePickerButton.vue';
  import { defaultInputWidgetAttributes } from '../../inputWidgetAttributes';

  const defaultProps = cloneDeep(defaultInputWidgetAttributes);
  const props = defineProps<{
    modelValue: any;
    schemaField: SchemaField;
  }>();

  const emits = defineEmits(['update:modelValue']);

  const handleChange = (val) => {
    console.log(val);
  };
  const inputValue = computed({
    get() {
      return props.modelValue || {};
    },
    set(val: any) {
      emits('update:modelValue', val);
    },
  });
</script>
