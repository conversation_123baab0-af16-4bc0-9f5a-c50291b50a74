<script setup lang="ts">
  import { computed, PropType, ref, onMounted, onUnmounted, watch } from 'vue';
  import { SchemaField } from '@repo/infrastructure/types';
  import { cloneDeep, extend } from 'lodash';
  import { getOssProcessor } from '@repo/infrastructure/upload';
  import { Message } from '@arco-design/web-vue';
  import { randomColor } from '../../../utils/randomColor';
  import { defaultInputWidgetAttributes } from '../../inputWidgetAttributes';

  const props = defineProps({
    modelValue: {
      type: Object as PropType<Record<string, any>>,
      required: true,
      default: () => ({
        url: null,
        udf1: null,
      }),
    },
    schemaField: {
      type: Object as PropType<SchemaField>,
      required: true,
    },
    option: {
      type: Object,
      default: () => ({}),
    },
    useType: {
      type: String,
      default: 'Web',
    },
  });

  const defaultProps = cloneDeep(defaultInputWidgetAttributes);

  const emit = defineEmits(['update:modelValue', 'saveSignature', 'closeSignature']);

  const canvasRef = ref<HTMLCanvasElement | null>(null);
  let ctx: CanvasRenderingContext2D | null = null;
  let isDrawing = false;
  const isDrawn = ref(false);
  let lastX = 0;
  let lastY = 0;

  const oss = getOssProcessor();
  const isLoading = ref(false);

  const signatureVal = computed({
    get() {
      return props.modelValue;
    },
    set(value: any | null) {
      emit('update:modelValue', value);
    },
  });

  // Accept string values (like '100%', '50vw', '300px')
  const totalWidth = computed(() => {
    return props.schemaField?.inputWidgetProps?.width || '350px';
  });

  const totalHeight = computed(() => {
    return props.schemaField?.inputWidgetProps?.height || '200px';
  });

  const actionDirection = computed(() => {
    return props.schemaField.inputWidgetProps?.actionDirection || 'bottom';
  });

  // Fixed internal resolution for crisp drawing
  const canvasResolution = {
    width: 350,
    height: 200,
  };

  const buttonAreaHeight = 50;
  const buttonAreaWidth = 80;

  // Internal canvas dimensions (fixed numbers)
  const canvasWidth = computed(() => {
    return actionDirection.value === 'bottom' ? canvasResolution.width : canvasResolution.width - buttonAreaWidth;
  });

  const canvasHeight = computed(() => {
    return actionDirection.value === 'bottom' ? canvasResolution.height - buttonAreaHeight : canvasResolution.height;
  });

  const containerFlexClass = computed(() => {
    return actionDirection.value === 'bottom' ? 'flex-col' : 'flex-row';
  });

  const buttonContainerStyle = computed(() => {
    if (actionDirection.value === 'bottom') {
      return { height: `${buttonAreaHeight}px`, width: '100%' };
    }
    return { width: `${buttonAreaWidth}px`, height: '100%' };
  });

  const buttonContainerFlexClass = computed(() => {
    return actionDirection.value === 'bottom' ? 'flex-row' : 'flex-col';
  });
  const updateCanvasSize = () => {
    if (!canvasRef.value) return;

    const container: any = canvasRef.value.parentElement || {};
    const dpr = window.devicePixelRatio || 2;
    const width = container?.clientWidth || 0;
    const height = container?.clientHeight || 0;

    // 设置样式大小（显示大小）
    canvasRef.value.style.width = `${width}px`;
    canvasRef.value.style.height = `${height}px`;

    // 设置实际像素大小（物理分辨率）
    canvasRef.value.width = width * dpr;
    canvasRef.value.height = height * dpr;
  };

  const resetCtx = () => {
    if (ctx && canvasRef.value) {
      const dpr = window.devicePixelRatio || 1;

      ctx.lineWidth = 5 * dpr;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
      ctx.strokeStyle = '#000';
      ctx.imageSmoothingEnabled = true;
    }
  };

  const initCanvas = () => {
    if (canvasRef.value) {
      ctx = canvasRef.value.getContext('2d');
      if (ctx) {
        resetCtx();
        canvasRef.value.width = canvasWidth.value;
        canvasRef.value.height = canvasHeight.value;
        updateCanvasSize();
        const url = props.modelValue?.url || props.modelValue?.udf1;
        // if (url && url.startsWith('http')) {
        // const img = new Image();
        // img.crossOrigin = 'Anonymous';
        // img.onload = () => {
        //   if (ctx && canvasRef.value) {
        //     ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
        //     ctx.drawImage(img, 0, 0, canvasRef.value.width, canvasRef.value.height);
        //     isDrawn.value = true;
        //   }
        // };
        // img.src = url;
        // } else {
        ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
        isDrawn.value = false;
        // }
      }
    }
  };

  const startDrawing = (e: MouseEvent | TouchEvent) => {
    if (!canvasRef.value) return;

    isDrawing = true;
    isDrawn.value = true; // Mark as drawn when user starts drawing

    const rect = canvasRef.value.getBoundingClientRect();
    const scaleX = canvasRef.value.width / rect.width; // Account for CSS vs. Canvas pixels
    const scaleY = canvasRef.value.height / rect.height;

    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;

    lastX = (clientX - rect.left) * scaleX;
    lastY = (clientY - rect.top) * scaleY;

    if (ctx) {
      resetCtx();
      ctx.beginPath();
      ctx.moveTo(lastX, lastY);
    }
  };

  const draw = (e: MouseEvent | TouchEvent) => {
    if (!isDrawing || !ctx || !canvasRef.value) return;

    const rect = canvasRef.value.getBoundingClientRect();
    const scaleX = canvasRef.value.width / rect.width;
    const scaleY = canvasRef.value.height / rect.height;

    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;

    const currentX = (clientX - rect.left) * scaleX;
    const currentY = (clientY - rect.top) * scaleY;

    ctx.lineTo(currentX, currentY);
    ctx.stroke();

    lastX = currentX;
    lastY = currentY;
  };

  const stopDrawing = () => {
    isDrawing = false;
    if (ctx) {
      ctx.closePath();
    }
  };

  const clearSignature = () => {
    if (ctx && canvasRef.value) {
      ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
      signatureVal.value = null;
      isDrawn.value = false;
    }
  };

  const saveSignature = async () => {
    if (!canvasRef.value || !isDrawn.value) {
      Message.warning('请先签名！');
      return;
    }

    isLoading.value = true;
    try {
      const originalCanvas = canvasRef.value;
      const originalWidth = originalCanvas.width;
      const originalHeight = originalCanvas.height;

      const rotatedCanvas = document.createElement('canvas');
      const rotatedCtx = rotatedCanvas.getContext('2d');

      if (!rotatedCtx) {
        throw new Error('Could not get 2D context for rotated canvas.');
      }

      rotatedCanvas.width = originalHeight;
      rotatedCanvas.height = originalWidth;

      rotatedCtx.translate(0, originalWidth);
      rotatedCtx.rotate(-Math.PI / 2);

      rotatedCtx.drawImage(originalCanvas, 0, 0);

      const dataURL = rotatedCanvas.toDataURL('image/png');
      const blob = await (await fetch(dataURL)).blob();

      const fileName = `signature_${Date.now()}.png`;
      const file = new File([blob], fileName, { type: 'image/png' });

      // 上传文件
      const url = await oss.uploadSimply({ file }, 'attachments/', props.option);

      signatureVal.value = {
        udf1: url,
        name: fileName,
      };
      emit('saveSignature', signatureVal.value);
      Message.success('签名保存成功！');
    } catch (error: any) {
      Message.error(`签名保存失败：${error.message || '未知错误'}`);
    } finally {
      isLoading.value = false;
    }
  };

  const closeSignature = () => {
    emit('closeSignature');
  };

  onMounted(() => {
    initCanvas();
    if (canvasRef.value) {
      canvasRef.value.addEventListener('mousedown', startDrawing);
      canvasRef.value.addEventListener('mousemove', draw);
      canvasRef.value.addEventListener('mouseup', stopDrawing);
      canvasRef.value.addEventListener('mouseout', stopDrawing);

      canvasRef.value.addEventListener('touchstart', startDrawing, { passive: true });
      canvasRef.value.addEventListener('touchmove', draw, { passive: true });
      canvasRef.value.addEventListener('touchend', stopDrawing);
      canvasRef.value.addEventListener('touchcancel', stopDrawing);
    }
  });

  onUnmounted(() => {
    if (canvasRef.value) {
      canvasRef.value.removeEventListener('mousedown', startDrawing);
      canvasRef.value.removeEventListener('mousemove', draw);
      canvasRef.value.removeEventListener('mouseup', stopDrawing);
      canvasRef.value.removeEventListener('mouseout', stopDrawing);

      canvasRef.value.removeEventListener('touchstart', startDrawing);
      canvasRef.value.removeEventListener('touchmove', draw);
      canvasRef.value.removeEventListener('touchend', stopDrawing);
      canvasRef.value.removeEventListener('touchcancel', stopDrawing);
    }
  });

  watch([canvasWidth, canvasHeight], () => {
    initCanvas();
  });
</script>

<template>
  <div
    :style="{
      width: totalWidth,
      height: totalHeight,
    }"
    :class="[
      'flex',
      'border',
      'border-gray-200',
      containerFlexClass,
      'items-center',
      'justify-center',
      'rounded-lg',
      'bg-white',
      'overflow-hidden',
      'shadow-md',
    ]"
  >
    <div
      class="flex-grow flex items-center justify-center rounded-md overflow-hidden relative"
      :class="{ 'border-r border-gray-200': actionDirection === 'right' }"
      :style="{
        width: actionDirection === 'bottom' ? '100%' : `calc(100% - ${buttonAreaWidth}px)`,
        height: actionDirection === 'bottom' ? `calc(100% - ${buttonAreaHeight}px)` : '100%',
      }"
    >
      <canvas
        ref="canvasRef"
        class="border bg-white rounded-md cursor-crosshair"
        :width="canvasWidth"
        :height="canvasHeight"
        style="width: 100%; height: 100%; display: block"
        v-bind="extend(defaultProps, schemaField.inputWidgetProps || {}, $attrs)"
      />
      <span
        v-if="!isDrawn && !(modelValue?.url || modelValue?.udf1)"
        class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-gray-400 text-sm"
      >
      </span>
    </div>

    <div class="w-full h-[120px] flex items-center justify-center">
      <div class="flex flex-col justify-start w-[120px] ml-[10%] gap-2 h-full rotate-90">
        <a-button
          type="dashed"
          size="large"
          class="w-[80%] rounded-md duration-200"
          :disabled="isLoading"
          @click="clearSignature"
        >
          清除
        </a-button>
        <a-button
          type="dashed"
          size="large"
          class="w-[80%] rounded-md duration-200"
          :loading="isLoading"
          :disabled="isLoading || !isDrawn"
          status="warning"
          @click="saveSignature"
        >
          确认
        </a-button>
        <a-button
          v-if="useType == 'WebApp'"
          type="dashed"
          status="danger"
          size="large"
          class="w-[80%] rounded-md duration-200"
          :disabled="isLoading"
          @click="closeSignature"
        >
          关闭
        </a-button>
      </div>
    </div>
    <div
      v-if="false"
      :style="buttonContainerStyle"
      :class="['flex', buttonContainerFlexClass, 'justify-center', 'items-center', 'gap-2', 'p-1', 'bg-gray-50']"
    >
      <a-button
        size="small"
        type="dashed"
        class="flex-1 h-full rounded-md duration-200"
        :loading="isLoading"
        :disabled="isLoading || !isDrawn"
        @click="saveSignature"
      >
        确认
      </a-button>
      <a-button
        size="small"
        type="dashed"
        class="flex-1 h-full rounded-md duration-200"
        :disabled="isLoading"
        @click="clearSignature"
      >
        清除
      </a-button>
      <a-button
        v-if="useType == 'WebApp'"
        size="small"
        type="dashed"
        status="danger"
        class="flex-1 h-full rounded-md duration-200 hover:bg-gray-100"
        :disabled="isLoading"
        @click="closeSignature"
      >
        关闭
      </a-button>
    </div>
  </div>
</template>

<style scoped>
  canvas {
    touch-action: none;
  }
</style>
