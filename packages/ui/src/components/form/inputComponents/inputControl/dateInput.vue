<template>
  <a-date-picker v-model="inputValue" v-bind="extend(defaultProps, schemaField.inputWidgetProps || {}, $attrs)" />
</template>

<script lang="ts" setup>
  import { cloneDeep, extend } from 'lodash';
  import { SchemaField } from '@repo/infrastructure/types';
  import { computed } from 'vue';
  import { defaultInputWidgetAttributes } from '../../inputWidgetAttributes';

  const defaultProps = cloneDeep(defaultInputWidgetAttributes);
  const props = defineProps<{
    modelValue: Date;
    schemaField: SchemaField;
  }>();

  const emits = defineEmits(['update:modelValue']);

  const inputValue: any = computed({
    get() {
      if (!props.modelValue) {
        return undefined;
      }
      return props.modelValue as Date;
    },
    set(val: any) {
      emits('update:modelValue', val);
    },
  });
</script>

<style scoped></style>
