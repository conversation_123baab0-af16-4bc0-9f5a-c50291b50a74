<template>
  <a-form-item
    v-if="fieldVisible"
    :field="field?.key"
    :label="label || field?.label"
    :rules="getValidateRules(record, field, schema)"
  >
    <slot>
      <input-field
        ref="inputFieldRef"
        v-model="inputValue"
        v-model:record="recordData"
        :field-key="field.key"
        :schema="schema"
      />
    </slot>
  </a-form-item>
</template>

<script lang="ts" setup>
  import { computed, inject, onMounted, PropType, provide, ref } from 'vue';
  import { Schema, SchemaField } from '@repo/infrastructure/types';
  import { cloneDeep } from 'lodash';
  import { getValidateRules } from '../validator';
  import InputField from './inputField.vue';

  const props = defineProps({
    schema: {
      type: Object as PropType<Schema>,
      required: true,
    },
    fieldKey: {
      type: String as PropType<string>,
      required: true,
    },
    label: {
      type: String,
      default: '',
    },
    modelValue: {
      type: [String, Number, Boolean, Array, Object] as PropType<any>,
    },
    record: {
      type: Object as PropType<Record<string, any>>,
    },
  });

  if (!props.schema?.schemaFieldsMap[props.fieldKey]) {
    throw new Error(`Field is required: ${props.fieldKey}`);
  }

  const field = ref<SchemaField>(cloneDeep(props.schema?.schemaFieldsMap[props.fieldKey]) || { key: props.fieldKey });
  const emit = defineEmits(['update:modelValue', 'update:record']);
  const inputFieldRef = ref<any>(null);
  const inputValue = computed({
    get() {
      return props.modelValue;
    },
    set(val: any) {
      emit('update:modelValue', val);
    },
  });

  const recordData = computed({
    get() {
      return props.record;
    },
    set(val: any) {
      emit('update:record', val);
    },
  });

  const fieldVisible = computed(() => {
    if (typeof field.value?.inputWidgetProps?.dynamicVisible !== 'function') {
      return true;
    }

    return field.value.inputWidgetProps.dynamicVisible(recordData.value);
  });

  const handleRecordChange = async (key: string, value: any, isInit?: boolean) => {
    inputFieldRef.value?.handleRecordChange(key, value, isInit);
  };

  const formData = inject('formData');
  provide('formData', formData);

  defineExpose({
    handleRecordChange,
  });

  onMounted(() => {
    if (!field.value) {
      throw new Error(`Field is required: ${props.fieldKey}`);
    }
  });
</script>

<script lang="ts">
  export default {
    name: 'FormItem',
  };
</script>
