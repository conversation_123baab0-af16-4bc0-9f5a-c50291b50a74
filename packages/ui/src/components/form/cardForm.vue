<script setup lang="ts">
  import { computed, inject, PropType, ref } from 'vue';
  import CrudForm from './form.vue';

  const props = defineProps({
    modelValue: {
      type: Object as PropType<Record<string, any>>,
    },
    schema: {
      type: Object as () => PropType<any>,
      required: true,
    },
    moduleName: {
      type: String,
      required: true,
    },
    rawId: {
      type: String,
      default: '',
    },
  });

  const emit = defineEmits(['update:modelValue']);
  const router: any = inject('router');

  const editData = computed({
    get: () => props.modelValue || {},
    set: (val) => {
      emit('update:modelValue', val);
    },
  });
  const route = router.currentRoute.value || router.currentRoute;
  const id = ref(route?.params.id || route?.query.id || props.rawId);

  const title = computed(() => (id.value ? '修改' : '新增') + props.moduleName);
</script>

<template>
  <a-card v-if="schema" :title="title">
    <template #extra>
      <a-button size="mini" @click="() => router.back()">
        <template #icon>
          <IconArrowLeft />
        </template>
        返回
      </a-button>
    </template>
    <crud-form v-model="editData" :schema="schema" v-bind="$attrs" />
  </a-card>
</template>

<style scoped lang="scss"></style>
